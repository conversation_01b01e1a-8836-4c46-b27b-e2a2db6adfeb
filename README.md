# Bonds Web Application - Development Guide

A React + TypeScript + Vite application for bonds trading with comprehensive async flow handling and robust error management.

## Table of Contents

- [Architecture Overview](#architecture-overview)
- [Project Structure](#project-structure)
- [Core Patterns](#core-patterns)
- [State Management](#state-management)
- [Form Handling](#form-handling)
- [Error Handling](#error-handling)
- [Async Flow Management](#async-flow-management)
- [Best Practices](#best-practices)
- [Getting Started](#getting-started)

## Architecture Overview

This application is built with:

- **React 18** with TypeScript for type safety
- **Vite** for fast development and building
- **React Router** for client-side routing
- **TanStack Query** for server state management
- **Valtio** for client state management
- **Formik + Yup** for form handling and validation
- **Tailwind CSS** for styling
- **Playwright** for E2E testing
- **Protobuf** for API communication
- **Zag.js** for accessible UI components

## Project Structure

```
src/
├── app.tsx                    # Root app component with providers
├── main.tsx                   # Application entry point
├── router.tsx                 # React Router configuration
├── app.css                    # Global styles
├── assets/                    # Static assets (images, icons, translations)
├── clients/                   # API clients and HTTP utilities
│   ├── auth.ts               # Authentication utilities
│   ├── broking-api.ts        # Broking API client
│   ├── business-api.ts       # Business API client
│   ├── identity-api.ts       # Identity API client
│   ├── personalization-api.ts # Personalization API client
│   └── gen/                  # Generated protobuf types
├── components/               # Reusable UI components
│   ├── ui/                   # Base UI components
│   ├── functional/           # Functional components (error boundaries, etc.)
│   ├── layouts/              # Layout components
│   ├── bonds/                # Domain-specific components
│   └── icons/                # Icon components
├── exceptions/               # Custom exception classes
├── hooks/                    # Custom React hooks
├── pages/                    # Page components organized by feature
│   ├── auth/                 # Authentication pages
│   ├── onboarding/           # User onboarding flow
│   ├── bonds/                # Bond-related pages
│   └── ...                   # Other feature pages
├── queries/                  # TanStack Query configurations
├── stores/                   # Valtio state stores
├── types/                    # TypeScript type definitions
└── utils/                    # Utility functions

tests/
├── e2e/                      # Playwright E2E tests
│   ├── fixtures/             # Test fixtures and API mocks
│   ├── storage-states/       # StorageState files for SDK testing
│   └── ...                   # Test files organized by feature
├── stubs/                    # SDK stubs for testing
└── utils/                    # Test utilities
```

## Core Patterns

### Component Organization

- **`src/components/ui/`**: Base UI components (Button, Input, Modal, etc.)
- **`src/components/functional/`**: Functional components (ErrorBoundary, QueryRenderer)
- **`src/components/layouts/`**: Layout components (WorkflowLayout, Root)
- **`src/components/bonds/`**: Domain-specific components (FormPage)

### Page Structure

- Located in `src/pages/` organized by feature
- Each page has its own directory with `index.tsx` and `schema.ts` (for forms)
- Use `Head` component for SEO metadata
- Implement proper loading and error states

### TypeScript Conventions

- **Prefer `type` over `interface`** for all type definitions
- Use proper TypeScript props for all components

```typescript
type ButtonProps = {
  size?: "medium" | "small";
  loading?: boolean;
  disabled?: boolean;
  children: ReactNode;
};
```

### Input Components

Built-in input masks for common formats:

- `mobile_number`: 10-digit mobile numbers
- `pan`: PAN card format
- `date`: DD/MM/YYYY format
- `inr`: Currency formatting
- `number`: Numeric input only

```typescript
<Input
  name="mobile"
  label="Mobile Number"
  mask="mobile_number"
  prefix={<span>+91</span>}
/>
```

## State Management

### Decision Matrix

For flows that mutate server-side state (API calls or SDK operations):

- **Use Formik**: When the flow involves form fields that need validation
- **Use React Query Mutations**: When the flow has no form fields (just button clicks)

### Client State (Valtio)

Used for user authentication and app-wide state:

```typescript
const store = proxy<{ data: UserProfileState }>({
  data: { type: "guest" },
});

export function useUser() {
  return useSnapshot(store);
}
```

### Server State (TanStack Query)

Used for all API data fetching with automatic caching, retries, and error handling:

```typescript
export function createBondDetailsQueryOptions(bondId: string) {
  return {
    queryKey: ["bond-details", bondId],
    queryFn: () => getBondDetails(bondId),
  };
}
```

## Form Handling

### Validation Behavior

**Key Principles:**

- **Validate on Mount**: Always use `validateOnMount={true}` to enable proper submit button state
- **Error Display**: Only show field errors after the user has attempted to submit at least once

### Form Pattern

```typescript
<Formik
  initialValues={initialValues}
  validationSchema={validationSchema}
  validateOnMount={true}  // Required for submit button state
  onSubmit={withErrorToast(async (values) => {
    await submitData(values);
    navigate("/next-step");
  })}
>
  {({ isSubmitting, isValid, submitCount }) => (
    <Form>
      <Field
        name="email"
        component={Input}
        showError={submitCount > 0}  // Only show errors after first submit
      />
      <Button type="submit" loading={isSubmitting} disabled={!isValid}>
        Submit
      </Button>
    </Form>
  )}
</Formik>
```

### Validation Schemas

Use Yup for comprehensive validation:

```typescript
export const validationSchema = yup.object({
  pan: yup
    .string()
    .required("PAN number is required")
    .matches(PAN_NUMBER, "PAN is invalid, check again?"),
  dateOfBirth: yup
    .string()
    .required("Date of birth is required")
    .test("is-valid-date", "Date is not valid", (value) =>
      dayjs(value, "DD/MM/YYYY", true).isValid()
    ),
});

export type FormValues = yup.InferType<typeof validationSchema>;
```

## Error Handling

### Centralized System

1. **Query Client Configuration**: Automatic error toasts for mutations
2. **Error Boundaries**: Route-level error catching
3. **Error Utilities**: Consistent error message formatting
4. **Custom Exceptions**: Typed error handling

### Error Toast Wrapper

```typescript
const handleSubmit = withErrorToast(async (values: FormValues) => {
  await submitForm(values);
});
```

### Error Message Patterns

- **Network errors**: "Please check your internet connection and try again."
- **500 server errors**: "Something went wrong. Please try again."
- **Validation errors**: Specific field-level messages
- **Unknown errors**: Generic fallback message

### Query Client Configuration

```typescript
export const queryClient = new QueryClient({
  defaultOptions: {
    mutations: {
      onError: async (error) => {
        const message = await getErrorMessage(error);
        toaster.create({
          description: message,
          type: "error",
          duration: 3000,
        });
      },
    },
  },
});
```

## Async Flow Management

### Multi-Step Flow States

Handle complex async flows with multiple states:

1. **Loading States**: Show appropriate loading indicators
2. **Error States**: Display specific error messages and retry options
3. **Success States**: Navigate to next step or show confirmation
4. **Polling States**: Handle long-running operations with status polling

### Polling Pattern

```typescript
export function getRpdStatusQueryOptions(refId: string) {
  return {
    queryKey: ["bank-rpd-status", refId],
    queryFn: () => getRpdStatus(refId),
    refetchInterval(query) {
      return query.state.dataUpdateCount <= maxRpdFetchCount &&
        !query.state.data?.verified &&
        !["SUCCESS", "EXPIRED", "FAILED"].includes(
          query.state.data?.status ?? ""
        )
        ? 3000 // Poll every 3 seconds
        : false; // Stop polling
    },
  };
}
```

### External SDK Integration

**Always model SDK calls as async functions**, even if the SDK uses callbacks:

```typescript
// Wrap callback-based SDKs in Promises
async function initializeDigioSDK(config: DigioConfig): Promise<DigioResult> {
  return new Promise((resolve, reject) => {
    const digio = new Digio(config);
    digio.init({
      onSuccess: (result) => resolve(result),
      onError: (error) => reject(new Error(error.message)),
      onCancel: () => reject(new Error("User cancelled the process")),
    });
  });
}
```

**Then wrap with React Query mutations:**

```typescript
export function useDigioKYCMutation() {
  return useMutation({
    mutationFn: async (config: DigioConfig) => {
      return await initializeDigioSDK(config);
    },
    onSuccess: (result) => {
      // Handle successful KYC completion
    },
  });
}

// Component usage
function KYCPage() {
  const digioMutation = useDigioKYCMutation();

  return (
    <Button
      onClick={() => digioMutation.mutate(kycConfig)}
      loading={digioMutation.isPending}
    >
      Start KYC
    </Button>
  );
}
```

### Formik vs React Query Decision

**For flows with form fields** → Use Formik:

```typescript
<Formik
  onSubmit={withErrorToast(async (values) => {
    await submitPANDetails(values);  // API call
    // Or: await initializeKYCSDK(values);  // SDK call
    navigate("/next-step");
  })}
>
```

**For flows without forms** → Use React Query mutations:

```typescript
const startKYCMutation = useMutation({
  mutationFn: () => initializeKYCSDK(config),
  onSuccess: () => navigate("/kyc-status"),
});
```

## Best Practices

### TypeScript

- **Prefer `type` over `interface`** for all type definitions
- Use proper TypeScript props for all components
- Leverage TypeScript for better developer experience

### Component Development

- **Single Responsibility**: Each component should have one clear purpose
- **Accessibility**: Include proper ARIA labels and keyboard navigation
- **Performance**: Use React.memo for expensive components

### State Management

- **Server State**: Use TanStack Query for all API data
- **Client State**: Use Valtio for app-wide state (user, preferences)
- **Local State**: Use useState for component-specific state
- **Mutations**: Use Formik for flows with forms, React Query mutations for button-only flows

### Error Handling

- **Centralized**: Use query client configuration for global error handling
- **User-Friendly**: Always show actionable error messages
- **withErrorToast**: Use for automatic error handling in async operations

### Testing

- **Comprehensive Documentation**: See `tests/README.md` for detailed patterns
- **Focus on User Journeys**: Test complete workflows, not isolated functions
- **Realistic Test Data**: Use faker-generated data over hardcoded values

### Performance

- **Code Splitting**: Use React.lazy for route-based code splitting
- **Query Optimization**: Use proper query keys and stale times
- **Bundle Analysis**: Regularly check bundle size and dependencies

### Documentation

Do not add comments for self-explanatory code.

### API Integration

- **Type Safety**: Use generated types from protobuf schemas
- **Query Organization**: Create query functions for API calls in `queries/` folder for reusability
- **External SDKs**: Model SDK calls as async functions and wrap with React Query mutations
- **Authentication**: Automatic token refresh and 401 handling

#### Query Function Pattern

Organize all API calls in the `queries/` folder with reusable query options:

```typescript
// queries/bonds.ts
export async function getBondDetails(bondId: string) {
  const response = await request({
    url: `/v1/bond-details/${bondId}`,
    method: "GET",
    responseSchema: BondDetailsResponseSchema,
  });
  return response.bondDetails;
}

export function createBondDetailsQueryOptions(bondId: string) {
  return {
    queryKey: ["bond-details", bondId],
    queryFn: () => getBondDetails(bondId),
    staleTime: 5 * 60 * 1000, // 5 minutes
  };
}

// Component usage
const query = useQuery(createBondDetailsQueryOptions(bondId));
```

#### Mutation Pattern

```typescript
// queries/onboarding.ts
export async function submitPANDetails(data: PANSubmissionData) {
  return await request({
    url: "/v1/onboarding",
    method: "POST",
    data: toBinary(
      OnboardingRequestSchema,
      create(OnboardingRequestSchema, data)
    ),
    responseSchema: OnboardingResponseSchema,
  });
}

export function usePANSubmissionMutation() {
  return useMutation({
    mutationFn: submitPANDetails,
    onSuccess: (response) => {
      // Handle success, invalidate related queries
      queryClient.invalidateQueries({ queryKey: ["onboarding-state"] });
    },
  });
}
```

Use mutation's state directly instead of adding new component state for error/loading handling.

## Getting Started

### Quick Start

```bash
npm install          # Install dependencies
npm run dev          # Start development server
npm run test:e2e     # Run tests
```

### Development Commands

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier
- `npm run test:e2e` - Run Playwright tests
- `npm run storybook` - Start Storybook
- `npm run generate:platform-proto` - Generate platform protobuf types
- `npm run generate:broking-proto` - Generate broking protobuf types

---

This guide provides the foundation for building robust, maintainable features in the bonds web application. Always prioritize user experience, error handling, and comprehensive testing when implementing new functionality.
