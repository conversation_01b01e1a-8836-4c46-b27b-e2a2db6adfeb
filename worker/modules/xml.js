/**
 * Bundled by jsDelivr using Rollup v2.79.2 and Terser v5.39.0.
 * Original file: /npm/xml@1.0.1/lib/xml.js
 *
 * Do NOT use SRI with dynamically generated files! More information: https://www.jsdelivr.com/using-sri-with-dynamic-files
 */
var e="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{};function t(){throw new Error("setTimeout has not been defined")}function n(){throw new Error("clearTimeout has not been defined")}var r=t,i=n;function o(e){if(r===setTimeout)return setTimeout(e,0);if((r===t||!r)&&setTimeout)return r=setTimeout,setTimeout(e,0);try{return r(e,0)}catch(t){try{return r.call(null,e,0)}catch(t){return r.call(this,e,0)}}}"function"==typeof e.setTimeout&&(r=setTimeout),"function"==typeof e.clearTimeout&&(i=clearTimeout);var a,s=[],u=!1,f=-1;function h(){u&&a&&(u=!1,a.length?s=a.concat(s):f=-1,s.length&&c())}function c(){if(!u){var e=o(h);u=!0;for(var t=s.length;t;){for(a=s,s=[];++f<t;)a&&a[f].run();f=-1,t=s.length}a=null,u=!1,function(e){if(i===clearTimeout)return clearTimeout(e);if((i===n||!i)&&clearTimeout)return i=clearTimeout,clearTimeout(e);try{return i(e)}catch(t){try{return i.call(null,e)}catch(t){return i.call(this,e)}}}(e)}}function l(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];s.push(new p(e,t)),1!==s.length||u||o(c)}function p(e,t){this.fun=e,this.array=t}p.prototype.run=function(){this.fun.apply(null,this.array)};function d(){}var g=d,y=d,v=d,w=d,b=d,m=d,_=d;var E=e.performance||{},R=E.now||E.mozNow||E.msNow||E.oNow||E.webkitNow||function(){return(new Date).getTime()};var S=new Date;var A={nextTick:l,title:"browser",browser:!0,env:{},argv:[],version:"",versions:{},on:g,addListener:y,once:v,off:w,removeListener:b,removeAllListeners:m,emit:_,binding:function(e){throw new Error("process.binding is not supported")},cwd:function(){return"/"},chdir:function(e){throw new Error("process.chdir is not supported")},umask:function(){return 0},hrtime:function(e){var t=.001*R.call(E),n=Math.floor(t),r=Math.floor(t%1*1e9);return e&&(n-=e[0],(r-=e[1])<0&&(n--,r+=1e9)),[n,r]},platform:"browser",release:{},config:{},uptime:function(){return(new Date-S)/1e3}};function L(e){return e&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var T={exports:{}},k={"&":"&amp;",'"':"&quot;","'":"&apos;","<":"&lt;",">":"&gt;"};var M=function(e){return e&&e.replace?e.replace(/([&"<>'])/g,(function(e,t){return k[t]})):e};function x(){}function C(){C.init.call(this)}function P(e){return void 0===e._maxListeners?C.defaultMaxListeners:e._maxListeners}function O(e,t,n,r){var i,o,a,s;if("function"!=typeof n)throw new TypeError('"listener" argument must be a function');if((o=e._events)?(o.newListener&&(e.emit("newListener",t,n.listener?n.listener:n),o=e._events),a=o[t]):(o=e._events=new x,e._eventsCount=0),a){if("function"==typeof a?a=o[t]=r?[n,a]:[a,n]:r?a.unshift(n):a.push(n),!a.warned&&(i=P(e))&&i>0&&a.length>i){a.warned=!0;var u=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+t+" listeners added. Use emitter.setMaxListeners() to increase limit");u.name="MaxListenersExceededWarning",u.emitter=e,u.type=t,u.count=a.length,s=u,"function"==typeof console.warn?console.warn(s):console.log(s)}}else a=o[t]=n,++e._eventsCount;return e}function j(e,t,n){var r=!1;function i(){e.removeListener(t,i),r||(r=!0,n.apply(e,arguments))}return i.listener=n,i}function B(e){var t=this._events;if(t){var n=t[e];if("function"==typeof n)return 1;if(n)return n.length}return 0}function U(e,t){for(var n=new Array(t);t--;)n[t]=e[t];return n}x.prototype=Object.create(null),C.EventEmitter=C,C.usingDomains=!1,C.prototype.domain=void 0,C.prototype._events=void 0,C.prototype._maxListeners=void 0,C.defaultMaxListeners=10,C.init=function(){this.domain=null,C.usingDomains&&undefined.active,this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=new x,this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},C.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},C.prototype.getMaxListeners=function(){return P(this)},C.prototype.emit=function(e){var t,n,r,i,o,a,s,u="error"===e;if(a=this._events)u=u&&null==a.error;else if(!u)return!1;if(s=this.domain,u){if(t=arguments[1],!s){if(t instanceof Error)throw t;var f=new Error('Uncaught, unspecified "error" event. ('+t+")");throw f.context=t,f}return t||(t=new Error('Uncaught, unspecified "error" event')),t.domainEmitter=this,t.domain=s,t.domainThrown=!1,s.emit("error",t),!1}if(!(n=a[e]))return!1;var h="function"==typeof n;switch(r=arguments.length){case 1:!function(e,t,n){if(t)e.call(n);else for(var r=e.length,i=U(e,r),o=0;o<r;++o)i[o].call(n)}(n,h,this);break;case 2:!function(e,t,n,r){if(t)e.call(n,r);else for(var i=e.length,o=U(e,i),a=0;a<i;++a)o[a].call(n,r)}(n,h,this,arguments[1]);break;case 3:!function(e,t,n,r,i){if(t)e.call(n,r,i);else for(var o=e.length,a=U(e,o),s=0;s<o;++s)a[s].call(n,r,i)}(n,h,this,arguments[1],arguments[2]);break;case 4:!function(e,t,n,r,i,o){if(t)e.call(n,r,i,o);else for(var a=e.length,s=U(e,a),u=0;u<a;++u)s[u].call(n,r,i,o)}(n,h,this,arguments[1],arguments[2],arguments[3]);break;default:for(i=new Array(r-1),o=1;o<r;o++)i[o-1]=arguments[o];!function(e,t,n,r){if(t)e.apply(n,r);else for(var i=e.length,o=U(e,i),a=0;a<i;++a)o[a].apply(n,r)}(n,h,this,i)}return!0},C.prototype.addListener=function(e,t){return O(this,e,t,!1)},C.prototype.on=C.prototype.addListener,C.prototype.prependListener=function(e,t){return O(this,e,t,!0)},C.prototype.once=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.on(e,j(this,e,t)),this},C.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.prependListener(e,j(this,e,t)),this},C.prototype.removeListener=function(e,t){var n,r,i,o,a;if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');if(!(r=this._events))return this;if(!(n=r[e]))return this;if(n===t||n.listener&&n.listener===t)0==--this._eventsCount?this._events=new x:(delete r[e],r.removeListener&&this.emit("removeListener",e,n.listener||t));else if("function"!=typeof n){for(i=-1,o=n.length;o-- >0;)if(n[o]===t||n[o].listener&&n[o].listener===t){a=n[o].listener,i=o;break}if(i<0)return this;if(1===n.length){if(n[0]=void 0,0==--this._eventsCount)return this._events=new x,this;delete r[e]}else!function(e,t){for(var n=t,r=n+1,i=e.length;r<i;n+=1,r+=1)e[n]=e[r];e.pop()}(n,i);r.removeListener&&this.emit("removeListener",e,a||t)}return this},C.prototype.off=function(e,t){return this.removeListener(e,t)},C.prototype.removeAllListeners=function(e){var t,n;if(!(n=this._events))return this;if(!n.removeListener)return 0===arguments.length?(this._events=new x,this._eventsCount=0):n[e]&&(0==--this._eventsCount?this._events=new x:delete n[e]),this;if(0===arguments.length){for(var r,i=Object.keys(n),o=0;o<i.length;++o)"removeListener"!==(r=i[o])&&this.removeAllListeners(r);return this.removeAllListeners("removeListener"),this._events=new x,this._eventsCount=0,this}if("function"==typeof(t=n[e]))this.removeListener(e,t);else if(t)do{this.removeListener(e,t[t.length-1])}while(t[0]);return this},C.prototype.listeners=function(e){var t,n=this._events;return n&&(t=n[e])?"function"==typeof t?[t.listener||t]:function(e){for(var t=new Array(e.length),n=0;n<t.length;++n)t[n]=e[n].listener||e[n];return t}(t):[]},C.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):B.call(e,t)},C.prototype.listenerCount=B,C.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};var D=[],I=[],Y="undefined"!=typeof Uint8Array?Uint8Array:Array,z=!1;function q(){z=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t=0;t<64;++t)D[t]=e[t],I[e.charCodeAt(t)]=t;I["-".charCodeAt(0)]=62,I["_".charCodeAt(0)]=63}function N(e,t,n){for(var r,i,o=[],a=t;a<n;a+=3)r=(e[a]<<16)+(e[a+1]<<8)+e[a+2],o.push(D[(i=r)>>18&63]+D[i>>12&63]+D[i>>6&63]+D[63&i]);return o.join("")}function W(e){var t;z||q();for(var n=e.length,r=n%3,i="",o=[],a=16383,s=0,u=n-r;s<u;s+=a)o.push(N(e,s,s+a>u?u:s+a));return 1===r?(t=e[n-1],i+=D[t>>2],i+=D[t<<4&63],i+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],i+=D[t>>10],i+=D[t>>4&63],i+=D[t<<2&63],i+="="),o.push(i),o.join("")}function F(e,t,n,r,i){var o,a,s=8*i-r-1,u=(1<<s)-1,f=u>>1,h=-7,c=n?i-1:0,l=n?-1:1,p=e[t+c];for(c+=l,o=p&(1<<-h)-1,p>>=-h,h+=s;h>0;o=256*o+e[t+c],c+=l,h-=8);for(a=o&(1<<-h)-1,o>>=-h,h+=r;h>0;a=256*a+e[t+c],c+=l,h-=8);if(0===o)o=1-f;else{if(o===u)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,r),o-=f}return(p?-1:1)*a*Math.pow(2,o-r)}function H(e,t,n,r,i,o){var a,s,u,f=8*o-i-1,h=(1<<f)-1,c=h>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,p=r?0:o-1,d=r?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=h):(a=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-a))<1&&(a--,u*=2),(t+=a+c>=1?l/u:l*Math.pow(2,1-c))*u>=2&&(a++,u/=2),a+c>=h?(s=0,a=h):a+c>=1?(s=(t*u-1)*Math.pow(2,i),a+=c):(s=t*Math.pow(2,c-1)*Math.pow(2,i),a=0));i>=8;e[n+p]=255&s,p+=d,s/=256,i-=8);for(a=a<<i|s,f+=i;f>0;e[n+p]=255&a,p+=d,a/=256,f-=8);e[n+p-d]|=128*g}var $={}.toString,J=Array.isArray||function(e){return"[object Array]"==$.call(e)};function G(){return V.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function Z(e,t){if(G()<t)throw new RangeError("Invalid typed array length");return V.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=V.prototype:(null===e&&(e=new V(t)),e.length=t),e}function V(e,t,n){if(!(V.TYPED_ARRAY_SUPPORT||this instanceof V))return new V(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return X(this,e)}return K(this,e,t,n)}function K(e,t,n,r){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,n,r){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");t=void 0===n&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,n):new Uint8Array(t,n,r);V.TYPED_ARRAY_SUPPORT?(e=t).__proto__=V.prototype:e=ee(e,t);return e}(e,t,n,r):"string"==typeof t?function(e,t,n){"string"==typeof n&&""!==n||(n="utf8");if(!V.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|re(t,n);e=Z(e,r);var i=e.write(t,n);i!==r&&(e=e.slice(0,i));return e}(e,t,n):function(e,t){if(ne(t)){var n=0|te(t.length);return 0===(e=Z(e,n)).length||t.copy(e,0,0,n),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(r=t.length)!=r?Z(e,0):ee(e,t);if("Buffer"===t.type&&J(t.data))return ee(e,t.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function Q(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function X(e,t){if(Q(t),e=Z(e,t<0?0:0|te(t)),!V.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function ee(e,t){var n=t.length<0?0:0|te(t.length);e=Z(e,n);for(var r=0;r<n;r+=1)e[r]=255&t[r];return e}function te(e){if(e>=G())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+G().toString(16)+" bytes");return 0|e}function ne(e){return!(null==e||!e._isBuffer)}function re(e,t){if(ne(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return xe(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Ce(e).length;default:if(r)return xe(e).length;t=(""+t).toLowerCase(),r=!0}}function ie(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return be(this,t,n);case"utf8":case"utf-8":return ge(this,t,n);case"ascii":return ve(this,t,n);case"latin1":case"binary":return we(this,t,n);case"base64":return de(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return me(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function oe(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function ae(e,t,n,r,i){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(i)return-1;n=e.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof t&&(t=V.from(t,r)),ne(t))return 0===t.length?-1:se(e,t,n,r,i);if("number"==typeof t)return t&=255,V.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):se(e,[t],n,r,i);throw new TypeError("val must be string, number or Buffer")}function se(e,t,n,r,i){var o,a=1,s=e.length,u=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;a=2,s/=2,u/=2,n/=2}function f(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var h=-1;for(o=n;o<s;o++)if(f(e,o)===f(t,-1===h?0:o-h)){if(-1===h&&(h=o),o-h+1===u)return h*a}else-1!==h&&(o-=o-h),h=-1}else for(n+u>s&&(n=s-u),o=n;o>=0;o--){for(var c=!0,l=0;l<u;l++)if(f(e,o+l)!==f(t,l)){c=!1;break}if(c)return o}return-1}function ue(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r))>i&&(r=i):r=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var a=0;a<r;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[n+a]=s}return a}function fe(e,t,n,r){return Pe(xe(t,e.length-n),e,n,r)}function he(e,t,n,r){return Pe(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function ce(e,t,n,r){return he(e,t,n,r)}function le(e,t,n,r){return Pe(Ce(t),e,n,r)}function pe(e,t,n,r){return Pe(function(e,t){for(var n,r,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)r=(n=e.charCodeAt(a))>>8,i=n%256,o.push(i),o.push(r);return o}(t,e.length-n),e,n,r)}function de(e,t,n){return 0===t&&n===e.length?W(e):W(e.slice(t,n))}function ge(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var o,a,s,u,f=e[i],h=null,c=f>239?4:f>223?3:f>191?2:1;if(i+c<=n)switch(c){case 1:f<128&&(h=f);break;case 2:128==(192&(o=e[i+1]))&&(u=(31&f)<<6|63&o)>127&&(h=u);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(u=(15&f)<<12|(63&o)<<6|63&a)>2047&&(u<55296||u>57343)&&(h=u);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(u=(15&f)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&u<1114112&&(h=u)}null===h?(h=65533,c=1):h>65535&&(h-=65536,r.push(h>>>10&1023|55296),h=56320|1023&h),r.push(h),i+=c}return function(e){var t=e.length;if(t<=ye)return String.fromCharCode.apply(String,e);var n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=ye));return n}(r)}V.TYPED_ARRAY_SUPPORT=void 0===e.TYPED_ARRAY_SUPPORT||e.TYPED_ARRAY_SUPPORT,G(),V.poolSize=8192,V._augment=function(e){return e.__proto__=V.prototype,e},V.from=function(e,t,n){return K(null,e,t,n)},V.TYPED_ARRAY_SUPPORT&&(V.prototype.__proto__=Uint8Array.prototype,V.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&V[Symbol.species]),V.alloc=function(e,t,n){return function(e,t,n,r){return Q(t),t<=0?Z(e,t):void 0!==n?"string"==typeof r?Z(e,t).fill(n,r):Z(e,t).fill(n):Z(e,t)}(null,e,t,n)},V.allocUnsafe=function(e){return X(null,e)},V.allocUnsafeSlow=function(e){return X(null,e)},V.isBuffer=function(e){return null!=e&&(!!e._isBuffer||Oe(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&Oe(e.slice(0,0))}(e))},V.compare=function(e,t){if(!ne(e)||!ne(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,i=0,o=Math.min(n,r);i<o;++i)if(e[i]!==t[i]){n=e[i],r=t[i];break}return n<r?-1:r<n?1:0},V.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},V.concat=function(e,t){if(!J(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return V.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=V.allocUnsafe(t),i=0;for(n=0;n<e.length;++n){var o=e[n];if(!ne(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},V.byteLength=re,V.prototype._isBuffer=!0,V.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)oe(this,t,t+1);return this},V.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)oe(this,t,t+3),oe(this,t+1,t+2);return this},V.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)oe(this,t,t+7),oe(this,t+1,t+6),oe(this,t+2,t+5),oe(this,t+3,t+4);return this},V.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?ge(this,0,e):ie.apply(this,arguments)},V.prototype.equals=function(e){if(!ne(e))throw new TypeError("Argument must be a Buffer");return this===e||0===V.compare(this,e)},V.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},V.prototype.compare=function(e,t,n,r,i){if(!ne(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),t<0||n>e.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&t>=n)return 0;if(r>=i)return-1;if(t>=n)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(r>>>=0),a=(n>>>=0)-(t>>>=0),s=Math.min(o,a),u=this.slice(r,i),f=e.slice(t,n),h=0;h<s;++h)if(u[h]!==f[h]){o=u[h],a=f[h];break}return o<a?-1:a<o?1:0},V.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},V.prototype.indexOf=function(e,t,n){return ae(this,e,t,n,!0)},V.prototype.lastIndexOf=function(e,t,n){return ae(this,e,t,n,!1)},V.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return ue(this,e,t,n);case"utf8":case"utf-8":return fe(this,e,t,n);case"ascii":return he(this,e,t,n);case"latin1":case"binary":return ce(this,e,t,n);case"base64":return le(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return pe(this,e,t,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},V.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var ye=4096;function ve(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}function we(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}function be(e,t,n){var r=e.length;(!t||t<0)&&(t=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=t;o<n;++o)i+=Me(e[o]);return i}function me(e,t,n){for(var r=e.slice(t,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function _e(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function Ee(e,t,n,r,i,o){if(!ne(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function Re(e,t,n,r){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-n,2);i<o;++i)e[n+i]=(t&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function Se(e,t,n,r){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-n,4);i<o;++i)e[n+i]=t>>>8*(r?i:3-i)&255}function Ae(e,t,n,r,i,o){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function Le(e,t,n,r,i){return i||Ae(e,0,n,4),H(e,t,n,r,23,4),n+4}function Te(e,t,n,r,i){return i||Ae(e,0,n,8),H(e,t,n,r,52,8),n+8}V.prototype.slice=function(e,t){var n,r=this.length;if((e=~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),(t=void 0===t?r:~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e),V.TYPED_ARRAY_SUPPORT)(n=this.subarray(e,t)).__proto__=V.prototype;else{var i=t-e;n=new V(i,void 0);for(var o=0;o<i;++o)n[o]=this[o+e]}return n},V.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||_e(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r},V.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||_e(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},V.prototype.readUInt8=function(e,t){return t||_e(e,1,this.length),this[e]},V.prototype.readUInt16LE=function(e,t){return t||_e(e,2,this.length),this[e]|this[e+1]<<8},V.prototype.readUInt16BE=function(e,t){return t||_e(e,2,this.length),this[e]<<8|this[e+1]},V.prototype.readUInt32LE=function(e,t){return t||_e(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},V.prototype.readUInt32BE=function(e,t){return t||_e(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},V.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||_e(e,t,this.length);for(var r=this[e],i=1,o=0;++o<t&&(i*=256);)r+=this[e+o]*i;return r>=(i*=128)&&(r-=Math.pow(2,8*t)),r},V.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||_e(e,t,this.length);for(var r=t,i=1,o=this[e+--r];r>0&&(i*=256);)o+=this[e+--r]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},V.prototype.readInt8=function(e,t){return t||_e(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},V.prototype.readInt16LE=function(e,t){t||_e(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},V.prototype.readInt16BE=function(e,t){t||_e(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},V.prototype.readInt32LE=function(e,t){return t||_e(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},V.prototype.readInt32BE=function(e,t){return t||_e(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},V.prototype.readFloatLE=function(e,t){return t||_e(e,4,this.length),F(this,e,!0,23,4)},V.prototype.readFloatBE=function(e,t){return t||_e(e,4,this.length),F(this,e,!1,23,4)},V.prototype.readDoubleLE=function(e,t){return t||_e(e,8,this.length),F(this,e,!0,52,8)},V.prototype.readDoubleBE=function(e,t){return t||_e(e,8,this.length),F(this,e,!1,52,8)},V.prototype.writeUIntLE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||Ee(this,e,t,n,Math.pow(2,8*n)-1,0);var i=1,o=0;for(this[t]=255&e;++o<n&&(i*=256);)this[t+o]=e/i&255;return t+n},V.prototype.writeUIntBE=function(e,t,n,r){(e=+e,t|=0,n|=0,r)||Ee(this,e,t,n,Math.pow(2,8*n)-1,0);var i=n-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+n},V.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,1,255,0),V.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},V.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,2,65535,0),V.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Re(this,e,t,!0),t+2},V.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,2,65535,0),V.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Re(this,e,t,!1),t+2},V.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,4,4294967295,0),V.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):Se(this,e,t,!0),t+4},V.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,4,4294967295,0),V.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):Se(this,e,t,!1),t+4},V.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);Ee(this,e,t,n,i-1,-i)}var o=0,a=1,s=0;for(this[t]=255&e;++o<n&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a|0)-s&255;return t+n},V.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t|=0,!r){var i=Math.pow(2,8*n-1);Ee(this,e,t,n,i-1,-i)}var o=n-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a|0)-s&255;return t+n},V.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,1,127,-128),V.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},V.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,2,32767,-32768),V.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):Re(this,e,t,!0),t+2},V.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,2,32767,-32768),V.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):Re(this,e,t,!1),t+2},V.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,4,2147483647,-2147483648),V.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):Se(this,e,t,!0),t+4},V.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||Ee(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),V.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):Se(this,e,t,!1),t+4},V.prototype.writeFloatLE=function(e,t,n){return Le(this,e,t,!0,n)},V.prototype.writeFloatBE=function(e,t,n){return Le(this,e,t,!1,n)},V.prototype.writeDoubleLE=function(e,t,n){return Te(this,e,t,!0,n)},V.prototype.writeDoubleBE=function(e,t,n){return Te(this,e,t,!1,n)},V.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,o=r-n;if(this===e&&n<t&&t<r)for(i=o-1;i>=0;--i)e[i+t]=this[i+n];else if(o<1e3||!V.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},V.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!V.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var o;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=ne(e)?e:xe(new V(e,r).toString()),s=a.length;for(o=0;o<n-t;++o)this[o+t]=a[o%s]}return this};var ke=/[^+\/0-9A-Za-z-_]/g;function Me(e){return e<16?"0"+e.toString(16):e.toString(16)}function xe(e,t){var n;t=t||1/0;for(var r=e.length,i=null,o=[],a=0;a<r;++a){if((n=e.charCodeAt(a))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===r){(t-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function Ce(e){return function(e){var t,n,r,i,o,a;z||q();var s=e.length;if(s%4>0)throw new Error("Invalid string. Length must be a multiple of 4");o="="===e[s-2]?2:"="===e[s-1]?1:0,a=new Y(3*s/4-o),r=o>0?s-4:s;var u=0;for(t=0,n=0;t<r;t+=4,n+=3)i=I[e.charCodeAt(t)]<<18|I[e.charCodeAt(t+1)]<<12|I[e.charCodeAt(t+2)]<<6|I[e.charCodeAt(t+3)],a[u++]=i>>16&255,a[u++]=i>>8&255,a[u++]=255&i;return 2===o?(i=I[e.charCodeAt(t)]<<2|I[e.charCodeAt(t+1)]>>4,a[u++]=255&i):1===o&&(i=I[e.charCodeAt(t)]<<10|I[e.charCodeAt(t+1)]<<4|I[e.charCodeAt(t+2)]>>2,a[u++]=i>>8&255,a[u++]=255&i),a}(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(ke,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function Pe(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function Oe(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}var je="function"==typeof Object.create?function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:function(e,t){e.super_=t;var n=function(){};n.prototype=t.prototype,e.prototype=new n,e.prototype.constructor=e},Be=/%[sdj%]/g;function Ue(e){if(!Ge(e)){for(var t=[],n=0;n<arguments.length;n++)t.push(ze(arguments[n]));return t.join(" ")}n=1;for(var r=arguments,i=r.length,o=String(e).replace(Be,(function(e){if("%%"===e)return"%";if(n>=i)return e;switch(e){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch(e){return"[Circular]"}default:return e}})),a=r[n];n<i;a=r[++n])Je(a)||!Ke(a)?o+=" "+a:o+=" "+ze(a);return o}function De(t,n){if(Ze(e.process))return function(){return De(t,n).apply(this,arguments)};if(!0===A.noDeprecation)return t;var r=!1;return function(){if(!r){if(A.throwDeprecation)throw new Error(n);A.traceDeprecation?console.trace(n):console.error(n),r=!0}return t.apply(this,arguments)}}var Ie,Ye={};function ze(e,t){var n={seen:[],stylize:Ne};return arguments.length>=3&&(n.depth=arguments[2]),arguments.length>=4&&(n.colors=arguments[3]),$e(t)?n.showHidden=t:t&&function(e,t){if(!t||!Ke(t))return e;var n=Object.keys(t),r=n.length;for(;r--;)e[n[r]]=t[n[r]]}(n,t),Ze(n.showHidden)&&(n.showHidden=!1),Ze(n.depth)&&(n.depth=2),Ze(n.colors)&&(n.colors=!1),Ze(n.customInspect)&&(n.customInspect=!0),n.colors&&(n.stylize=qe),We(n,e,n.depth)}function qe(e,t){var n=ze.styles[t];return n?"["+ze.colors[n][0]+"m"+e+"["+ze.colors[n][1]+"m":e}function Ne(e,t){return e}function We(e,t,n){if(e.customInspect&&t&&et(t.inspect)&&t.inspect!==ze&&(!t.constructor||t.constructor.prototype!==t)){var r=t.inspect(n,e);return Ge(r)||(r=We(e,r,n)),r}var i=function(e,t){if(Ze(t))return e.stylize("undefined","undefined");if(Ge(t)){var n="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(n,"string")}if(r=t,"number"==typeof r)return e.stylize(""+t,"number");var r;if($e(t))return e.stylize(""+t,"boolean");if(Je(t))return e.stylize("null","null")}(e,t);if(i)return i;var o=Object.keys(t),a=function(e){var t={};return e.forEach((function(e,n){t[e]=!0})),t}(o);if(e.showHidden&&(o=Object.getOwnPropertyNames(t)),Xe(t)&&(o.indexOf("message")>=0||o.indexOf("description")>=0))return Fe(t);if(0===o.length){if(et(t)){var s=t.name?": "+t.name:"";return e.stylize("[Function"+s+"]","special")}if(Ve(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(Qe(t))return e.stylize(Date.prototype.toString.call(t),"date");if(Xe(t))return Fe(t)}var u,f,h="",c=!1,l=["{","}"];(u=t,Array.isArray(u)&&(c=!0,l=["[","]"]),et(t))&&(h=" [Function"+(t.name?": "+t.name:"")+"]");return Ve(t)&&(h=" "+RegExp.prototype.toString.call(t)),Qe(t)&&(h=" "+Date.prototype.toUTCString.call(t)),Xe(t)&&(h=" "+Fe(t)),0!==o.length||c&&0!=t.length?n<0?Ve(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),f=c?function(e,t,n,r,i){for(var o=[],a=0,s=t.length;a<s;++a)nt(t,String(a))?o.push(He(e,t,n,r,String(a),!0)):o.push("");return i.forEach((function(i){i.match(/^\d+$/)||o.push(He(e,t,n,r,i,!0))})),o}(e,t,n,a,o):o.map((function(r){return He(e,t,n,a,r,c)})),e.seen.pop(),function(e,t,n){var r=e.reduce((function(e,t){return t.indexOf("\n"),e+t.replace(/\u001b\[\d\d?m/g,"").length+1}),0);if(r>60)return n[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+n[1];return n[0]+t+" "+e.join(", ")+" "+n[1]}(f,h,l)):l[0]+h+l[1]}function Fe(e){return"["+Error.prototype.toString.call(e)+"]"}function He(e,t,n,r,i,o){var a,s,u;if((u=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?s=u.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):u.set&&(s=e.stylize("[Setter]","special")),nt(r,i)||(a="["+i+"]"),s||(e.seen.indexOf(u.value)<0?(s=Je(n)?We(e,u.value,null):We(e,u.value,n-1)).indexOf("\n")>-1&&(s=o?s.split("\n").map((function(e){return"  "+e})).join("\n").substr(2):"\n"+s.split("\n").map((function(e){return"   "+e})).join("\n")):s=e.stylize("[Circular]","special")),Ze(a)){if(o&&i.match(/^\d+$/))return s;(a=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+s}function $e(e){return"boolean"==typeof e}function Je(e){return null===e}function Ge(e){return"string"==typeof e}function Ze(e){return void 0===e}function Ve(e){return Ke(e)&&"[object RegExp]"===tt(e)}function Ke(e){return"object"==typeof e&&null!==e}function Qe(e){return Ke(e)&&"[object Date]"===tt(e)}function Xe(e){return Ke(e)&&("[object Error]"===tt(e)||e instanceof Error)}function et(e){return"function"==typeof e}function tt(e){return Object.prototype.toString.call(e)}function nt(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function rt(){this.head=null,this.tail=null,this.length=0}ze.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},ze.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},rt.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},rt.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},rt.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},rt.prototype.clear=function(){this.head=this.tail=null,this.length=0},rt.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,n=""+t.data;t=t.next;)n+=e+t.data;return n},rt.prototype.concat=function(e){if(0===this.length)return V.alloc(0);if(1===this.length)return this.head.data;for(var t=V.allocUnsafe(e>>>0),n=this.head,r=0;n;)n.data.copy(t,r),r+=n.data.length,n=n.next;return t};var it=V.isEncoding||function(e){switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function ot(e){switch(this.encoding=(e||"utf8").toLowerCase().replace(/[-_]/,""),function(e){if(e&&!it(e))throw new Error("Unknown encoding: "+e)}(e),this.encoding){case"utf8":this.surrogateSize=3;break;case"ucs2":case"utf16le":this.surrogateSize=2,this.detectIncompleteChar=st;break;case"base64":this.surrogateSize=3,this.detectIncompleteChar=ut;break;default:return void(this.write=at)}this.charBuffer=new V(6),this.charReceived=0,this.charLength=0}function at(e){return e.toString(this.encoding)}function st(e){this.charReceived=e.length%2,this.charLength=this.charReceived?2:0}function ut(e){this.charReceived=e.length%3,this.charLength=this.charReceived?3:0}ot.prototype.write=function(e){for(var t="";this.charLength;){var n=e.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:e.length;if(e.copy(this.charBuffer,this.charReceived,0,n),this.charReceived+=n,this.charReceived<this.charLength)return"";if(e=e.slice(n,e.length),!((i=(t=this.charBuffer.slice(0,this.charLength).toString(this.encoding)).charCodeAt(t.length-1))>=55296&&i<=56319)){if(this.charReceived=this.charLength=0,0===e.length)return t;break}this.charLength+=this.surrogateSize,t=""}this.detectIncompleteChar(e);var r=e.length;this.charLength&&(e.copy(this.charBuffer,0,e.length-this.charReceived,r),r-=this.charReceived);var i;r=(t+=e.toString(this.encoding,0,r)).length-1;if((i=t.charCodeAt(r))>=55296&&i<=56319){var o=this.surrogateSize;return this.charLength+=o,this.charReceived+=o,this.charBuffer.copy(this.charBuffer,o,0,o),e.copy(this.charBuffer,0,0,o),t.substring(0,r)}return t},ot.prototype.detectIncompleteChar=function(e){for(var t=e.length>=3?3:e.length;t>0;t--){var n=e[e.length-t];if(1==t&&n>>5==6){this.charLength=2;break}if(t<=2&&n>>4==14){this.charLength=3;break}if(t<=3&&n>>3==30){this.charLength=4;break}}this.charReceived=t},ot.prototype.end=function(e){var t="";if(e&&e.length&&(t=this.write(e)),this.charReceived){var n=this.charReceived,r=this.charBuffer,i=this.encoding;t+=r.slice(0,n).toString(i)}return t},ct.ReadableState=ht;var ft=function(e){if(Ze(Ie)&&(Ie=A.env.NODE_DEBUG||""),e=e.toUpperCase(),!Ye[e])if(new RegExp("\\b"+e+"\\b","i").test(Ie)){Ye[e]=function(){var t=Ue.apply(null,arguments);console.error("%s %d: %s",e,0,t)}}else Ye[e]=function(){};return Ye[e]}("stream");function ht(e,t){e=e||{},this.objectMode=!!e.objectMode,t instanceof Yt&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var n=e.highWaterMark,r=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:r,this.highWaterMark=~~this.highWaterMark,this.buffer=new rt,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.ranOut=!1,this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(this.decoder=new ot(e.encoding),this.encoding=e.encoding)}function ct(e){if(!(this instanceof ct))return new ct(e);this._readableState=new ht(e,this),this.readable=!0,e&&"function"==typeof e.read&&(this._read=e.read),C.call(this)}function lt(e,t,n,r,i){var o=function(e,t){var n=null;V.isBuffer(t)||"string"==typeof t||null==t||e.objectMode||(n=new TypeError("Invalid non-string/buffer chunk"));return n}(t,n);if(o)e.emit("error",o);else if(null===n)t.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var n=t.decoder.end();n&&n.length&&(t.buffer.push(n),t.length+=t.objectMode?1:n.length)}t.ended=!0,gt(e)}(e,t);else if(t.objectMode||n&&n.length>0)if(t.ended&&!i){var a=new Error("stream.push() after EOF");e.emit("error",a)}else if(t.endEmitted&&i){var s=new Error("stream.unshift() after end event");e.emit("error",s)}else{var u;!t.decoder||i||r||(n=t.decoder.write(n),u=!t.objectMode&&0===n.length),i||(t.reading=!1),u||(t.flowing&&0===t.length&&!t.sync?(e.emit("data",n),e.read(0)):(t.length+=t.objectMode?1:n.length,i?t.buffer.unshift(n):t.buffer.push(n),t.needReadable&&gt(e))),function(e,t){t.readingMore||(t.readingMore=!0,l(vt,e,t))}(e,t)}else i||(t.reading=!1);return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(t)}je(ct,C),ct.prototype.push=function(e,t){var n=this._readableState;return n.objectMode||"string"!=typeof e||(t=t||n.defaultEncoding)!==n.encoding&&(e=V.from(e,t),t=""),lt(this,n,e,t,!1)},ct.prototype.unshift=function(e){return lt(this,this._readableState,e,"",!0)},ct.prototype.isPaused=function(){return!1===this._readableState.flowing},ct.prototype.setEncoding=function(e){return this._readableState.decoder=new ot(e),this._readableState.encoding=e,this};var pt=8388608;function dt(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=pt?e=pt:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function gt(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(ft("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?l(yt,e):yt(e))}function yt(e){ft("emit readable"),e.emit("readable"),mt(e)}function vt(e,t){for(var n=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(ft("maybeReadMore read 0"),e.read(0),n!==t.length);)n=t.length;t.readingMore=!1}function wt(e){ft("readable nexttick read 0"),e.read(0)}function bt(e,t){t.reading||(ft("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),mt(e),t.flowing&&!t.reading&&e.read(0)}function mt(e){var t=e._readableState;for(ft("flow",t.flowing);t.flowing&&null!==e.read(););}function _t(e,t){return 0===t.length?null:(t.objectMode?n=t.buffer.shift():!e||e>=t.length?(n=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):n=function(e,t,n){var r;e<t.head.data.length?(r=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):r=e===t.head.data.length?t.shift():n?function(e,t){var n=t.head,r=1,i=n.data;e-=i.length;for(;n=n.next;){var o=n.data,a=e>o.length?o.length:e;if(a===o.length?i+=o:i+=o.slice(0,e),0===(e-=a)){a===o.length?(++r,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(a));break}++r}return t.length-=r,i}(e,t):function(e,t){var n=V.allocUnsafe(e),r=t.head,i=1;r.data.copy(n),e-=r.data.length;for(;r=r.next;){var o=r.data,a=e>o.length?o.length:e;if(o.copy(n,n.length-e,0,a),0===(e-=a)){a===o.length?(++i,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(a));break}++i}return t.length-=i,n}(e,t);return r}(e,t.buffer,t.decoder),n);var n}function Et(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,l(Rt,t,e))}function Rt(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function St(e,t){for(var n=0,r=e.length;n<r;n++)if(e[n]===t)return n;return-1}function At(){}function Lt(e,t,n){this.chunk=e,this.encoding=t,this.callback=n,this.next=null}function Tt(e,t){Object.defineProperty(this,"buffer",{get:De((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.")}),e=e||{},this.objectMode=!!e.objectMode,t instanceof Yt&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var n=e.highWaterMark,r=this.objectMode?16:16384;this.highWaterMark=n||0===n?n:r,this.highWaterMark=~~this.highWaterMark,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1;var i=!1===e.decodeStrings;this.decodeStrings=!i,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var n=e._writableState,r=n.sync,i=n.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),t)!function(e,t,n,r,i){--t.pendingcb,n?l(i,r):i(r);e._writableState.errorEmitted=!0,e.emit("error",r)}(e,n,r,t,i);else{var o=Pt(n);o||n.corked||n.bufferProcessing||!n.bufferedRequest||Ct(e,n),r?l(xt,e,n,o,i):xt(e,n,o,i)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new Bt(this)}function kt(e){if(!(this instanceof kt||this instanceof Yt))return new kt(e);this._writableState=new Tt(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev)),C.call(this)}function Mt(e,t,n,r,i,o,a){t.writelen=r,t.writecb=a,t.writing=!0,t.sync=!0,n?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function xt(e,t,n,r){n||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,r(),jt(e,t)}function Ct(e,t){t.bufferProcessing=!0;var n=t.bufferedRequest;if(e._writev&&n&&n.next){var r=t.bufferedRequestCount,i=new Array(r),o=t.corkedRequestsFree;o.entry=n;for(var a=0;n;)i[a]=n,n=n.next,a+=1;Mt(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new Bt(t)}else{for(;n;){var s=n.chunk,u=n.encoding,f=n.callback;if(Mt(e,t,!1,t.objectMode?1:s.length,s,u,f),n=n.next,t.writing)break}null===n&&(t.lastBufferedRequest=null)}t.bufferedRequestCount=0,t.bufferedRequest=n,t.bufferProcessing=!1}function Pt(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function Ot(e,t){t.prefinished||(t.prefinished=!0,e.emit("prefinish"))}function jt(e,t){var n=Pt(t);return n&&(0===t.pendingcb?(Ot(e,t),t.finished=!0,e.emit("finish")):Ot(e,t)),n}function Bt(e){var t=this;this.next=null,this.entry=null,this.finish=function(n){var r=t.entry;for(t.entry=null;r;){var i=r.callback;e.pendingcb--,i(n),r=r.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t}}ct.prototype.read=function(e){ft("read",e),e=parseInt(e,10);var t=this._readableState,n=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return ft("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?Et(this):gt(this),null;if(0===(e=dt(e,t))&&t.ended)return 0===t.length&&Et(this),null;var r,i=t.needReadable;return ft("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&ft("length less than watermark",i=!0),t.ended||t.reading?ft("reading or ended",i=!1):i&&(ft("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=dt(n,t))),null===(r=e>0?_t(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),n!==e&&t.ended&&Et(this)),null!==r&&this.emit("data",r),r},ct.prototype._read=function(e){this.emit("error",new Error("not implemented"))},ct.prototype.pipe=function(e,t){var n=this,r=this._readableState;switch(r.pipesCount){case 0:r.pipes=e;break;case 1:r.pipes=[r.pipes,e];break;default:r.pipes.push(e)}r.pipesCount+=1,ft("pipe count=%d opts=%j",r.pipesCount,t);var i=!t||!1!==t.end?a:f;function o(e){ft("onunpipe"),e===n&&f()}function a(){ft("onend"),e.end()}r.endEmitted?l(i):n.once("end",i),e.on("unpipe",o);var s=function(e){return function(){var t=e._readableState;ft("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&e.listeners("data").length&&(t.flowing=!0,mt(e))}}(n);e.on("drain",s);var u=!1;function f(){ft("cleanup"),e.removeListener("close",d),e.removeListener("finish",g),e.removeListener("drain",s),e.removeListener("error",p),e.removeListener("unpipe",o),n.removeListener("end",a),n.removeListener("end",f),n.removeListener("data",c),u=!0,!r.awaitDrain||e._writableState&&!e._writableState.needDrain||s()}var h=!1;function c(t){ft("ondata"),h=!1,!1!==e.write(t)||h||((1===r.pipesCount&&r.pipes===e||r.pipesCount>1&&-1!==St(r.pipes,e))&&!u&&(ft("false write response, pause",n._readableState.awaitDrain),n._readableState.awaitDrain++,h=!0),n.pause())}function p(t){var n;ft("onerror",t),y(),e.removeListener("error",p),0===(n="error",e.listeners(n).length)&&e.emit("error",t)}function d(){e.removeListener("finish",g),y()}function g(){ft("onfinish"),e.removeListener("close",d),y()}function y(){ft("unpipe"),n.unpipe(e)}return n.on("data",c),function(e,t,n){if("function"==typeof e.prependListener)return e.prependListener(t,n);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(n):e._events[t]=[n,e._events[t]]:e.on(t,n)}(e,"error",p),e.once("close",d),e.once("finish",g),e.emit("pipe",n),r.flowing||(ft("pipe resume"),n.resume()),e},ct.prototype.unpipe=function(e){var t=this._readableState;if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this)),this;if(!e){var n=t.pipes,r=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;i<r;i++)n[i].emit("unpipe",this);return this}var o=St(t.pipes,e);return-1===o||(t.pipes.splice(o,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this)),this},ct.prototype.on=function(e,t){var n=C.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var r=this._readableState;r.endEmitted||r.readableListening||(r.readableListening=r.needReadable=!0,r.emittedReadable=!1,r.reading?r.length&&gt(this):l(wt,this))}return n},ct.prototype.addListener=ct.prototype.on,ct.prototype.resume=function(){var e=this._readableState;return e.flowing||(ft("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,l(bt,e,t))}(this,e)),this},ct.prototype.pause=function(){return ft("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(ft("pause"),this._readableState.flowing=!1,this.emit("pause")),this},ct.prototype.wrap=function(e){var t=this._readableState,n=!1,r=this;for(var i in e.on("end",(function(){if(ft("wrapped end"),t.decoder&&!t.ended){var e=t.decoder.end();e&&e.length&&r.push(e)}r.push(null)})),e.on("data",(function(i){(ft("wrapped data"),t.decoder&&(i=t.decoder.write(i)),t.objectMode&&null==i)||(t.objectMode||i&&i.length)&&(r.push(i)||(n=!0,e.pause()))})),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));return function(e,t){for(var n=0,r=e.length;n<r;n++)t(e[n],n)}(["error","close","destroy","pause","resume"],(function(t){e.on(t,r.emit.bind(r,t))})),r._read=function(t){ft("wrapped _read",t),n&&(n=!1,e.resume())},r},ct._fromList=_t,kt.WritableState=Tt,je(kt,C),Tt.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},kt.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},kt.prototype.write=function(e,t,n){var r=this._writableState,i=!1;return"function"==typeof t&&(n=t,t=null),V.isBuffer(e)?t="buffer":t||(t=r.defaultEncoding),"function"!=typeof n&&(n=At),r.ended?function(e,t){var n=new Error("write after end");e.emit("error",n),l(t,n)}(this,n):function(e,t,n,r){var i=!0,o=!1;return null===n?o=new TypeError("May not write null values to stream"):V.isBuffer(n)||"string"==typeof n||void 0===n||t.objectMode||(o=new TypeError("Invalid non-string/buffer chunk")),o&&(e.emit("error",o),l(r,o),i=!1),i}(this,r,e,n)&&(r.pendingcb++,i=function(e,t,n,r,i){n=function(e,t,n){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=V.from(t,n));return t}(t,n,r),V.isBuffer(n)&&(r="buffer");var o=t.objectMode?1:n.length;t.length+=o;var a=t.length<t.highWaterMark;a||(t.needDrain=!0);if(t.writing||t.corked){var s=t.lastBufferedRequest;t.lastBufferedRequest=new Lt(n,r,i),s?s.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else Mt(e,t,!1,o,n,r,i);return a}(this,r,e,t,n)),i},kt.prototype.cork=function(){this._writableState.corked++},kt.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||Ct(this,e))},kt.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},kt.prototype._write=function(e,t,n){n(new Error("not implemented"))},kt.prototype._writev=null,kt.prototype.end=function(e,t,n){var r=this._writableState;"function"==typeof e?(n=e,e=null,t=null):"function"==typeof t&&(n=t,t=null),null!=e&&this.write(e,t),r.corked&&(r.corked=1,this.uncork()),r.ending||r.finished||function(e,t,n){t.ending=!0,jt(e,t),n&&(t.finished?l(n):e.once("finish",n));t.ended=!0,e.writable=!1}(this,r,n)},je(Yt,ct);for(var Ut=Object.keys(kt.prototype),Dt=0;Dt<Ut.length;Dt++){var It=Ut[Dt];Yt.prototype[It]||(Yt.prototype[It]=kt.prototype[It])}function Yt(e){if(!(this instanceof Yt))return new Yt(e);ct.call(this,e),kt.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",zt)}function zt(){this.allowHalfOpen||this._writableState.ended||l(qt,this)}function qt(e){e.end()}function Nt(e){this.afterTransform=function(t,n){return function(e,t,n){var r=e._transformState;r.transforming=!1;var i=r.writecb;if(!i)return e.emit("error",new Error("no writecb in Transform class"));r.writechunk=null,r.writecb=null,null!=n&&e.push(n);i(t);var o=e._readableState;o.reading=!1,(o.needReadable||o.length<o.highWaterMark)&&e._read(o.highWaterMark)}(e,t,n)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null,this.writeencoding=null}function Wt(e){if(!(this instanceof Wt))return new Wt(e);Yt.call(this,e),this._transformState=new Nt(this);var t=this;this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.once("prefinish",(function(){"function"==typeof this._flush?this._flush((function(e){Ft(t,e)})):Ft(t)}))}function Ft(e,t){if(t)return e.emit("error",t);var n=e._writableState,r=e._transformState;if(n.length)throw new Error("Calling transform done when ws.length != 0");if(r.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}function Ht(e){if(!(this instanceof Ht))return new Ht(e);Wt.call(this,e)}function $t(){C.call(this)}je(Wt,Yt),Wt.prototype.push=function(e,t){return this._transformState.needTransform=!1,Yt.prototype.push.call(this,e,t)},Wt.prototype._transform=function(e,t,n){throw new Error("Not implemented")},Wt.prototype._write=function(e,t,n){var r=this._transformState;if(r.writecb=n,r.writechunk=e,r.writeencoding=t,!r.transforming){var i=this._readableState;(r.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},Wt.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},je(Ht,Wt),Ht.prototype._transform=function(e,t,n){n(null,e)},je($t,C),$t.Readable=ct,$t.Writable=kt,$t.Duplex=Yt,$t.Transform=Wt,$t.PassThrough=Ht,$t.Stream=$t,$t.prototype.pipe=function(e,t){var n=this;function r(t){e.writable&&!1===e.write(t)&&n.pause&&n.pause()}function i(){n.readable&&n.resume&&n.resume()}n.on("data",r),e.on("drain",i),e._isStdio||t&&!1===t.end||(n.on("end",a),n.on("close",s));var o=!1;function a(){o||(o=!0,e.end())}function s(){o||(o=!0,"function"==typeof e.destroy&&e.destroy())}function u(e){if(f(),0===C.listenerCount(this,"error"))throw e}function f(){n.removeListener("data",r),e.removeListener("drain",i),n.removeListener("end",a),n.removeListener("close",s),n.removeListener("error",u),e.removeListener("error",u),n.removeListener("end",f),n.removeListener("close",f),e.removeListener("close",f)}return n.on("error",u),e.on("error",u),n.on("end",f),n.on("close",f),e.on("close",f),e.emit("pipe",n),e};var Jt=M,Gt=L(Object.freeze({__proto__:null,default:$t,Readable:ct,Writable:kt,Duplex:Yt,Transform:Wt,PassThrough:Ht,Stream:$t})).Stream;function Zt(e,t,n){n=n||0;var r,i,o=(r=t,new Array(n||0).join(r||"")),a=e;if("object"==typeof e&&((a=e[i=Object.keys(e)[0]])&&a._elem))return a._elem.name=i,a._elem.icount=n,a._elem.indent=t,a._elem.indents=o,a._elem.interrupt=a,a._elem;var s,u=[],f=[];function h(e){Object.keys(e).forEach((function(t){u.push(function(e,t){return e+'="'+Jt(t)+'"'}(t,e[t]))}))}switch(typeof a){case"object":if(null===a)break;a._attr&&h(a._attr),a._cdata&&f.push(("<![CDATA["+a._cdata).replace(/\]\]>/g,"]]]]><![CDATA[>")+"]]>"),a.forEach&&(s=!1,f.push(""),a.forEach((function(e){"object"==typeof e?"_attr"==Object.keys(e)[0]?h(e._attr):f.push(Zt(e,t,n+1)):(f.pop(),s=!0,f.push(Jt(e)))})),s||f.push(""));break;default:f.push(Jt(a))}return{name:i,interrupt:!1,attributes:u,content:f,icount:n,indents:o,indent:t}}function Vt(e,t,n){if("object"!=typeof t)return e(!1,t);var r=t.interrupt?1:t.content.length;function i(){for(;t.content.length;){var i=t.content.shift();if(void 0!==i){if(o(i))return;Vt(e,i)}}e(!1,(r>1?t.indents:"")+(t.name?"</"+t.name+">":"")+(t.indent&&!n?"\n":"")),n&&n()}function o(t){return!!t.interrupt&&(t.interrupt.append=e,t.interrupt.end=i,t.interrupt=!1,e(!0),!0)}if(e(!1,t.indents+(t.name?"<"+t.name:"")+(t.attributes.length?" "+t.attributes.join(" "):"")+(r?t.name?">":"":t.name?"/>":"")+(t.indent&&r>1?"\n":"")),!r)return e(!1,t.indent?"\n":"");o(t)||i()}T.exports=function(e,t){"object"!=typeof t&&(t={indent:t});var n,r,i=t.stream?new Gt:null,o="",a=!1,s=t.indent?!0===t.indent?"    ":t.indent:"",u=!0;function f(e){u?A.nextTick(e):e()}function h(e,t){if(void 0!==t&&(o+=t),e&&!a&&(i=i||new Gt,a=!0),e&&a){var n=o;f((function(){i.emit("data",n)})),o=""}}function c(e,t){Vt(h,Zt(e,s,s?1:0),t)}function l(){if(i){var e=o;f((function(){i.emit("data",e),i.emit("end"),i.readable=!1,i.emit("close")}))}}return f((function(){u=!1})),t.declaration&&(n=t.declaration,r={version:"1.0",encoding:n.encoding||"UTF-8"},n.standalone&&(r.standalone=n.standalone),c({"?xml":{_attr:r}}),o=o.replace("/>","?>")),e&&e.forEach?e.forEach((function(t,n){var r;n+1===e.length&&(r=l),c(t,r)})):c(e,l),i?(i.readable=!0,i):o};var Kt=T.exports.element=T.exports.Element=function(){var e={_elem:Zt(Array.prototype.slice.call(arguments)),push:function(e){if(!this.append)throw new Error("not assigned to a parent!");var t=this,n=this._elem.indent;Vt(this.append,Zt(e,n,this._elem.icount+(n?1:0)),(function(){t.append(!0)}))},close:function(e){void 0!==e&&this.push(e),this.end&&this.end()}};return e},Qt=T.exports;export{Qt as default,Kt as element};
//# sourceMappingURL=/sm/ade1315322e52433f4435c26fa4e1e8335cf27fbf632dde923c151704aa78159.map