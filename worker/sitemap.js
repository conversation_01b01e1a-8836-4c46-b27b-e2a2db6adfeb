import xml from "./modules/xml.js";
import { getAllBonds } from "./repository.js";

/**
 * Generate sitemap
 *
 * @param {URL} url The requested URL
 */
export async function handleSitemapUrl(url) {
  if (url.pathname === "/sitemap.xml") {
    return getRootSitemap();
  }
  const splits = url.pathname.split("/");
  const id = splits[2];
  const page = url.searchParams.get("page") ?? "0";
  switch (id) {
    case "pages.xml":
      return getPagesSitemap();
    case "bonds.xml":
      return getBondsSitemap(parseInt(page, 10));
    default:
      return new Response("Not found", { status: 404 });
  }
}

async function getRootSitemap() {
  const sitemapRoutes = ["pages.xml"];
  const { total_count } = await getAllBonds();
  // Push all bonds pages with ?page={page}
  for (let i = 0; i <= Math.floor(total_count / 25); i++) {
    sitemapRoutes.push(`bonds.xml?page=${i}`);
  }
  const sitemaps = sitemapRoutes.map((route) => {
    return {
      sitemap: [
        { loc: `https://stablebonds.in/sitemap/${route}` },
        { lastmod: new Date().toISOString() },
      ],
    };
  });
  // @ts-ignore
  const sitemap = xml(
    {
      sitemapindex: [
        { _attr: { xmlns: "https://www.sitemaps.org/schemas/sitemap/0.9" } },
        ...sitemaps,
      ],
    },
    {
      declaration: { encoding: "UTF-8" },
    }
  );
  return new Response(sitemap, {
    headers: {
      "Content-Type": "application/xml",
    },
  });
}

function getPagesSitemap() {
  const pages = [
    "/",
    "/contact-us",
    "/about-us",
    "/how-to-fill-form15g",
    "/compliance/term-and-condition",
    "/compliance/privacy-policy",
    "/compliance/governance",
    "/compliance/investor-charter-for-depositroy-participant",
    "/compliance/forms",
    "/compliance/contact",
  ];
  return getSitemap(pages);
}

async function getBondsSitemap(page = 0) {
  const { bonds } = await getAllBonds(page);
  const bondPaths = bonds.map(
    (bond) => `/bonds/${bond.issuer_slug}/${bond.isin_code}`
  );
  return getSitemap(bondPaths);
}

/**
 * Generate sitemap for given sitemap URLs
 *
 * @param {string[]} pages
 * @returns {Response}
 */
function getSitemap(pages) {
  const sitemap = pages.map((page) => {
    return {
      url: [
        { loc: `https://stablebonds.in${page}` },
        { lastmod: new Date().toISOString() },
      ],
    };
  });
  // @ts-ignore
  const body = xml(
    {
      urlset: [
        { _attr: { xmlns: "https://www.sitemaps.org/schemas/sitemap/0.9" } },
        ...sitemap,
      ],
    },
    {
      declaration: { encoding: "UTF-8" },
    }
  );
  return new Response(body, {
    headers: {
      "Content-Type": "application/xml",
    },
  });
}
