/**
 * Get all bonds
 *
 * @typedef {Object} AllBondsResponse
 * @property {AllBondsResponse_Bond[]} bonds
 * @property {number} total_count
 *
 * @typedef {Object} AllBondsResponse_Bond
 * @property {string} issuer_slug
 * @property {string} isin_code
 * @property {string} name
 *
 * @param {number} page
 * @returns {Promise<AllBondsResponse>}
 */
export async function getAllBonds(page = 0) {
  const response = await fetch("https://broking-api.stablebonds.in/v1/bonds", {
    method: "POST",
    headers: {
      accept: "application/json",
      "content-type": "application/json",
    },
    body: JSON.stringify({
      pageNumber: page,
      pageSize: 25,
      secretKey: "7944767327472-aak1uq5ydJVM4IoTnhFaBV8C",
    }),
  });
  if (!response.ok) {
    throw new Error("Failed to fetch bonds");
  }
  return response.json();
}
