{"name": "bonds-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"sync:platform-proto": "aws --profile=alpha-staging-dev s3 sync s3://alpha-stage-frontend-proto ./src/protos/platform", "generate:broking-proto": "npx buf generate --template buf.gen-broking.yaml", "generate:platform-proto": "npx buf generate --template buf.gen-platform.yaml", "generate:openapi": "orval --prettier --config orval.config.cjs", "dev": "vite --port 3000", "build": "tsc -b && vite build", "lint": "eslint . --fix", "preview": "vite preview --port 3000", "deploy": "npx wrangler deploy", "format": "prettier --write .", "lint:fix": "prettier --write .", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "prepare": "husky"}, "dependencies": {"@bufbuild/buf": "^1.54.0", "@bufbuild/protobuf": "^2.5.1", "@bufbuild/protoc-gen-es": "^2.5.1", "@cashfreepayments/cashfree-js": "^1.0.5", "@dr.pogodin/react-helmet": "^3.0.2", "@hotwired/hotwire-native-bridge": "^1.0.0", "@lottiefiles/dotlottie-react": "^0.14.1", "@react-hook/media-query": "^1.1.1", "@react-hook/resize-observer": "^2.0.2", "@sentry/react": "^9.31.0", "@sentry/vite-plugin": "^3.5.0", "@tailwindcss/postcss": "^4.1.8", "@tailwindcss/vite": "^4.1.8", "@tanstack/query-sync-storage-persister": "^5.80.2", "@tanstack/react-query": "^5.79.2", "@tanstack/react-query-persist-client": "^5.80.2", "@types/react-signature-canvas": "^1.0.7", "@types/swiper": "^5.4.3", "@xstate/react": "^5.0.5", "@zag-js/accordion": "^1.15.7", "@zag-js/avatar": "^1.15.7", "@zag-js/checkbox": "^1.15.7", "@zag-js/dialog": "^1.15.7", "@zag-js/pin-input": "^1.15.7", "@zag-js/radio-group": "^1.15.7", "@zag-js/react": "^1.15.7", "@zag-js/select": "^1.15.7", "@zag-js/slider": "^1.15.7", "@zag-js/switch": "^1.15.7", "@zag-js/toast": "^1.15.7", "base64-arraybuffer": "^1.0.2", "change-case": "^5.4.4", "clsx": "^2.1.1", "dayjs": "^1.11.13", "formik": "^2.4.6", "hotwire-native-bolt": "^0.1.3", "i18next": "^25.2.1", "i18next-http-backend": "^3.0.2", "imask": "^7.6.1", "load-script": "^2.0.0", "mixpanel-browser": "^2.65.0", "my-ua-parser": "^2.0.4", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.5.2", "react-imask": "^7.6.1", "react-intersection-observer": "^9.16.0", "react-router": "^7.6.1", "react-router-dom": "^7.6.1", "react-signature-canvas": "^1.1.0-alpha.2", "react-stacked-center-carousel": "^1.0.14", "spin-delay": "^2.0.1", "swiper": "^11.2.8", "tailwindcss": "^4.1.8", "throttle-debounce": "^5.0.2", "vaul": "^1.1.2", "xstate": "^5.19.4", "yup": "^1.6.1"}, "devDependencies": {"@chromatic-com/storybook": "^4.0.0", "@eslint/js": "^9.25.0", "@faker-js/faker": "^9.8.0", "@playwright/test": "^1.53.0", "@storybook/addon-a11y": "^9.0.4", "@storybook/addon-docs": "^9.0.4", "@storybook/react-vite": "^9.0.4", "@tailwindcss/typography": "^0.5.16", "@types/js-cookie": "^3.0.6", "@types/mixpanel-browser": "^2.60.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/sinon": "^17.0.4", "@types/throttle-debounce": "^5.0.2", "@vitejs/plugin-react": "^4.5.1", "@vitejs/plugin-react-swc": "^3.9.0", "dotenv": "^16.5.0", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "husky": "^9.1.7", "js-cookie": "^3.0.5", "lint-staged": "^16.1.0", "orval": "^7.9.0", "playwright": "^1.53.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.12", "sinon": "^21.0.0", "storybook": "^9.0.4", "tailwind-scrollbar": "^4.0.2", "tailwindcss-safe-area": "^0.6.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.21.2"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,css,md,html}": ["prettier --write"]}}