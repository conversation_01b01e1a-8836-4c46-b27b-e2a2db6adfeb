import { setup, assign } from "xstate";

export const countDownTimerMachine = setup({
  types: {
    context: {} as {
      duration: number;
      current: number;
    },
    events: {} as
      | { type: "START"; duration?: number }
      | { type: "PAUSE" }
      | { type: "RESUME" }
      | { type: "RESET"; duration?: number }
      | { type: "TICK" }
      | { type: "FINISH" },
    input: {} as { duration?: number } | undefined,
  },
  actions: {
    decrementCurrent: assign({
      current: ({ context }) => Math.max(0, context.current - 1),
    }),
    resetTimer: assign({
      current: ({ context }) => {
        return context.duration;
      },
    }),
  },
}).createMachine({
  id: "countDownTimer",
  initial: "idle",
  context({ input }) {
    return {
      duration: input.duration ?? 30,
      current: input.duration ?? 30,
    };
  },
  states: {
    idle: {
      on: {
        START: {
          target: "running",
          actions: "resetTimer",
        },
      },
    },
    running: {
      after: {
        1000: [
          {
            guard: ({ context }) => context.current <= 1,
            target: "finished",
            actions: "decrementCurrent",
          },
          {
            target: "running",
            actions: "decrementCurrent",
            reenter: true,
          },
        ],
      },
      on: {
        PAUSE: {
          target: "paused",
        },
        RESET: {
          target: "idle",
          actions: "resetTimer",
        },
      },
    },
    paused: {
      on: {
        RESUME: {
          target: "running",
        },
        RESET: {
          target: "idle",
          actions: "resetTimer",
        },
      },
    },
    finished: {
      on: {
        START: {
          target: "running",
          actions: "resetTimer",
        },
        RESET: {
          target: "idle",
          actions: "resetTimer",
        },
      },
    },
  },
});
