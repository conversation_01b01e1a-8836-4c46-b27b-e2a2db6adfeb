import { trackEvent } from "@/utils/analytics";
import {
  assign,
  fromPromise,
  setup,
  type ActorRef,
  type Snapshot,
} from "xstate";
import { useMachine } from "@xstate/react";
import { useCallback, useEffect, useMemo } from "react";

type ChildEvent<T> =
  | { type: "LIMIT_REACHED"; error?: unknown }
  | { type: "COMPLETED"; response: T };
type ParentActor<T> = ActorRef<Snapshot<unknown>, ChildEvent<T>>;

type PollingContext<T> = {
  count: number;
  parentRef?: ParentActor<T>;
  response?: T;
  error?: unknown;
  maxCount: number;
  interval: number;
  runner: () => Promise<T>;
  guard: (result: T) => boolean;
  errorGuard: (error: unknown) => boolean;
};

export const createPollingMachine = <T>(
  machineId: string,
  initialState: "idle" | "evaluating" = "idle"
) =>
  setup({
    types: {
      context: {} as PollingContext<T>,
      input: {} as Partial<PollingContext<T>> | undefined,
      events: {} as
        | {
            type: "START";
            input: Partial<Omit<PollingContext<T>, "count" | "response">>;
          }
        | {
            type: "STOP";
          },
      output: {} as T | undefined,
    },
    guards: {
      limitReached: ({ context, event }) => {
        return (
          ("error" in event && context.errorGuard(event.error)) ||
          context.count >= context.maxCount
        );
      },
    },
    actors: {
      evaluator: fromPromise(({ input }: { input: () => Promise<T> }) =>
        input()
      ),
    },
    delays: {
      interval: ({ context }) => {
        return context.interval;
      },
    },
  }).createMachine({
    id: machineId,
    initial: initialState,
    context({ input }) {
      return {
        count: 0,
        response: undefined,
        maxCount: 10,
        interval: 1000,
        runner: () => Promise.resolve() as Promise<T>,
        guard: () => false,
        errorGuard: () => false,
        ...input,
      };
    },
    on: {
      START: {
        target: ".evaluating",
        actions: assign(({ event }) => {
          return { ...event.input, count: 0, response: undefined };
        }),
      },
      STOP: {
        target: ".idle",
      },
    },
    states: {
      idle: {},
      evaluating: {
        entry({ context }) {
          trackEvent(`${machineId}.run`, {
            count: context.count,
            maxCount: context.maxCount,
          });
        },
        invoke: {
          src: "evaluator",
          input: ({ context }) => context.runner,
          onDone: [
            {
              target: "completed",
              actions: assign({
                response: ({ event }) => event.output,
              }),
              guard({ event, context }) {
                return context.guard(event.output);
              },
            },
            {
              target: "limitReached",
              guard: "limitReached",
              actions: assign({
                response: ({ event }) => event.output,
              }),
            },
            {
              target: "waiting",
              actions: assign({
                response: ({ event }) => event.output,
              }),
            },
          ],
          onError: [
            {
              target: "limitReached",
              guard: "limitReached",
              actions: assign(({ context, event }) => {
                trackEvent(`${machineId}.run_error`, {
                  count: context.count,
                  maxCount: context.maxCount,
                });
                return { error: event.error };
              }),
            },
            {
              target: "waiting",
              actions: assign(({ context, event }) => {
                trackEvent(`${machineId}.run_error`, {
                  count: context.count,
                  maxCount: context.maxCount,
                });
                return { error: event.error };
              }),
            },
          ],
        },
      },
      waiting: {
        entry: assign({
          count: ({ context }) => context.count + 1,
        }),
        after: {
          interval: { target: "evaluating" },
        },
      },
      limitReached: {
        entry: [
          ({ context }) => {
            trackEvent(`${machineId}.limit_reached`, {
              maxCount: context.maxCount,
            });
            context.parentRef?.send({
              type: "LIMIT_REACHED",
              error: context.error,
            });
          },
        ],
      },
      completed: {
        type: "final",
        entry: [
          ({ context }) => {
            trackEvent(`${machineId}.completed`, {
              count: context.count,
            });
          },
          ({ context }) => {
            context.parentRef?.send({
              type: "COMPLETED",
              response: context.response!,
            });
          },
        ],
      },
    },
    output: ({ context }) => context.response as T,
  });

export function usePolling<T>(
  machineId: string,
  onCompleted?: (response: T) => unknown
) {
  const pollingMachine = useMemo(
    () => createPollingMachine<T>(machineId),
    [machineId]
  );
  const [state, send] = useMachine(pollingMachine, {
    input: undefined,
  });
  useEffect(() => {
    if (state.value === "completed") {
      onCompleted?.(state.context.response!);
    }
  }, [state, onCompleted]);
  const start = useCallback(
    function (input: Partial<Omit<PollingContext<T>, "count" | "response">>) {
      send({ type: "START", input });
    },
    [send]
  );
  const stop = useCallback(() => {
    send({ type: "STOP" });
  }, [send]);
  return {
    start,
    stop,
    response: state.context.response,
    error: state.context.error,
    count: state.context.count,
    status: state.value as
      | "idle"
      | "evaluating"
      | "waiting"
      | "limitReached"
      | "completed",
  };
}
