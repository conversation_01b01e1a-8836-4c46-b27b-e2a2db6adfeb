import type { InspectionEvent } from "xstate";
import * as <PERSON><PERSON> from "@sentry/browser";

export function createInspector(id: string) {
  let prevStateValue: unknown = null;

  return (inspectionEvent: InspectionEvent) => {
    if (inspectionEvent.type === "@xstate.snapshot") {
      const currentSnapshot = inspectionEvent.actorRef.getSnapshot();
      const currentState = currentSnapshot?.value ?? prevStateValue;
      if (import.meta.env.DEV) {
        console.log({
          id,
          currentState,
          currentSnapshot,
          event: inspectionEvent.event,
        });
      }
      Sentry.logger.debug(Sentry.logger.fmt`FSM ${id} snapshot`, {
        currentState,
        event: inspectionEvent.event.type,
      });

      if (currentSnapshot?.value != null) {
        prevStateValue = currentSnapshot.value;
      }
    }
  };
}
