import * as Base64 from "base64-arraybuffer";

type EncryptedText = string;
type EncryptedAesKey = string;

async function importRSAPublicKey(base64Key: string) {
  return window.crypto.subtle.importKey(
    "spki",
    Base64.decode(base64Key),
    {
      name: "RSA-<PERSON>A<PERSON>",
      hash: "SHA-256",
    },
    true,
    ["encrypt"]
  );
}

async function encryptAESKey(aesKey: CryptoKey, rsaPublicKey: Crypto<PERSON>ey) {
  const exportedAesKey = await window.crypto.subtle
    .exportKey("raw", aesKey)
    .then(Base64.encode);
  return window.crypto.subtle.encrypt(
    {
      name: "RSA-OAEP",
    },
    rsaPublicKey,
    new TextEncoder().encode(exportedAesKey)
  );
}

export async function aesGcmEncrypt(
  data: string,
  pem: string
): Promise<[EncryptedText, EncryptedAes<PERSON>ey, Crypto<PERSON><PERSON>]> {
  const iv = crypto.getRandomValues(new Uint8Array(12));
  const encodedPlaintext = new TextEncoder().encode(data);
  const secretKey = await window.crypto.subtle.generateKey(
    {
      name: "AES-GCM",
      length: 256,
    },
    true,
    ["encrypt", "decrypt"]
  );
  const cipherText = await crypto.subtle.encrypt(
    {
      name: "AES-GCM",
      iv,
    },
    secretKey,
    encodedPlaintext
  );
  const encryptedKey = await importRSAPublicKey(pem)
    .then((publicKey) => encryptAESKey(secretKey, publicKey))
    .then(Base64.encode);
  const combined = new Uint8Array(iv.length + cipherText.byteLength);
  combined.set(iv, 0);
  combined.set(new Uint8Array(cipherText), iv.length);
  return [Base64.encode(combined.buffer), encryptedKey, secretKey];
}

export async function aesGcmDecrypt(
  encryptedMessage: string,
  aesKey: CryptoKey
): Promise<string> {
  const crypto = window.crypto.subtle;
  const encryptedData = Base64.decode(encryptedMessage);
  const nonceSize = 12;
  const tagSize = 16;

  if (encryptedData.byteLength <= nonceSize + tagSize) {
    throw new Error("Encrypted data is too short.");
  }

  const nonce = encryptedData.slice(0, nonceSize);
  const ciphertext = encryptedData.slice(nonceSize, -tagSize);
  const tag = encryptedData.slice(-tagSize);

  const decrypted = await crypto.decrypt(
    {
      name: "AES-GCM",
      iv: nonce,
      tagLength: tagSize * 8,
    },
    aesKey,
    new Uint8Array([...new Uint8Array(ciphertext), ...new Uint8Array(tag)])
  );

  return new TextDecoder().decode(decrypted);
}
