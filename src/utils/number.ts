import * as Sentry from "@sentry/react";

export function formatToInr(value: number): string {
  try {
    return new Intl.NumberFormat("en-IN", {
      style: "currency",
      currency: "INR",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  } catch {
    Sentry.logger.info("Error formatting currency", { value });
    return "";
  }
}

export function formatAmount(value: number): string {
  return Intl.NumberFormat("en-IN", {
    style: "decimal",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
}
