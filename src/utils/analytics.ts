import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";
import dayjs from "dayjs";
import { addBreadcrumb } from "@sentry/react";
import type { BondDetailItem } from "@/clients/gen/broking/BondDetails_pb";
import { queryClient } from "@/queries/client";
import { getProfileQueryOptions } from "@/queries/profile";
import { isHotwireNative } from "./routing";
import mixpanel from "mixpanel-browser";
import * as native from "./native-integration";

export function initAnalytics(token: string) {
  mixpanel.init(token, { autocapture: false, record_sessions_percent: 100 });
}

function getAnalyticsProperties() {
  const profile = queryClient.getQueryData(getProfileQueryOptions().queryKey);
  return {
    bonds_life_time_status: profile?.lifeTimeStatus ?? "unknown",
    bonds_current_status: profile?.currentStatus ?? "unknown",
    platform:
      isHotwireNative() || window.flutter_inappwebview
        ? "PLATFORM_FLUTTER"
        : "PLATFORM_WEB",
  };
}

export function trackEvent(
  event: string,
  properties?: Record<string, unknown>
) {
  const analyticsProperties = {
    ...properties,
    ...getAnalyticsProperties(),
  };

  addBreadcrumb({
    type: "info",
    message: event,
    category: "analytics",
    data: analyticsProperties,
  });

  console.info(`Event tracked`, event, analyticsProperties);

  if (native.analytics.isSupported()) {
    return native.analytics.track(event, analyticsProperties);
  }
  mixpanel.track(event, analyticsProperties);
  window.gtag?.("event", event, analyticsProperties);
}

export function getBondDetailsEventProps(
  bondDetails: CollectionItem | BondDetailItem
) {
  const maturityDateInISO = dayjs(bondDetails?.maturityDate);
  const now = dayjs();
  const maturityDate = dayjs(maturityDateInISO, "YYYY-MM-DD");
  const differenceInDays = maturityDate.diff(now, "days");
  return {
    bondId: bondDetails?.id,
    bondName: bondDetails.aboutTheInstitution?.title,
    issuer: bondDetails.aboutTheInstitution?.bondInstitutionName,
    yield: bondDetails.xirr,
    creditRating: bondDetails.rating,
    faceValue: Number.parseFloat(bondDetails?.faceValue ?? ""),
    maturityDate: bondDetails.maturityDate,
    tenureInDays: differenceInDays,
    couponFrequency: bondDetails?.interestPayment,
    investibilityStatus: bondDetails?.investabilityStatus,
    isin: bondDetails?.isinCode,
  };
}
