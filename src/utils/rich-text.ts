/**
 * Parses and formats rich text with special tags
 * Supports:
 * - [b]bold text[/b]
 * - [color=#hexcode]colored text[/color]
 * - [link href='url' color=#hexcode]link text[/link]
 *
 * @param text The rich text string to parse
 * @returns An object with the parsed HTML
 */
export function parseRichText(text: string): { html: string } {
  if (!text) return { html: "" };

  const regex =
    /\[b\](.*?)\[\/b\]|\[color=(.*?)\](.*?)\[\/color\]|\[link href='(.*?)' color=(.*?)\](.*?)\[\/link\]/g;
  let lastMatchEnd = 0;
  let result = "";
  let match: RegExpExecArray | null = null;

  while ((match = regex.exec(text)) !== null) {
    // Add text between matches
    if (match.index > lastMatchEnd) {
      result += text.substring(lastMatchEnd, match.index);
    }

    if (match[0].startsWith("[b]")) {
      // Bold text
      result += `<span class="font-bold">${parseRichText(match[1]).html}</span>`;
    } else if (match[0].startsWith("[color=")) {
      // Colored text
      result += `<span style="color: ${match[2]}">${match[3]}</span>`;
    } else if (match[0].startsWith("[link")) {
      // Link
      result += `<a href="${match[4]}" style="color: ${match[5]}; text-decoration: underline;">${match[6]}</a>`;
    }

    lastMatchEnd = match.index + match[0].length;
  }

  // Add remaining text
  if (lastMatchEnd < text.length) {
    result += text.substring(lastMatchEnd);
  }

  return { html: result };
}

/**
 * Strips all rich text formatting tags from a string
 *
 * @param text The rich text string to strip
 * @returns Plain text without formatting tags
 */
export function stripRichText(text: string): string {
  if (!text) return "";

  return text
    .replace(/\[b\](.*?)\[\/b\]/g, "$1")
    .replace(/\[color=(.*?)\](.*?)\[\/color\]/g, "$2")
    .replace(/\[link href='(.*?)' color=(.*?)\](.*?)\[\/link\]/g, "$3");
}

/**
 * Checks if a string contains rich text formatting
 *
 * @param text The text to check
 * @returns True if the text contains rich text formatting
 */
export function hasRichText(text: string): boolean {
  if (!text) return false;

  const regex = /\[b\]|\[color=|\[link href=/;
  return regex.test(text);
}
