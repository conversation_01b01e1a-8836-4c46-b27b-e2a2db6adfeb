import loadScript from "load-script";

// Cache to track loaded SDKs
const loadedSDKs = new Set<string>();

/**
 * Load Digio SDK dynamically
 * @returns Promise that resolves when the SDK is loaded
 */
export function loadDigioSDK(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check if SDK is already loaded
    if (window.Digio || loadedSDKs.has("digio")) {
      resolve();
      return;
    }

    loadScript(
      "https://app.digio.in/sdk/v11/digio.js",
      { type: "text/javascript" },
      (error) => {
        if (error) {
          reject(new Error(`Failed to load Digio SDK: ${error.message}`));
        } else {
          loadedSDKs.add("digio");
          resolve();
        }
      }
    );
  });
}

/**
 * Load HyperVerge SDK dynamically
 * @returns Promise that resolves when the SDK is loaded
 */
export function loadHyperVergeSDK(): Promise<void> {
  return new Promise((resolve, reject) => {
    // Check if SDK is already loaded
    if (
      (window.HyperKYCModule && window.HyperKycConfig) ||
      loadedSDKs.has("hyperverge")
    ) {
      resolve();
      return;
    }

    loadScript(
      "https://hv-camera-web-sg.s3-ap-southeast-1.amazonaws.com/hyperverge-web-sdk@9.8.0/src/sdk.min.js",
      (error) => {
        if (error) {
          reject(new Error(`Failed to load HyperVerge SDK: ${error.message}`));
        } else {
          resolve();
        }
      }
    );
  });
}
