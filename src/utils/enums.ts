import { sentenceCase } from "change-case";

export function extractEnums<T extends { [key: string]: string | number }>(
  enumObject: T
) {
  return Object.entries(enumObject)
    .filter(
      ([key]) => Number.isNaN(Number.parseInt(key)) && !key.includes("UNKNOWN")
    )
    .map(([key, value]) => ({
      key: sentenceCase(key),
      value,
    }));
}

export function enumName<T extends { [key: string]: string | number }>(
  enumObject: T,
  value: number
): string {
  const entry = Object.entries(enumObject).find(
    ([key, val]) => !Number.isNaN(Number.parseInt(key)) && val === value
  );

  if (!entry) {
    return "";
  }

  return sentenceCase(entry[0]);
}
