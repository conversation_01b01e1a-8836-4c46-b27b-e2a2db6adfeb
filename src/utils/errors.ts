import { ErrorResponseSchema } from "@/clients/gen/broking/Common_pb";
import { HttpCallException } from "@/exceptions/http-call-exception";
import { fromBinary } from "@bufbuild/protobuf";
import { toaster } from "@/components/ui/toast/store";
import * as Sentry from "@sentry/react";

export async function getErrorMessage(error: unknown): Promise<string> {
  if (error instanceof Response) {
    if (error.headers.get("content-type") === "application/json") {
      const errorResponseData = await error.clone().json();
      return (
        errorResponseData.error_message ||
        errorResponseData.message ||
        `Error: ${errorResponseData.error_code}`
      );
    }
    if (error.headers.get("content-type") === "application/x-protobuf") {
      const errorResponseData = fromBinary(
        ErrorResponseSchema,
        new Uint8Array(await error.clone().arrayBuffer())
      );
      return (
        errorResponseData.errorMessage ||
        `Error: ${errorResponseData.errorCode}`
      );
    }
    return `HTTP Error: ${error.status} - ${error.statusText}`;
  }
  if (error instanceof HttpCallException) {
    return (await error.getError()).message;
  }
  // Check for network errors
  if (error instanceof TypeError && error.message.includes("fetch")) {
    return "Please check your internet connection and try again.";
  }
  if (error instanceof Error) {
    return error.message;
  }
  if (
    typeof error === "object" &&
    error &&
    "message" in error &&
    typeof error.message === "string"
  ) {
    return error.message;
  }
  return "Oops, looks like something went wrong. Our best engineers are on it and this should be fixed soon.";
}

/**
 * Wraps an async function to show an error toast if it throws
 * @param fn The async function to wrap
 * @returns A wrapped function that shows error toasts
 * @throws Re-throws the original error after showing the toast
 */
export function withErrorToast<TArgs extends unknown[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>
): (...args: TArgs) => Promise<TReturn> {
  return async (...args: TArgs) => {
    try {
      return await fn(...args);
    } catch (error) {
      const message = await getErrorMessage(error);
      console.error(message);

      // Add breadcrumb for error toast
      Sentry.addBreadcrumb({
        message: "Error toast displayed",
        category: "ui",
        data: {
          message,
          timestamp: new Date().toISOString(),
        },
      });

      toaster.create({
        description: message,
        type: "error",
        duration: 3000,
      });

      // Report to Sentry if it's an actual Error object
      if (error instanceof Error) {
        Sentry.setContext("errorToast", {
          functionName: fn.name || "anonymous",
          args: args.length,
          timestamp: new Date().toISOString(),
        });
        Sentry.captureException(error, {
          tags: {
            source: "withErrorToast",
            errorMessage: message,
          },
        });
      }

      // Re-throw the error so calling code can handle it if needed
      throw error;
    }
  };
}
