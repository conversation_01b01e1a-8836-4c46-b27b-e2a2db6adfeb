import { UserLifetimeStatusResponse_KycStatus } from "@/clients/gen/broking/Common_pb";
import { StepName } from "@/clients/gen/broking/Kyc_pb";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";
import { getHomeUrl } from "./routing";

// For now, we'll create a simple route function
// This should be replaced with the actual routing solution for TanStack Start
function route(path: string, params?: Record<string, string>): string {
  if (params) {
    let result = path;
    for (const [key, value] of Object.entries(params)) {
      result = result.replace(`[${key}]`, value);
    }
    return result;
  }
  return path;
}

/**
 * Get the path for the next onboarding step
 * @param nextStep The next step from the API
 * @returns The path for the next step
 */
export function getPathForStep(
  nextStep: StepName | string,
  kycStatus: UserLifetimeStatusResponse_KycStatus
): string {
  switch (nextStep) {
    case StepName.PAN_KYC:
      return "/onboarding/pan";
    case StepName.KYC:
      return "/onboarding/kyc";
    case StepName.AADHAAR_PAN_MATCH:
      return "/onboarding/aadhar-pan-match";
    case StepName.BANK_ACCOUNT:
      return "/onboarding/bank-rpd";
    case StepName.DEMAT_ACCOUNT:
      return "/onboarding/demat";
    case StepName.ESIGN:
      return "/onboarding/esign";
    case StepName.USER_PROFILE:
      return "/onboarding/basic-details";
    case StepName.NOMINEE:
      return "/onboarding/nominee";
    case StepName.SELFIE:
      return "/onboarding/selfie";
    case StepName.CURRENT_ADDRESS:
      return "/onboarding/current-address";
    case StepName.WET_SIGNATURE:
      return "/onboarding/wetsign";
    case StepName.CVL_PARTIAL_FETCH:
      return route("/onboarding/cvl/[operation]", { operation: "fetch" });
    case StepName.CVL_PULL:
      return route("/onboarding/cvl/[operation]", { operation: "pull" });
    case StepName.CVL_PUSH:
      return route("/onboarding/cvl/[operation]", { operation: "push" });
    case StepName.KYC_TYPE_UNKNOWN:
    case StepName.POA:
    case StepName.POI:
    case StepName.ADDRESS_SYNC_CVL_PUSH:
    case StepName.KRA_KYC:
    case StepName.QUESTIONNAIRE:
    case StepName.WHITELIST_CHECK:
      if (
        kycStatus === UserLifetimeStatusResponse_KycStatus.COMPLETED ||
        kycStatus ===
          UserLifetimeStatusResponse_KycStatus.ONBOARDING_ON_EXCHANGE ||
        kycStatus ===
          UserLifetimeStatusResponse_KycStatus.EXCHANGE_ONBOARDING_FAILED ||
        kycStatus === UserLifetimeStatusResponse_KycStatus.CVL_PUSH_REQUIRED ||
        kycStatus === UserLifetimeStatusResponse_KycStatus.CVL_VALIDATED
      ) {
        return "/onboarding/status";
      }
      throw new Error(
        `Cannot handle the step: ${nextStep} with kycStatus: ${kycStatus}`
      );
    default:
      throw new Error(
        `Cannot handle the step: ${nextStep} with kycStatus: ${kycStatus}`
      );
  }
}

export const getPathForNextAlphaStep = (nextStep: KycType) => {
  switch (nextStep) {
    case KycType.NAME:
      return "/authentication/name";
    case KycType.EMAIL:
      return "/authentication/email";
    case KycType.KYC_TYPE_UNKNOWN:
      return getHomeUrl();
    default:
      throw new Error(`Cannot handle the step: ${nextStep}`);
  }
};
