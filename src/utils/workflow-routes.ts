import {
  StepName,
  WorkflowName,
  type ContinueWorkflowResponse,
} from "@/clients/gen/broking/WorkflowStep_pb";

/**
 * Get the path for the next workflow step
 * @param nextStep The next step from the workflow API
 * @param workflowStatus The current workflow status
 * @param workflowName The workflow name (used in URL slug)
 * @returns The path for the next step
 */
export function getPathForWorkflowStep(
  currentWorkflowName: WorkflowName,
  response: ContinueWorkflowResponse | undefined
): string {
  if (!response?.nextStep) {
    return `/workflow/${getWorkflowSlug(response?.workflowName || currentWorkflowName)}/status`;
  }
  const { nextStep, workflowName } = response;
  const step = nextStep;
  const slug = getWorkflowSlug(workflowName);
  if (!step) {
    return `/workflow/${slug}/status`;
  }
  switch (step) {
    case StepName.PAN_KYC:
      return `/workflow/${slug}/pan`;
    case StepName.DIGIO_KYC:
      return `/workflow/${slug}/kyc`;
    case StepName.SELFIE:
      return `/workflow/${slug}/selfie`;
    case StepName.USER_PROFILE:
      return `/workflow/${slug}/basic-details`;
    case StepName.WET_SIGNATURE:
      return `/workflow/${slug}/wetsign`;
    case StepName.ESIGN:
      return `/workflow/${slug}/esign`;
    case StepName.DEMAT_ACCOUNT:
      return `/workflow/${slug}/demat`;
    case StepName.AADHAAR_PAN_MATCH:
      return `/workflow/${slug}/aadhar-pan-match`;
    case StepName.BANK_ACCOUNT:
      return `/workflow/${slug}/bank-rpd`;
    case StepName.NOMINEE:
      return `/workflow/${slug}/nominee`;
    case StepName.ESIGN_WITH_DEMAT:
      return `/workflow/${slug}/esign?withDemat=true`;
    case StepName.CVL_PULL_WAITING:
    case StepName.CVL_PUSH_WAITING:
    case StepName.CDSL_PUSH_WAITING:
    case StepName.CDSL_STATUS_CHECK:
    case StepName.NSE_ONBOARDING:
    case StepName.NSE_PUSH_WAITING:
      return `/workflow/${slug}/status`;
    default:
      throw new Error(`Cannot handle the step: ${step}`);
  }
}

/**
 * Convert WorkflowName enum to URL slug
 */
export function getWorkflowEnumFromString(workflowName?: string): WorkflowName {
  switch (workflowName) {
    case "DEMAT_ACCOUNT_OPENING":
      return WorkflowName.DEMAT_ACCOUNT_OPENING;
    case "TRADING_ACCOUNT_OPENING":
      return WorkflowName.TRADING_ACCOUNT_OPENING;
    case "TRADING_DEMAT_ACCOUNT_OPENING":
      return WorkflowName.TRADING_DEMAT_ACCOUNT_OPENING;
    case "ONBOARDING_INITIATION":
      return WorkflowName.ONBOARDING_INITIATION;
    default:
      return WorkflowName.UNKNOWN_WORKFLOW;
  }
}

export function getWorkflowNameStringRepresentation(
  workflowName: WorkflowName
): string {
  switch (workflowName) {
    case WorkflowName.DEMAT_ACCOUNT_OPENING:
      return "DEMAT_ACCOUNT_OPENING";
    case WorkflowName.TRADING_ACCOUNT_OPENING:
      return "TRADING_ACCOUNT_OPENING";
    case WorkflowName.TRADING_DEMAT_ACCOUNT_OPENING:
      return "TRADING_DEMAT_ACCOUNT_OPENING";
    case WorkflowName.ONBOARDING_INITIATION:
      return "ONBOARDING_INITIATION";
    default:
      return "UNKNOWN_WORKFLOW";
  }
}

/**
 * Convert WorkflowName enum to URL slug
 */
export function getWorkflowSlug(workflowName?: WorkflowName): string {
  switch (workflowName) {
    case WorkflowName.DEMAT_ACCOUNT_OPENING:
      return "demat-account-opening";
    case WorkflowName.TRADING_ACCOUNT_OPENING:
      return "trading-account-opening";
    case WorkflowName.TRADING_DEMAT_ACCOUNT_OPENING:
      return "trading-demat-account-opening";
    case WorkflowName.ONBOARDING_INITIATION:
      return "onboarding-initiation";
    default:
      return "";
  }
}

/**
 * Convert URL slug back to WorkflowName enum
 */
export function getWorkflowNameFromSlug(slug: string): WorkflowName {
  switch (slug) {
    case "demat-account-opening":
      return WorkflowName.DEMAT_ACCOUNT_OPENING;
    case "trading-account-opening":
      return WorkflowName.TRADING_ACCOUNT_OPENING;
    case "trading-demat-account-opening":
      return WorkflowName.TRADING_DEMAT_ACCOUNT_OPENING;
    case "onboarding-initiation":
      return WorkflowName.ONBOARDING_INITIATION;
    default:
      throw new Error(`Cannot handle the slug: ${slug}`);
  }
}

/**
 * Get the workflow display name for UI
 */
export function getWorkflowDisplayName(workflowName: WorkflowName): string {
  switch (workflowName) {
    case WorkflowName.DEMAT_ACCOUNT_OPENING:
      return "Demat Account Opening";
    case WorkflowName.TRADING_ACCOUNT_OPENING:
      return "Trading Account Opening";
    case WorkflowName.TRADING_DEMAT_ACCOUNT_OPENING:
      return "Trading & Demat Account Opening";
    case WorkflowName.ONBOARDING_INITIATION:
      return "Onboarding Initiation";
    default:
      return "Account Opening";
  }
}

/**
 * Get the step display name for UI
 */
export function getStepDisplayName(stepName: StepName): string {
  switch (stepName) {
    case StepName.DIGIO_KYC:
      return "KYC Verification";
    case StepName.SELFIE:
      return "Selfie Verification";
    case StepName.USER_PROFILE:
      return "Basic Details";
    case StepName.WET_SIGNATURE:
      return "Upload Signature";
    case StepName.ESIGN:
      return "Digital Signature";
    case StepName.CVL_PULL:
    case StepName.CVL_PUSH:
      return "CVL Processing";
    case StepName.CDSL_PUSH_INITIATE:
    case StepName.CDSL_PUSH:
    case StepName.CDSL_STATUS_CHECK:
    case StepName.CDSL_PUSH_WAITING:
      return "CDSL Processing";
    case StepName.NSE_ONBOARDING:
    case StepName.NSE_PUSH_WAITING:
      return "NSE Processing";
    case StepName.CVL_PUSH_WAITING:
      return "CVL Processing";
    default:
      return "Processing";
  }
}
