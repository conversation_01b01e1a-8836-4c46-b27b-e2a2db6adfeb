export function calculateInitialValue(
  pricePerBond: number,
  bondIntialUnits: number
) {
  if (!pricePerBond || pricePerBond === 0) {
    return 10;
  }

  const clamp = (value: number, min: number, max: number) =>
    Math.min(Math.max(value, min), max);

  if (bondIntialUnits !== null && bondIntialUnits !== undefined) {
    return Math.floor(
      clamp(Math.round(bondIntialUnits), 1, Math.floor(1000000 / pricePerBond))
    );
  } else {
    const initialUnits = 100000 / pricePerBond;
    return Math.floor(
      clamp(Math.round(initialUnits), 1, Math.floor(1000000 / pricePerBond))
    );
  }
}
