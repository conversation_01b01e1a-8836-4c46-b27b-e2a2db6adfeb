interface StorageItem<T> {
  value: T;
  expiresAt: number;
}

/**
 * Set an item in session storage with expiration time
 * @param expirationMinutes Expiration time in minutes
 */
export function setSessionItem<T>(
  key: string,
  value: T,
  expirationMinutes: number
): void {
  const expiresAt = Date.now() + expirationMinutes * 60 * 1000;
  const item: StorageItem<T> = {
    value,
    expiresAt,
  };

  try {
    sessionStorage.setItem(key, JSON.stringify(item));
  } catch (error) {
    console.warn(`Failed to set session storage item ${key}:`, error);
  }
}

export function getSessionItem<T>(key: string): T | null {
  try {
    const itemStr = sessionStorage.getItem(key);
    if (!itemStr) {
      return null;
    }

    const item: StorageItem<T> = JSON.parse(itemStr);
    if (Date.now() > item.expiresAt) {
      sessionStorage.removeItem(key);
      return null;
    }

    return item.value;
  } catch (error) {
    console.warn(`Failed to get session storage item ${key}:`, error);
    sessionStorage.removeItem(key);
    return null;
  }
}

export function removeSessionItem(key: string): void {
  try {
    sessionStorage.removeItem(key);
  } catch (error) {
    console.warn(`Failed to remove session storage item ${key}:`, error);
  }
}

export function hasValidSessionItem(key: string): boolean {
  return getSessionItem(key) !== null;
}

export function getOrCreateSessionItem<T>(
  key: string,
  factory: () => T,
  expirationMinutes: number
): T {
  const existing = getSessionItem<T>(key);
  if (existing !== null) {
    return existing;
  }

  const newValue = factory();
  setSessionItem(key, newValue, expirationMinutes);
  return newValue;
}
