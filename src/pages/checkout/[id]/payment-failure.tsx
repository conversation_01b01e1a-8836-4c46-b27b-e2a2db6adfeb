import { useCallback, useMemo } from "react";
import Button from "@/components/ui/button/button";
import LinkButton from "@/components/ui/button/link-button";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { formatCurrency } from "@/utils/format";
import type {
  PaymentStatusResponse,
  PlaceOrderResponseV2,
} from "@/clients/gen/broking/Order_pb";
import useNavigate from "@/hooks/navigate";
import { supportLink } from "@/config/support";

interface PaymentFailureProps {
  order: PlaceOrderResponseV2;
  payment: PaymentStatusResponse;
}

export default function PaymentFailure({
  order,
  payment,
}: PaymentFailureProps) {
  const navigate = useNavigate();
  const statusContent = useMemo(() => {
    switch (payment.status) {
      case "PAYMENT_FAILED":
      case "PAYMENT_INITIATED":
        return {
          title: "Payment unsuccessful!",
          message: `Your payment of ${formatCurrency(payment.payableAmount)} to buy ${payment.quantity} unit of ${payment.bondName} was unsuccessful`,
          buttonText: "Retry Payment",
        };
      case "EXPIRED":
        return {
          title: "Payment expired!",
          message: `Your payment of ${formatCurrency(payment.payableAmount)} to buy ${payment.quantity} unit of ${payment.bondName} was unsuccessful`,
          buttonText: "Retry booking",
        };
      case "ORDER_PENDING":
        return {
          title: "Payment pending!",
          message: `Your payment of ${formatCurrency(payment.payableAmount)} to buy ${payment.quantity} unit of ${payment.bondName} is pending`,
          buttonText: "Retry Payment",
        };
      default:
        return {
          title: "Payment unsuccessful!",
          message: `Your payment of ${formatCurrency(payment.payableAmount)} to buy ${payment.quantity} unit of ${payment.bondName} was unsuccessful`,
          buttonText: "Retry Payment",
        };
    }
  }, [
    payment.status,
    payment.payableAmount,
    payment.quantity,
    payment.bondName,
  ]);

  const retry = useCallback(() => {
    navigate(`/checkout/${payment.orderId}/pay`, { replace: true });
  }, [navigate, payment.orderId]);

  return (
    <>
      <div className="flex items-center justify-center pt-10">
        {(order.orderData?.orderItemData ?? []).map((item) => (
          <div key={item.bondOfferingId} className="relative">
            <EntityLogo
              url={item.bondIssuerLogo}
              color={item.bondIssuerColor}
              size="large"
              seamless={true}
              elevation="none"
            />
            <img
              src="https://assets.stablemoney.in/app/ic_error_bond.svg"
              alt="Payment Error Icon"
              className="absolute -right-1 -bottom-1 w-5"
            />
          </div>
        ))}
      </div>

      <p className="text-heading2 mt-8 mb-1.5 px-5 text-center">
        {statusContent.title}
      </p>
      <p className="text-body1 text-black-50 px-5 text-center">
        {statusContent.message}
      </p>

      <FloatingFooterContent className="text-center">
        <LinkButton href={supportLink} className="m-auto mb-3 block w-fit">
          Need help? Call us
        </LinkButton>
        <Button onClick={retry}>{statusContent.buttonText}</Button>
      </FloatingFooterContent>
    </>
  );
}
