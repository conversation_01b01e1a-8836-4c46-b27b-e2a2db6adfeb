import { useMemo } from "react";
import Button from "@/components/ui/button/button";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import VerticalStepper from "@/components/ui/vertical-stepper/vertical-stepper";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { formatCurrency } from "@/utils/format";
import { getInvestmentsUrl } from "@/utils/routing";
import dayjs from "dayjs";
import paymentSuccessReferral from "@/assets/images/illustrations/payment-success-refer.webp";
import type {
  PaymentStatusResponse,
  PlaceOrderResponseV2,
} from "@/clients/gen/broking/Order_pb";
import Anchor from "@/components/functional/anchor";
import { isRepeatCustomer } from "@/utils/status";
import QueryRenderer from "@/components/functional/query-renderer";
import { getInvestmentsQueryOptions } from "@/queries/investments";
import { useQuery } from "@tanstack/react-query";
import { trackEvent } from "@/utils/analytics";

interface PaymentSuccessProps {
  order: PlaceOrderResponseV2;
  payment: PaymentStatusResponse;
}

export default function PaymentSuccess({
  order,
  payment,
}: PaymentSuccessProps) {
  const successSteps = useMemo(
    () => [
      {
        icon: "https://assets.stablemoney.in/app/ic_bond_stepper.svg",
        label: "Payment successful",
        caption: payment.paymentTime
          ? dayjs(payment.paymentTime).format("MMM D, YYYY")
          : "",
      },
      {
        icon: "https://assets.stablemoney.in/app/pending-icon.webp",
        label: "Units credited (in progress)",
        caption: payment.settlementDate
          ? dayjs(payment.settlementDate).format("MMM D, YYYY")
          : "",
      },
    ],
    [payment.paymentTime, payment.settlementDate]
  );

  const handleReferralImageTapped = () => {
    trackEvent("payment_success_referral_image_tapped");
  };

  const investmentsQuery = useQuery(getInvestmentsQueryOptions());
  return (
    <>
      <div className="flex items-center justify-center pt-10">
        {(order.orderData?.orderItemData ?? []).map((item) => (
          <div key={item.bondOfferingId} className="relative">
            <EntityLogo
              url={item.bondIssuerLogo}
              color={item.bondIssuerColor}
              size="large"
              seamless={true}
              elevation="none"
            />
            <img
              src="https://assets.stablemoney.in/app/ic_success_bond.svg"
              alt=""
              className="absolute -right-1 -bottom-1 w-5"
            />
          </div>
        ))}
      </div>

      <p className="text-heading2 mt-8 mb-1.5 text-center">
        Payment Successful!
      </p>

      <div className="flex items-center justify-center gap-2">
        <span className="text-heading4 text-black-50">
          {formatCurrency(payment.payableAmount)}
        </span>
        <span className="bg-black-50 block h-1.5 w-1.5 rounded-full"></span>
        <span className="text-heading4 text-black-50">
          {payment.quantity} unit
        </span>
      </div>

      <hr className="border-black-10 my-8 border-t-1 border-dashed" />

      <div className="flex items-center justify-center">
        <VerticalStepper steps={successSteps} />
      </div>
      <QueryRenderer query={investmentsQuery}>
        {({ orders }) => {
          return (
            isRepeatCustomer(orders) && (
              <Anchor
                className="mt-10 mb-50 block px-5 sm:hidden"
                href="/dynamic/referral"
                onClick={handleReferralImageTapped}
              >
                <img
                  src={paymentSuccessReferral}
                  alt="Payment Success Referral"
                />
              </Anchor>
            )
          );
        }}
      </QueryRenderer>
      <FloatingFooterContent>
        <p className="text-body1 text-black-50 mb-3 text-center">
          Your order receipt will be sent to your email
        </p>
        <Button href={getInvestmentsUrl()}>Go to passbook</Button>
      </FloatingFooterContent>
    </>
  );
}
