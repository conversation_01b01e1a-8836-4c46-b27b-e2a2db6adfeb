import { useEffect } from "react";
import { useParams } from "react-router";
import useNavigate from "@/hooks/navigate";
import { processPayment } from "@/queries/orders";
import { startCheckout } from "./checkout";
import LoadingPage from "@/components/functional/loading-page";
import { fromPromise, setup, assign, type DoneActorEvent } from "xstate";
import { useMachine } from "@xstate/react";
import type { ProcessPaymentResponse } from "@/clients/gen/broking/Order_pb";
import type { CheckoutResult } from "@cashfreepayments/cashfree-js";
import { createInspector } from "@/utils/xstate";

const machine = setup({
  types: {
    context: {} as {
      paymentSessionId?: string;
      orderId: string;
    },
    input: {} as {
      orderId: string;
    },
    events: {} as
      | DoneActorEvent<ProcessPaymentResponse>
      | DoneActorEvent<CheckoutResult>,
  },
  actors: {
    checkout: fromPromise(
      ({ input }: { input: { paymentSessionId: string; orderId: string } }) =>
        startCheckout(input.paymentSessionId, input.orderId)
    ),
    fetchPaymentSessionId: fromPromise(
      ({ input }: { input: { orderId: string } }) =>
        processPayment({ orderId: input.orderId })
    ),
  },
  actions: {
    assignPaymentSessionId: assign({
      paymentSessionId: ({ event }) => {
        if ("output" in event && "paymentSessionId" in event.output) {
          return event.output.paymentSessionId;
        }
      },
    }),
  },
}).createMachine({
  id: "checkout",
  initial: "fetchingPaymentSessionId",
  context({ input }) {
    return input;
  },
  states: {
    fetchingPaymentSessionId: {
      invoke: {
        src: "fetchPaymentSessionId",
        input: ({ context }) => context,
        onDone: { target: "checkingOut", actions: "assignPaymentSessionId" },
        onError: { target: "finished" },
      },
    },
    checkingOut: {
      invoke: {
        src: "checkout",
        input: ({ context }) => ({
          paymentSessionId: context.paymentSessionId!,
          orderId: context.orderId,
        }),
        onDone: { target: "finished" },
        onError: { target: "finished" },
      },
    },
    finished: {
      type: "final",
    },
  },
});

export default function CheckoutPayPage() {
  const { id } = useParams();
  const [state] = useMachine(machine, {
    input: { orderId: id! },
    inspect: createInspector(machine.id),
  });
  const navigate = useNavigate();
  useEffect(() => {
    if (state.value === "finished") {
      navigate(`/checkout/${id}`, { replace: true });
    }
  }, [state.value, id, navigate]);

  return <LoadingPage />;
}
