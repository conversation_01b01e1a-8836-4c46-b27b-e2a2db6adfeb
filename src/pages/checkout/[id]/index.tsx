import { useSuspenseQuery } from "@tanstack/react-query";
import { useParams, type LoaderFunctionArgs } from "react-router";
import { getOrderStatusQueryOptions } from "@/queries/orders";
import PaymentSuccess from "./payment-success";
import PaymentFailure from "./payment-failure";
import Head from "@/components/ui/head";
import { queryClient } from "@/queries/client";

export async function loader({ params }: LoaderFunctionArgs) {
  const id = params.id;
  if (!id) throw new Error("Order ID is required");

  await queryClient.ensureQueryData(getOrderStatusQueryOptions(id));
}

export default function CheckoutPage() {
  const { id } = useParams<{ id: string }>();

  if (!id) {
    throw new Error("Order ID is required");
  }

  const {
    data: [order, payment],
  } = useSuspenseQuery(getOrderStatusQueryOptions(id));

  return (
    <>
      <Head title="Order Status" />
      {payment.status === "PAYMENT_DONE" ? (
        <PaymentSuccess order={order} payment={payment} />
      ) : (
        <PaymentFailure order={order} payment={payment} />
      )}
    </>
  );
}
