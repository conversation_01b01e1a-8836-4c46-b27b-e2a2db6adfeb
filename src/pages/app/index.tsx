import { buildPersonalizationWidget } from "@/components/personalization/builder";
import { personalizationPageQuery } from "@/queries/bonds";
import { useSuspenseQuery } from "@tanstack/react-query";
import Head from "@/components/ui/head";
import { queryClient } from "@/queries/client";
import Nudges from "@/components/functional/nudges/nudges";

export async function loader() {
  await queryClient.ensureQueryData(personalizationPageQuery("bonds-home"));
}

export default function AppHomePage() {
  const { data: pageResponse } = useSuspenseQuery(
    personalizationPageQuery("bonds-home")
  );

  return (
    <>
      <Head title="Dashboard" />
      {buildPersonalizationWidget(pageResponse.root)}
      <Nudges pageUri="bonds-home" />
    </>
  );
}
