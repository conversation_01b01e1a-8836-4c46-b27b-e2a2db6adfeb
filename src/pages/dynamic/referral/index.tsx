import { useEffect, useState } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { buildPersonalizationWidget } from "@/components/personalization/builder";
import { personalizationPageQuery } from "@/queries/bonds";
import { getCampaign } from "@/queries/identity";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import Head from "@/components/ui/head";
import type { BondsReferralCampaignMetadata } from "@/clients/gen/platform/public/models/identity/Campaign_pb";
import SingleCategoryFAQ from "@/components/personalization/widgets/single-category-faq";
import { queryClient } from "@/queries/client";
import * as native from "@/utils/native-integration";
import { trackEvent } from "@/utils/analytics";

export async function loader() {
  await queryClient.ensureQueryData(
    personalizationPageQuery("bonds-referral-page")
  );
}

export default function DynamicReferralPage() {
  const [campaign, setCampaign] =
    useState<BondsReferralCampaignMetadata | null>(null);

  // Fetch campaign data
  useEffect(() => {
    getCampaign("BONDS_REFERRAL_CAMPAIGN_TYPE").then(setCampaign);
  }, []);

  const { data: pageResponse } = useSuspenseQuery(
    personalizationPageQuery("bonds-referral-page")
  );

  const handleShare = () => {
    if (!campaign) return;

    const { shareText, campaignReferralLink } = campaign;
    const shareContent = `${shareText} ${campaignReferralLink}`;

    trackEvent("dynamic_image_tapped", {
      type: "raster",
      id: "bonds-referral-page-cta",
      url: "https://assets.stablemoney.in/app/referral_cta_22may.webp",
    });

    // Check if native sharing is available (for mobile apps)
    if (native.share.isSupported()) {
      return native.share.shareText(shareContent, "Invite now");
    }

    // Fallback to Web Share API
    if (navigator.share) {
      navigator.share({
        text: shareContent,
      });
    } else {
      // Fallback to copying to clipboard
      navigator.clipboard?.writeText(shareContent);
    }
  };

  return (
    <>
      <Head title="Referral Program" />
      <div
        className="bg-bg relative mx-auto max-w-3xl"
        style={{ "--color-bg": "black" } as React.CSSProperties}
      >
        {buildPersonalizationWidget(pageResponse.root)}{" "}
        <div className="floating-footer-padding relative -mt-(--floating-footer-height)">
          <SingleCategoryFAQ
            faqCategory={"BOND_REFERRAL"}
            faqNamespaceIdentifier="BOND_REFERRAL_GENERIC"
            isDarkMode={true}
          />
          <a
            href="https://stablebonds.in/compliance/term-and-condition"
            target="_blank"
            rel="noopener noreferrer"
          >
            <img
              src="https://assets.stablemoney.in/app/referral_tnc_v2_22may.webp"
              alt="Terms and conditions"
            />
          </a>
        </div>
      </div>
      <FloatingFooterContent
        style={
          {
            margin: "0 auto",
            bottom: "0",
            left: "0",
            right: "0",
            padding: "0",
          } as React.CSSProperties
        }
        className="max-w-3xl"
      >
        <button className="m-auto block" onClick={handleShare}>
          <img
            src="https://assets.stablemoney.in/app/CTA_refer_and_earn_landingpage_footer_7july_807pm.webp"
            alt="Invite now"
          />
        </button>
      </FloatingFooterContent>
    </>
  );
}
