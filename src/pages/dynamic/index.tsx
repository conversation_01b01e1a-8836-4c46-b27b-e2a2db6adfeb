import { buildPersonalizationWidget } from "@/components/personalization/builder";
import { personalizationPageQuery } from "@/queries/bonds";
import { useSuspenseQuery } from "@tanstack/react-query";
import {
  useParams,
  useSearchParams,
  type LoaderFunctionArgs,
} from "react-router";
import { queryClient } from "@/queries/client";

export async function loader({ params, request }: LoaderFunctionArgs) {
  const path = params.path;
  if (!path) throw new Error("Path is required");

  const url = new URL(request.url);
  const searchParams = url.searchParams;
  const pageParams = searchParams.get("params");
  const parsedParams = pageParams ? JSON.parse(pageParams) : undefined;

  await queryClient.ensureQueryData(
    personalizationPageQuery(path, parsedParams)
  );
}

export default function DynamicPage() {
  const navigationParams = useParams();
  const [searchParams] = useSearchParams();
  const params = searchParams.get("params");
  const { path } = navigationParams;
  const pageParams = params ? JSON.parse(params) : undefined;
  const { data: pageResponse } = useSuspenseQuery(
    personalizationPageQuery(path!, pageParams)
  );

  return buildPersonalizationWidget(pageResponse.root);
}
