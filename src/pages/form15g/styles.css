.prose {
  max-width: 65ch;
  color: rgb(55 65 81);
}

.prose h1 {
  color: rgb(17 24 39);
  font-weight: 800;
  font-size: 2.25em;
  margin-top: 0;
  margin-bottom: 0.8888889em;
  line-height: 1.1111111;
}

.prose h2 {
  color: rgb(17 24 39);
  font-weight: 700;
  font-size: 1.5em;
  margin-top: 2em;
  margin-bottom: 1em;
  line-height: 1.3333333;
}

.prose h3 {
  color: rgb(17 24 39);
  font-weight: 600;
  font-size: 1.25em;
  margin-top: 1.6em;
  margin-bottom: 0.6em;
  line-height: 1.6;
}

.prose p {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
}

.prose ul {
  margin-top: 1.25em;
  margin-bottom: 1.25em;
  padding-left: 1.625em;
  list-style-type: disc;
}

.prose li {
  margin-top: 0.5em;
  margin-bottom: 0.5em;
}

.prose hr {
  margin-top: 3em;
  margin-bottom: 3em;
  border-color: rgb(229 231 235);
}

.prose a {
  color: rgb(37 99 235);
  text-decoration: underline;
  font-weight: 500;
}
