import ProfileLayout from "@/components/profile/profile-layout";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getProfileQueryOptions } from "@/queries/profile";
import Button from "@/components/ui/button/button";
import { queryClient } from "@/queries/client";

export async function loader() {
  await queryClient.ensureQueryData(getProfileQueryOptions());
}

export default function ProfileBankPage() {
  const { data: userData } = useSuspenseQuery(getProfileQueryOptions());

  return (
    <ProfileLayout title="Bank Details">
      <div className="flex flex-col space-y-4">
        {userData.bankAccountDetail ? (
          <>
            <div className="flex flex-col">
              <p className="font-medium">Bank Name</p>
              <p className="text-black-60 font-medium">
                {userData.bankAccountDetail?.bankName}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="font-medium">Account No.</p>
              <p className="text-black-60 font-medium">
                {userData.bankAccountDetail?.accountNumberMasked}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="font-medium">IFSC</p>
              <p className="text-black-60 font-medium">
                {userData.bankAccountDetail?.ifscCode}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="font-medium">Branch</p>
              <p className="text-black-60 font-medium">
                {userData.bankAccountDetail?.branch}
              </p>
            </div>
          </>
        ) : (
          <div className="flex">
            <Button href="/onboarding">Complete Bank Details</Button>
          </div>
        )}
      </div>
    </ProfileLayout>
  );
}
