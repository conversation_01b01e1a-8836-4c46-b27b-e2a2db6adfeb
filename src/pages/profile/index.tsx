import ProfileLayout from "@/components/profile/profile-layout";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getProfileQueryOptions, getNameFromProfile } from "@/queries/profile";
import { Gender } from "@/clients/gen/broking/Common_pb";
import { queryClient } from "@/queries/client";

export async function loader() {
  await queryClient.ensureQueryData(getProfileQueryOptions());
}

export default function ProfilePage() {
  const { data: userData } = useSuspenseQuery(getProfileQueryOptions());
  return (
    <ProfileLayout title="General Details">
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col">
          <p className="font-medium">Name</p>
          <p className="text-black-60 font-medium">
            {getNameFromProfile(userData)}
          </p>
        </div>
        <div className="flex flex-col">
          <p className="font-medium">Email</p>
          <p className="text-black-60 font-medium">{userData.data?.email}</p>
        </div>
        <div className="flex flex-col">
          <p className="font-medium">Phone</p>
          <p className="text-black-60 font-medium">{userData.data?.mobile}</p>
        </div>
        {userData.profileData?.dob && (
          <div className="flex flex-col">
            <p className="font-medium">Date of Birth</p>
            <p className="text-black-60 font-medium">
              {userData.profileData?.dob}
            </p>
          </div>
        )}
        {userData.profileData?.panNumber && (
          <div className="flex flex-col">
            <p className="font-medium">PAN Number</p>
            <p className="text-black-60 font-medium">
              {userData.profileData?.panNumber}
            </p>
          </div>
        )}
        {userData.profileData?.gender !== Gender.UNKNOWN_GENDER && (
          <div className="flex flex-col">
            <p className="font-medium">Gender</p>
            <p className="text-black-60 font-medium">
              {Gender[userData.profileData?.gender ?? Gender.UNKNOWN_GENDER]}
            </p>
          </div>
        )}
        {userData.profileData?.fatherName && (
          <div className="flex flex-col">
            <p className="font-medium">Father's Name</p>
            <p className="text-black-60 font-medium">
              {userData.profileData?.fatherName}
            </p>
          </div>
        )}
        {userData.profileData?.motherName && (
          <div className="flex flex-col">
            <p className="font-medium">Mother's Name</p>
            <p className="text-black-60 font-medium">
              {userData.profileData?.motherName}
            </p>
          </div>
        )}
      </div>
    </ProfileLayout>
  );
}
