import ProfileLayout from "@/components/profile/profile-layout";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getForm15GDataQueryOptions } from "@/queries/bonds";
import Button from "@/components/ui/button/button";
import Surface from "@/components/ui/surface/surface";
import { trackEvent } from "@/utils/analytics";
import type { Filled15GFormFile } from "@/clients/gen/broking/Order_pb";
import { queryClient } from "@/queries/client";

export async function loader() {
  await queryClient.ensureQueryData(getForm15GDataQueryOptions());
}

export default function ProfileReportsPage() {
  const { data: form15GData } = useSuspenseQuery(getForm15GDataQueryOptions());

  function downloadForm(form: Filled15GFormFile) {
    try {
      trackEvent("pdf_download_initiated", {
        filename: form.fileName,
        hasContent: !!form.fileContent,
        contentType: "raw",
      });
      const blob = new Blob([form.fileContent], { type: "application/pdf" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = form.fileName;
      a.click();
      trackEvent("form15g_download_success", {
        filename: form.fileName,
      });
    } catch (error) {
      trackEvent("form15g_pdf_download_error", {
        filename: form.fileName,
        error: error instanceof Error ? error.message : String(error),
      });
    }
  }

  return (
    <ProfileLayout title="Reports">
      <div className="prose flex flex-col gap-2">
        {form15GData && form15GData.forms.length > 0 ? (
          form15GData.forms.map((form) => (
            <Surface key={form.fileName} elevation="md">
              <div className="flex items-center justify-between gap-4 px-4">
                <p className="text-black-80 text-body1 font-medium">
                  {form.fileName}
                </p>
                <Button size="small" onClick={() => downloadForm(form)}>
                  Download
                </Button>
              </div>
            </Surface>
          ))
        ) : (
          <p className="text-black-60 text-center">No reports available</p>
        )}
      </div>
    </ProfileLayout>
  );
}
