import ProfileLayout from "@/components/profile/profile-layout";
import { useQuery, useSuspenseQuery } from "@tanstack/react-query";
import { getProfileQueryOptions, getNameFromProfile } from "@/queries/profile";
import Button from "@/components/ui/button/button";
import { queryClient } from "@/queries/client";
import { getWorkflowStatus } from "@/queries/workflow";
import {
  WorkflowName,
  WorkflowStatus,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { isEnabled } from "@/utils/feature-flags";

export async function loader() {
  await queryClient.ensureQueryData(getProfileQueryOptions());
}

export default function ProfileDematPage() {
  const { data: userData } = useSuspenseQuery(getProfileQueryOptions());

  const dematWorkflowQuery = useQuery({
    queryKey: ["workflow-status", WorkflowName.DEMAT_ACCOUNT_OPENING],
    queryFn: () => getWorkflowStatus(WorkflowName.DEMAT_ACCOUNT_OPENING),
  });
  const tradingAccountWorkflowQuery = useQuery({
    queryKey: ["workflow-status", WorkflowName.TRADING_ACCOUNT_OPENING],
    queryFn: () => getWorkflowStatus(WorkflowName.TRADING_ACCOUNT_OPENING),
  });

  const featureFlageQuery = useQuery({
    queryKey: ["feature-flags"],
    queryFn: () => isEnabled("feature-bonds-demat"),
  });

  const dematWorkflowEnabled =
    tradingAccountWorkflowQuery.data &&
    dematWorkflowQuery.data &&
    tradingAccountWorkflowQuery.data.workflowStatus ===
      WorkflowStatus.COMPLETED &&
    dematWorkflowQuery.data.workflowStatus !== WorkflowStatus.COMPLETED &&
    featureFlageQuery.data;

  return (
    <ProfileLayout title="Demat Detail">
      <div className="flex flex-col space-y-4">
        {userData.dematAccountDetail ? (
          <>
            <div className="flex flex-col">
              <p className="font-medium">Beneficiary ID</p>
              <p className="text-black-60 font-medium">
                {userData.dematAccountDetail?.dematId}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="font-medium">Broker Name</p>
              <p className="text-black-60 font-medium">
                {userData.dematAccountDetail?.brokerName}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="font-medium">Account Type</p>
              <p className="text-black-60 font-medium">SOLE</p>
            </div>
            <div className="flex flex-col">
              <p className="font-medium">Account Holder</p>
              <p className="text-black-60 font-medium">
                {getNameFromProfile(userData)}
              </p>
            </div>
            <div className="flex flex-col">
              <p className="font-medium">PAN</p>
              <p className="text-black-60 font-medium">
                {userData.profileData?.panNumber}
              </p>
            </div>
            {dematWorkflowEnabled ? (
              <div className="flex w-fit">
                <Button href="/workflow/demat-account-opening">
                  Open new Demat with us
                </Button>
              </div>
            ) : null}
          </>
        ) : null}
      </div>
    </ProfileLayout>
  );
}
