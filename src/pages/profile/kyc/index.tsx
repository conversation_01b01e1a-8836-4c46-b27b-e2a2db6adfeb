import ProfileLayout from "@/components/profile/profile-layout";
import { useSuspenseQuery } from "@tanstack/react-query";
import { getProfileQueryOptions } from "@/queries/profile";
import Button from "@/components/ui/button/button";
import Anchor from "@/components/functional/anchor";
import { queryClient } from "@/queries/client";

export async function loader() {
  await queryClient.ensureQueryData(getProfileQueryOptions());
}

export default function ProfileKycPage() {
  const { data: userData } = useSuspenseQuery(getProfileQueryOptions());

  return (
    <ProfileLayout title="KYC Details">
      <div className="flex flex-col space-y-4">
        <div className="flex flex-col">
          {userData.accountOpeningFormUrl ? (
            <>
              <p className="font-medium">Account opening form</p>
              <div>
                <Anchor
                  href={userData.accountOpeningFormUrl}
                  className="text-body1 font-medium underline underline-offset-6"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Click here
                </Anchor>
              </div>
            </>
          ) : (
            <div className="flex">
              <Button href="/onboarding">Complete KYC</Button>
            </div>
          )}
        </div>
      </div>
    </ProfileLayout>
  );
}
