import Cover from "./home/<USER>/cover";
import FaqSupport from "./home/<USER>/faq-support";
import Footer from "./home/<USER>/footer";
import Head from "@/components/ui/head";
import HighReturns from "./home/<USER>/high-returns-statement";
import Shield from "./home/<USER>/shield";
import KnowMore from "./home/<USER>/know-more";
import Testimonial from "./home/<USER>/testimonial/testimonial";
import BackedByBest from "./home/<USER>/backed-by-best/backed-by-best";
import YieldMaturityCalculator from "./home/<USER>/yield-maturity-calculator";
import Button from "@/components/ui/button/button";
import { useRef } from "react";
import { queryOptions } from "@tanstack/react-query";
import LandingPageFloatingFooter from "@/components/ui/landing-page-floating-footer";
import { queryClient } from "@/queries/client";
import { redirect } from "react-router";
import { authQueryOptions } from "@/queries/auth";

export async function loader() {
  const authTokenQuery = queryOptions(authQueryOptions());
  const token = await queryClient.fetchQuery(authTokenQuery);
  if (token && window.innerWidth <= 768) {
    return redirect("/app");
  }
}

export default function Home() {
  const shieldRef = useRef<HTMLDivElement>(null);
  const scrollToShield = () => {
    if (shieldRef.current) {
      shieldRef.current.scrollIntoView({
        behavior: "smooth",
        block: "end",
      });
    }
  };

  return (
    <>
      <Head
        title="Invest in Bonds - High Returns"
        ogUrl="https://stablebonds.in"
      />
      <Cover />
      <HighReturns />
      <div ref={shieldRef}>
        <Shield />
      </div>
      <div className="mx-auto max-w-[1366px]">
        <KnowMore />
      </div>
      <Testimonial />
      <BackedByBest />
      <div className="mx-auto px-5 md:w-[210px]">
        <Button onClick={scrollToShield}>View all bonds</Button>
      </div>
      <FaqSupport />
      <div className="px-5 sm:hidden">
        <YieldMaturityCalculator />
      </div>
      <Footer />
      <div className="sm:hidden">
        <LandingPageFloatingFooter
          children={
            <Button href="/authentication/mobile-number">Get started </Button>
          }
        />
      </div>
    </>
  );
}
