import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import Button from "@/components/ui/button/button";
import Surface from "@/components/ui/surface/surface";

export default function FormsPage() {
  return (
    <>
      <p className="text-heading1 text-black-80 mb-2 font-medium underline underline-offset-8">
        Forms
      </p>
      <br />
      <Surface elevation="md">
        <Accordion defaultValue={["forms"]}>
          <AccordionItem
            id="forms"
            label={
              <p className="text-heading4">Download following forms here</p>
            }
          >
            <div className="mt-4 flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <p>Account Closure Request Form</p>
                <Button
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/Account-Closure.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Account Opening Form</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/AOF-Individual-Demat.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Bond of Indemnity</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/Bond-of-indemnity.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p> Account Modification Form</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/Account%20modification%20form.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Dematerialisation Request Form</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/DRF.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Freeze / Unfreeze Request Form</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/Freeze_Unfreeze.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Margin Pledge / Repledge Request Form (MPRF)</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/MPRF.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Pledge Request Form (PRF)</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/PRF-1.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Rejection of pending demat request and account closure</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/Rejection-of-pending-demat-request.pdf"
                >
                  Download now
                </Button>
              </div>
            </div>
          </AccordionItem>
        </Accordion>
      </Surface>
      <br />
      <Surface elevation="md">
        <Accordion defaultValue={["annexures"]}>
          <AccordionItem id="annexures" label={<span>Annexures to AOF</span>}>
            <div className="mt-4 flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <p>Dos and Donts</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/demat-aof/Do's+and+Dont's.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Voluntary Declaration and TnCs</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/demat-aof/Voluntary+Declaration+and+TnCs.docx.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Tariff Sheet</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/demat-aof/Tariff Sheet.docx.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Rights & Obligations</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/demat-aof/Rights+%26+Obligations.docx.pdf"
                >
                  Download now
                </Button>
              </div>

              <div className="flex items-center justify-between">
                <p>Policies and Procedures</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/demat-aof/Policies+%26+Procedures.docx.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Nomination Opt in Form</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/demat-aof/Nomination+Opt+In+Form.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Instructions to fill KYC</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/form/demat-aof/Instructions+to+fill+KYC.docx.pdf"
                >
                  Download now
                </Button>
              </div>
              <div className="flex items-center justify-between">
                <p>Risk Disclosure Document</p>
                <Button
                  className="shrink-0"
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/Risk Disclosure Document.pdf"
                >
                  Download now
                </Button>
              </div>
            </div>
          </AccordionItem>
        </Accordion>
      </Surface>
    </>
  );
}
