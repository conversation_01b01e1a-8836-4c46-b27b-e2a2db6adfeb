import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import Button from "@/components/ui/button/button";
import Head from "@/components/ui/head";
import Surface from "@/components/ui/surface/surface";

export default function ContactPage() {
  return (
    <>
      <Head
        title="Contact Us"
        description="Contact Stable Bonds for any queries or concerns. We're here to help!"
      />

      <p className="text-heading1 text-black-80 mb-2 font-medium underline underline-offset-8">
        Escalation Matrix
      </p>
      <br />
      <Surface elevation="md">
        <Accordion defaultValue={["contact-details"]}>
          <AccordionItem
            id="contact-details"
            label={<p className="text-heading4">Contact Details</p>}
          >
            <div className="mt-4 flex flex-col space-y-4">
              <div className="flex items-center justify-between">
                <p>Contact Details</p>
                <Button
                  size="small"
                  href="https://assets.stablemoney.in/bonds-assets/compliance/escalation-contact.pdf"
                >
                  Download now
                </Button>
              </div>
            </div>
          </AccordionItem>
        </Accordion>
      </Surface>
    </>
  );
}
