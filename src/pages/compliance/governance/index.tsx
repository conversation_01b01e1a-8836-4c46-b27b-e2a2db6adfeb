import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import Button from "@/components/ui/button/button";
import Head from "@/components/ui/head";
import Surface from "@/components/ui/surface/surface";

export default function GovernancePage() {
  return (
    <>
      <Head title="Governance" description="Governance for Stable Bonds" />
      <p className="text-heading1 text-black-80 mb-2 font-medium underline underline-offset-8">
        Governance
      </p>
      <br />
      <Surface elevation="md">
        <Accordion defaultValue={["complaint-status-report"]}>
          <AccordionItem
            id="complaint-status-report"
            label={
              <p className="text-heading4">SEBI complaint status report</p>
            }
          >
            <span slot="description">
              <div className="mt-4 flex flex-col space-y-4">
                <div className="flex items-center justify-between">
                  <p> Complaint Status Report</p>
                  <Button
                    size="small"
                    href="https://assets.stablemoney.in/bonds-assets/compliance/SCORES-SBPL.pdf"
                    className="not-prose"
                  >
                    Download now
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <p>Customer Grievance Tracking Sheet</p>
                  <Button
                    className="shrink-0"
                    size="small"
                    href="https://assets.stablemoney.in/bonds-assets/compliance/Customer_Grievance_Tracking_Sheet.pdf"
                  >
                    Download now
                  </Button>
                </div>
              </div>
            </span>
          </AccordionItem>
        </Accordion>
      </Surface>
    </>
  );
}
