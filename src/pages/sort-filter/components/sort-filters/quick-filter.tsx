import {
  FilterType,
  type FilterItem,
  type FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import Switch from "@/components/ui/switch/switch";
import type { AppliedFilter } from "../../machine";
import CustomSelect from "../select/select";
import { t } from "i18next";

interface QuickFilterProps {
  quickFilter?: FilterItem[];
  handleFilterChange: (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean
  ) => void;
  appliedFilters: AppliedFilter[];
}

interface ItemRendererProps {
  item: FilterItem;
  handleFilterChange: (
    filters: FilterOptionValue,
    isSelected: boolean,
    label: string
  ) => void;
  appliedFilters: AppliedFilter[];
}
const ItemRenderer = ({
  item,
  handleFilterChange,
  appliedFilters,
}: ItemRendererProps) => {
  switch (item.type) {
    case FilterType.MULTI_SELECT_PILLS:
    case FilterType.RANGE_MULTI_SELECT:
    case FilterType.MULTI_SELECT:
    case FilterType.MULTI_SELECT_WITH_ICON:
      return (
        <CustomSelect
          options={item.options.map((option) => ({
            label: option.label,
            value: option.label,
          }))}
          placeholder={t(item.shortLabel)}
          onChange={(index, isSelected) => {
            handleFilterChange(
              item.options[index].optionValue!,
              isSelected,
              item.options[index].label
            );
          }}
          appliedFilters={appliedFilters}
        />
      );

    case FilterType.BOOLEAN_SELECT:
      return (
        <div className="border-black-10 flex items-center gap-2 rounded-sm border px-[10px] py-[6px]">
          {t(item.shortLabel)}
          <Switch
            name={item.key}
            defaultChecked={item.options[0]?.isSelected || false}
            density="compact"
          />
        </div>
      );
    default:
      return null;
  }
};

const QuickFilter = ({
  quickFilter,
  handleFilterChange,
  appliedFilters,
}: QuickFilterProps) => {
  return (
    <div className="quick-filter hide-scrollbar flex gap-3 overflow-x-scroll">
      {quickFilter?.map((item, index) => {
        return (
          <div key={index} className="flex w-fit shrink-0 flex-col">
            <ItemRenderer
              item={item}
              handleFilterChange={(filters, isSelected, label) => {
                handleFilterChange(
                  {
                    key: item.key,
                    filters,
                    label: label,
                  },
                  isSelected
                );
              }}
              appliedFilters={appliedFilters}
            />
          </div>
        );
      })}
    </div>
  );
};

export default QuickFilter;
