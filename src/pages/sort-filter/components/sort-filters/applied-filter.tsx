import closeIcon from "@/assets/images/icons/close-menu.svg";
import type { AppliedFilter } from "../../machine";

interface AppliedFilterProps {
  appliedFilters: AppliedFilter[];
  handleClearFilters: () => void;
  handleFilterRemoval: (label: string) => void;
}

const AppliedFilters = ({
  appliedFilters,
  handleClearFilters,
  handleFilterRemoval,
}: AppliedFilterProps) => {
  const allLabels = appliedFilters.flatMap((group) =>
    group.filters.map((filter) => filter.label)
  );

  return (
    <div className="flex items-center gap-2.5">
      {allLabels.length > 1 && (
        <>
          <span
            onClick={handleClearFilters}
            className="text-body1 shrink-0 underline [text-decoration-color:#BBB] [text-decoration-style:dotted] [text-decoration-thickness:9.5%] [text-underline-offset:40%] [text-decoration-skip-ink:none] [text-underline-position:from-font]"
          >
            Clear all
          </span>
          <span className="h-8 border-l-[1.5px]" />
        </>
      )}

      <div className="hide-scrollbar flex gap-3 overflow-scroll">
        {allLabels.map((label, index) => (
          <div
            key={index}
            className="border-black-10 bg-black-3 flex shrink-0 items-center gap-1 rounded-sm border-1 px-2.5 py-1.5"
            onClick={() => handleFilterRemoval(label ?? "")}
          >
            <p className="text-body1">{label}</p>
            <img
              src={closeIcon}
              alt=""
              className="h-3 w-3 brightness-0 saturate-100 filter"
            />
          </div>
        ))}
      </div>
    </div>
  );
};

export default AppliedFilters;
