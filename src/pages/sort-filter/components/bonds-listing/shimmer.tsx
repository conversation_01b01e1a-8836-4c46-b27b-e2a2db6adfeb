const BondsCardShimmer = () => {
  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center gap-4">
        <div className="flex items-center gap-3">
          <div className="border-black-10 shimmer flex h-10 w-10 items-center justify-center rounded-sm border-[0.8px] shadow-md"></div>
          <div className="flex flex-col gap-2">
            <div className="shimmer h-4 w-16 rounded"></div>
            <div className="flex items-center gap-1">
              <div className="shimmer h-3 w-20 rounded"></div>
              <span className="bg-black-50 inline-block h-1 w-1 rounded-[1px]"></span>

              <div className="shimmer h-3 w-8 rounded"></div>
            </div>
          </div>
        </div>
        <div className="flex flex-1 items-center justify-end gap-2">
          <div className="shimmer h-4 w-12 rounded"></div>

          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-black-20 block size-4 flex-shrink-0"
          >
            <path
              d="M8 5L15 12L8 19"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default BondsCardShimmer;
