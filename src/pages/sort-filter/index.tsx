import { useMachine } from "@xstate/react";
import { filterSortMachine, type FilterOptionValueWithLabel } from "./machine";

import Button from "@/components/ui/button/button";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import BondsLisiting from "./components/bonds-listing";
import smsFilterIcon from "@/assets/images/icons/sms_filter_icon.svg";
import smsSortIcon from "@/assets/images/icons/sms_sort_icon.svg";
import infoIcon from "@/assets/images/icons/info.svg";
import type { SortType } from "@/clients/gen/broking/FilterSearch_pb";
import QuickFilter from "./components/sort-filters/quick-filter";
import AppliedFilters from "./components/sort-filters/applied-filter";
import SortTray from "./components/sort-filters/sort-tray";
import FilterTray from "./components/sort-filters/filter-tray";
import ErrorPage from "@/components/functional/error-page";
import FloatingFooterContent from "@/components/ui/floating-footer-content";

const FilterSortPage = () => {
  const [state, send] = useMachine(filterSortMachine);
  const { filterConfig, currentPage, totalPages, isLoadingResults, error } =
    state.context;
  const quickFilter = filterConfig?.filter?.items.filter(
    (item) => item.isQuickFilter
  );
  const hasConfigError = state.matches("configError");
  const hasResultsError = state.matches("resultsError");
  const hasError = hasConfigError || hasResultsError;

  const handlePageChange = (page: number) => {
    send({ type: "SET_PAGE", page });
  };

  const handleSort = (sortType: SortType) => {
    send({ type: "UPDATE_SORT", sortType });
  };

  const handleClearFilters = () => {
    send({ type: "CLEAR_FILTERS" });
  };

  const handleFilterRemoval = (label: string) => {
    const currentFilters = state.context.appliedFilters;

    const updatedFilters = currentFilters
      .map((group) => ({
        ...group,
        filters: group.filters.filter((filter) => filter.label !== label),
      }))
      .filter((group) => group.filters.length > 0);
    send({
      type: "APPLY_FILTERS",
      filters: updatedFilters,
    });
  };

  const handleRetryConfig = () => {
    send({ type: "RETRY_LOAD_CONFIG" });
  };

  const handleRetryResults = () => {
    send({ type: "RETRY_LOAD_RESULTS" });
  };

  if (hasError) {
    return (
      <ErrorPage
        error={error}
        title={error ? undefined : "Failed to load bonds"}
        description="Please try again. If the issue persists, contact our support team."
        onRetry={hasConfigError ? handleRetryConfig : handleRetryResults}
      />
    );
  }

  return (
    <>
      <div className="mb-1 flex flex-col pt-0 pr-5 pl-5">
        <div className="flex flex-col gap-3 py-5">
          <QuickFilter
            quickFilter={quickFilter}
            handleFilterChange={(filter, isSelected) => {
              const currentFilters = state.context.appliedFilters;
              (filter.filters as FilterOptionValueWithLabel).label =
                filter.label;
              let updatedFilters;

              if (isSelected) {
                const existingFilterIndex = currentFilters.findIndex(
                  (f) => f.key === filter.key
                );

                if (existingFilterIndex >= 0) {
                  updatedFilters = currentFilters.map((f, index) =>
                    index === existingFilterIndex
                      ? {
                          ...f,
                          filters: [...f.filters, filter.filters],
                        }
                      : f
                  );
                } else {
                  updatedFilters = [
                    ...currentFilters,
                    {
                      key: filter.key,
                      filters: [filter.filters],
                    },
                  ];
                }
              } else {
                updatedFilters = currentFilters
                  .map((f) => {
                    if (f.key === filter.key) {
                      const filteredOptions = f.filters.filter((option) => {
                        return (
                          JSON.stringify(option) !==
                          JSON.stringify(filter.filters)
                        );
                      });
                      return {
                        ...f,
                        filters: filteredOptions,
                      };
                    }
                    return f;
                  })
                  .filter((f) => f.filters.length > 0);
              }

              send({
                type: "APPLY_FILTERS",
                filters: updatedFilters,
              });
            }}
            appliedFilters={state.context.appliedFilters}
          />
          {state.context.appliedFilters.length > 0 && (
            <AppliedFilters
              appliedFilters={state.context.appliedFilters}
              handleClearFilters={handleClearFilters}
              handleFilterRemoval={handleFilterRemoval}
            />
          )}
        </div>
      </div>
      <div className="sticky top-3 flex justify-between bg-white px-5">
        <p className="text-body2 font-medium">
          {state.context.totalResults} results
        </p>
        <div className="text-body2 flex gap-1 font-medium underline [text-decoration-color:#BBB] [text-decoration-style:dotted] [text-decoration-thickness:10.5%] [text-underline-offset:40%] [text-decoration-skip-ink:none] [text-underline-position:from-font]">
          Yield to maturity
          <img src={infoIcon} />
        </div>
      </div>

      <BondsLisiting
        listData={state.context.bonds}
        onPageChange={handlePageChange}
        currentPage={currentPage}
        totalPages={totalPages}
        isLoading={isLoadingResults}
        isFilterLoading={isLoadingResults && state.context.currentPage === 0}
        suggestedFilters={state.context.suggestedFilters}
      />
      <FloatingFooterContent>
        <div className="flex gap-3">
          <AdaptiveModal
            trigger={
              <Button className="shrink-1">
                <span className="flex gap-1">
                  <img src={smsSortIcon} alt="sort" />
                  <p className="text-body2">SORT BY</p>
                </span>
              </Button>
            }
            size="small"
            href="/sheets/mandatory-documents"
          >
            {({ dismiss }) => (
              <SortTray
                sortConfig={filterConfig?.sort}
                handleSort={(sortType) => {
                  handleSort(sortType);
                }}
                currentSort={state.context.currentSort}
                toggleSortPanel={dismiss}
              />
            )}
          </AdaptiveModal>
          <AdaptiveModal
            trigger={
              <Button className="shrink-1">
                <span className="flex items-center gap-1">
                  <img src={smsFilterIcon} alt="filter" />
                  <p className="text-body2">FILTERS </p>
                  {state.context.appliedFilters.length > 0 && (
                    <span className="bg-green h-1 w-1"></span>
                  )}
                </span>
              </Button>
            }
            size="small"
            href="/sheets/mandatory-documents"
          >
            {({ dismiss }) => (
              <FilterTray
                filterConfig={filterConfig?.filter}
                totalResults={state.context.totalResults}
                toggleFilterPanel={dismiss}
                appliedFilters={state.context.appliedFilters}
                handleFilterChange={(filter, isSelected) => {
                  const currentFilters = state.context.appliedFilters;
                  (filter.filters as FilterOptionValueWithLabel).label =
                    filter.label;
                  let updatedFilters;

                  if (isSelected) {
                    const existingFilterIndex = currentFilters.findIndex(
                      (f) => f.key === filter.key
                    );

                    if (existingFilterIndex >= 0) {
                      updatedFilters = currentFilters.map((f, index) =>
                        index === existingFilterIndex
                          ? {
                              ...f,
                              filters: [...f.filters, filter.filters],
                            }
                          : f
                      );
                    } else {
                      updatedFilters = [
                        ...currentFilters,
                        {
                          key: filter.key,
                          filters: [filter.filters],
                        },
                      ];
                    }
                  } else {
                    updatedFilters = currentFilters
                      .map((f) => {
                        if (f.key === filter.key) {
                          const filteredOptions = f.filters.filter((option) => {
                            return (
                              JSON.stringify(option) !==
                              JSON.stringify(filter.filters)
                            );
                          });
                          return {
                            ...f,
                            filters: filteredOptions,
                          };
                        }
                        return f;
                      })
                      .filter((f) => f.filters.length > 0);
                  }

                  send({
                    type: "APPLY_FILTERS",
                    filters: updatedFilters,
                  });
                }}
                handleClearFilters={handleClearFilters}
                isLoading={isLoadingResults && state.context.currentPage === 0}
              />
            )}
          </AdaptiveModal>
        </div>
      </FloatingFooterContent>
    </>
  );
};

export default FilterSortPage;
