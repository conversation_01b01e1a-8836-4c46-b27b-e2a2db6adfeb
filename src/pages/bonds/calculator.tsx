import { useState, useMemo } from "react";
import { use<PERSON><PERSON><PERSON>, type LoaderFunctionArgs } from "react-router";
import {
  useQuery,
  useQueryClient,
  useSuspenseQuery,
} from "@tanstack/react-query";

import AppBar from "@/components/ui/app-bar/app-bar";
import Panel from "@/components/ui/panel/panel";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import Button from "@/components/ui/button/button";
import UnitSelector from "@/components/ui/unit-selector/unit-selector";
import PurchaseButton from "@/components/bonds/purchase-button";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import SupportAppbarAction from "@/components/functional/support-appbar-action";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import OrderLimitMessage from "@/components/bonds/order-limit-message";
import BondsReturnSchedule from "@/components/bonds/bonds-return-schedule";

import {
  createBondDetailsQueryOptions,
  getBondCalculationQueryOptions,
} from "@/queries/bonds";
import { formatToInr } from "@/utils/number";
import Head from "@/components/ui/head";
import { queryClient } from "@/queries/client";

export async function loader({ params }: LoaderFunctionArgs) {
  const bondId = params.bond_id;
  if (!bondId) throw new Error("Bond ID is required");

  await queryClient.ensureQueryData(createBondDetailsQueryOptions(bondId));
}

export default function BondCalculatorPage() {
  const params = useParams();
  const bondISIN = params.bond_id!;

  const { data: bondDetails } = useSuspenseQuery(
    createBondDetailsQueryOptions(bondISIN)
  );

  const queryClient = useQueryClient();
  const { data: value } = useQuery({
    queryKey: [`${bondISIN}-calculation-value`],
    queryFn: () => bondDetails.defaultQuantity, // default value
    staleTime: 2 * 60 * 1000,
  });

  const [quantity, setQuantity] = useState(1);
  // Update quantity when bond details load
  useMemo(() => {
    if (bondDetails?.defaultQuantity) {
      setQuantity(value ?? bondDetails.defaultQuantity);
    }
  }, [bondDetails?.defaultQuantity]);

  const calculationQuery = useQuery({
    ...getBondCalculationQueryOptions(bondDetails?.id ?? "", quantity),
    enabled: !!bondDetails?.id && !!quantity,
  });

  return (
    <>
      <Head title={`${bondDetails.aboutTheInstitution?.title} Calculator`} />
      <AppBar left={<BackAppbarAction />} right={<SupportAppbarAction />}>
        <div className="text-center">
          <h1 className="text-black-80 line-clamp-1">
            {bondDetails.aboutTheInstitution?.title}
          </h1>
          <p className="text-body2">
            {bondDetails.xirr}% YTM for {bondDetails.timeLeftToMaturity}
          </p>
        </div>
      </AppBar>

      <p className="text-heading4 text-black-50 mt-10 text-center">Units</p>

      <UnitSelector
        className="mx-auto mb-5"
        value={quantity.toString()}
        min={bondDetails.bondQuantity?.minCount ?? 1}
        max={bondDetails.bondQuantity?.maxCount ?? Infinity}
        onChange={(e) => {
          const parsedValue = parseInt(e.target.value) || 1;
          setQuantity(parsedValue);
          queryClient.setQueryData(
            [`${bondISIN}-calculation-value`],
            parsedValue
          );
        }}
        data-testid="quantity-input"
        isSoldOut={bondDetails.bondQuantity?.availableCount === 0}
      />

      <OrderLimitMessage
        investmentAmount={calculationQuery.data?.totalConsideration ?? 0}
        availableUnits={bondDetails.bondQuantity?.availableCount ?? 0}
        units={quantity}
        pricePerUnit={bondDetails.pricePerBond}
      />

      <div className="px-5">
        <Panel
          footer={
            <div className="text-body1 md:text-body2 mt-4 inline">
              <AdaptiveModal
                trigger={
                  <button className="cursor-pointer space-x-1">
                    <span className="border-purple border-b">
                      View all payouts
                    </span>
                    <svg
                      width="10"
                      height="10"
                      viewBox="0 0 10 10"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="inline-block align-middle"
                    >
                      <path
                        d="M9.1 7.58009L9.1 7.68009L9 7.68009L8.04968 7.68009L7.94933 7.68009L7.94968 7.57974L7.96611 2.85303L1.76193 9.07063L1.69203 9.14068L1.62126 9.07152L0.930113 8.3962L0.857932 8.32567L0.929117 8.25414L7.11794 2.03507L2.4514 2.03507L2.3514 2.03507L2.3514 1.93507L2.3514 1L2.3514 0.900001L2.4514 0.900001L9 0.900001L9.1 0.900001L9.1 1L9.1 7.58009Z"
                        fill="currentColor"
                        stroke="currentColor"
                        strokeWidth="0.2"
                      />
                    </svg>
                  </button>
                }
                href={`/bonds/${params.issuer}/${params.bond_id}/returns-schedule?units=${quantity}`}
              >
                {() => <BondsReturnSchedule quantity={quantity} />}
              </AdaptiveModal>
            </div>
          }
        >
          <Accordion className="px-5" multiple>
            <AccordionItem
              label={
                <div className="flex w-full justify-between">
                  <span>Investment amount</span>
                  <span>
                    {calculationQuery.data
                      ? formatToInr(calculationQuery.data.totalConsideration)
                      : ""}
                  </span>
                </div>
              }
            >
              <div className="flex justify-between">
                <span className="text-black-50">Principal</span>
                {calculationQuery.data && (
                  <span data-testid="principal-amount">
                    {formatToInr(calculationQuery.data.purchaseAmount)}
                  </span>
                )}
              </div>
              <div className="flex justify-between">
                <span className="text-black-50">Accrued interest</span>
                {calculationQuery.data && (
                  <span data-testid="accrued-interest-amount">
                    {formatToInr(calculationQuery.data.accruedInterest)}
                  </span>
                )}
              </div>
              {calculationQuery.data && calculationQuery.data.stampDuty > 0 && (
                <div className="flex justify-between">
                  <span className="text-black-50">Stamp duty</span>
                  <span data-testid="stamp-duty-amount">
                    {formatToInr(calculationQuery.data.stampDuty)}
                  </span>
                </div>
              )}
            </AccordionItem>
            <AccordionItem
              label={
                <div className="flex w-full justify-between">
                  <span>Total returns</span>
                  <span>
                    {calculationQuery.data &&
                    calculationQuery.data.maturityAmount > 0 ? (
                      <span className="text-green">
                        {formatToInr(calculationQuery.data.maturityAmount)}
                      </span>
                    ) : (
                      ""
                    )}
                  </span>
                </div>
              }
            >
              <div className="flex justify-between">
                <span className="text-black-50">Total gains</span>
                {calculationQuery.data && (
                  <span data-testid="total-gains-amount">
                    {formatToInr(
                      calculationQuery.data.maturityAmount -
                        calculationQuery.data.totalConsideration
                    )}
                  </span>
                )}
              </div>
              <div className="flex justify-between">
                <span className="text-black-50">Investment amount</span>
                {calculationQuery.data && (
                  <span data-testid="total-investment-amount">
                    {formatToInr(calculationQuery.data.totalConsideration)}
                  </span>
                )}
              </div>
            </AccordionItem>
          </Accordion>
        </Panel>

        <FloatingFooterContent>
          {bondDetails.bondQuantity?.availableCount === 0 ? (
            <Button disabled>Sold out</Button>
          ) : (
            <PurchaseButton bondDetailId={bondDetails.id} quantity={quantity} />
          )}
        </FloatingFooterContent>
      </div>
    </>
  );
}
