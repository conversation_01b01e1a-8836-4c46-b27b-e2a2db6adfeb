import { useLocation, Navigate } from "react-router-dom";
import { rewritePath } from "@/utils/routing";

export default function CatchallPage() {
  const location = useLocation();
  const currentPath = location.pathname;
  const rewrittenPath = rewritePath(currentPath);

  // If the path was rewritten (changed), redirect to the new path
  if (rewrittenPath !== currentPath) {
    return <Navigate to={rewrittenPath} replace />;
  }

  throw new Error("Page not found");
}
