import {
  use<PERSON>ara<PERSON>,
  useSearch<PERSON>arams,
  type LoaderFunctionArgs,
} from "react-router";
import { useSuspenseQuery } from "@tanstack/react-query";
import AppBar from "@/components/ui/app-bar/app-bar";
import Surface from "@/components/ui/surface/surface";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import Tag from "@/components/ui/tag/tag";
import Button from "@/components/ui/button/button";
import CollectionItem from "@/components/bonds/collection-item";
import IndexedCollectionItem from "@/components/bonds/indexed-collection-item";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import Anchor from "@/components/functional/anchor";
import RenderBond from "@/components/collections/render-bond";
import { createCollectionQueryOptions } from "@/queries/bonds";
import { makeBondUrl } from "@/utils/routing";
import type { ScrollStyleParams } from "@/hooks/scroll-styles";
import Head from "@/components/ui/head";
import { queryClient } from "@/queries/client";

export async function loader({ params }: LoaderFunctionArgs) {
  const collection = params.collection;
  if (!collection) throw new Error("Collection is required");

  await queryClient.ensureQueryData(createCollectionQueryOptions(collection));
}

export default function CollectionPage() {
  const params = useParams();
  const [searchParams] = useSearchParams();

  const collection = params.collection!;
  const displayType = searchParams.get("displayType");
  const cardType = searchParams.get("cardType");

  const scrollStyleOptions: ScrollStyleParams = {
    offsetStyles: {
      backgroundColor: {
        threshold: 20,
        before: "transparent",
        after: "var(--color-bg)",
      },
      boxShadow: {
        threshold: 20,
        before: "none",
        after: "0 2px 4px rgba(0, 0, 0, 0.1)",
      },
    },
  };

  const { data: collectionData } = useSuspenseQuery(
    createCollectionQueryOptions(collection)
  );

  if (displayType === "collection") {
    return (
      <>
        <Head title={collectionData.title} />
        <AppBar
          scrollStyleOptions={scrollStyleOptions}
          left={<BackAppbarAction />}
        />
        <div className="p-5">
          <p className="text-black-80 text-heading1">{collectionData.title}</p>
          <p className="text-black-40 text-body1">
            {collectionData.description}
          </p>
        </div>
        <div className="grid grid-cols-2 gap-4 px-5 py-5">
          {collectionData.collectionItem.map((item, index) => (
            <div key={item.id} className="contents">
              {cardType === "indexedCard" ? (
                <IndexedCollectionItem item={item} rank={index} />
              ) : (
                <CollectionItem
                  item={item}
                  collectionName={collection}
                  rank={index}
                />
              )}
            </div>
          ))}
        </div>
        <div className="h-10"></div>
      </>
    );
  } else if (displayType === "userBased") {
    return (
      <>
        <Head title={collectionData.title} />
        <AppBar
          scrollStyleOptions={scrollStyleOptions}
          left={<BackAppbarAction />}
        />
        <div className="body-behind-appbar relative">
          <img src={collectionData.iconUrl} alt={collectionData.title} />

          <div className="flex flex-col gap-5 px-5 pt-8 pb-4">
            {collectionData.collectionItem.map((item) => (
              <Anchor key={item.id} href={makeBondUrl(item)}>
                <div className="relative">
                  <Surface elevation="md">
                    <div className="flex flex-col">
                      <div className="flex justify-between p-4 pb-0">
                        <RenderBond
                          item={item}
                          displayType={collectionData.displayType}
                        />
                        <div className="flex flex-col">
                          <Button size="small">Invest now</Button>
                        </div>
                      </div>
                      <hr className="bg-black-10 mt-6 mb-3 ml-4 h-[0.5px] border-none" />

                      <div className="flex items-center gap-2 p-4 pt-0">
                        <EntityLogo
                          url={item.aboutTheInstitution?.logo ?? ""}
                          size="small"
                          color="#FFFF"
                          elevation="md"
                        />

                        <p className="text-black-50 text-body1">
                          {item.aboutTheInstitution?.title}
                        </p>
                      </div>
                    </div>
                  </Surface>
                  <div className="absolute -top-[8px] left-4">
                    {item.tagConfig?.name && (
                      <>
                        {item.tagConfig?.type === "tag" ? (
                          <Tag
                            color={item.tagConfig.color}
                            backgroundColor={item.tagConfig.bgColor}
                            shimmerColor="#916CFF"
                            borderColor="#916CFF33"
                            hasShimmer
                            leading={
                              item.tagConfig?.iconUrl ? (
                                <span>
                                  <img
                                    src={item.tagConfig!.iconUrl}
                                    alt="Tag Icon"
                                    className="mr-1 w-2"
                                    crossOrigin="anonymous"
                                  />
                                </span>
                              ) : undefined
                            }
                          >
                            <span>{item.tagConfig.name}</span>
                          </Tag>
                        ) : (
                          <div className="mt-1 flex items-center">
                            {item.tagConfig?.iconUrl && (
                              <img
                                src={item.tagConfig.iconUrl}
                                alt="Tag Icon"
                                className="mr-1 w-2"
                                crossOrigin="anonymous"
                              />
                            )}
                            <span className="text-body2 text-black-50 line-clamp-1 text-ellipsis">
                              {item.tagConfig.name}
                            </span>
                          </div>
                        )}
                      </>
                    )}
                  </div>
                </div>
              </Anchor>
            ))}
          </div>
          <img
            src="https://assets.stablemoney.in/app/bond_secure_banner.webp"
            className="px-15 pt-10"
            alt="Bond Secure Banner"
          />
        </div>
      </>
    );
  }

  return null;
}
