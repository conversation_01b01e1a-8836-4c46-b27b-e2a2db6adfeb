import FormPage from "@/components/bonds/form-page";
import Anchor from "@/components/functional/anchor";
import Button from "@/components/ui/button/button";
import Input from "@/components/ui/input/input";
import { Formik, Field } from "formik";
import { initiateAuth } from "@/queries/auth";
import { aesGcmEncrypt } from "@/utils/browser-encryption";
import useNavigate from "@/hooks/navigate";
import { getConfig } from "@/queries/identity";
import { AppConfigType } from "@/clients/gen/platform/public/models/identity/Common_pb";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import { withErrorToast } from "@/utils/errors";
import Form from "@/components/functional/form";

export default function MobileNumberPage() {
  const navigate = useNavigate();
  return (
    <>
      <Head title="Mobile Number Verification" />
      <Formik
        initialValues={{} as FormValues}
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(values) {
          const config = await getConfig();
          const publicKey =
            config.data
              .find(
                (config) =>
                  config.configName === AppConfigType.ENCRYPTION_PUBLIC_KEY
              )
              ?.configValue.replace(/-----.*?-----/g, "")
              .replace(/[\n\r\s]/g, "")
              .trim() ?? "";
          const [encryptedMobile, aesKey] = await aesGcmEncrypt(
            values.mobileNumber,
            publicKey
          );
          const { challenge, userId } = await initiateAuth(
            values.mobileNumber,
            encryptedMobile,
            aesKey
          );
          return navigate(
            `/authentication/mobile-otp?userId=${userId}&challenge=${challenge.challengeId}&mobileNumber=${values.mobileNumber}`,
            { replace: true }
          );
        })}
      >
        {({ isSubmitting, errors, submitCount }) => {
          return (
            <Form id="mobile-form">
              <FormPage
                title="What's your mobile number?"
                description="Please enter your Aadhaar linked mobile number"
                footer={
                  <>
                    <p className="text-body1 text-black-50 mb-3 text-center">
                      By proceeding, you agree to
                      <Anchor
                        href="/compliance/term-and-condition"
                        className="text-black-80"
                      >
                        {" "}
                        T&C
                      </Anchor>
                      &nbsp;&amp;&nbsp;
                      <Anchor
                        href="/compliance/privacy-policy"
                        className="text-black-80"
                      >
                        Privacy Policy
                      </Anchor>
                    </p>
                    <Button
                      type="submit"
                      form="mobile-form"
                      loading={isSubmitting}
                    >
                      Verify mobile number
                    </Button>
                  </>
                }
              >
                <Field
                  name="mobileNumber"
                  label="Mobile number"
                  as={Input}
                  mask="mobile_number"
                  error={submitCount > 0 ? errors.mobileNumber : undefined}
                />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
