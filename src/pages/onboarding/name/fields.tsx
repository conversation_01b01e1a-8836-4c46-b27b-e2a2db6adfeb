import Input from "@/components/ui/input/input";
import { Field, type FormikProps } from "formik";
import { type FormValues } from "./schema";

export default function FormFields({
  errors,
  submitCount,
}: FormikProps<FormValues>) {
  return (
    <>
      <Field
        name="firstName"
        label="First name"
        as={Input}
        error={submitCount > 0 ? errors.firstName : undefined}
        spellCheck="false"
      />

      <Field
        name="lastName"
        label="Last name"
        as={Input}
        error={submitCount > 0 ? errors.lastName : undefined}
        spellCheck="false"
      />
    </>
  );
}
