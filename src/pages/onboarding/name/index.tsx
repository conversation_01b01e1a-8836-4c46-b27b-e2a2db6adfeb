import FormPage from "@/components/bonds/form-page";
import <PERSON>ton from "@/components/ui/button/button";
import { Formik } from "formik";
import { trackEvent } from "@/utils/analytics";
import { withErrorToast } from "@/utils/errors";
import useNavigate from "@/hooks/navigate";
import { postName, getOnboardingStatus } from "@/queries/identity";
import { getPathForNextAlphaStep } from "@/utils/onboarding-routes";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";
import { getHomeUrl } from "@/utils/routing";

export default function NamePage() {
  const navigate = useNavigate();

  return (
    <>
      <Head title="Enter Your Name" />
      <Formik
        initialValues={
          {
            firstName: "",
            lastName: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(values) {
          await postName({
            firstName: values.firstName,
            lastName: values.lastName,
          });

          trackEvent("name_submitted", {
            firstName: values.firstName,
            lastName: values.lastName,
          });

          const alphaStatus = await getOnboardingStatus();

          if (alphaStatus.next) {
            navigate(getPathForNextAlphaStep(alphaStatus.next), {
              replace: true,
            });
          } else {
            navigate(getHomeUrl());
          }
        })}
      >
        {(formState) => {
          return (
            <Form id="name-form">
              <FormPage
                title="What's your name?"
                description="Please enter your full name as it appears on your official documents"
                footer={
                  <Button
                    type="submit"
                    form="name-form"
                    loading={formState.isSubmitting}
                  >
                    Continue
                  </Button>
                }
              >
                <FormFields {...formState} />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
