import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { Formik } from "formik";
import { initiateEmailVerification, verifyEmail } from "@/queries/auth";
import { getPathForNextAlphaStep } from "@/utils/onboarding-routes";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";
import { trackEvent } from "@/utils/analytics";
import { useSearchParams } from "react-router";
import useNavigate from "@/hooks/navigate";
import { withErrorToast, getErrorMessage } from "@/utils/errors";
import { HttpCallException } from "@/exceptions/http-call-exception";
import { useEffect } from "react";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";
import { useMachine } from "@xstate/react";
import { countDownTimerMachine } from "@/machines/count-down-timer-machine";
import clsx from "clsx";
import { getHomeUrl } from "@/utils/routing";

export default function EmailOtpPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [state, send] = useMachine(countDownTimerMachine, {
    input: { duration: 30 },
  });
  const challengeId = searchParams.get("challenge");
  const email = searchParams.get("email") ?? "";

  useEffect(() => {
    send({ type: "START", duration: 30 });
  }, [send]);

  const resendOtp = async () => {
    if (state.matches("idle") || state.matches("finished")) {
      send({ type: "START", duration: 30 });
      await withErrorToast(() => initiateEmailVerification(email))();
    }
  };

  useEffect(() => {
    if (!challengeId || !email) {
      navigate("/onboarding/email");
    }
  }, [challengeId, email, navigate]);

  if (!challengeId || !email) {
    return null;
  }

  return (
    <>
      <Head title="Email OTP Verification" />
      <Formik
        initialValues={
          {
            answer: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(
          values,
          { setFieldError }
        ) {
          try {
            const { alphaStatus } = await verifyEmail(
              challengeId,
              values.answer
            );

            if (alphaStatus.next) {
              trackEvent("email_otp_verified", { email });
              if (
                alphaStatus.next === KycType.NAME ||
                alphaStatus.next === KycType.EMAIL
              ) {
                navigate(getPathForNextAlphaStep(alphaStatus.next), {
                  replace: true,
                });
              } else {
                // For unknown or unsupported steps, go to home
                navigate(getHomeUrl());
              }
            } else {
              navigate(getHomeUrl());
            }
          } catch (error) {
            if (
              error instanceof HttpCallException &&
              error.response.status === 400
            ) {
              setFieldError("answer", await getErrorMessage(error));
            } else {
              throw error;
            }
          }
        })}
      >
        {(formState) => {
          return (
            <Form id="email-otp-form">
              <FormPage
                title="OTP please?"
                description={`We have sent it to ${email}`}
                footer={
                  <Button
                    type="submit"
                    form="email-otp-form"
                    loading={formState.isSubmitting}
                  >
                    Submit OTP
                  </Button>
                }
              >
                <FormFields {...formState} />
                <div className="flex gap-1">
                  <button
                    onClick={resendOtp}
                    disabled={state.matches("running")}
                    className={clsx(
                      "text-body1 link-button inline-flex cursor-default items-center justify-center gap-0.5 underline underline-offset-4",
                      {
                        "text-black-50": state.matches("running"),
                        "text-purple cursor-pointer": !state.matches("running"),
                      }
                    )}
                    type="button"
                  >
                    {state.matches("running") ? "Resend in" : "Resend OTP"}
                  </button>
                  {state.matches("running") && (
                    <span className="text-body1">
                      {Math.floor(state.context.current / 60)
                        .toString()
                        .padStart(2, "0")}
                      :
                      {(state.context.current % 60).toString().padStart(2, "0")}
                    </span>
                  )}
                </div>
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
