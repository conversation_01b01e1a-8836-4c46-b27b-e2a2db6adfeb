import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { Formik } from "formik";
import { initiateEmailVerification } from "@/queries/auth";
import useNavigate from "@/hooks/navigate";
import { withErrorToast, getErrorMessage } from "@/utils/errors";
import { HttpCallException } from "@/exceptions/http-call-exception";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";

export default function EmailPage() {
  const navigate = useNavigate();

  return (
    <>
      <Head title="Email Verification" />
      <Formik
        initialValues={
          {
            email: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(
          values,
          { setFieldError }
        ) {
          try {
            const { challenge } = await initiateEmailVerification(values.email);
            navigate(
              `/authentication/email-otp?challenge=${challenge.challengeId}&email=${values.email}`
            );
          } catch (error) {
            if (
              error instanceof HttpCallException &&
              error.response.status === 400
            ) {
              setFieldError("email", await getErrorMessage(error));
            } else {
              throw error;
            }
          }
        })}
      >
        {(formState) => {
          return (
            <Form id="email-form">
              <FormPage
                title="What's your email?"
                description="Your Bonds receipt will be sent directly to your email"
                footer={
                  <Button
                    type="submit"
                    form="email-form"
                    loading={formState.isSubmitting}
                  >
                    Verify email
                  </Button>
                }
              >
                <FormFields {...formState} />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
