import { useParams, useSearchParams } from "react-router";
import FindDematByBrokerPage from "./components/find-demat-by-broker-page";
import DontHaveDemat from "./components/dont-have-demat";
import BondsTooltipPage from "./components/bonds-tool-tip";
import FirstTimeInvestor from "./components/first-time-investor";
import MandatoryDocuments from "./components/mandatory-documents";

export default function BottomSheetPage() {
  const [searchParams] = useSearchParams();
  const params = useParams();
  const bottomsheetType = params.id;

  if (bottomsheetType === "mandatory-documents") {
    return <MandatoryDocuments />;
  } else if (bottomsheetType === "find-demat-by-broker") {
    return <FindDematByBrokerPage />;
  } else if (bottomsheetType === "dont-have-demat") {
    return <DontHaveDemat />;
  } else if (bottomsheetType === "info") {
    return (
      <BondsTooltipPage
        question={searchParams.get("question")!}
        answer={searchParams.get("answer")!}
      />
    );
  } else if (bottomsheetType === "first-time-investor") {
    return <FirstTimeInvestor />;
  }
}
