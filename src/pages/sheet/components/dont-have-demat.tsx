import Button from "@/components/ui/button/button";
import { trackEvent } from "@/utils/analytics";
import { useViewEvent } from "@/hooks/view-event";

export default function DontHaveDemat() {
  const { ref } = useViewEvent({
    eventName: "dont_have_demat",
  });

  return (
    <div ref={ref} className="space-y-5 p-10">
      <p className="text-heading3 text-black-80 text-center">
        A demat account is mandatory to purchase bonds. You can create one with
        any of these apps:
      </p>
      <img
        src="https://assets.stablemoney.in/app/demat-provider.webp"
        alt="Demat Providers"
      />
      <Button
        href="/onboarding/demat"
        onClick={() => {
          trackEvent("dont_have_demat_understood_clicked");
        }}
      >
        Understood
      </Button>
    </div>
  );
}
