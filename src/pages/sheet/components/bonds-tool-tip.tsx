import useDataKey from "@/hooks/data-key";

export default function BondsTooltipPage({
  question,
  answer,
}: {
  question: string;
  answer: string;
}) {
  const questionText = useDataKey({ key: question });
  const answerText = useDataKey({ key: answer });

  return (
    <div className="flex h-full flex-col gap-8 p-5">
      <div className="flex flex-col gap-4">
        <p className="text-heading3 text-black-80 font-medium">
          {" "}
          {questionText}
        </p>
        <p className="text-heading4 text-black-60">{answerText}</p>
      </div>
    </div>
  );
}
