import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import LinkButton from "@/components/ui/button/link-button";
import { trackEvent } from "@/utils/analytics";
import DontHaveDemat from "./dont-have-demat";

const items = [
  {
    logoUrl: "https://assets.stablemoney.in/app/demat-providers/zerodha.webp",
    path: "https://support.zerodha.com/category/your-zerodha-account/dp-id-and-bank-details/dp-id-bo-id/articles/dp-name-dp-id-bo-id-and-demat-id",
    dematName: "Zerodha",
  },
  {
    logoUrl: "https://assets.stablemoney.in/app/demat-providers/groww.webp",
    path: "https://groww.in/help/stocks/sx-demat-account/where-can-i-see-my-demat-account-number",
    dematName: "Groww",
  },
  {
    logoUrl: "https://assets.stablemoney.in/app/demat-providers/angel_one.webp",
    path: "https://www.angelone.in/knowledge-center/demat-account/how-to-know-your-demat-account-number",
    dematName: "Angel One",
  },
  {
    logoUrl: "https://assets.stablemoney.in/app/demat-providers/upstox.webp",
    path: "https://help.upstox.com/support/solutions/articles/250104-how-to-check-demat-details-#:~:text=Step%202%3A%20Click%20on%20'Account,number%20and%20your%20plan%20details",
    dematName: "Upstox",
  },
  {
    logoUrl:
      "https://assets.stablemoney.in/app/demat-providers/icici_securities.webp",
    path: "https://www.chittorgarh.com/faq_pg/what-is-dp-id-and-client-id-in-demat-account-in-icici-direct/1993/",
    dematName: "ICICI Securities",
  },
  {
    logoUrl: "https://assets.stablemoney.in/app/demat-providers/kodak.webp",
    path: "https://www.kotaksecurities.com/support/where-can-i-view-my-demat-profile-details/?platform=neoMobile",
    dematName: "Kotak Securities",
  },
  {
    logoUrl:
      "https://assets.stablemoney.in/app/demat-providers/hdfc_securities.webp",
    path: "https://support.hdfcsky.com/portal/en/kb/articles/how-do-i-check-my-demat-account-number-on-the-hdfc-sky-app#:~:text=After%20logging%20in%2C%20tap%20on,Number%20(BO%20ID)%20here.",
    dematName: "HDFC Securities",
  },
  {
    logoUrl:
      "https://assets.stablemoney.in/app/demat-providers/paytm_money.webp",
    path: "https://www.paytmmoney.com/stocks/customer/support/trading-and-demat/demat-account-with-paytmmoney/what-is-a-demat-account#:~:text=You%20can%20check%20your%20demat,Account%20section%20in%20app%2Fweb.",
    dematName: "Paytm Money",
  },
];

export default function FindDematByBrokerPage() {
  return (
    <div className="flex flex-col justify-center gap-7 p-5">
      <p className="text-heading2 text-black-80">Choose your broker</p>
      <div className="grid grid-cols-4 grid-rows-2 gap-4">
        {items.map((item) => (
          <a href={item.path} target="_blank" rel="noreferrer">
            <div className="flex flex-col items-center justify-center rounded-lg">
              <img src={item.logoUrl} alt="" className="h-14 w-14" />
              <p className="text-body1 text-black-50 mt-2 text-center">
                {item.dematName}
              </p>
            </div>
          </a>
        ))}
      </div>

      <div className="text-center">
        <AdaptiveModal
          trigger={
            <LinkButton
              onClick={() => {
                trackEvent("dont_have_demat_clicked");
              }}
            >
              I don't have a demat ID
            </LinkButton>
          }
          href="sheets/dont-have-demat"
          children={() => <DontHaveDemat />}
        />
      </div>
    </div>
  );
}
