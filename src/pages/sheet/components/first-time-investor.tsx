import Button from "@/components/ui/button/button";
import { trackEvent } from "@/utils/analytics";
import { isHotwireNative } from "@/utils/routing";

export default function FirstTimeInvestor() {
  const isTurboNative = isHotwireNative();
  const handleYesClick = () => {
    trackEvent("first_time_investor_yes_clicked");
  };

  const handleNoClick = () => {
    trackEvent("first_time_investor_no_clicked");
  };

  return (
    <div className="flex flex-col gap-4 p-5">
      <p className="text-heading1 text-black-80">
        Before we continue, is this your first time investing in bonds?{" "}
      </p>
      <div className="flex flex-col gap-3 md:absolute md:right-5 md:bottom-5 md:left-5">
        <Button
          onClick={handleYesClick}
          href={
            isTurboNative
              ? "/flutter/passbook_page?initialIndex=1&presentation=REPLACE"
              : "/investments"
          }
          size="medium"
        >
          Yes
        </Button>
        <Button
          onClick={handleNoClick}
          href={
            isTurboNative
              ? "/flutter/passbook_page?initialIndex=1&presentation=REPLACE"
              : "/investments"
          }
          size="medium"
        >
          No
        </Button>
      </div>
    </div>
  );
}
