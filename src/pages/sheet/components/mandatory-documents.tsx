import Anchor from "@/components/functional/anchor";
import SquareLink from "@/components/icons/square-link";

export default function MandatoryDocuments() {
  return (
    <div className="flex flex-col p-5">
      <h2 className="text-heading1 text-left">
        Mandatory and <br />
        important documents
      </h2>
      <div className="mt-6 flex flex-col gap-3">
        <Anchor
          href="/ckyc-instruction"
          className="flex items-center"
          target="_blank"
          rel="noreferrer"
        >
          Instruction to account opening form
          <SquareLink />
        </Anchor>
        <hr className="text-black-10" />
        <a
          href="https://www.sebi.gov.in/sebi_data/commondocs/ann4rights_p.pdf"
          className="flex items-center"
          target="_blank"
          rel="noreferrer"
        >
          Rights and Obligations
          <SquareLink />
        </a>
        <hr className="text-black-10" />
        <a
          href="https://www.sebi.gov.in/sebi_data/commondocs/ann5risk_p.pdf"
          className="flex items-center"
          target="_blank"
          rel="noreferrer"
        >
          Risk Disclosure Document (RDD)
          <SquareLink />
        </a>
        <hr className="text-black-10" />
        <a
          href="https://www.sebi.gov.in/sebi_data/commondocs/ann6guidance_p.pdf"
          className="flex items-center"
          target="_blank"
          rel="noreferrer"
        >
          Guidance note
          <SquareLink />
        </a>
        <hr className="text-black-10" />
        <a
          href="https://bonds.stablemoney.in/policy-and-procedures"
          className="flex items-center"
          target="_blank"
          rel="noreferrer"
        >
          Policies and Procedures
          <SquareLink />
        </a>
        <hr className="text-black-10" />
        <a
          href="https://assets.stablemoney.in/bonds-assets/tarrif_structure.pdf"
          className="flex items-center"
          target="_blank"
          rel="noreferrer"
        >
          Tariff structure
          <SquareLink />
        </a>
      </div>
    </div>
  );
}
