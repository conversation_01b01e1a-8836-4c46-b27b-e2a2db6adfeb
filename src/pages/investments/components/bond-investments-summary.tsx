import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Surface from "@/components/ui/surface/surface";
import { getBondsNetworthQueryOptions } from "@/queries/bonds";
import { formatCurrency } from "@/utils/format";
import dayjs from "dayjs";

const today = dayjs().format("MMM D, YYYY");

export default function BondInvestmentsSummary() {
  const query = useQuery(getBondsNetworthQueryOptions());

  return (
    <QueryRenderer query={query}>
      {(networth) => (
        <div className="w-full">
          <Surface elevation="md" className="mb-4">
            <div className="m-0.5 flex items-center gap-2 bg-gradient-to-tr from-white to-[#F8FFF3] p-4">
              <div className="flex-1 space-y-1">
                <h4 className="text-heading4">Current value</h4>
                <p className="text-body2 text-black-50">As of {today}</p>
              </div>
              <div className="flex flex-col">
                <h3 className="text-heading3 text-green flex items-center justify-end gap-1 font-medium">
                  <svg
                    width="13"
                    height="8"
                    viewBox="0 0 13 8"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M4.85023 1.70663C5.55419 0.885345 5.90617 0.474704 6.36496 0.474704C6.82374 0.474704 7.17572 0.885345 7.87968 1.70663L10.3992 4.64612C11.6175 6.06742 12.2266 6.77807 11.9596 7.35876C11.6925 7.93945 10.7565 7.93945 8.88452 7.93945H3.84539C1.97342 7.93945 1.03744 7.93945 0.770359 7.35876C0.50328 6.77807 1.11241 6.06742 2.33067 4.64612L4.85023 1.70663Z"
                      fill="currentColor"
                    />
                  </svg>
                  {formatCurrency(
                    networth.totalInvestment + networth.currentGains
                  )}
                </h3>
                <p className="text-body2 text-black-50 text-right">
                  Overall gains {formatCurrency(networth.currentGains)}
                </p>
              </div>
            </div>
          </Surface>
        </div>
      )}
    </QueryRenderer>
  );
}
