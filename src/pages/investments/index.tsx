import { useSuspenseQuery } from "@tanstack/react-query";
import LinkButton from "@/components/ui/button/link-button";
import SecondaryHeader from "@/components/layouts/secondary-header";
import { getInvestmentsQueryOptions } from "@/queries/investments";
import { formatCurrency } from "@/utils/format";
import BondInvestmentsSummary from "./components/bond-investments-summary";
import Head from "@/components/ui/head";
import type { OrderData } from "@/clients/gen/broking/Order_pb";
import { queryClient } from "@/queries/client";

export async function loader() {
  await queryClient.ensureQueryData(getInvestmentsQueryOptions());
}

export default function InvestmentsPage() {
  const { data: investmentData } = useSuspenseQuery(
    getInvestmentsQueryOptions()
  );

  return (
    <>
      <Head
        title="Your Bond Investments"
        description="View and manage your bond investments with Stable Bonds. Track your portfolio, returns, and investment status."
      />
      <div className="min-h-screen bg-white">
        <SecondaryHeader />
        <div className="container mx-auto my-5 flex max-w-[800px] flex-col items-start space-y-4 px-5">
          <p className="text-heading1">Your Bonds Investment</p>
          <BondInvestmentsSummary />
          {investmentData.orders.map((investment: OrderData) => (
            <div
              key={investment.id}
              data-order-id={investment.id}
              className="border-black-10 flex w-full flex-col gap-5 rounded-2xl border p-5"
            >
              <div className="flex flex-wrap items-center justify-between gap-5">
                <div className="flex items-center gap-2">
                  <img
                    src={investment.bondIssuerLogo}
                    className="border-black-10 mt-1 h-8 w-8 rounded border bg-white p-1"
                    alt={investment.bondIssuerName}
                  />
                  <p className="font-medium md:max-w-[300px]">
                    {investment.bondIssuerName}
                  </p>
                </div>
                <div className="flex gap-2">
                  <div className="flex flex-col">
                    <p className="font-medium">
                      {formatCurrency(investment.totalConsideration)}
                    </p>
                    <p className="text-body2 text-black-40">Invested</p>
                  </div>
                  <div className="flex flex-col">
                    <p className="font-medium">
                      {formatCurrency(investment.expectedReturns)}
                    </p>
                    <p className="text-body2 text-black-40">Expected Returns</p>
                  </div>
                  <div className="flex flex-col">
                    <p className="font-medium">{investment.ytm}%</p>
                    <p className="text-body2 text-black-40">YTM</p>
                  </div>
                  <div className="flex flex-col">
                    <p className="font-medium">{investment.quantity}</p>
                    <p className="text-body2 text-black-40">Unit (s)</p>
                  </div>
                  <div className="flex flex-col">
                    <p className="font-medium">{investment.tenure}</p>
                    <p className="text-body2 text-black-40">Tenure</p>
                  </div>
                </div>
              </div>
              <div className="flex flex-col justify-between gap-4 md:flex-row">
                <div className="flex gap-1.5">
                  <div>
                    {investment.orderPlaced?.status ? (
                      <img
                        src="https://assets.stablemoney.in/app/ic_success_bond.svg"
                        className="mt-1 h-4 w-4"
                        alt="Success"
                      />
                    ) : (
                      <img
                        src="https://assets.stablemoney.in/app/ic_error_bond.svg"
                        className="mt-1 h-4 w-4"
                        alt="Error"
                      />
                    )}
                  </div>
                  <div className="flex flex-col">
                    <p className="font-medium">Order Placed</p>
                    <p className="text-body2 text-black-40">
                      {investment.orderPlaced?.dateTime}
                    </p>
                  </div>
                </div>
                <div className="flex flex-grow flex-col-reverse justify-evenly gap-4 md:flex-row-reverse">
                  <div className="flex gap-1.5">
                    <div>
                      {investment.tradePlaced?.status ? (
                        <img
                          src="https://assets.stablemoney.in/app/ic_success_bond.svg"
                          className="mt-1 h-4 w-4"
                          alt="Success"
                        />
                      ) : (
                        <img
                          src="https://assets.stablemoney.in/app/ic_error_bond.svg"
                          className="mt-1 h-4 w-4"
                          alt="Error"
                        />
                      )}
                    </div>
                    <div className="flex flex-col">
                      <p className="font-medium">Trade Placed</p>
                      <p className="text-body2 text-black-40">
                        {investment.tradePlaced?.status
                          ? investment.tradePlaced.dateTime
                          : "Pending"}
                      </p>
                    </div>
                  </div>
                  <div className="flex gap-1.5">
                    <div>
                      {investment.payment?.status ? (
                        <img
                          src="https://assets.stablemoney.in/app/ic_success_bond.svg"
                          className="mt-1 h-4 w-4"
                          alt="Success"
                        />
                      ) : (
                        <img
                          src="https://assets.stablemoney.in/app/ic_error_bond.svg"
                          className="mt-1 h-4 w-4"
                          alt="Error"
                        />
                      )}
                    </div>
                    <div className="flex flex-col">
                      <p className="font-medium">Payment</p>
                      <p className="text-body2 text-black-40">
                        {investment.payment?.status
                          ? investment.payment.dateTime
                          : "Pending"}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="flex gap-1.5">
                  <div>
                    {investment.settlement?.status ? (
                      <img
                        src="https://assets.stablemoney.in/app/ic_success_bond.svg"
                        className="mt-1 h-4 w-4"
                        alt="Success"
                      />
                    ) : (
                      <img
                        src="https://assets.stablemoney.in/app/ic_error_bond.svg"
                        className="mt-1 h-4 w-4"
                        alt="Error"
                      />
                    )}
                  </div>
                  <div className="flex flex-col">
                    <p className="font-medium">Settlement</p>
                    <p className="text-body2 text-black-40">
                      {investment.settlement?.status
                        ? investment.settlement.dateTime
                        : "Pending"}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex space-x-3">
                {investment.dealSheetUrl && (
                  <LinkButton href={investment.dealSheetUrl}>
                    Deal sheet
                  </LinkButton>
                )}
                {investment.orderReceiptUrl && (
                  <LinkButton href={investment.orderReceiptUrl}>
                    Order Receipt
                  </LinkButton>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}
