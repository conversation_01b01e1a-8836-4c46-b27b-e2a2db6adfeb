interface ChecklistCardProps {
  title: string;
  subtitle: string;
  imgSrc: string;
}

export default function ChecklistCard({
  title,
  subtitle,
  imgSrc,
}: ChecklistCardProps) {
  return (
    <div className="flex w-full shrink-1 flex-col items-center gap-5 rounded-xl border border-gray-200 bg-white p-5 md:w-[357px] md:items-start md:gap-12">
      <img
        decoding="sync"
        src={imgSrc}
        alt={title}
        className="h-12 w-12 object-cover md:h-15 md:w-15"
      />
      <div className="flex flex-col">
        <p className="text-black-80 text-center text-[15px] md:text-left lg:text-[28px]">
          {title}
        </p>
        <p className="text-black-50 text-center text-[13px] md:text-left lg:text-[24px]">
          {subtitle}
        </p>
      </div>
    </div>
  );
}
