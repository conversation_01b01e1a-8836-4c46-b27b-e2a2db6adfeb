import FooterContent from "./footer-content";
import isoLogo from "@/assets/images/logos/iso.webp";
import iso1Logo from "@/assets/images/logos/iso1.webp";
import stablebondsVector from "@/assets/images/logos/stablebonds-vector.webp";
import classes from "./home.module.css";
import clsx from "clsx";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import Accordion from "@/components/ui/accordion/accordion";
import Anchor from "@/components/functional/anchor";

export default function Footer() {
  return (
    <footer className={clsx(classes.footerBackground, "px-5")}>
      <div className="container mx-auto py-[36px] md:py-[78px]">
        {/* <p className={classes.backedByTheBest}>backed by the best</p>
        <img src={investorLogo} alt="Investors" className="h-[54px]" />

        <hr className="my-[100px] border-white opacity-20" /> */}

        <img src={stablebondsVector} />
        <div className="text-body2 mb-[39px] flex flex-col justify-center gap-2 md:mb-[78px] md:flex-row md:justify-normal md:gap-[47px] md:text-[15px]">
          <div className="flex justify-center gap-10 md:gap-[47px]">
            <Anchor href="/compliance/privacy-policy" target="_blank">
              Privacy policy
            </Anchor>
            <Anchor href="/compliance/term-and-condition" target="_blank">
              Terms & conditions
            </Anchor>
            <Anchor href="/contact-us">Contact us</Anchor>
          </div>
        </div>

        <div className="mb-[16px] flex flex-col items-center gap-4 tracking-[2px] sm:mb-[24px] md:mb-[32px] md:flex-row">
          <nav>
            <span className="text-body2 font-medium uppercase opacity-50 md:text-[15px]">
              © {new Date().getFullYear()} Stable Broking Pvt Ltd
            </span>
          </nav>
          <div className="block flex-shrink-0 rounded-lg bg-white px-2 py-1.5">
            <img src={isoLogo} alt="" className="inline-block h-5" />
            <img src={iso1Logo} alt="" className="inline-block h-5" />
            <span className="text-black-80 text-body1 ml-2 font-medium">
              ISO 27001:2022
            </span>
          </div>
        </div>
        <div className="text-body2 space-y-[32px] px-[32px] text-center opacity-50 md:px-0 md:text-left md:text-[15px]">
          <p>
            Address - Third floor, Block A, Stable Money, Bhive HSR Premium
            Campus, Krishna Reddy Industrial Area, Kudlu gate, Bommanahalli,
            Bangalore, Karnataka, India, 560068
          </p>
          <p>
            Stable Broking Private Limited (Formerly known as Stable Wealth
            Private Limited)
            <strong>(U65990KA2023PTC170330)</strong> is a stock broking entity
            operating in the debt segment. It functions as an online bond
            platform provider.
          </p>
        </div>

        <hr className="my-[20px] border-white opacity-20" />
        <Accordion className="pr-0 pl-0" defaultValue={["sebi-registration"]}>
          <AccordionItem
            isDarkMode
            id="sebi-registration"
            label={
              <p className="text-body2 opacity-50 md:text-[15px]">
                SEBI Registration No: <strong>INZ000314637</strong> | NSE Member
                Code: <strong>90363</strong> | BSE Member Code:{" "}
                <strong>6829</strong>| DP Registration No:{" "}
                <strong>INDP7912025</strong>
              </p>
            }
          >
            <FooterContent />
          </AccordionItem>
        </Accordion>
      </div>
    </footer>
  );
}
