import { Swiper, SwiperSlide, type SwiperClass } from "swiper/react";
import { type UseQueryResult } from "@tanstack/react-query";
import BondCard from "@/components/bonds/bond-card";
import Pagination from "@/components/ui/pagination/pagination";
import { useEffect, useState } from "react";
import "./mobile-carousel.css";
import type { CollectionResponse } from "@/clients/gen/broking/Collection_pb";

interface SwiperSlideElement extends HTMLElement {
  progress?: number;
}

const MobileCarousel = ({
  query,
}: {
  query: UseQueryResult<CollectionResponse, Error>;
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [spaceBetween, setSpaceBetween] = useState(-40);

  const handleSlideChange = (swiper: SwiperClass) => {
    setActiveIndex(swiper.realIndex);
  };

  const calculateSpaceBetween = () => {
    const screenWidth = window.innerWidth;
    const scaleFactor = Math.min(Math.max(screenWidth / 360, 1), 1.2);
    const cardWidth = 300 * scaleFactor;
    const leftSpace = (screenWidth - cardWidth) / 2;
    const calculatedSpaceBetween = -(leftSpace + leftSpace / 2 + 20);
    setSpaceBetween(calculatedSpaceBetween);
  };

  const swiperProgress = (swiper: SwiperClass) => {
    const slides = swiper.slides;

    for (let i = 0; i < slides.length; i++) {
      const slide = slides[i] as SwiperSlideElement;
      const slideProgress = slide.progress || 0;
      const absProgress = Math.abs(slideProgress);
      let scale;

      if (absProgress <= 1) {
        scale = 1 - absProgress * 0.1;
      } else {
        scale = 0.9;
      }

      const clampedScale = Math.max(0.9, Math.min(1, scale));
      slide.style.transform = `scale(${clampedScale})`;
      const opacity = absProgress <= 1 ? 1 - absProgress * 0.3 : 0.7;
      const clampedOpacity = Math.max(0.7, Math.min(1, opacity));
      slide.style.opacity = clampedOpacity.toString();
    }
  };

  const setTransition = (swiper: SwiperClass, duration: number) => {
    const slides = swiper.slides;
    for (let i = 0; i < slides.length; i++) {
      slides[i].style.transitionDuration = `${duration}ms`;
    }
  };

  useEffect(() => {
    calculateSpaceBetween();
  }, []);

  return (
    <>
      {query.isLoading ? (
        <div className="flex animate-pulse gap-4">
          <div className="bg-black-5 h-45 w-5 justify-self-start rounded-lg"></div>
          <div className="bg-black-5 h-45 w-75 rounded-lg"></div>
          <div className="bg-black-5 h-45 w-5 justify-self-end rounded-lg"></div>
        </div>
      ) : (
        <div className="space-y-3">
          <Swiper
            loop
            centeredSlides
            initialSlide={0}
            speed={600}
            slidesPerView={1}
            spaceBetween={spaceBetween}
            updateOnWindowResize={true}
            watchSlidesProgress={true}
            onRealIndexChange={handleSlideChange}
            onResize={calculateSpaceBetween}
            onProgress={swiperProgress}
            onSetTransition={setTransition}
            className="h-55"
          >
            {query.data?.collectionItem.map(
              (item) =>
                item && (
                  <SwiperSlide key={item.id}>
                    <div className="mx-auto w-fit">
                      <BondCard item={item} />
                    </div>
                  </SwiperSlide>
                )
            )}
          </Swiper>

          {query.data?.collectionItem && (
            <Pagination
              totalSlides={query.data.collectionItem.length}
              activeIndex={activeIndex}
            />
          )}
        </div>
      )}
    </>
  );
};

export default MobileCarousel;
