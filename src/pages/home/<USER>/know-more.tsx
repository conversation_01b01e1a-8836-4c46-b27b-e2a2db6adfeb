import Animation from "@/components/ui/animation/animation";
import Surface from "@/components/ui/surface/surface";
import tick from "@/assets/images/icons/tick.svg";
import Button from "@/components/ui/button/button";
import star from "@/assets/images/icons/gold-star.svg";

export default function KnowMore() {
  return (
    <div className="flex flex-col justify-center gap-4 px-5 pt-8 pb-[24px] lg:flex-row lg:items-center lg:gap-20 lg:pt-20 lg:pb-5">
      <div className="text-center lg:flex-shrink-0 lg:text-left">
        <div className="space-y-[24px]">
          <div>
            <p className="text-black-80 text-[26px] leading-9 md:text-[48px] md:leading-normal">
              <span className="font-serif text-[24px] font-medium tracking-[-1px] text-[#B67E25] italic md:text-[48px]">
                Know more
              </span>
              <span className="tracking-[-1px]"> about bonds</span>
            </p>
          </div>
          <div className="text-black-60 flex justify-center gap-3 text-[15px] tracking-[-0.2px] md:text-[22px] lg:hidden">
            <p className="text-center">SEBI regulated platform</p>
            <img src={star} alt="Star" />
            <p className="text-center"> Start with ₹100</p>
            <img src={star} alt="Star" />
            <p className="text-center">Earn fixed returns</p>
          </div>
        </div>
        <div className="hidden space-y-6 text-[22px] lg:block">
          <div className="flex items-center gap-2">
            <img src={tick} alt="Tick" />
            <p className="text-black-80 tracking-[-0.2px]">
              <span className="font-medium"> SEBI regulated platform</span>
              <span className="text-black-50">, secured bonds</span>
            </p>
          </div>
          <div className="flex items-center gap-2">
            <img src={tick} alt="Tick" />
            <p className="text-black-80 tracking-[-0.2px]">
              <span className="text-black-50">Earn</span>
              <span className="font-medium"> fixed returns</span>
              <span className="text-black-50">, not linked to markets</span>
            </p>
          </div>
          <div className="flex items-center gap-2">
            <img src={tick} alt="Tick" />
            <p className="text-black-80 tracking-[-0.2px]">
              <span className="text-black-50">Start with</span>
              <span className="font-medium"> as low as ₹100</span>
            </p>
          </div>
        </div>
        <div className="hidden w-34 pt-8 lg:block">
          <Button className="hidden">
            <p className="text-[15px] tracking-[-0.3px]"> View all bonds </p>
          </Button>
        </div>
      </div>
      <Surface
        elevation="md"
        className="max-w-[415px] flex-shrink-0 max-lg:mx-auto"
      >
        <div className="p-5">
          <Animation
            src="https://assets.stablemoney.in/app/bonds_explainer_19mb_final_26_jun.mp4"
            type="video"
            playOnVisibility
            loop
          />
        </div>
      </Surface>
    </div>
  );
}
