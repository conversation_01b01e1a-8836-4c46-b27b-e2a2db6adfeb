import clsx from "clsx";
import { Drawer } from "vaul";
import HamburgerContent from "./hamburger-content";
import closeMenu from "@/assets/images/icons/close-menu.svg";
import classes from "./home.module.css";
import { useUserQuery } from "@/hooks/user";

export default function HamburgerMenu({
  children,
}: {
  children?: React.ReactNode;
}) {
  const userQuery = useUserQuery();
  const isLoggedIn = !!userQuery.data;

  return (
    <Drawer.Root direction="right" snapPoints={[1]}>
      <Drawer.Trigger className="z-2 md:hidden">{children}</Drawer.Trigger>
      <Drawer.Portal>
        <Drawer.Overlay className="bg-black-50 fixed inset-0" />
        <Drawer.Content
          className={clsx(
            "fixed top-0 right-0 z-70 h-full w-full",
            classes.coloredBackground
          )}
        >
          <Drawer.Title>
            <div className="flex h-16 w-full items-center justify-between border-b border-white/30 px-5">
              <img
                src="https://assets.stablemoney.in/web-frontend/stable_bonds_white_logo.webp"
                alt="logo"
                className="h-6 md:h-8"
              />
              <Drawer.Close>
                <img
                  decoding="sync"
                  src={closeMenu}
                  alt="close"
                  className="h-4 w-4"
                />
              </Drawer.Close>
            </div>
          </Drawer.Title>
          <HamburgerContent isLoggedIn={isLoggedIn} />
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
