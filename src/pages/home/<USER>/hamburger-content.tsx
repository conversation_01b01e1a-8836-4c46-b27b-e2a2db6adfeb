import { logout } from "@/clients/auth";
import Anchor from "@/components/functional/anchor";
export default function HamburgerContent({
  isLoggedIn,
}: {
  isLoggedIn: boolean;
}) {
  return (
    <div className="mt-9 flex h-250 w-full flex-col items-center gap-8 bg-transparent sm:hidden">
      {isLoggedIn ? (
        <button
          onClick={logout}
          className="text-body1 relative flex cursor-pointer text-white"
        >
          Logout
        </button>
      ) : (
        <a
          href="/authentication/mobile-number"
          className="text-body1 relative flex cursor-pointer items-center text-white"
        >
          Login/Register
        </a>
      )}
      {isLoggedIn && (
        <a
          href="/profile"
          className="text-body1 relative flex items-center text-white"
        >
          Profile
        </a>
      )}

      <a
        href="/about-us"
        target="_blank"
        className="text-body1 relative flex items-center text-white"
      >
        About us
      </a>
      {isLoggedIn && (
        <a
          href="/investments"
          className="text-body1 relative flex items-center text-white"
        >
          Passbook
        </a>
      )}
      <a
        href="/how-to-fill-form15g"
        className="text-body1 relative flex items-center text-white"
      >
        How to fill Form 15G
      </a>
      <Anchor
        href="/contact-us"
        className="text-body1 relative flex items-center text-white"
      >
        Contact us
      </Anchor>
    </div>
  );
}
