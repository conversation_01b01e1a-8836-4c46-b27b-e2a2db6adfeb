import purpleUnderline from "@/assets/images/icons/brown-underline.svg";
import classes from "./backed-by-best.module.css";

type RenderCardProps = {
  name: string;
  designation: string;
  investorImage: string;
  companyImage: string;
};

function RenderInvestor({
  name,
  designation,
  investorImage,
  companyImage,
}: RenderCardProps) {
  return (
    <div className="flex w-full shrink-1 items-center gap-3 rounded-xl md:w-[357px] md:flex-col md:items-center md:gap-6">
      <img
        src={investorImage}
        alt={name}
        className="h-34 w-34 object-cover md:h-64 md:w-64"
      />

      <div className="space-y-2 md:space-y-4">
        <div>
          <img
            src={companyImage}
            alt={name}
            className="m-0 h-6 w-auto md:m-auto md:h-14 md:w-auto"
          />
        </div>
        <div className="flex flex-col items-start md:items-center md:justify-center">
          <p className="text-black-80 text-left text-[18px] md:text-center lg:text-[24px] lg:tracking-[-0.6px]">
            {name}
          </p>
          <p className="text-black-50 text-left text-[15px] md:text-center lg:text-[20px]">
            {designation}
          </p>
        </div>
      </div>
    </div>
  );
}

export default function BackedByBest() {
  const data = [
    {
      investorImage:
        "https://assets.stablemoney.in/web-frontend/website/nandan.webp",
      investorCompanyImage:
        "https://assets.stablemoney.in/web-frontend/website/infosys.webp",
      name: "Nandan Nilekani",
      designation: "Non-executive chairman of Infosys",
    },
    {
      investorImage:
        "https://assets.stablemoney.in/web-frontend/website/kunal.webp",
      investorCompanyImage:
        "https://assets.stablemoney.in/web-frontend/website/snapdeal.webp",
      name: "Kunal Bahl",
      designation: "Former CEO, Snapdeal",
    },
    {
      investorImage:
        "https://assets.stablemoney.in/web-frontend/website/sriharsha.webp",
      investorCompanyImage:
        "https://assets.stablemoney.in/web-frontend/website/swiggy.webp",
      name: "Sriharsha Majety",
      designation: "Co-Founder, Swiggy",
    },
  ];
  return (
    <div className="mx-auto mt-[48px] mb-[48px] max-w-[1366px] px-5 md:mt-27 md:mb-[60px] md:px-15 xl:px-30">
      <div className="flex w-full flex-col justify-evenly gap-8 md:gap-14">
        <div className="flex items-center justify-center gap-2 md:gap-4 md:px-5">
          <div className="h-[1px] w-18 bg-linear-to-r from-[#FFFFFF00] to-[#AFAFAF94] md:w-70" />
          <h2
            className={
              classes.headingReklesss +
              " text-black-80 flex-shrink-0 text-center font-medium"
            }
          >
            <span>Backed by</span>
            &nbsp;
            <span
              className={classes.headingReklesss + " relative text-[#B08032]"}
            >
              the best
              <img
                src={purpleUnderline}
                alt=""
                className="absolute left-0 h-1.5 md:h-4"
              />
            </span>
          </h2>
          <div className="h-[1px] w-18 bg-linear-to-r from-[#AFAFAF94] to-[#FFFFFF00] md:w-70" />
        </div>
        <div className="flex flex-col gap-10 md:gap-12">
          <div className="flex flex-wrap gap-3 md:flex-nowrap md:gap-6">
            {data.map((bond) => {
              return (
                <RenderInvestor
                  name={bond.name}
                  designation={bond.designation}
                  investorImage={bond.investorImage}
                  companyImage={bond.investorCompanyImage}
                  key={bond.name}
                />
              );
            })}
          </div>
          <img
            src="https://assets.stablemoney.in/app/mweb_angels_25june.webp"
            className="block md:hidden"
          />
          <img
            src="https://assets.stablemoney.in/app/web_angels_25june.webp"
            className="hidden md:block"
          />
        </div>
      </div>
    </div>
  );
}
