import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import CollectionItem from "@/components/bonds/collection-item";
import { createCollectionQueryOptions } from "@/queries/bonds";
import arrowIcon from "@/assets/images/icons/left-carousel.svg";
import blackArrowIcon from "@/assets/images/icons/black-left-carousel.svg";
import { useState } from "react";
import clsx from "clsx";

interface BondCarouselProps {
  name: string;
  isCover?: boolean;
}

export default function BondCarousel({ name, isCover }: BondCarouselProps) {
  const query = useQuery(createCollectionQueryOptions(name));
  const [scrollPosition, setScrollPosition] = useState(0);
  const [carouselElement, setCarouselElement] = useState<HTMLDivElement | null>(
    null
  );
  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    setScrollPosition(e.currentTarget.scrollLeft);
  };
  const scrollWidth = carouselElement?.scrollWidth ?? 0;
  const canGoLeft = scrollPosition > 0;
  const canGoRight =
    scrollPosition < scrollWidth - (carouselElement?.clientWidth ?? 0);
  function goLeft() {
    if (!carouselElement) return;
    carouselElement.scrollBy({
      left: -carouselElement.clientWidth,
      behavior: "smooth",
    });
  }
  function goRight() {
    if (!carouselElement) return;
    carouselElement.scrollBy({
      left: carouselElement.clientWidth,
      behavior: "smooth",
    });
  }

  return (
    <div className="md:space-y-7 md:px-26">
      <QueryRenderer query={query}>
        {(collection) => (
          <>
            {isCover ? (
              <div className="flex items-center gap-2 px-5">
                <h3 className="text-[18px] font-medium tracking-[2px] uppercase">
                  {collection.title}
                </h3>
                {isCover && (
                  <div className="h-[1.5px] flex-1 bg-linear-to-r from-[#ffffff4d] to-[#ffffff00]" />
                )}
              </div>
            ) : (
              <div className="flex items-center justify-center gap-2 px-5">
                <div className="h-[1px] w-10 bg-linear-to-r from-[#FFFFFF00] to-[#000000] opacity-20 md:w-20 md:opacity-40" />
                <h3 className="text-black-80 text-[13px] font-medium tracking-[1.5px] uppercase md:text-[15px]">
                  Explore stable bonds
                </h3>
                <div className="h-[1px] w-10 bg-linear-to-r from-[#000000] to-[#FFFFFF00] opacity-20 md:w-20 md:opacity-40" />
              </div>
            )}
            <div className="relative">
              <button
                onClick={goLeft}
                disabled={!canGoLeft}
                className={clsx(
                  "absolute top-1/2 -left-20 z-10 hidden -translate-y-1/2 md:block",
                  {
                    "cursor-not-allowed opacity-50": !canGoLeft,
                  }
                )}
              >
                <img
                  src={isCover ? arrowIcon : blackArrowIcon}
                  alt="left carousel"
                  className="h-[44px] w-[44px] rotate-180 transform cursor-pointer"
                />
              </button>
              <div
                ref={setCarouselElement}
                onScroll={handleScroll}
                className="scrollbar-none text-black-80 relative space-x-[11px] overflow-x-auto p-5 whitespace-nowrap md:space-x-[26px]"
              >
                {collection.collectionItem.map((item, index) => (
                  <CollectionItem
                    key={item.id}
                    item={item}
                    collectionName={collection.name}
                    rank={index + 1}
                    className="w-[160px] rounded-[14px] bg-white p-[3px] md:w-[205px]"
                  />
                ))}
              </div>
              <button
                onClick={goRight}
                disabled={!canGoRight}
                className={clsx(
                  "absolute top-1/2 -right-20 z-10 hidden -translate-y-1/2 md:block",
                  {
                    "cursor-not-allowed opacity-50": !canGoRight,
                  }
                )}
              >
                <img
                  src={isCover ? arrowIcon : blackArrowIcon}
                  alt="right carousel"
                  className="h-[44px] w-[44px] cursor-pointer"
                />
              </button>
            </div>
          </>
        )}
      </QueryRenderer>
    </div>
  );
}
