import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import { getFAQsQueryOptions } from "@/queries/business";

export default function AllFaqs() {
  const query = useQuery(
    getFAQsQueryOptions({
      identifier: "BOND_LANDING_PAGE_FAQ",
      namespace: "BONDS_HOME",
    })
  );

  return (
    <QueryRenderer query={query}>
      {({ faqs }) => (
        <Accordion>
          {faqs.map((faq) => (
            <AccordionItem key={faq.question} label={faq.question}>
              <div dangerouslySetInnerHTML={{ __html: faq.answer }} />
            </AccordionItem>
          ))}
        </Accordion>
      )}
    </QueryRenderer>
  );
}
