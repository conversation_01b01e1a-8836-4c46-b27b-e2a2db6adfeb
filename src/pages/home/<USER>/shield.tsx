import shieldTop from "@/assets/images/illustrations/35crores_web.webp";
import gridTop from "@/assets/images/illustrations/top_grid_web.webp";
import gridBottom from "@/assets/images/illustrations/bottom_grid.webp";
import classes from "./shield.module.css";
import BondCarousel from "./bond-carousel";

export default function Shield() {
  return (
    <div className={classes.dottedBackground}>
      <img
        src={gridTop}
        alt="Grid"
        className="absolute top-0 h-[200px] w-screen md:h-auto"
      />
      <div className="mx-auto max-w-[1366px] space-y-12 pb-8 md:space-y-20 md:pb-14">
        <img
          src={shieldTop}
          alt="Shield Top"
          className="relative z-1 mx-auto w-75 md:w-122"
        />
        <div className="relative z-10 space-y-4 md:space-y-18">
          <div className="mx-auto">
            <BondCarousel name="all_bonds_new_user" />
          </div>
          <p className="text-black-40 relative z-1 px-10 text-center text-[13px] tracking-[-0.2px] opacity-80 md:px-0 md:text-[22px] md:tracking-[-0.5px]">
            • You get top-rated, senior and secured bonds •
          </p>
        </div>
      </div>
      <img
        src={gridBottom}
        alt="Grid"
        className="absolute top-94 z-2 h-[332px] w-screen md:top-176 md:h-auto"
      />
    </div>
  );
}
