.dottedBackground {
  background-image: url("/src/assets/images/icons/background-grey-dot.svg");
  background-repeat: repeat;
  background-size: 40px 40px;
  width: 100%;
  position: relative;
}

.testimonialContainer {
  width: 100%;
  max-width: 1366px;
  margin: 0 auto;
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1.75rem;
  padding: 48px 0 0 0;
}

.testimonialContainer::after,
.testimonialContainer::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  width: 5rem;
  pointer-events: none;
  z-index: 2;
  background: linear-gradient(
    -90deg,
    rgb(255, 255, 255) 0%,
    rgba(255, 255, 255, 0) 100%
  );
}

.testimonialContainer::after {
  right: 0;
}

.testimonialContainer::before {
  left: 0;
  transform: scaleX(-1);
}

@media screen and (max-width: 471px) {
  .testimonialContainer {
    gap: 12px;
    padding: 16px 0 0 0;
  }

  .testimonialContainer::after,
  .testimonialContainer::before {
    width: 20px;
  }
}

@keyframes moveSlidesLeft {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-50%);
  }
}

.TestimonialRowContainer {
  display: flex;
  gap: 1.5rem;
  padding-right: 1.5rem;
  width: fit-content;
  animation-play-state: running;
}

.TestimonialRowContainer.reverse {
  animation-direction: reverse;
}

.TestimonialRowContainer:hover {
  animation-play-state: paused;
}

.TestimonialRowContainer {
  animation: moveSlidesLeft 80s linear infinite;
}

.TestimonialRowContainer.left:hover {
  animation-play-state: paused;
}

@media screen and (max-width: 471px) {
  .TestimonialRowContainer {
    gap: 12px;
  }

  .TestimonialRowContainer.left {
    animation: moveSlidesLeft 40s linear infinite;
  }

  .TestimonialRowContainer.left:hover {
    animation-play-state: running;
  }
}
