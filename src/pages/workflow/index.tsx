import { type LoaderFunctionArgs, redirect } from "react-router";
import { getWorkflowStatus } from "@/queries/workflow";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import LoadingPage from "@/components/functional/loading-page";
import { getOnboardingStatus as getAlphaOnboardingStatus } from "@/queries/identity";
import { getPathForNextAlphaStep } from "@/utils/onboarding-routes";

export async function loader({ params }: LoaderFunctionArgs) {
  const alphaResponse = await getAlphaOnboardingStatus();
  if (alphaResponse.next) {
    return redirect(getPathForNextAlphaStep(alphaResponse.next));
  }
  const workflowName = getWorkflowNameFromSlug(params.slug!);
  const response = await getWorkflowStatus(workflowName);
  const nextPath = getPathForWorkflowStep(workflowName, response);
  return redirect(nextPath);
}

export default function WorkflowInitiatePage() {
  return <LoadingPage />;
}
