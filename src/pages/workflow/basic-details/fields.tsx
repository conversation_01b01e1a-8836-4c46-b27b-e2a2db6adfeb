import QueryRenderer from "@/components/functional/query-renderer";
import ChipGroup from "@/components/ui/chip/chip-group";
import ChipItem from "@/components/ui/chip/chip-item";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import Field from "@/components/ui/field/field";
import { getQuestionnaireListQueryOptions } from "@/queries/basic-details";
import { useQuery } from "@tanstack/react-query";
import { type FormikProps } from "formik";
import { type FormValues } from "./schema";

export default function FormFields({
  errors,
  submitCount,
  setFieldValue,
}: FormikProps<FormValues>) {
  const questionnaireQuery = useQuery(getQuestionnaireListQueryOptions());

  return (
    <QueryRenderer query={questionnaireQuery}>
      {(questionnaireData) => (
        <div className="space-y-5">
          {questionnaireData.map((question) => {
            function setValue(e: React.ChangeEvent<HTMLInputElement>) {
              setFieldValue(question.fieldName, e.target.value);
            }
            return (
              <div key={question.fieldName} className="space-y-4">
                <SectionHeading
                  separator
                  title={question.title.toUpperCase()}
                />
                <Field
                  error={
                    submitCount > 0
                      ? errors[question.fieldName as keyof FormValues]
                      : undefined
                  }
                >
                  <ChipGroup
                    onChange={setValue}
                    name={question.fieldName}
                    className="flex flex-grow flex-wrap gap-2"
                  >
                    {question.options.map((option) => (
                      <ChipItem
                        key={option.value}
                        value={option.value.toString()}
                      >
                        {option.label}
                      </ChipItem>
                    ))}
                  </ChipGroup>
                </Field>
              </div>
            );
          })}
        </div>
      )}
    </QueryRenderer>
  );
}
