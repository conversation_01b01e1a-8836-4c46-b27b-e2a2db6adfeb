import { useParams, useNavigate } from "react-router";
import { Formik, Form } from "formik";
import * as yup from "yup";
import Head from "@/components/ui/head";
import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { trackEvent } from "@/utils/analytics";
import { withErrorToast } from "@/utils/errors";
import FormFields from "./fields";

const validationSchema = yup.object({
  employmentType: yup.string().required("Employment type is required"),
  incomeRange: yup.string().required("Income range is required"),
  tradingExperience: yup.string().required("Trading experience is required"),
  maritalStatus: yup.string().required("Marital status is required"),
});

type FormValues = yup.InferType<typeof validationSchema>;

export default function WorkflowBasicDetailsPage() {
  const { slug } = useParams<{ slug: string }>();
  const navigate = useNavigate();

  return (
    <>
      <Head title="Basic Details" />
      <Formik
        initialValues={
          {
            incomeRange: "",
            maritalStatus: "",
            employmentType: "",
            tradingExperience: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(values) {
          trackEvent("basic_details_submit");
          const searchParams = new URLSearchParams(values).toString();
          navigate(`/workflow/${slug}/parent-details?${searchParams}`, {
            replace: true,
          });
        })}
      >
        {(formState) => {
          return (
            <Form id="basic-details-form">
              <FormPage
                title="Complete your KYC"
                description="Please enter these details to verify KYC"
                footer={
                  <Button
                    type="submit"
                    form="basic-details-form"
                    loading={formState.isSubmitting}
                  >
                    Proceed
                  </Button>
                }
              >
                <FormFields {...formState} />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
