import * as yup from "yup";
import { PAN_NUMBER } from "@/utils/patterns";
import dayjs from "dayjs";

export const validationSchema = yup.object({
  pan: yup
    .string()
    .required("Pan number is required")
    .matches(PAN_NUMBER, "PAN is invalid, check again?"),
  consent: yup
    .boolean()
    .oneOf([true], "You must agree to the terms and conditions")
    .required("You must agree to the terms and conditions"),
  name: yup
    .string()
    .required("Name is required")
    .min(1, "Name is required")
    .matches(/^[a-zA-Z\s]+$/, "Name can only contain alphabets and spaces"),
  dateOfBirth: yup
    .string()
    .required("Date of birth is required")
    .test("is-valid-date", "Date is not valid", (value) =>
      dayjs(value, "DD/MM/YYYY", true).isValid()
    ),
});

export type FormValues = yup.InferType<typeof validationSchema>;
