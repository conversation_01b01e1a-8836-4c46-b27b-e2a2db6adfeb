import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { Formik } from "formik";
import { submitPanDetails } from "@/queries/workflow";
import { trackEvent } from "@/utils/analytics";
import { withErrorToast } from "@/utils/errors";
import useNavigate from "@/hooks/navigate";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import { useParams } from "react-router";
import { StepName } from "@/clients/gen/broking/WorkflowStep_pb";

export default function WorkflowPanPage() {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();
  const workflowName = getWorkflowNameFromSlug(slug!);
  return (
    <>
      <Head title="PAN Details" />
      <Formik
        initialValues={
          {
            pan: "",
            name: "",
            dateOfBirth: "",
            consent: false,
          } as FormValues
        }
        initialStatus="invalid"
        validateOnMount
        validationSchema={validationSchema}
        onSubmit={withErrorToast(async function handleSubmit(
          values,
          { setFieldError }
        ) {
          const result = await submitPanDetails(
            {
              panNo: values.pan.toUpperCase(),
              name: values.name,
              dob: values.dateOfBirth,
            },
            workflowName
          );
          trackEvent("pan_details_submit");
          if (
            result.nextStep?.nextStep &&
            result.nextStep.nextStep !== StepName.PAN_KYC
          ) {
            return navigate(
              getPathForWorkflowStep(workflowName, result.nextStep),
              {
                replace: true,
              }
            );
          }

          if (!result.nameMatchStatus) {
            setFieldError("name", "Name is not as per PAN");
          } else if (!result.dobMatchStatus) {
            setFieldError("dateOfBirth", "Date of birth is not as per PAN");
          } else if (!result.isPanValid || !result.panKycStatus) {
            setFieldError("pan", "PAN is invalid, please check again");
          }
        })}
      >
        {(formState) => {
          return (
            <Form id="pan-form">
              <FormPage
                title="PAN details"
                description="Please enter PAN to complete KYC"
                footer={
                  <Button
                    type="submit"
                    form="pan-form"
                    loading={formState.isSubmitting}
                  >
                    Confirm PAN
                  </Button>
                }
              >
                <FormFields {...formState} />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
