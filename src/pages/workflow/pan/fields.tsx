import CircularProgressIndicator from "@/components/icons/circular-progress-indicator";
import Checkbox from "@/components/ui/checkbox/checkbox";
import Field from "@/components/ui/field/field";
import Input from "@/components/ui/input/input";
import { getPanName } from "@/queries/workflow";
import { PAN_NUMBER } from "@/utils/patterns";
import { useQuery } from "@tanstack/react-query";
import { Field as FormikField, type FormikProps } from "formik";
import { useEffect } from "react";
import { type FormValues } from "./schema";

export default function FormFields({
  errors,
  values,
  setFieldValue,
  submitCount,
}: FormikProps<FormValues>) {
  const panNameQuery = useQuery({
    queryKey: ["pan", values.pan],
    queryFn: () => getPanName(values.pan.toUpperCase()),
    enabled: values.pan?.length === 10 && PAN_NUMBER.test(values.pan),
    // We need to show the fields quickly, otherwise the user won't understand what's happening.
    // Hence, we retry only once and with a short delay.
    retry(failureCount) {
      return failureCount < 2;
    },
    retryDelay: 500,
  });

  useEffect(() => {
    if (panNameQuery.data) {
      setFieldValue("name", panNameQuery.data.fullName);
    }
  }, [panNameQuery.data, setFieldValue]);
  return (
    <>
      <FormikField
        name="pan"
        label="PAN"
        as={Input}
        maxLength={10}
        textTransform="uppercase"
        error={submitCount > 0 ? errors.pan : undefined}
        suffix={panNameQuery.isLoading ? <CircularProgressIndicator /> : null}
      />
      <FormikField
        name="name"
        label="Name as per PAN"
        as={Input}
        error={submitCount > 0 ? errors.name : undefined}
      />
      <FormikField
        name="dateOfBirth"
        label="Date of birth"
        as={Input}
        mask="date"
        placeholder="DD/MM/YYYY"
        error={submitCount > 0 ? errors.dateOfBirth : undefined}
      />
      <Field error={submitCount > 0 ? errors.consent : undefined}>
        <FormikField name="consent" as={Checkbox} checked={values.consent}>
          I authorize Stable Broking Pvt. Ltd. to fetch, verify, store and share
          my PAN and Aadhaar details with CVL KRA, Depositories, and regulated
          entities as required by law.
        </FormikField>
      </Field>
    </>
  );
}
