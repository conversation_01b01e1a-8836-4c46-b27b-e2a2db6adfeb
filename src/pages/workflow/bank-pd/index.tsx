import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { Formik } from "formik";
import { submitPennyDropRequest } from "@/queries/bank-workflow";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import { trackEvent } from "@/utils/analytics";
import { withErrorToast } from "@/utils/errors";
import useNavigate from "@/hooks/navigate";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";
import { useParams } from "react-router";

export default function WorkflowBankPdPage() {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();

  const workflowName = getWorkflowNameFromSlug(slug!);
  return (
    <>
      <Head title="Bank Details" />
      <Formik
        initialValues={
          {
            accountNumber: "",
            ifscCode: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(values) {
          const responseData = await submitPennyDropRequest(
            values.accountNumber,
            values.ifscCode,
            workflowName
          );
          trackEvent("pd_verify_clicked");
          navigate(
            getPathForWorkflowStep(workflowName, responseData.nextStep),
            {
              replace: true,
            }
          );
        })}
      >
        {(formState) => {
          return (
            <Form id="bank-form">
              <FormPage
                title="Bank details"
                description="Bond payments must come from this account"
                footer={
                  <Button
                    type="submit"
                    form="bank-form"
                    loading={formState.isSubmitting}
                  >
                    Verify bank account
                  </Button>
                }
              >
                <FormFields {...formState} />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
