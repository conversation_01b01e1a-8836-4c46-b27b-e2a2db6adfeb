import Input from "@/components/ui/input/input";
import Entity<PERSON>ogo from "@/components/ui/entity-logo/entity-logo";
import QueryRenderer from "@/components/functional/query-renderer";
import { getIfscDetails } from "@/queries/bank";
import { useQuery } from "@tanstack/react-query";
import { Field, type FormikProps } from "formik";
import { type FormValues } from "./schema";
import CircularProgressIndicator from "@/components/icons/circular-progress-indicator";

export default function FormFields({
  errors,
  values,
  submitCount,
}: FormikProps<FormValues>) {
  const ifscQuery = useQuery({
    queryKey: ["ifsc", values.ifscCode],
    queryFn: () => getIfscDetails(values.ifscCode),
    enabled: values.ifscCode?.length === 11,
  });

  return (
    <>
      <Field
        name="accountNumber"
        label="Bank account number"
        as={Input}
        error={submitCount > 0 ? errors.accountNumber : undefined}
      />
      <Field
        name="ifscCode"
        label="IFSC code"
        as={Input}
        textTransform="uppercase"
        error={submitCount > 0 ? errors.ifscCode : undefined}
        suffix={
          <QueryRenderer
            query={ifscQuery}
            loader={
              <CircularProgressIndicator data-testid="ifsc-loading-indicator" />
            }
          >
            {(ifscDetails) =>
              ifscDetails ? (
                <EntityLogo
                  url={ifscDetails.bankLogoUrl}
                  size="small"
                  data-testid="bank-logo"
                  color="#FFF6EE"
                  elevation="md"
                  seamless
                />
              ) : null
            }
          </QueryRenderer>
        }
        help={
          <QueryRenderer query={ifscQuery}>
            {(ifscDetails) =>
              ifscDetails ? `${ifscDetails.bank} - ${ifscDetails.branch}` : null
            }
          </QueryRenderer>
        }
      />
    </>
  );
}
