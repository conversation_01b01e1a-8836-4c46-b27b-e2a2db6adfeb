import * as yup from "yup";

export const validationSchema = yup.object({
  accountNumber: yup
    .string()
    .required("Account number is required")
    .min(9, "Account number must be at least 9 digits")
    .max(17, "Account number must be at most 17 digits")
    .matches(/^\d+$/, "Account number must contain only digits"),
  ifscCode: yup
    .string()
    .required("IFSC code is required")
    .length(11, "IFSC code must be 11 characters")
    .transform((value) => (value ? value.toUpperCase() : value))
    .matches(/^[A-Z]{4}0[A-Z0-9]{6}$/, "IFSC code is invalid"),
});

export type FormValues = yup.InferType<typeof validationSchema>;
