import { useParams, useSearchParams } from "react-router";
import { useSuspenseQuery } from "@tanstack/react-query";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import Head from "@/components/ui/head";
import { useCallback, useEffect } from "react";
import EsignInitial from "./components/esign-initial";
import { getProfileQueryOptions } from "@/queries/profile";
import LoadingPage from "@/components/functional/loading-page";
import ErrorPage from "@/components/functional/error-page";
import { esignMachine } from "./machine";
import { useMachine } from "@xstate/react";
import useNavigate from "@/hooks/navigate";
import { StepName } from "@/clients/gen/broking/WorkflowStep_pb";
import { createInspector } from "@/utils/xstate";

export default function WorkflowEsignPage() {
  const { slug } = useParams();
  const [searchParams] = useSearchParams();
  const withDemat = searchParams.get("withDemat") === "true";
  const navigate = useNavigate();
  const workflowName = getWorkflowNameFromSlug(slug!);
  const stepName = withDemat ? StepName.ESIGN_WITH_DEMAT : StepName.ESIGN;

  const [
    {
      value: status,
      context: { error, nextStep },
    },
    send,
  ] = useMachine(esignMachine, {
    input: { workflowName, stepName },
    inspect: createInspector(esignMachine.id),
  });

  const { data: userDetailsResponse } = useSuspenseQuery(
    getProfileQueryOptions()
  );

  const handleStartEsign = useCallback(() => {
    send({ type: "START_ESIGN" });
  }, [send]);

  useEffect(() => {
    if (nextStep) {
      navigate(getPathForWorkflowStep(workflowName, nextStep), {
        replace: true,
      });
    }
  }, [navigate, nextStep, workflowName]);

  if (status === "error") {
    return (
      <ErrorPage
        error={error}
        title={error ? undefined : "eSign verification failed"}
        description="Please try again. If the issue persists, contact our support team."
        onRetry={() => send({ type: "RETRY" })}
      />
    );
  }

  if (status === "fetchingEsignCredentials") {
    return <LoadingPage />;
  }

  if (status === "checkingStatus") {
    return (
      <LoadingPage
        title="Completing your 1-time bonds investment KYC"
        description="This usually takes a few moments. Please don't close this page."
      />
    );
  }

  return (
    <>
      <Head title="Aadhaar eSign" />
      <EsignInitial
        onSubmit={handleStartEsign}
        loading={status === "sdkOpen"}
        userData={userDetailsResponse}
      />
    </>
  );
}
