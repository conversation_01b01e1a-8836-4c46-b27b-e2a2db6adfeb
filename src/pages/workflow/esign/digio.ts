import type { GenerateTokenResponse } from "@/clients/gen/broking/Kyc_pb";
import { loadDigioSDK } from "@/utils/sdk-loader";
import { trackEvent } from "@/utils/analytics";
import type { GenerateEsignTokenResponse } from "@/clients/gen/broking/WorkflowStep_pb";
import * as native from "@/utils/native-integration";

export async function startDigio(
  esignData: GenerateTokenResponse | GenerateEsignTokenResponse
) {
  trackEvent("esign_initiate");

  if (native.digioEsign.isSupported()) {
    try {
      const result = await native.digioEsign.submit(
        esignData.id,
        esignData.customerIdentifier,
        esignData.accessToken
      );
      trackEvent("esign_completed", result as Record<string, unknown>);
      return result;
    } catch (error) {
      trackEvent("esign_failed", error as Record<string, unknown>);
      throw error;
    }
  }

  return new Promise((resolve, reject) => {
    loadDigioSDK().then(() => {
      const digio = new window.Digio!({
        environment: "production",
        callback: (response) => {
          if ("error_code" in response) {
            trackEvent("esign_failed", response);
            reject(response);
          } else {
            trackEvent("esign_completed", response);
            resolve(response);
          }
        },
        logo: "https://assets.stablemoney.in/web-frontend/v1/stablemoney-black-textlogo.svg",
        theme: { primaryColor: "#6A768A", secondaryColor: "#000000" },
        is_iframe: true,
        method: "otp",
      });
      digio.init();
      digio.submit(
        esignData.id,
        esignData.customerIdentifier,
        esignData.accessToken
      );
    });
  });
}
