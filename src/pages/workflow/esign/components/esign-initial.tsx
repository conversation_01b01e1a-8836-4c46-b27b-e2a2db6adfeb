import Button from "@/components/ui/button/button";
import LinkButton from "@/components/ui/button/link-button";
import FormPage from "@/components/bonds/form-page";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import { trackEvent } from "@/utils/analytics";
import MandatoryDocuments from "@/pages/sheet/components/mandatory-documents";
import { useViewEvent } from "@/hooks/view-event";
import { getNameFromProfile } from "@/queries/profile";
import type { UserProfileResponse } from "@/clients/gen/broking/Profile_pb";

interface EsignInitialProps {
  onSubmit: () => void;
  loading: boolean;
  userData: UserProfileResponse;
}

export default function EsignInitial({
  onSubmit,
  loading,
  userData,
}: EsignInitialProps) {
  const { ref } = useViewEvent({
    eventName: "esign_page_viewed",
  });

  return (
    <FormPage
      title="Finish KYC using Aadhaar eSign"
      description="Enter your Aadhaar and verify using OTP sent to your Aadhaar linked mobile number"
      footer={
        <>
          <div className="mb-2 text-center">
            <AdaptiveModal
              trigger={
                <LinkButton
                  onClick={() => {
                    trackEvent("mandatory_documents_clicked");
                  }}
                >
                  Mandatory and important documents
                </LinkButton>
              }
              size="small"
              href="/sheets/mandatory-documents"
            >
              {() => <MandatoryDocuments />}
            </AdaptiveModal>
          </div>
          <Button onClick={onSubmit} loading={loading}>
            Finish KYC
          </Button>
        </>
      }
    >
      <div className="flex justify-center" ref={ref}>
        <div className="relative">
          <div className="absolute top-[80px] left-[20px] flex flex-col gap-[30px]">
            <div className="flex flex-col gap-[3px]">
              <p className="text-body2 text-black-50 font-medium">
                AADHAAR NUMBER
              </p>
              <p className="text-black-80">XXXX XXXX XXXX</p>
            </div>
            <div className="flex flex-col gap-[3px]">
              <p className="text-body2 text-black-50 font-medium">FULL NAME</p>
              <p className="text-black-80 line-clamp-1">
                {getNameFromProfile(userData)}
              </p>
            </div>
          </div>
          <img
            src="https://assets.stablemoney.in/app/aadhar_card.webp"
            alt="Aadhaar card template"
            width="310px"
            height="210px"
          />
        </div>
      </div>

      <p className="text-body2 text-black-50">
        I confirm that my investment account will be created with the above
        information. I also allow Stable Broking Private limited to fetch,
        modify, upload, share the KYC details to Govt's CKYCR and other KRA's.
      </p>
      <div className="mt-16 flex justify-center">
        <img
          src="https://assets.stablemoney.in/app/secured_by_uidai.webp"
          alt="Secured by UIDAI"
          width="240px"
        />
      </div>
    </FormPage>
  );
}
