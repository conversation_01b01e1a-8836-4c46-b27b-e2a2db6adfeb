import {
  type WorkflowName,
  type ContinueWorkflowResponse,
  type GenerateEsignTokenResponse,
  StepName,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { getEsignCredential, getEsignStatus } from "@/queries/workflow";
import { fromPromise, setup, assign, type DoneActorEvent } from "xstate";
import { startDigio } from "./digio";
import { createPollingMachine } from "@/machines/polling";

type CheckEsignStatusResponse = Awaited<ReturnType<typeof getEsignStatus>>;

export const esignMachine = setup({
  types: {
    context: {} as {
      workflowName: WorkflowName;
      stepName: StepName.ESIGN | StepName.ESIGN_WITH_DEMAT;
      esignCredentials?: GenerateEsignTokenResponse;
      error?: unknown;
      nextStep?: ContinueWorkflowResponse;
    },
    input: {} as {
      workflowName: WorkflowName;
      stepName: StepName.ESIGN | StepName.ESIGN_WITH_DEMAT;
    },
    events: {} as
      | {
          type: "RETRY";
        }
      | {
          type: "START_ESIGN";
        }
      | {
          type: "COMPLETED";
          response: CheckEsignStatusResponse;
        }
      | DoneActorEvent<GenerateEsignTokenResponse>
      | {
          type: "LIMIT_REACHED";
          error?: unknown;
        },
    output: {} as CheckEsignStatusResponse,
  },
  guards: {
    isEsignComplete: ({ event }) => {
      return "output" in event && event.output.isComplete;
    },
    hasEsignCredentials: ({ context }) => {
      return !!context.esignCredentials;
    },
  },
  actions: {
    assignNextStepFromCredentials: assign({
      nextStep: ({ event }) => {
        if ("output" in event && event.output.nextStep) {
          return event.output.nextStep;
        }
      },
    }),
    assignEsignCredentials: assign({
      esignCredentials: ({ event }) => {
        if ("output" in event) {
          return event.output;
        }
      },
    }),
    assignError: assign({
      error: ({ event }) => {
        if ("error" in event) {
          return event.error;
        }
      },
    }),
    assignNextStepFromPolling: assign(({ event, context }) => {
      if ("output" in event) {
        return {
          ...context,
          nextStep: event.output.nextStep,
        };
      }
      return context;
    }),
  },
  actors: {
    getEsignCredentials: fromPromise(
      ({
        input,
      }: {
        input: {
          workflowName: WorkflowName;
          stepName: StepName.ESIGN | StepName.ESIGN_WITH_DEMAT;
        };
      }) => getEsignCredential(input.workflowName, input.stepName)
    ),
    startEsign: fromPromise(
      ({ input }: { input: GenerateEsignTokenResponse }) => startDigio(input)
    ),
    polling: createPollingMachine<CheckEsignStatusResponse>(
      "esign-polling",
      "evaluating"
    ),
  },
}).createMachine({
  id: "esign",
  initial: "fetchingEsignCredentials",
  context: ({ input }) => ({
    workflowName: input.workflowName,
    stepName: input.stepName,
  }),
  states: {
    fetchingEsignCredentials: {
      invoke: {
        src: "getEsignCredentials",
        input: ({ context }) => ({
          workflowName: context.workflowName,
          stepName: context.stepName,
        }),
        onDone: [
          {
            target: "success",
            guard: "isEsignComplete",
            actions: "assignNextStepFromCredentials",
          },
          {
            target: "fetchedEsignCredentials",
            actions: "assignEsignCredentials",
          },
        ],
        onError: {
          target: "error",
          actions: "assignError",
        },
      },
    },
    fetchedEsignCredentials: {
      on: {
        START_ESIGN: "sdkOpen",
      },
    },
    sdkOpen: {
      invoke: {
        src: "startEsign",
        input: ({ context }) => context.esignCredentials!,
        onDone: {
          target: "checkingStatus",
        },
        onError: {
          target: "error",
          actions: "assignError",
        },
      },
    },
    checkingStatus: {
      invoke: {
        src: "polling",
        input: ({ context, self }) => ({
          runner: () =>
            getEsignStatus(
              context.workflowName,
              context.stepName,
              context.esignCredentials!.id
            ),
          guard: (result) => result.esignStatus === "signed",
          parentRef: self,
          maxCount: 24,
          interval: 5000,
        }),
        onDone: {
          target: "success",
          actions: "assignNextStepFromPolling",
        },
      },
      on: {
        LIMIT_REACHED: {
          target: "error",
          actions: "assignError",
        },
      },
    },
    success: {
      type: "final",
    },
    error: {
      output: ({ context }) => context.error,
      on: {
        RETRY: "fetchingEsignCredentials",
      },
    },
  },
});
