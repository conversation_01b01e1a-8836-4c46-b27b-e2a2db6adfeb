import { useEffect, useMemo } from "react";
import { getPathForWorkflowStep } from "@/utils/workflow-routes";
import { useParams } from "react-router";
import useNavigate from "@/hooks/navigate";
import { getWorkflowNameFromSlug } from "@/utils/workflow-routes";
import { useMachine } from "@xstate/react";
import { StepName, WorkflowName } from "@/clients/gen/broking/WorkflowStep_pb";
import LoadingPage from "@/components/functional/loading-page";
import LimitReached from "../components/limit-reached";
import { statusMachine } from "./machine";
import { useQuery } from "@tanstack/react-query";
import { getCart } from "@/queries/orders";
import { getHomeUrl } from "@/utils/routing";
import Head from "@/components/ui/head";
import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { createInspector } from "@/utils/xstate";
import ErrorPage from "@/components/functional/error-page";

export default function WorkflowStatusPage() {
  const navigate = useNavigate();

  const { slug } = useParams();
  const workflowName = getWorkflowNameFromSlug(slug!);
  const [state, send] = useMachine(statusMachine, {
    input: { workflowName },
    inspect: createInspector(statusMachine.id),
  });

  const titleText = useMemo(() => {
    switch (state.context.nextStep?.nextStep) {
      case StepName.CVL_PULL_WAITING:
        return "Verifying your KYC details";
      case StepName.CVL_PUSH_WAITING:
        return "Updating your KYC details";
      case StepName.CDSL_PUSH_WAITING:
        return "Creating your free demat account";
      default:
        return "Getting you investment-ready";
    }
  }, [state.context.nextStep]);

  useEffect(() => {
    if (state.matches("exit")) {
      navigate(getPathForWorkflowStep(workflowName, state.context.nextStep), {
        replace: true,
      });
    }
  }, [state, navigate, workflowName]);

  if (state.value === "completed") {
    return <SuccessPage workflowName={workflowName} />;
  }
  if (state.value === "takingTooLong") {
    return (
      <LimitReached
        title="Your onboarding is in progress"
        description="It usually takes less than 10 minutes. We will reach out to you if any additional info is required."
      />
    );
  }
  if (state.value === "nseFailure") {
    return (
      <LimitReached
        title="NSE Onboarding failed"
        description="We will get in touch with you within next 24 hours to get you investment-ready."
      />
    );
  }
  if (state.value === "error") {
    return (
      <ErrorPage
        error={state.context.error}
        onRetry={() => send({ type: "RETRY" })}
      />
    );
  }
  return (
    <LoadingPage
      title={titleText}
      description="This usually takes a few moments. Please don't close this page."
    />
  );
}

function SuccessPage({ workflowName }: { workflowName: WorkflowName }) {
  const navigate = useNavigate();
  const { data: cart } = useQuery({
    queryKey: ["cart"],
    queryFn: getCart,
  });
  const buyUrl = useMemo(() => {
    if (cart?.cartItems.length && cart.cartItems.length > 0) {
      return `/bonds/unknown/${cart.cartItems[0].bondDetailId}/calculator`;
    }
    return getHomeUrl();
  }, [cart]);

  const handleContinue = () => {
    navigate(buyUrl, { replace: true });
  };

  return (
    <>
      <Head title="Workflow Complete" />
      <FormPage
        footer={<Button onClick={handleContinue}>Continue to Home</Button>}
      >
        <div className="md:bg-bg md:border-black-10 flex flex-1 flex-col items-center justify-center gap-6 md:min-h-[470px] md:rounded-2xl md:border md:p-6">
          <img
            src="https://assets.stablemoney.in/app/badge-check.webp"
            width={50}
            crossOrigin="anonymous"
          />
          <div className="flex flex-col gap-2">
            <h2 className="text-heading2 text-black-80 text-center">
              {getSuccessTitle(workflowName)}
            </h2>
            <p className="text-body1 text-black-50 text-center"></p>
          </div>
        </div>
      </FormPage>
    </>
  );
}

function getSuccessTitle(workflowName: WorkflowName) {
  switch (workflowName) {
    case WorkflowName.DEMAT_ACCOUNT_OPENING:
      return "Your demat account is now open";
    case WorkflowName.TRADING_ACCOUNT_OPENING:
      return "Your KYC is now complete";
    case WorkflowName.TRADING_DEMAT_ACCOUNT_OPENING:
      return "Your demat and trading accounts are now open";
    case WorkflowName.ONBOARDING_INITIATION:
      return "Your onboarding is now complete";
    default:
      return "Process completed";
  }
}
