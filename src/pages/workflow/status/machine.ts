import {
  NseStatusResponse_NseStatus,
  type NseStatusResponse,
} from "@/clients/gen/broking/Common_pb";
import {
  StepName,
  WorkflowStatus,
  type ContinueWorkflowResponse,
  type WorkflowName,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { createPollingMachine } from "@/machines/polling";
import { getNseStatus } from "@/queries/profile";
import { getWorkflowStatus } from "@/queries/workflow";
import { trackEvent } from "@/utils/analytics";
import { getWorkflowDisplayName } from "@/utils/workflow-routes";
import dayjs from "dayjs";
import { setup, assign, type DoneActorEvent, fromPromise } from "xstate";

export const statusMachine = setup({
  types: {
    context: {} as {
      workflowName: WorkflowName;
      error?: unknown;
      nextStep?: ContinueWorkflowResponse;
      nseStatus?: NseStatusResponse;
    },
    input: {} as {
      workflowName: WorkflowName;
    },
    events: {} as
      | {
          type: "RETRY";
        }
      | {
          type: "LIMIT_REACHED";
          error?: unknown;
        }
      | {
          type: "COMPLETED";
          response: ContinueWorkflowResponse | NseStatusResponse;
        }
      | DoneActorEvent<ContinueWorkflowResponse>
      | DoneActorEvent<NseStatusResponse>,
    output: {} as ContinueWorkflowResponse,
  },
  actors: {
    polling: createPollingMachine<ContinueWorkflowResponse>(
      "waiting-polling",
      "evaluating"
    ),
    nsePolling: createPollingMachine<NseStatusResponse>(
      "nse-polling",
      "evaluating"
    ),
    getWorkflowStatus: fromPromise(
      ({ input }: { input: { workflowName: WorkflowName } }) =>
        getWorkflowStatus(input.workflowName)
    ),
    getNseStatus: fromPromise(getNseStatus),
  },
  guards: {
    isNotWaitingStep: ({ event }) => {
      return (
        ("response" in event &&
          "nextStep" in event.response &&
          !!event.response.nextStep &&
          ![
            StepName.CVL_PULL_WAITING,
            StepName.CVL_PUSH_WAITING,
            StepName.CDSL_PUSH_WAITING,
            StepName.NSE_PUSH_WAITING,
          ].includes(event.response.nextStep)) ||
        ("output" in event &&
          "nextStep" in event.output &&
          !!event.output.nextStep &&
          ![
            StepName.CVL_PULL_WAITING,
            StepName.CVL_PUSH_WAITING,
            StepName.CDSL_PUSH_WAITING,
            StepName.NSE_PUSH_WAITING,
          ].includes(event.output.nextStep))
      );
    },
    isNseWaiting: ({ event }) => {
      return (
        ("output" in event &&
          "nextStep" in event.output &&
          event.output.nextStep === StepName.NSE_PUSH_WAITING) ||
        ("response" in event &&
          "nextStep" in event.response &&
          event.response.nextStep === StepName.NSE_PUSH_WAITING)
      );
    },
    nseCompleted: ({ event }) => {
      return (
        ("response" in event &&
          "nseStatus" in event.response &&
          event.response.nseStatus === NseStatusResponse_NseStatus.SUCCESS) ||
        ("output" in event &&
          "nseStatus" in event.output &&
          event.output.nseStatus === NseStatusResponse_NseStatus.SUCCESS)
      );
    },
    hasWaitedCurrentStepBefore: ({ context, event }) => {
      const workflowName =
        ("output" in event && "workflowName" in event.output
          ? event.output.workflowName
          : undefined) || context.nextStep?.workflowName;
      const step =
        ("output" in event && "nextStep" in event.output
          ? event.output.nextStep
          : undefined) || context.nextStep?.nextStep;
      if (!workflowName || !step) {
        return false;
      }
      const seenTimestamp = localStorage.getItem(
        `workflow-${workflowName}-${step}-seen`
      );
      if (!seenTimestamp) {
        return false;
      }
      const seenDate = new Date(Number(seenTimestamp));
      const now = dayjs();
      const diff = now.diff(seenDate, "minutes");
      return diff >= 2;
    },
    nseFailed: ({ event }) => {
      return (
        ("response" in event &&
          "nseStatus" in event.response &&
          event.response.nseStatus === NseStatusResponse_NseStatus.FAILED) ||
        ("output" in event &&
          "nseStatus" in event.output &&
          event.output.nseStatus === NseStatusResponse_NseStatus.FAILED)
      );
    },
    isCompleted: ({ event }) => {
      return (
        ("response" in event &&
          "workflowStatus" in event.response &&
          event.response.workflowStatus === WorkflowStatus.COMPLETED) ||
        ("output" in event &&
          "workflowStatus" in event.output &&
          event.output.workflowStatus === WorkflowStatus.COMPLETED)
      );
    },
  },
  actions: {
    assignNextStep: assign(({ event, context }) => {
      if ("response" in event && "nextStep" in event.response) {
        return {
          ...context,
          nextStep: event.response,
        };
      }
      if ("output" in event && "nextStep" in event.output) {
        return {
          ...context,
          nextStep: event.output,
        };
      }
      return context;
    }),
    assignError: assign(({ event, context }) => {
      if ("error" in event) {
        return {
          ...context,
          error: event.error,
        };
      }
      return context;
    }),
    assignNseStatus: assign(({ event, context }) => {
      if ("response" in event && "nseStatus" in event.response) {
        return {
          ...context,
          nseStatus: event.response,
        };
      }
      return context;
    }),
    trackCompletion: ({ context }) => {
      trackEvent("workflow_completed", {
        workflow_name: getWorkflowDisplayName(context.workflowName),
      });
    },
    trackNseFailure: ({ context }) => {
      trackEvent("nse_onboarding_failed", {
        workflow_name: getWorkflowDisplayName(context.workflowName),
      });
    },
    storeStepSeenTimestamp({ context }) {
      const existingTimestamp = localStorage.getItem(
        `workflow-${context.workflowName}-${context.nextStep?.nextStep}-seen`
      );
      if (!existingTimestamp) {
        localStorage.setItem(
          `workflow-${context.workflowName}-${context.nextStep?.nextStep}-seen`,
          Date.now().toString()
        );
      }
    },
  },
}).createMachine({
  id: "waitingStatus",
  initial: "fetchingWorkflowStatus",
  context: ({ input }) => ({
    workflowName: input.workflowName,
  }),
  states: {
    fetchingWorkflowStatus: {
      invoke: {
        src: "getWorkflowStatus",
        input: ({ context }) => ({
          workflowName: context.workflowName,
        }),
        onDone: [
          {
            guard: "isCompleted",
            target: "completed",
          },
          {
            guard: "isNotWaitingStep",
            target: "exit",
          },
          {
            guard: "isNseWaiting",
            target: "gettingNseStatus",
            actions: "assignNextStep",
          },
          {
            guard: "hasWaitedCurrentStepBefore",
            target: "takingTooLong",
          },
          {
            target: "polling",
            actions: "assignNextStep",
          },
        ],
        onError: {
          target: "error",
          actions: "assignError",
        },
      },
    },
    polling: {
      entry: "storeStepSeenTimestamp",
      invoke: {
        src: "polling",
        input: ({ context, self }) => ({
          runner: () => getWorkflowStatus(context.workflowName),
          guard: (result) => {
            return (
              result.nextStep !== context.nextStep?.nextStep ||
              result.workflowName !== context.workflowName ||
              result.workflowStatus === WorkflowStatus.COMPLETED
            );
          },
          parentRef: self,
          maxCount: 24,
          interval: 5000,
        }),
      },
      on: {
        LIMIT_REACHED: {
          target: "takingTooLong",
        },
        COMPLETED: [
          {
            guard: "isCompleted",
            target: "completed",
          },
          {
            guard: "isNotWaitingStep",
            target: "exit",
          },
          {
            guard: "isNseWaiting",
            target: "gettingNseStatus",
            actions: "assignNextStep",
          },
          {
            guard: "hasWaitedCurrentStepBefore",
            target: "takingTooLong",
            actions: "assignNextStep",
          },
          {
            target: "polling",
            actions: "assignNextStep",
          },
        ],
      },
    },
    gettingNseStatus: {
      invoke: {
        src: "getNseStatus",
        onDone: [
          {
            guard: "nseCompleted",
            target: "completed",
          },
          {
            guard: "nseFailed",
            target: "nseFailure",
          },
          {
            target: "takingTooLong",
            guard: "hasWaitedCurrentStepBefore",
          },
          {
            target: "nsePolling",
          },
        ],
        onError: {
          target: "error",
          actions: "assignError",
        },
      },
    },
    nsePolling: {
      entry: "storeStepSeenTimestamp",
      invoke: {
        src: "nsePolling",
        input: ({ self }) => ({
          runner: () => getNseStatus(),
          guard: (result) =>
            result.nseStatus === NseStatusResponse_NseStatus.SUCCESS ||
            result.nseStatus === NseStatusResponse_NseStatus.FAILED,
          parentRef: self,
          maxCount: 24,
          interval: 5000,
        }),
      },
      on: {
        LIMIT_REACHED: {
          target: "takingTooLong",
        },
        COMPLETED: [
          {
            target: "completed",
            guard: "nseCompleted",
          },
          {
            target: "nseFailure",
          },
        ],
      },
    },
    takingTooLong: {
      on: {
        RETRY: "polling",
      },
    },
    exit: {
      type: "final",
    },
    completed: {
      entry: "trackCompletion",
      type: "final",
    },
    nseFailure: {
      entry: "trackNseFailure",
      type: "final",
    },
    error: {
      on: {
        RETRY: "fetchingWorkflowStatus",
      },
    },
  },
});
