import LoadingPageContainer from "@/components/functional/loading-page-container";
import successImage from "@/assets/images/icons/purple-tick.svg";
import type { ReactNode } from "react";
import LinkButton from "@/components/ui/button/link-button";
import { supportLink } from "@/config/support";
import Button from "@/components/ui/button/button";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { useNavigate } from "react-router";
import { getHomeUrl } from "@/utils/routing";

export default function LimitReached({
  title = "Your onboarding is in progress",
  description = "It usually takes less than 10 minutes. We will reach out to you if any additional info is required.",
}: {
  title?: ReactNode;
  description?: ReactNode;
}) {
  const navigate = useNavigate();
  return (
    <LoadingPageContainer>
      <img src={successImage} alt="Success" className="m-0 h-12 w-12" />
      <div className="flex flex-col space-y-2">
        <p className="text-heading2">{title}</p>
        <p className="text-body1 text-black-50">{description}</p>
      </div>
      <FloatingFooterContent className="space-y-3 text-center md:-mx-5 md:-mb-5">
        <LinkButton href={supportLink}>Need help? Call us</LinkButton>
        <Button onClick={() => navigate(getHomeUrl(), { replace: true })}>
          Got it
        </Button>
      </FloatingFooterContent>
    </LoadingPageContainer>
  );
}
