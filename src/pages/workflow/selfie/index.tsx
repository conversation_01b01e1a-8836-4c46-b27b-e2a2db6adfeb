import { useCallback, useEffect } from "react";
import ConnectAnimation from "@/components/ui/connect-animation/connect-animation";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import useNavigate from "@/hooks/navigate";
import stableLogo from "@/assets/images/logos/stable_logo.webp";
import hypervergeLogo from "@/assets/images/logos/hyperverge.webp";
import Head from "@/components/ui/head";
import ErrorPage from "@/components/functional/error-page";
import LoadingPage from "@/components/functional/loading-page";
import LoadingPageContainer from "@/components/functional/loading-page-container";
import { useMachine } from "@xstate/react";
import { selfieMachine } from "./machine";
import { useParams } from "react-router";
import { createInspector } from "@/utils/xstate";

export default function WorkflowSelfiePage() {
  const navigate = useNavigate();
  const { slug } = useParams();
  const workflowName = getWorkflowNameFromSlug(slug!);

  const [
    {
      value: status,
      context: { error, nextStep },
    },
    send,
  ] = useMachine(selfieMachine, {
    input: { workflowName: workflowName },
    inspect: createInspector(selfieMachine.id),
  });

  const handleAnimationComplete = useCallback(() => {
    send({ type: "START_HYPERKYC" });
  }, [send]);

  useEffect(() => {
    if (nextStep) {
      navigate(getPathForWorkflowStep(workflowName, nextStep), {
        replace: true,
      });
    }
  }, [navigate, nextStep, workflowName]);

  if (status === "error") {
    const errorStatus = (error as { status: string }).status;
    const errorMessage =
      errorStatus === "user_cancelled" || errorStatus === "auto_declined"
        ? "Couldn't finish selfie verification?"
        : "Selfie verification failed";
    return (
      <ErrorPage
        error={error}
        title={errorMessage}
        description="Please try again. If the issue persists, contact our support team."
        onRetry={() => send({ type: "RETRY" })}
      />
    );
  }

  if (status === "checkingStatus") {
    return (
      <LoadingPage
        title="Completing your selfie verification"
        description="This usually takes a few moments. Please don't close this page."
      />
    );
  }

  return (
    <>
      <Head title="Selfie Verification" />
      <LoadingPageContainer>
        <ConnectAnimation
          status={
            status === "fetchingHypervergeCredentials"
              ? "connecting"
              : "connected"
          }
          onCompleted={handleAnimationComplete}
          firstEntity={
            <EntityLogo
              elevation="md"
              url={stableLogo}
              size="large"
              color="#FFFFFF"
              seamless
            />
          }
          secondEntity={
            <EntityLogo
              elevation="md"
              url={hypervergeLogo}
              size="large"
              color="#F4F1FF"
              seamless
            />
          }
        >
          <p className="px-5 text-center">
            Redirecting you to Hyperverge
            <br />
            for seamless Liveness check
          </p>
        </ConnectAnimation>
      </LoadingPageContainer>
    </>
  );
}
