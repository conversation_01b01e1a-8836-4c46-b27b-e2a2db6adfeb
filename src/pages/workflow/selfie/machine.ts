import {
  getHypervergeCredential,
  getHypervergeSelfieStatus,
} from "@/queries/workflow";
import { fromPromise, setup, assign } from "xstate";
import { startHyperKyc } from "./hyperkyc";
import { createPollingMachine } from "@/machines/polling";
import {
  WorkflowName,
  type ContinueWorkflowResponse,
  type SelfieInitiationResponse,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { pollingLimit } from "@/config/limits";

type SelfieStatusResponse = Awaited<
  ReturnType<typeof getHypervergeSelfieStatus>
>;

export const selfieMachine = setup({
  types: {
    context: {} as {
      workflowName: WorkflowName;
      hypervergeCredentials?: SelfieInitiationResponse;
      error?: unknown;
      nextStep?: ContinueWorkflowResponse;
    },
    input: {} as {
      workflowName: WorkflowName;
    },
    events: {} as
      | {
          type: "RETRY";
        }
      | {
          type: "START_HYPERKYC";
        }
      | {
          type: "LIMIT_REACHED";
          error?: unknown;
        }
      | {
          type: "COMPLETED";
          response: SelfieStatusResponse;
        },
    output: {} as SelfieStatusResponse,
  },
  actors: {
    getHypervergeCredentials: fromPromise(
      ({ input }: { input: { workflowName: WorkflowName } }) => {
        const transactionId = Math.floor(new Date().getTime() / 1000);
        return getHypervergeCredential(
          input.workflowName,
          transactionId.toString()
        );
      }
    ),
    startHyperKyc: fromPromise(
      ({ input }: { input: SelfieInitiationResponse }) => startHyperKyc(input)
    ),
    polling: createPollingMachine<SelfieStatusResponse>(
      "selfie-polling",
      "evaluating"
    ),
  },
}).createMachine({
  id: "selfie",
  initial: "fetchingHypervergeCredentials",
  context: ({ input }) => ({
    workflowName: input.workflowName,
  }),
  states: {
    fetchingHypervergeCredentials: {
      invoke: {
        src: "getHypervergeCredentials",
        input: ({ context }) => ({
          workflowName: context.workflowName,
        }),
        onDone: [
          {
            target: "success",
            actions: assign({
              nextStep: ({ event }) => event.output.nextStep,
            }),
            guard: ({ event }) => event.output.isComplete,
          },
          {
            target: "fetchedHypervergeCredentials",
            actions: assign({
              hypervergeCredentials: ({ event }) => event.output,
            }),
          },
        ],
        onError: {
          target: "error",
          actions: assign({
            error: ({ event }) => event.error,
          }),
        },
      },
    },
    fetchedHypervergeCredentials: {
      on: {
        START_HYPERKYC: "sdkOpen",
      },
    },
    sdkOpen: {
      invoke: {
        src: "startHyperKyc",
        input: ({ context }) => context.hypervergeCredentials!,
        onDone: "checkingStatus",
        onError: {
          target: "error",
          actions: assign({
            error: ({ event }) => event.error,
          }),
        },
      },
    },
    checkingStatus: {
      invoke: {
        src: "polling",
        input: ({ context, self }) => ({
          runner: () =>
            getHypervergeSelfieStatus(
              context.workflowName,
              context.hypervergeCredentials!.transactionId
            ),
          maxCount: pollingLimit,
          guard: (result) => !!result.isComplete,
          parentRef: self,
        }),
        onDone: {
          target: "success",
          actions: assign(({ event, context }) => {
            return {
              ...context,
              nextStep: event.output.nextStep,
            };
          }),
        },
      },
      on: {
        LIMIT_REACHED: {
          target: "error",
          actions: assign({
            error: ({ event }) => event.error,
          }),
        },
      },
    },
    success: {
      type: "final",
    },
    error: {
      output: ({ context }) => context.error,
      on: {
        RETRY: "fetchingHypervergeCredentials",
      },
    },
  },
});
