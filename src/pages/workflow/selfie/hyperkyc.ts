import { loadHyperVergeSDK } from "@/utils/sdk-loader";
import { trackEvent } from "@/utils/analytics";
import type { SelfieInitiationResponse } from "@/clients/gen/broking/WorkflowStep_pb";
import * as native from "@/utils/native-integration";

export async function startHyperKyc(hypervergeData: SelfieInitiationResponse) {
  trackEvent("selfie_hyperverge_sdk_opened", {
    id: hypervergeData.transactionId,
  });

  if (native.hyperKyc.isSupported()) {
    try {
      const result = await native.hyperKyc.launch(
        hypervergeData.accessToken,
        hypervergeData.workflowId,
        hypervergeData.transactionId,
        hypervergeData.selfie
      );
      trackEvent("selfie_hyperverge_status", result as Record<string, unknown>);
      return result;
    } catch (error) {
      trackEvent("selfie_hyperverge_status", error as Record<string, unknown>);
      throw error;
    }
  }

  return new Promise((resolve, reject) => {
    return loadHyperVergeSDK().then(() => {
      const hyperKycConfig = new window.HyperKycConfig!(
        hypervergeData.accessToken,
        hypervergeData.workflowId,
        hypervergeData.transactionId
      );
      hyperKycConfig.inputs["input_img"] = hypervergeData.selfie;
      window.HyperKYCModule!.launch(
        hyperKycConfig,
        (result: HyperKycResult) => {
          trackEvent("selfie_hyperverge_status", result);

          if (result.status === "auto_approved") {
            resolve(result);
          } else {
            reject(result);
          }
        }
      );
    });
  });
}
