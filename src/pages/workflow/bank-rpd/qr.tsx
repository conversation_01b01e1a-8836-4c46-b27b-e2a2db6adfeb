import { useState } from "react";
import { clsx } from "clsx";
import LinkButton from "@/components/ui/button/link-button";
import { trackEvent } from "@/utils/analytics";
import upiIcon from "@/assets/images/logos/upi.webp";

interface QRComponentProps {
  qrCode: string;
  refId: string;
  onStartPolling: (refId: string) => void;
}

export default function QRComponent({
  qrCode,
  refId,
  onStartPolling,
}: QRComponentProps) {
  const [canScan, setCanScan] = useState(false);

  function startScan() {
    trackEvent("scan_qr_code_clicked");
    setCanScan(true);
    onStartPolling(refId);
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex flex-col items-center gap-2">
        {canScan ? (
          <img src={upiIcon} alt="upi_logo" className="w-10" />
        ) : (
          <p className="text-black-50 text-body1 text-center">
            QR code will expire in{" "}
            <span className="text-black-80">60 seconds</span>
          </p>
        )}

        <div className="relative flex items-center justify-center">
          <div className="border-b-black-1 rounded-2xl border p-1">
            <img
              src={`data:image/png;base64,${qrCode}`}
              className={clsx("m-auto h-auto w-[160px]", !canScan && "blur-md")}
              alt="Scan"
            />
          </div>
          {!canScan && (
            <LinkButton className="absolute" type="button" onClick={startScan}>
              <p>Scan QR code</p>
            </LinkButton>
          )}
        </div>
      </div>
      <img
        src="https://assets.stablemoney.in/web-frontend/payment-options.webp"
        alt="payment_options"
        className="m-auto h-6 w-auto"
        crossOrigin="anonymous"
      />
    </div>
  );
}
