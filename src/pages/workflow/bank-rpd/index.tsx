import LinkButton from "@/components/ui/button/link-button";
import Separator from "@/components/ui/separator/separator";
import { trackEvent } from "@/utils/analytics";
import useNavigate from "@/hooks/navigate";
import { InitiateRpdResponse_PaymentApp } from "@/clients/gen/broking/BankAccountVerification_pb";
import QRComponent from "./qr";
import Head from "@/components/ui/head";
import successImage from "@/assets/images/icons/purple-tick.svg";
import { useMachine } from "@xstate/react";
import { rpdMachine } from "./machine";
import { useCallback, useEffect } from "react";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
  getWorkflowSlug,
} from "@/utils/workflow-routes";
import ErrorPage from "@/components/functional/error-page";
import { useMediaQuery } from "@react-hook/media-query";
import LoadingPage from "@/components/functional/loading-page";
import { useParams } from "react-router";
import { createInspector } from "@/utils/xstate";

function getPaymentAppName(app: InitiateRpdResponse_PaymentApp) {
  switch (app) {
    case InitiateRpdResponse_PaymentApp.PHONEPE:
      return "PhonePe";
    case InitiateRpdResponse_PaymentApp.GPAY:
      return "Google Pay";
    case InitiateRpdResponse_PaymentApp.PAYTM:
      return "Paytm";
    case InitiateRpdResponse_PaymentApp.BHIM:
      return "BHIM";
    case InitiateRpdResponse_PaymentApp.MISC:
      return "Other UPI apps";
    default:
      throw new Error("Invalid payment app");
  }
}

export default function WorkflowBankRpdPage() {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();
  const workflowName = getWorkflowNameFromSlug(slug!);
  const [state, send] = useMachine(rpdMachine, {
    input: { workflowName },
    inspect: createInspector(rpdMachine.id),
  });
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const startPolling = useCallback(
    () => send({ type: "START_POLLING" }),
    [send]
  );
  const retry = useCallback(() => send({ type: "RETRY" }), [send]);
  const handleAppLinkClick = useCallback(
    (upiIntent: string, appName: InitiateRpdResponse_PaymentApp) => {
      trackEvent("upi_app_clicked", {
        upiAppName: InitiateRpdResponse_PaymentApp[appName],
        refId: state.context.rpdData?.refId,
      });
      if (isDesktop) {
        startPolling();
      } else {
        navigate(
          `/workflow/${slug}/bank-rpd/${state.context.rpdData?.refId}?upiIntent=${encodeURIComponent(upiIntent)}`,
          {
            replace: true,
          }
        );
      }
    },
    [isDesktop, navigate, slug, startPolling, state.context.rpdData?.refId]
  );
  useEffect(() => {
    if (state.matches("completed")) {
      navigate(getPathForWorkflowStep(workflowName, state.context.nextStep), {
        replace: true,
      });
    }
  }, [state, navigate, workflowName]);

  if (state.matches("success")) {
    return (
      <>
        <Head title="Bank Verification - Success" />
        <div className="flex flex-col items-center justify-center space-y-6 p-5 py-12">
          <div className="flex flex-col items-center space-y-4">
            <img src={successImage} alt="Success" className="h-12 w-12" />
            <div className="space-y-2 text-center">
              <h1 className="text-heading1 text-green-600">
                Bank account verified!
              </h1>
              <p className="text-black-50 text-body1">
                Redirecting you to the next step...
              </p>
            </div>
          </div>
        </div>
      </>
    );
  }

  if (state.matches("error")) {
    return (
      <>
        <Head title="Bank Verification Failed" />
        <ErrorPage
          error={state.context.error}
          title={state.context.error ? undefined : "Bank verification failed"}
          description="Please try again. If the issue persists, contact our support team."
          onRetry={retry}
        />
      </>
    );
  }

  if (state.context.rpdData) {
    return (
      <>
        <Head title="Bank Verification - UPI" />
        <div className="space-y-6 p-5">
          <div className="space-y-3">
            <h1 className="text-heading1">
              <p className="hidden md:block">
                Verify your bank account with UPI
              </p>
              <p className="block md:hidden">Verify bank account with UPI</p>
            </h1>
            <p className="text-black-50 text-body1">
              Pay ₹1 via UPI from that account. The money will be refunded in 1
              day.
            </p>
          </div>
          <div className="flex gap-6 md:hidden">
            {state.context.rpdData?.paymentOptions.map((item) => (
              <a
                key={item.app}
                href={item.upiIntent}
                className="flex flex-1 flex-shrink-0 flex-col items-center gap-2"
                onClick={() => handleAppLinkClick(item.upiIntent, item.app)}
              >
                <img
                  src={item.logoUrl}
                  alt={`${item.app}_app`}
                  className="h-14 w-14 md:h-12 md:w-12"
                  crossOrigin="anonymous"
                />
                <p className="text-body1 text-center opacity-50">
                  {getPaymentAppName(item.app)}
                </p>
              </a>
            ))}
          </div>
          <div className="hidden md:block">
            {state.context.rpdData ? (
              <QRComponent
                qrCode={state.context.rpdData.qrCode}
                refId={state.context.rpdData.refId}
                onStartPolling={startPolling}
              />
            ) : null}
          </div>
          <Separator>
            <p className="text-body2 text-black-50">OR</p>
          </Separator>
          <div className="flex items-center justify-center">
            <LinkButton
              href={`/workflow/${getWorkflowSlug(workflowName)}/bank-pd`}
              onClick={() => trackEvent("enter_bank_details_manually")}
            >
              Enter account details manually
            </LinkButton>
          </div>
        </div>
      </>
    );
  }

  return <LoadingPage />;
}
