import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { retryPan, getAadharMatchStatusQueryOptions } from "@/queries/workflow";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import { trackEvent } from "@/utils/analytics";
import useNavigate from "@/hooks/navigate";
import { useMutation, useSuspenseQuery } from "@tanstack/react-query";
import aadhaarPanErrorImage from "@/assets/images/illustrations/aadhaar-pan-error-image.webp";
import Head from "@/components/ui/head";
import { useEffect } from "react";
import { queryClient } from "@/queries/client";
import { useParams, type LoaderFunctionArgs } from "react-router";

export async function loader({ params }: LoaderFunctionArgs) {
  const workflowName = getWorkflowNameFromSlug(params.slug!);
  await queryClient.fetchQuery(getAadharMatchStatusQueryOptions(workflowName));
}

export default function WorkflowAadharPanMatchPage() {
  const navigate = useNavigate();
  const { slug } = useParams();

  const workflowName = getWorkflowNameFromSlug(slug!);

  // Query to get Aadhaar PAN match status
  const { data: aadharMatchData } = useSuspenseQuery(
    getAadharMatchStatusQueryOptions(workflowName)
  );

  // Mutation for retrying PAN
  const retryPanMutation = useMutation({
    mutationFn: () => retryPan(workflowName),
    onSuccess: (responseData) => {
      trackEvent("aadhaar_match_retry_pan_clicked");
      navigate(getPathForWorkflowStep(workflowName, responseData.nextStep), {
        replace: true,
      });
    },
  });

  // Auto-navigate if Aadhaar PAN match is successful
  useEffect(() => {
    if (aadharMatchData && aadharMatchData.aadhaarPanMatchStatus) {
      navigate(getPathForWorkflowStep(workflowName, aadharMatchData.nextStep), {
        replace: true,
      });
    }
  }, [aadharMatchData, navigate, workflowName]);

  return (
    <>
      <Head title="Aadhaar PAN Mismatch" />
      <FormPage
        title={
          <>
            The name on your PAN <br />
            differs from name on Aadhaar
          </>
        }
        description="As per SEBI's guidelines, your personal details should be same across all documents"
        footer={
          <Button
            className="busy-visible"
            loading={retryPanMutation.isPending}
            disabled={retryPanMutation.isPending}
            onClick={() => retryPanMutation.mutate()}
          >
            Retry PAN
          </Button>
        }
      >
        <div className="relative">
          <div className="absolute top-[90px] left-[30px] flex flex-col gap-[30px]">
            <div className="flex flex-col gap-[3px]">
              <p className="text-body2 text-black-50">PAN NUMBER</p>
              <p className="text-body2">
                {aadharMatchData?.panDetails?.panNumber}
              </p>
            </div>
            <div className="flex flex-col gap-[3px]">
              <p className="text-body2 text-black-50">FULL NAME</p>
              <p className="decoration-red text-body2 uppercase underline decoration-wavy underline-offset-4">
                {aadharMatchData?.panDetails?.panName}
              </p>
            </div>
          </div>

          <img src={aadhaarPanErrorImage} alt="Error" />
          <div className="absolute bottom-[30px] left-[30px] flex flex-col gap-[30px]">
            <div className="flex flex-col gap-[3px]">
              <p className="text-body2 text-black-50">AADHAAR NUMBER</p>
              <p className="text-body2">
                {aadharMatchData?.aadhaarDetails?.aadhaarNumber}
              </p>
            </div>
            <div className="flex flex-col gap-[3px]">
              <p className="text-body2 text-black-50">FULL NAME</p>
              <p className="decoration-red text-body2 uppercase underline decoration-wavy underline-offset-4">
                {aadharMatchData?.aadhaarDetails?.aadhaarName}
              </p>
            </div>
          </div>
        </div>
      </FormPage>
    </>
  );
}
