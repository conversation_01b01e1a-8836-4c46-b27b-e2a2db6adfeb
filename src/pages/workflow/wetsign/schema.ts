import * as yup from "yup";

export const validationSchema = yup.object({
  signatureBase: yup.string().required("Signature is required to proceed"),
  pep: yup.boolean().required().oneOf([true], "Please confirm to proceed"),
  indianCitizen: yup
    .boolean()
    .required()
    .oneOf([true], "Indian citizenship is required to proceed"),
  sebi: yup.boolean().required().oneOf([true], "Please confirm to proceed"),
  raaDuration: yup.number().required("Duration is required"),
});

export type FormValues = yup.InferType<typeof validationSchema>;

export const stableDematValidationSchema = yup.object({
  signatureBase: yup.string().required("Signature is required to proceed"),
  pep: yup.boolean().required().oneOf([true], "Please confirm to proceed"),
  indianCitizen: yup
    .boolean()
    .required()
    .oneOf([true], "Indian citizenship is required to proceed"),
  sebi: yup.boolean().required().oneOf([true], "Please confirm to proceed"),
  isEdis: yup.boolean().required().oneOf([true], "Please confirm to proceed"),
  smsConsent: yup
    .boolean()
    .required()
    .oneOf([true], "Please confirm to proceed"),
  isUnlawful: yup
    .boolean()
    .required()
    .oneOf([true], "Please confirm to proceed"),
  sebiViolation: yup
    .boolean()
    .required()
    .oneOf([true], "Please confirm to proceed"),
  raaDuration: yup.number().required("Duration is required"),
});

export type StableDematFormValues = yup.InferType<
  typeof stableDematValidationSchema
>;
