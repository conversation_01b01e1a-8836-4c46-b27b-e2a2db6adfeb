import { useRef } from "react";
import SignatureCanvas from "react-signature-canvas";
import Field from "@/components/ui/field/field";
import Checkbox from "@/components/ui/checkbox/checkbox";
import Refresh from "@/components/icons/refresh";
import { RaaDuration } from "@/clients/gen/broking/Kyc_pb";
import { Field as FormikField, type FormikProps } from "formik";
import { type StableDematFormValues } from "./schema";
import Surface from "@/components/ui/surface/surface";
import { trackEvent } from "@/utils/analytics";

export default function DematFormFields({
  errors,
  setFieldValue,
  submitCount,
  values,
}: FormikProps<StableDematFormValues>) {
  const signatureRef = useRef<SignatureCanvas>(null);
  const handleSignatureEnd = () => {
    if (signatureRef.current) {
      const dataURL = signatureRef.current.toDataURL("image/png");
      setFieldValue("signatureBase", dataURL);
    }
  };
  const clearSignature = () => {
    trackEvent("wetsign_reset");
    if (signatureRef.current) {
      signatureRef.current.clear();
      setFieldValue("signatureBase", "");
    }
  };

  return (
    <>
      <Field error={submitCount > 0 ? errors.signatureBase : undefined}>
        <Surface elevation="md" className="relative">
          <SignatureCanvas
            ref={signatureRef}
            canvasProps={{
              className: "h-[350px] w-full",
            }}
            onEnd={handleSignatureEnd}
          />
          <button
            type="button"
            className="border-black-10 border-w-sm bg-black-3 absolute top-4 right-4 cursor-pointer rounded-xs p-1.5"
            onClick={clearSignature}
          >
            <Refresh className="block size-4" />
          </button>
          <div className="border-black-10 border-w-sm bg-black-3 absolute right-4 bottom-4 rounded-xs px-2 py-1">
            <label htmlFor="raaDuration" className="text-body2 text-black-60">
              Use till
            </label>
            <FormikField
              as="select"
              id="raaDuration"
              name="raaDuration"
              className="text-body2 outline-none"
            >
              <option value={RaaDuration.RAA_60_DAYS}>60 days</option>
              <option value={RaaDuration.RAA_90_DAYS}>90 days</option>
            </FormikField>
          </div>
        </Surface>
      </Field>
      <Field error={submitCount > 0 ? errors.pep : undefined}>
        <FormikField name="pep" as={Checkbox} checked={values.pep}>
          I am not a PEP (Politically exposed person) or related to PEP
        </FormikField>
      </Field>

      <Field error={submitCount > 0 ? errors.indianCitizen : undefined}>
        <FormikField
          name="indianCitizen"
          as={Checkbox}
          checked={values.indianCitizen}
        >
          I am an Indian citizen, born and residing in India
        </FormikField>
      </Field>

      <Field error={submitCount > 0 ? errors.sebi : undefined}>
        <FormikField name="sebi" as={Checkbox} checked={values.sebi}>
          There has been no action initiated/ taken against me for violation of
          applicable SEBI regulations
        </FormikField>
      </Field>

      <Field error={submitCount > 0 ? errors.isEdis : undefined}>
        <FormikField name="isEdis" as={Checkbox} checked={values.isEdis}>
          I wish to receive all future documents and statements in: Electronic
          form and I prefer e-DIS over physical DIS Booklet.
        </FormikField>
      </Field>

      <Field error={submitCount > 0 ? errors.smsConsent : undefined}>
        <FormikField
          name="smsConsent"
          as={Checkbox}
          checked={values.smsConsent}
        >
          I request you to enroll me for SMS and Email facility, offered by the
          Exchanges and CDSL.
        </FormikField>
      </Field>

      <Field error={submitCount > 0 ? errors.isUnlawful : undefined}>
        <FormikField
          name="isUnlawful"
          as={Checkbox}
          checked={values.isUnlawful}
        >
          I/We undersigned hereby declare that I/We have not been involved in
          any unlawful activities and I have not been declared a defaulter or my
          name is not appearing in the defaulter database as per SEBI/ Various
          Exchange/ Regulatory bodies, etc. I further declare that the above
          mentioned information is provided by me and is true and correct.
        </FormikField>
      </Field>

      <Field error={submitCount > 0 ? errors.sebiViolation : undefined}>
        <FormikField
          name="sebiViolation"
          as={Checkbox}
          checked={values.sebiViolation}
        >
          There has been no action initiated/ taken against me for violation of
          applicable SEBI regulations
        </FormikField>
      </Field>
    </>
  );
}
