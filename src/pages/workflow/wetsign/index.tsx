import { use<PERSON>ara<PERSON>, useNavigate } from "react-router";
import { Formik, Form } from "formik";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import Head from "@/components/ui/head";
import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { trackEvent } from "@/utils/analytics";
import { getErrorMessage, withErrorToast } from "@/utils/errors";
import { useViewEvent } from "@/hooks/view-event";
import { stableDematValidationSchema, validationSchema } from "./schema";
import BaseFormFields from "./base-fields";
import DematFormFields from "./demat-fields";
import { WorkflowName } from "@/clients/gen/broking/WorkflowStep_pb";
import { uploadWetSignature } from "@/queries/workflow";
import { RaaDuration } from "@/clients/gen/broking/Kyc_pb";

function DematWetsignForm({ workflowName }: { workflowName: WorkflowName }) {
  const navigate = useNavigate();

  return (
    <Formik
      initialValues={{
        signatureBase: "",
        pep: true,
        indianCitizen: true,
        sebi: true,
        isEdis: true,
        smsConsent: true,
        isUnlawful: true,
        sebiViolation: true,
        raaDuration: RaaDuration.RAA_60_DAYS,
      }}
      validationSchema={stableDematValidationSchema}
      validateOnMount
      onSubmit={withErrorToast(async function handleSubmit(
        values,
        { setFieldError }
      ) {
        try {
          if (!values.signatureBase) {
            setFieldError("signatureBase", "Signature is required to proceed");
            return;
          }

          const fd = new FormData();
          const imageBlob = await fetch(values.signatureBase).then((res) =>
            res.blob()
          );
          fd.append("file", imageBlob, "signature.png");
          fd.append("file-type", "WET_SIGNATURE_DOCUMENT_TYPE");

          const responseData = await uploadWetSignature(
            fd,
            values.raaDuration,
            workflowName
          );
          trackEvent("wetsign_submit_clicked");
          navigate(
            getPathForWorkflowStep(workflowName, responseData.nextStep),
            {
              replace: true,
            }
          );
        } catch (error) {
          const errorMessage = await getErrorMessage(error);
          setFieldError("signatureBase", errorMessage);
          throw error;
        }
      })}
    >
      {(formState) => (
        <Form id="wetsign-form">
          <FormPage
            title="Verify your signature"
            description="Please sign below to complete your account opening"
            footer={
              <Button
                type="submit"
                form="wetsign-form"
                loading={formState.isSubmitting}
              >
                Proceed
              </Button>
            }
          >
            <DematFormFields {...formState} />
          </FormPage>
        </Form>
      )}
    </Formik>
  );
}

function BaseWetsignForm({ workflowName }: { workflowName: WorkflowName }) {
  const navigate = useNavigate();

  return (
    <Formik
      initialValues={{
        signatureBase: "",
        pep: true,
        indianCitizen: true,
        sebi: true,
        raaDuration: RaaDuration.RAA_60_DAYS,
      }}
      validationSchema={validationSchema}
      validateOnMount
      onSubmit={withErrorToast(async function handleSubmit(
        values,
        { setFieldError }
      ) {
        try {
          if (!values.signatureBase) {
            setFieldError("signatureBase", "Signature is required to proceed");
            return;
          }

          const fd = new FormData();
          const imageBlob = await fetch(values.signatureBase).then((res) =>
            res.blob()
          );
          fd.append("file", imageBlob, "signature.png");
          fd.append("file-type", "WET_SIGNATURE_DOCUMENT_TYPE");

          const responseData = await uploadWetSignature(
            fd,
            values.raaDuration,
            workflowName
          );
          trackEvent("wetsign_submit_clicked");
          navigate(
            getPathForWorkflowStep(workflowName, responseData.nextStep),
            {
              replace: true,
            }
          );
        } catch (error) {
          const errorMessage = await getErrorMessage(error);
          setFieldError("signatureBase", errorMessage);
          throw error;
        }
      })}
    >
      {(formState) => (
        <Form id="wetsign-form">
          <FormPage
            title="Verify your signature"
            description="Please sign below to complete your account opening"
            footer={
              <Button
                type="submit"
                form="wetsign-form"
                loading={formState.isSubmitting}
              >
                Proceed
              </Button>
            }
          >
            <BaseFormFields {...formState} />
          </FormPage>
        </Form>
      )}
    </Formik>
  );
}

export default function WorkflowWetsignPage() {
  const { slug } = useParams<{ slug: string }>();
  const workflowName = getWorkflowNameFromSlug(slug!);

  const { ref } = useViewEvent({
    eventName: "wet_sign_page",
  });

  const isDematWorkflow =
    workflowName === WorkflowName.DEMAT_ACCOUNT_OPENING ||
    workflowName === WorkflowName.TRADING_DEMAT_ACCOUNT_OPENING;

  return (
    <>
      <Head title="Wet Signature" />
      <div ref={ref}>
        {isDematWorkflow ? (
          <DematWetsignForm workflowName={workflowName} />
        ) : (
          <BaseWetsignForm workflowName={workflowName} />
        )}
      </div>
    </>
  );
}
