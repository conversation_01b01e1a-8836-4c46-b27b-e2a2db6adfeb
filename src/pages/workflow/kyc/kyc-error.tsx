import ErrorPage from "@/components/functional/error-page";
import { getErrorMessage } from "@/utils/errors";
import { useState } from "react";

const convertErrorMessage = (result?: string) => {
  if (!result) {
    return {
      title: "Aadhaar verification failed",
      description: "You can retry and verify <PERSON><PERSON><PERSON><PERSON> to get  investment ready",
    };
  }
  if (
    result.includes("Cancelled By User") ||
    result.includes("User cancelled before completion") ||
    result.includes("KYC Process Cancelled")
  ) {
    return {
      title: "Couldn't finish Aadhaar verification?",
      description:
        " It’s completely safe, and required to get you investment ready. ",
    };
  } else if (result.includes("Unable to load Webview. Please retry")) {
    return {
      title: "<PERSON><PERSON><PERSON><PERSON> didn’t respond, please try again",
      description:
        "You can retry and verify <PERSON><PERSON><PERSON><PERSON> while we check what went wrong",
    };
  }
  return {
    title: "Aadhaar verification failed",
    description: "You can retry and verify <PERSON>ad<PERSON>ar to get  investment ready",
  };
};

type KycErrorPageProps = {
  error?: unknown;
  onRetry?: () => void;
};

export default function KycError({ error, onRetry }: KycErrorPageProps) {
  const [title, setTitle] = useState("Aadhaar verification failed");
  const [description, setDescription] = useState(
    "You can retry and verify Aadhaar to get  investment ready"
  );
  getErrorMessage(error).then((result) => {
    const { title, description } = convertErrorMessage(result);
    setTitle(title);
    setDescription(description);
  });
  return (
    <ErrorPage
      error={error}
      title={title}
      description={description}
      onRetry={onRetry}
    />
  );
}
