import {
  type InitiateDigioKycResponse,
  type WorkflowName,
  type ContinueWorkflowResponse,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { getDigioCredential, getDigioStatus } from "@/queries/workflow";
import { fromPromise, setup, assign, type DoneActorEvent } from "xstate";
import { startDigio } from "./digio";
import { createPollingMachine } from "@/machines/polling";

type CheckDigioKycStatusResponse = Awaited<ReturnType<typeof getDigioStatus>>;

export const kycMachine = setup({
  types: {
    context: {} as {
      workflowName: WorkflowName;
      digioCredentials?: InitiateDigioKycResponse;
      error?: unknown;
      nextStep?: ContinueWorkflowResponse;
    },
    input: {} as {
      workflowName: WorkflowName;
    },
    events: {} as
      | {
          type: "RETRY";
        }
      | {
          type: "START_DIGIO";
        }
      | {
          type: "COMPLETED";
          response: CheckDigioKycStatusResponse;
        }
      | DoneActorEvent<InitiateDigioKycResponse>
      | {
          type: "LIMIT_REACHED";
          error?: unknown;
        },
    output: {} as CheckDigioKycStatusResponse,
  },
  guards: {
    isKycComplete: ({ event }) => {
      return "output" in event && event.output.isComplete;
    },
    hasDigioCredentials: ({ context }) => {
      return !!context.digioCredentials;
    },
  },
  actions: {
    assignNextStepFromCredentials: assign({
      nextStep: ({ event }) => {
        if ("output" in event && event.output.nextStep) {
          return event.output.nextStep;
        }
      },
    }),
    assignDigioCredentials: assign({
      digioCredentials: ({ event }) => {
        if ("output" in event) {
          return event.output;
        }
      },
    }),
    assignCredentialsError: assign({
      error: ({ event }) => {
        if ("error" in event) {
          return event.error;
        }
        return undefined;
      },
    }),
    assignError: assign({
      error: ({ event }) => {
        if ("error" in event) {
          return event.error;
        }
      },
    }),
    assignNextStepFromPolling: assign(({ event, context }) => {
      if ("output" in event) {
        return {
          ...context,
          nextStep: event.output.nextStep,
        };
      }
      return context;
    }),
  },
  actors: {
    getDigioCredentials: fromPromise(
      ({ input }: { input: { workflowName: WorkflowName } }) =>
        getDigioCredential(input.workflowName)
    ),
    startDigio: fromPromise(({ input }: { input: InitiateDigioKycResponse }) =>
      startDigio(input)
    ),
    polling: createPollingMachine<CheckDigioKycStatusResponse>(
      "kyc-polling",
      "evaluating"
    ),
  },
}).createMachine({
  id: "kyc",
  initial: "fetchingDigioCredentials",
  context: ({ input }) => ({
    workflowName: input.workflowName,
  }),
  states: {
    fetchingDigioCredentials: {
      invoke: {
        src: "getDigioCredentials",
        input: ({ context }) => ({ workflowName: context.workflowName }),
        onDone: [
          {
            target: "success",
            actions: "assignNextStepFromCredentials",
            guard: "isKycComplete",
          },
          {
            target: "fetchedDigioCredentials",
            actions: "assignDigioCredentials",
          },
        ],
        onError: {
          target: "error",
          actions: "assignCredentialsError",
        },
      },
    },
    fetchedDigioCredentials: {
      on: {
        START_DIGIO: "sdkOpen",
      },
    },
    sdkOpen: {
      invoke: {
        src: "startDigio",
        input: ({ context }) => context.digioCredentials!,
        onDone: {
          target: "checkingStatus",
        },
        onError: {
          target: "error",
          actions: "assignError",
        },
      },
    },
    checkingStatus: {
      invoke: {
        src: "polling",
        input: ({ context, self }) => ({
          runner: () => getDigioStatus(context.workflowName),
          guard: (result) =>
            result.kycStatus === "approved" ||
            result.kycStatus === "approval_pending",
          parentRef: self,
        }),
        onDone: {
          target: "success",
          actions: "assignNextStepFromPolling",
        },
      },
      on: {
        LIMIT_REACHED: {
          target: "error",
          actions: "assignError",
        },
      },
    },
    success: {
      type: "final",
    },
    error: {
      output: ({ context }) => context.error,
      on: {
        RETRY: "fetchingDigioCredentials",
      },
    },
  },
});
