import { loadDigioSDK } from "@/utils/sdk-loader";
import { trackEvent } from "@/utils/analytics";
import type { InitiateDigioKycResponse } from "@/clients/gen/broking/WorkflowStep_pb";
import * as native from "@/utils/native-integration";

export async function startDigio(digioData: InitiateDigioKycResponse) {
  trackEvent("digilocker_initiated");

  if (native.digioKyc.isSupported()) {
    try {
      const result = await native.digioKyc.submit(
        digioData.id,
        digioData.customerIdentifier,
        digioData.accessToken
      );
      trackEvent("digilocker_success", result as Record<string, unknown>);
      return result;
    } catch (error) {
      trackEvent("digilocker_failure", error as Record<string, unknown>);
      throw error;
    }
  }

  await loadDigioSDK();
  return new Promise((resolve, reject) => {
    const digio = new window.Digio!({
      environment: "production",
      callback: (result) => {
        if ("error_code" in result) {
          trackEvent("digilocker_failure", result);
          reject(result);
        } else {
          trackEvent("digilocker_success", result);
          resolve(result);
        }
      },
      logo: "https://assets.stablemoney.in/web-frontend/v1/stablemoney-black-textlogo.svg",
      theme: { primaryColor: "#6A768A", secondaryColor: "#000000" },
      is_iframe: true,
      method: "otp",
    });
    digio.init();
    digio.submit(
      digioData.id,
      digioData.customerIdentifier,
      digioData.accessToken
    );
  });
}
