import { useCallback, useEffect } from "react";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import useNavigate from "@/hooks/navigate";
import Head from "@/components/ui/head";
import LoadingPage from "@/components/functional/loading-page";
import ConnectAnimation from "@/components/ui/connect-animation/connect-animation";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import stableLogo from "@/assets/images/logos/stable_logo.webp";
import digilockerLogo from "@/assets/images/logos/digilocker.webp";
import LoadingPageContainer from "@/components/functional/loading-page-container";
import { kycMachine } from "./machine";
import { useMachine } from "@xstate/react";
import { useParams } from "react-router";
import KycError from "./kyc-error";
import { createInspector } from "@/utils/xstate";

export default function WorkflowKycPage() {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();
  const workflowName = getWorkflowNameFromSlug(slug!);
  const [
    {
      value: status,
      context: { error, nextStep },
    },
    send,
  ] = useMachine(kycMachine, {
    input: { workflowName },
    inspect: createInspector(kycMachine.id),
  });
  const handleAnimationComplete = useCallback(() => {
    send({ type: "START_DIGIO" });
  }, [send]);
  useEffect(() => {
    if (nextStep) {
      navigate(getPathForWorkflowStep(workflowName, nextStep), {
        replace: true,
      });
    }
  }, [navigate, nextStep, workflowName]);

  if (status === "error") {
    return <KycError error={error} onRetry={() => send({ type: "RETRY" })} />;
  }

  if (status === "checkingStatus") {
    return (
      <LoadingPage
        title="Completing your KYC verification"
        description="This usually takes a few moments. Please don't close this page."
      />
    );
  }

  return (
    <>
      <Head title="KYC Verification" />
      <LoadingPageContainer>
        <ConnectAnimation
          status={
            status === "fetchedDigioCredentials" ? "connected" : "connecting"
          }
          onCompleted={handleAnimationComplete}
          firstEntity={
            <EntityLogo
              elevation="md"
              url={stableLogo}
              size="large"
              color="#FFFFFF"
              seamless
            />
          }
          secondEntity={
            <EntityLogo
              elevation="md"
              url={digilockerLogo}
              size="large"
              color="#F4F1FF"
              seamless
            />
          }
        >
          <p className="px-5 text-center">
            Redirecting you to Digilocker
            <br />
            for seamless KYC verification
          </p>
        </ConnectAnimation>
      </LoadingPageContainer>
    </>
  );
}
