import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import LinkButton from "@/components/ui/button/link-button";
import { Formik } from "formik";
import {
  GuardianDetailsSchema,
  NomineeDetailsSchema,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { AddressProtoSchema } from "@/clients/gen/broking/Common_pb";
import { saveNomineeDetails, skipNominee } from "@/queries/workflow";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import { trackEvent } from "@/utils/analytics";
import { getErrorMessage, withErrorToast } from "@/utils/errors";
import { create } from "@bufbuild/protobuf";
import useNavigate from "@/hooks/navigate";
import { useMutation } from "@tanstack/react-query";
import Head from "@/components/ui/head";
import {
  isMinor,
  isOtherRelationship,
  validationSchema,
  type FormValues,
} from "./schema";
import FormFields from "./fields";
import { toaster } from "@/components/ui/toast/store";
import Form from "@/components/functional/form";
import { RelationshipType } from "@/clients/gen/broking/Nominee_pb";
import { useParams } from "react-router";

export default function WorkflowNomineePage() {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();
  const workflowName = getWorkflowNameFromSlug(slug!);

  const skipNomineeMutation = useMutation({
    mutationFn: skipNominee,
    onSuccess: (responseData) => {
      navigate(getPathForWorkflowStep(workflowName, responseData.nextStep), {
        replace: true,
      });
    },
    onError: async (error) => {
      toaster.create({
        type: "error",
        description: await getErrorMessage(error),
      });
    },
  });

  function handleSkipNominee() {
    trackEvent("nominee_skip");
    skipNomineeMutation.mutate(workflowName);
  }

  return (
    <>
      <Head title="Nominee Details" />
      <Formik
        initialValues={
          {
            nomineeName: "",
            nomineeRelationship: "",
            nomineeCustomRelationship: "",
            nomineeDob: "",
            guardianName: "",
            guardianRelationship: "",
            guardianCustomRelationship: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(values) {
          const responseData = await saveNomineeDetails(
            [
              create(NomineeDetailsSchema, {
                name: values.nomineeName,
                relationshipType: +values.nomineeRelationship,
                relationship: isOtherRelationship(values.nomineeRelationship)
                  ? values.nomineeCustomRelationship || ""
                  : "",
                dob: values.nomineeDob,
                guardianDetails: isMinor(values.nomineeDob)
                  ? create(GuardianDetailsSchema, {
                      name: values.guardianName as string,
                      relationshipType: +(
                        values.guardianRelationship ??
                        RelationshipType.UNKNOWN_RELATIONSHIP
                      ),
                      relationship: isOtherRelationship(
                        values.guardianRelationship
                      )
                        ? values.guardianCustomRelationship || ""
                        : "",
                      address: create(AddressProtoSchema, {
                        addressLine1: "Not applicable",
                        addressLine2: "Not applicable",
                        addressLine3: "Not applicable",
                        city: "Not applicable",
                        country: "India",
                        state: "NA",
                        postCode: "000000",
                      }),
                    })
                  : undefined,
                allocationPercentage: 100,
                identifier: "",
                identifierType: undefined,

                address: create(AddressProtoSchema, {
                  addressLine1: "NA",
                  addressLine2: "NA",
                  addressLine3: "NA",
                  city: "NA",
                  state: "NA",
                  country: "India",
                  postCode: "000000",
                }),
              }),
            ],
            workflowName
          );
          navigate(
            getPathForWorkflowStep(workflowName, responseData.nextStep),
            {
              replace: true,
            }
          );
        })}
      >
        {(formState) => {
          return (
            <Form id="nominee-form">
              <FormPage
                title="Assign a nominee"
                description="Bonds transfer to nominee upon your passing away"
                footer={
                  <div className="space-y-2 text-center">
                    <LinkButton
                      onClick={handleSkipNominee}
                      type="button"
                      loading={skipNomineeMutation.isPending}
                    >
                      Skip nominee
                    </LinkButton>
                    <Button
                      type="submit"
                      form="nominee-form"
                      loading={formState.isSubmitting}
                      disabled={formState.isSubmitting}
                    >
                      Proceed
                    </Button>
                  </div>
                }
              >
                <FormFields {...formState} />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
