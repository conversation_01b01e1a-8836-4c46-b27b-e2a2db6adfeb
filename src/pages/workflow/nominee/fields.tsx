import Input from "@/components/ui/input/input";
import Select from "@/components/ui/select/select";
import { RelationshipType } from "@/clients/gen/broking/Nominee_pb";
import {
  Field as FormikField,
  type FormikProps,
  type FieldProps,
} from "formik";
import {
  isMinor,
  isOtherRelationship,
  relationshipOptions,
  type FormValues,
} from "./schema";

export default function FormFields({
  errors,
  values,
  submitCount,
}: FormikProps<FormValues>) {
  const isNomineeCustomRelationshipFieldVisible =
    +values.nomineeRelationship === RelationshipType.OTHER;
  const isNomineeMinor = isMinor(values.nomineeDob);
  const showGuardianCustomRelationship =
    isNomineeMinor && isOtherRelationship(values.guardianRelationship);

  return (
    <>
      <FormikField
        name="nomineeName"
        label="Nominee's full name"
        as={Input}
        error={submitCount > 0 ? errors.nomineeName : undefined}
      />

      <FormikField name="nomineeRelationship">
        {({ field, form }: FieldProps) => (
          <Select
            id={`${field.name}-select`}
            label="Nominee's relationship"
            error={submitCount > 0 ? errors.nomineeRelationship : undefined}
            value={field.value ? [field.value] : []}
            onValueChange={(details) =>
              form.setFieldValue(field.name, details.value[0])
            }
            items={relationshipOptions}
          />
        )}
      </FormikField>

      {isNomineeCustomRelationshipFieldVisible && (
        <FormikField
          name="nomineeCustomRelationship"
          label="Nominee's relationship"
          as={Input}
          error={submitCount > 0 ? errors.nomineeCustomRelationship : undefined}
        />
      )}

      <FormikField
        name="nomineeDob"
        label="Nominee's date of birth"
        as={Input}
        mask="date"
        placeholder="DD/MM/YYYY"
        error={submitCount > 0 ? errors.nomineeDob : undefined}
      />

      {isNomineeMinor && (
        <>
          <p className="text-black-50">
            Nominee is minor, please add guardian name.
          </p>
          <FormikField
            name="guardianName"
            label="Guardian's full name"
            as={Input}
            error={submitCount > 0 ? errors.guardianName : undefined}
            autoFocus={isNomineeMinor}
          />

          <FormikField name="guardianRelationship">
            {({ field, form }: FieldProps) => (
              <Select
                id={`${field.name}-select`}
                label="Guardian's relationship"
                error={
                  submitCount > 0 ? errors.guardianRelationship : undefined
                }
                value={field.value ? [field.value] : []}
                onValueChange={(details) =>
                  form.setFieldValue(field.name, details.value[0])
                }
                items={relationshipOptions}
              />
            )}
          </FormikField>

          {showGuardianCustomRelationship && (
            <FormikField
              name="guardianCustomRelationship"
              label="Guardian's relationship"
              as={Input}
              data-testid="guardian-custom-relationship"
              error={
                submitCount > 0 ? errors.guardianCustomRelationship : undefined
              }
            />
          )}
        </>
      )}
    </>
  );
}
