import { RelationshipType } from "@/clients/gen/broking/Nominee_pb";
import { extractEnums } from "@/utils/enums";
import dayjs from "dayjs";
import * as yup from "yup";

export const relationshipOptions = extractEnums(RelationshipType).map(
  (option) => ({
    label: option.key,
    value: option.value.toString(),
  })
);

export const validationSchema = yup.object({
  nomineeName: yup
    .string()
    .required("Nominee name is required")
    .min(2, "Nominee name must be at least 2 characters")
    .test("lastNameCheck", "Name should contain last name", (value) => {
      return value.split(/\s+/).length > 1;
    }),
  nomineeRelationship: yup
    .string()
    .required("Nominee relationship is required")
    .oneOf(
      relationshipOptions.map((option) => option.value.toString()),
      "Nominee relationship is required"
    ),
  nomineeCustomRelationship: yup.string().when("nomineeRelationship", {
    is: isOtherRelationship,
    then: (schema) =>
      schema
        .required("Nominee's relation is required")
        .min(2, "Nominee's relation must be at least 2 characters"),
  }),
  nomineeDob: yup
    .string()
    .required("Nominee date of birth is required")
    .test(
      "is-valid-date",
      "Please enter a valid date in DD/MM/YYYY format",
      (val) => {
        return dayjs(val, "DD/MM/YYYY", true).isValid();
      }
    ),
  guardianName: yup.string().when("nomineeDob", {
    is: isMinor,
    then: (schema) =>
      schema
        .required("Guardian name is required")
        .min(2, "Guardian name must be at least 2 characters")
        .test("lastNameCheck", "Name should contain last name", (value) => {
          return value.split(/\s+/).length > 1;
        }),
  }),
  guardianRelationship: yup.string().when("nomineeDob", {
    is: isMinor,
    then: (schema) =>
      schema.required("Guardian relationship is required").oneOf(
        relationshipOptions.map((option) => option.value.toString()),
        "Guardian relationship is required"
      ),
  }),
  guardianCustomRelationship: yup
    .string()
    .when(["nomineeDob", "guardianRelationship"], {
      is: (nomineeDob: string, guardianRelationship: string) =>
        isMinor(nomineeDob) && isOtherRelationship(guardianRelationship),
      then: (schema) =>
        schema
          .required("Guardian's relation is required")
          .min(2, "Guardian's relation must be at least 2 characters"),
    }),
});

export type FormValues = yup.InferType<typeof validationSchema>;

export function isMinor(dob: string): boolean {
  if (!dob || !dayjs(dob, "DD/MM/YYYY", true).isValid()) {
    return false;
  }
  const now = dayjs();
  const birthDate = dayjs(dob, "DD/MM/YYYY");
  const age = now.diff(birthDate, "years");
  return age < 18;
}

export function isOtherRelationship(
  value: number | string | undefined
): boolean {
  if (value === undefined) {
    return false;
  }
  const relationship = +value;
  return relationship === RelationshipType.OTHER;
}
