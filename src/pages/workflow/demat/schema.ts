import * as yup from "yup";

export const validationSchema = yup.object({
  dematType: yup
    .string()
    .required("Please select a demat account option")
    .oneOf(["stable", "existing"], "Invalid demat account option"),

  demat: yup
    .string()
    .transform((value) => (value ? value.toUpperCase() : value))
    .when("dematType", {
      is: "existing",
      then: (schema) =>
        schema
          .required("Demat ID is required")
          .length(16, "Demat ID must be 16 characters long"),
      otherwise: (schema) => schema.notRequired(),
    }),

  dematConfirmation: yup
    .string()
    .transform((value) => (value ? value.toUpperCase() : value))
    .when("dematType", {
      is: "existing",
      then: (schema) =>
        schema
          .required("Confirm Demat ID is required")
          .length(16, "Confirm Demat ID must be 16 characters long")
          .test("demat-match", "Demat IDs do not match", function (value) {
            return value === this.parent.demat;
          }),
      otherwise: (schema) => schema.notRequired(),
    }),
});

export type FormValues = yup.InferType<typeof validationSchema>;
