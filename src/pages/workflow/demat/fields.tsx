import Input from "@/components/ui/input/input";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import QueryRenderer from "@/components/functional/query-renderer";
import { getDematProviderDetails } from "@/queries/workflow";
import { useQuery } from "@tanstack/react-query";
import { Field, type FormikProps } from "formik";
import { type FormValues } from "./schema";
import { useEffect, useRef } from "react";

function preventPaste(e: React.ClipboardEvent) {
  e.preventDefault();
}

export default function FormFields({
  errors,
  values,
  submitCount,
}: FormikProps<FormValues>) {
  const formFieldsRef = useRef<HTMLDivElement>(null);
  const dematProviderQuery = useQuery({
    queryKey: ["demat-provider", values.demat],
    queryFn: () => getDematProviderDetails(values.demat!),
    enabled: (values.demat?.length ?? 0) >= 8,
  });

  useEffect(() => {
    if (values.dematType === "existing" && formFieldsRef.current) {
      setTimeout(() => {
        formFieldsRef.current?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  }, [values.dematType]);

  return (
    <div ref={formFieldsRef} className="space-y-3">
      <Field
        name="demat"
        label="16 digit Demat ID"
        as={Input}
        type="password"
        error={submitCount > 0 ? errors.demat : undefined}
        onPaste={preventPaste}
        autoFocus
        suffix={
          <QueryRenderer query={dematProviderQuery}>
            {(dematProvider) =>
              dematProvider ? (
                <EntityLogo
                  url={dematProvider.iconUrl}
                  size="small"
                  color="#FFF6EE"
                  elevation="md"
                  seamless
                />
              ) : null
            }
          </QueryRenderer>
        }
      />

      <Field
        name="dematConfirmation"
        label="Confirm 16 digit demat ID"
        as={Input}
        error={submitCount > 0 ? errors.dematConfirmation : undefined}
      />
    </div>
  );
}
