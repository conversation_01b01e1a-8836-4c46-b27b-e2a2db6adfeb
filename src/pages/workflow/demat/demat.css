.checkmark {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(var(--spacing) * 4);
  width: calc(var(--spacing) * 4);
  background-color: var(--color-white);
  border: var(--border-w-md) solid var(--color-black-20);
  border-radius: 80%;
  flex-shrink: 0;
}
.checkmark.selected {
  border: var(--border-w-md) solid var(--color-black-80);
}
.checkmark:after {
  content: "";
  display: none;
}
.selected.checkmark:after {
  display: block;
}
.checkmark:after {
  width: calc(var(--spacing) * 3);
  height: calc(var(--spacing) * 3);
  border-radius: 50%;
  background: var(--color-black-80);
}
