import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { Formik } from "formik";
import { addDematAccount } from "@/queries/workflow";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import { trackEvent } from "@/utils/analytics";
import { withErrorToast, getErrorMessage } from "@/utils/errors";
import { HttpCallException } from "@/exceptions/http-call-exception";
import useNavigate from "@/hooks/navigate";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import tick from "@/assets/images/icons/white-tick.svg";
import Tag from "@/components/ui/tag/tag";
import clsx from "clsx";
import "./demat.css";
import { useParams } from "react-router";
import { useQuery } from "@tanstack/react-query";
import { isEnabled } from "@/utils/feature-flags";
import OldDematPage from "./old";
import LoadingPage from "@/components/functional/loading-page";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import FindByBrokerPage from "./find-by-broker";
import LinkButton from "@/components/ui/button/link-button";

function WorkflowDematPage() {
  const navigate = useNavigate();
  const { slug } = useParams<{ slug: string }>();

  const workflowName = getWorkflowNameFromSlug(slug!);

  const ourDematBenefits = [
    "Sell bond anytime - coming soon",
    "Instant account opening",
    "Easy tracking of investments",
  ];
  const anotherBenefits = [
    "Units credited to your existing demat a/c",
    "Premature bond sale not available",
  ];
  return (
    <>
      <Head title="Demat Account" />
      <Formik
        initialValues={
          {
            dematType: "stable",
            demat: "",
            dematConfirmation: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(
          values,
          { setFieldError }
        ) {
          try {
            const responseData = await addDematAccount(
              workflowName,
              values.dematType === "stable",
              values.demat || ""
            );
            trackEvent("verify_demat_clicked");
            if (responseData.nextStep) {
              navigate(
                getPathForWorkflowStep(workflowName, responseData.nextStep),
                {
                  replace: true,
                }
              );
            }
          } catch (error) {
            if (
              error instanceof HttpCallException &&
              error.response.status === 400
            ) {
              const errorMessage = await getErrorMessage(error);
              setFieldError("demat", errorMessage);
              setFieldError("dematConfirmation", errorMessage);
            } else {
              throw error;
            }
          }
        })}
      >
        {(formState) => {
          return (
            <Form id="demat-form">
              <FormPage
                title="Demat account number"
                description="Open or link your demat account today and start investing immediately"
                footer={
                  <Button
                    type="submit"
                    form="demat-form"
                    loading={formState.isSubmitting}
                    disabled={formState.isSubmitting}
                  >
                    Continue
                  </Button>
                }
              >
                <div className="space-y-4">
                  <div
                    className={clsx(
                      "relative mt-3 flex cursor-pointer flex-col gap-4 rounded-[12px] border-1 p-4 transition-all duration-200",
                      formState.values.dematType === "stable"
                        ? "border-black-80"
                        : "border-black-10"
                    )}
                    onClick={() =>
                      formState.setFieldValue("dematType", "stable")
                    }
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="absolute -top-2 left-4 flex">
                          <Tag
                            color="#FFFFFF"
                            backgroundColor="#916CFF"
                            shimmerColor="#FFFFFF"
                            borderColor="#916CFF33"
                            hasShimmer
                          >
                            <span className="tracking-wider">RECOMMENDED</span>
                          </Tag>
                        </div>
                        <p className="mt-2">Open free demat account</p>
                      </div>
                      <div
                        className={clsx(
                          "checkmark",
                          formState.values.dematType === "stable" && "selected"
                        )}
                      />
                    </div>
                    <hr className="border-black-10" />
                    <Accordion className="p-0">
                      <AccordionItem
                        id="item1"
                        label="Ideal for first time investors and effortless portfolio tracking"
                        className="text-black-50 items-start p-0"
                      >
                        <ul className="py-3">
                          {ourDematBenefits.map((item, index) => {
                            return (
                              <p
                                key={index}
                                className="flex items-center gap-2 italic"
                              >
                                <img
                                  src={tick}
                                  alt=""
                                  srcSet=""
                                  className="h-2 invert filter"
                                />
                                {item}
                              </p>
                            );
                          })}
                        </ul>
                      </AccordionItem>
                    </Accordion>
                  </div>

                  <div
                    className={clsx(
                      "relative mt-3 flex cursor-pointer flex-col gap-4 rounded-[12px] border-1 p-4 transition-all duration-200",
                      formState.values.dematType === "existing"
                        ? "border-black-80"
                        : "border-black-10"
                    )}
                    onClick={() =>
                      formState.setFieldValue("dematType", "existing")
                    }
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p>Use existing demat account</p>
                      </div>
                      <div
                        className={clsx(
                          "checkmark",
                          formState.values.dematType === "existing" &&
                            "selected"
                        )}
                      />
                    </div>
                    <hr />
                    <Accordion className="p-0">
                      <AccordionItem
                        id="item2"
                        label="For investors who like to keep all bond holdings in one place"
                        className="text-black-50 items-start p-0"
                      >
                        <ul className="py-3">
                          {anotherBenefits.map((item, index) => {
                            return (
                              <p
                                key={index}
                                className="flex items-center gap-2 italic"
                              >
                                <img
                                  src={tick}
                                  alt=""
                                  srcSet=""
                                  className="h-2 invert filter"
                                />
                                {item}
                              </p>
                            );
                          })}
                        </ul>
                      </AccordionItem>
                    </Accordion>
                  </div>
                </div>

                {formState.submitCount > 0 && formState.errors.dematType && (
                  <div className="mt-2 text-sm text-red-500">
                    {formState.errors.dematType}
                  </div>
                )}
                {formState.values.dematType === "existing" && (
                  <div className="space-y-3">
                    <FormFields {...formState} />
                    <p className="text-body2 text-black-50">
                      By proceeding, you confirm the demat account number is
                      correct and yours. An incorrect demat account number may
                      result in securities being credited to another account.
                    </p>
                    <AdaptiveModal
                      href={`/workflow/${slug}/demat/find-by-broker`}
                      trigger={
                        <LinkButton>
                          Where can I find my demat account number?
                        </LinkButton>
                      }
                    >
                      {() => <FindByBrokerPage />}
                    </AdaptiveModal>
                  </div>
                )}
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}

export default function DematPage() {
  const featureFlageQuery = useQuery({
    queryKey: ["feature-flags"],
    queryFn: () => isEnabled("feature-bonds-new-demat"),
  });
  if (featureFlageQuery.isLoading) {
    return <LoadingPage />;
  }

  const stableDematEnabled = !!featureFlageQuery.data;
  return stableDematEnabled ? <WorkflowDematPage /> : <OldDematPage />;
}
