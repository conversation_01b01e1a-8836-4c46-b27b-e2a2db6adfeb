import * as yup from "yup";

export const validationSchema = yup.object({
  demat: yup
    .string()
    .required("Demat ID is required")
    .length(16, "Demat ID must be 16 characters long")
    .transform((value) => (value ? value.toUpperCase() : value)),
  dematConfirmation: yup
    .string()
    .required("Confirm Demat ID is required")
    .length(16, "Confirm Demat ID must be 16 characters long")
    .transform((value) => (value ? value.toUpperCase() : value))
    .test("demat-match", "Demat IDs do not match", function (value) {
      return value === this.parent.demat;
    }),
});

export type FormValues = yup.InferType<typeof validationSchema>;
