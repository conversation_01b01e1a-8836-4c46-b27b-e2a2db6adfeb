import Input from "@/components/ui/input/input";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import QueryRenderer from "@/components/functional/query-renderer";
import { getDematProviderDetails } from "@/queries/demat";
import { useQuery } from "@tanstack/react-query";
import { Field, type FormikProps } from "formik";
import { type FormValues } from "./schema";

function preventPaste(e: React.ClipboardEvent) {
  e.preventDefault();
}

export default function FormFields({
  errors,
  values,
  submitCount,
}: FormikProps<FormValues>) {
  const dematProviderQuery = useQuery({
    queryKey: ["demat-provider", values.demat],
    queryFn: () => getDematProviderDetails(values.demat),
    enabled: values.demat?.length >= 8,
  });

  return (
    <>
      <Field
        name="demat"
        label="16 digit Demat ID"
        as={Input}
        type="password"
        error={submitCount > 0 ? errors.demat : undefined}
        onPaste={preventPaste}
        suffix={
          <QueryRenderer query={dematProviderQuery}>
            {(dematProvider) =>
              dematProvider ? (
                <EntityLogo
                  url={dematProvider.iconUrl}
                  size="small"
                  color="#FFF6EE"
                  elevation="md"
                  seamless
                />
              ) : null
            }
          </QueryRenderer>
        }
      />

      <Field
        name="dematConfirmation"
        label="Confirm 16 digit demat ID"
        as={Input}
        error={submitCount > 0 ? errors.dematConfirmation : undefined}
      />
    </>
  );
}
