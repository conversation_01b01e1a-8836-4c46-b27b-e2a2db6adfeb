import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { Formik } from "formik";
import { addDematAccount } from "@/queries/workflow";
import { trackEvent } from "@/utils/analytics";
import { withErrorToast, getErrorMessage } from "@/utils/errors";
import { HttpCallException } from "@/exceptions/http-call-exception";
import useNavigate from "@/hooks/navigate";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";
import { useParams } from "react-router";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import FindByBrokerPage from "../find-by-broker";

export default function DematPage() {
  const navigate = useNavigate();
  const { slug } = useParams();
  const workflowName = getWorkflowNameFromSlug(slug!);

  return (
    <>
      <Head title="Demat Account" />
      <Formik
        initialValues={
          {
            demat: "",
            dematConfirmation: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(
          values,
          { setFieldError }
        ) {
          try {
            const responseData = await addDematAccount(
              workflowName,
              false,
              values.demat
            );
            trackEvent("verify_demat_clicked");
            navigate(
              getPathForWorkflowStep(workflowName, responseData.nextStep),
              {
                replace: true,
              }
            );
          } catch (error) {
            if (
              error instanceof HttpCallException &&
              error.response.status === 400
            ) {
              const errorMessage = await getErrorMessage(error);
              setFieldError("demat", errorMessage);
              setFieldError("dematConfirmation", errorMessage);
            } else {
              throw error;
            }
          }
        })}
      >
        {(formState) => {
          return (
            <Form id="demat-form">
              <FormPage
                title="Demat account number"
                description="Your bond units will be credited to the 16 digit demat account number (BOID) that you enter"
                footer={
                  <Button
                    type="submit"
                    form="demat-form"
                    loading={formState.isSubmitting}
                    disabled={formState.isSubmitting}
                  >
                    Verify demat account number
                  </Button>
                }
              >
                <FormFields {...formState} />

                <p className="text-body2 text-black-50 mt-6">
                  By proceeding, you confirm the demat account number is correct
                  and yours. An incorrect demat account number may result in
                  securities being credited to another account.
                </p>
                <AdaptiveModal
                  href={`/workflow/${slug}/demat/find-by-broker`}
                  trigger={
                    <img
                      src="https://assets.stablemoney.in/app/cant-find-demat-banner.webp"
                      className="mt-8 cursor-pointer"
                      alt="Can't find your demat account?"
                    />
                  }
                >
                  {() => <FindByBrokerPage />}
                </AdaptiveModal>
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
