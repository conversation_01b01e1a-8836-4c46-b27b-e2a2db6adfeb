import * as yup from "yup";

export const validationSchema = yup.object({
  fatherName: yup
    .string()
    .required("Father's name is required")
    .min(4, "Father's name must be at least 4 characters"),
  motherName: yup
    .string()
    .required("Mother's name is required")
    .min(4, "Mother's name must be at least 4 characters"),
});

export type FormValues = yup.InferType<typeof validationSchema>;
