import Input from "@/components/ui/input/input";
import { Field, type FormikProps } from "formik";
import { type FormValues } from "./schema";

export default function FormFields({
  errors,
  submitCount,
}: FormikProps<FormValues>) {
  return (
    <>
      <Field
        name="fatherName"
        label="Father's full name"
        as={Input}
        error={submitCount > 0 ? errors.fatherName : undefined}
        spellCheck="false"
      />

      <Field
        name="motherName"
        label="Mother's full name"
        as={Input}
        error={submitCount > 0 ? errors.motherName : undefined}
        spellCheck="false"
      />
    </>
  );
}
