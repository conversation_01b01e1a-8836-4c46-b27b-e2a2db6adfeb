import { useEffect } from "react";
import FormPage from "@/components/bonds/form-page";
import Button from "@/components/ui/button/button";
import { Formik } from "formik";
import { trackEvent } from "@/utils/analytics";
import { withErrorToast } from "@/utils/errors";
import { useParams, useSearchParams } from "react-router";
import useNavigate from "@/hooks/navigate";
import FormFields from "./fields";
import Head from "@/components/ui/head";
import { validationSchema, type FormValues } from "./schema";
import Form from "@/components/functional/form";
import { submitBasicDetails } from "@/queries/workflow";
import {
  getPathForWorkflowStep,
  getWorkflowNameFromSlug,
} from "@/utils/workflow-routes";

export default function WorkflowParentDetailsPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { slug } = useParams<{ slug: string }>();
  const workflowName = getWorkflowNameFromSlug(slug!);

  const incomeRange = searchParams.get("incomeRange");
  const maritalStatus = searchParams.get("maritalStatus");
  const employmentType = searchParams.get("employmentType");
  const tradingExperience = searchParams.get("tradingExperience");

  // Redirect if required parameters are missing
  useEffect(() => {
    if (
      !incomeRange ||
      !maritalStatus ||
      !employmentType ||
      !tradingExperience
    ) {
      navigate(`/workflow/${slug}/basic-details`, {
        replace: true,
      });
    }
  }, [
    incomeRange,
    maritalStatus,
    employmentType,
    tradingExperience,
    navigate,
    slug,
  ]);

  if (!incomeRange || !maritalStatus || !employmentType || !tradingExperience) {
    return null;
  }

  return (
    <>
      <Head title="Parent Details" />
      <Formik
        initialValues={
          {
            fatherName: "",
            motherName: "",
          } as FormValues
        }
        validationSchema={validationSchema}
        validateOnMount
        onSubmit={withErrorToast(async function handleSubmit(values) {
          const responseData = await submitBasicDetails(
            {
              incomeRange: incomeRange,
              employmentType: employmentType,
              tradingExperience: tradingExperience,
              maritalStatus: maritalStatus,
              fatherName: values.fatherName,
              motherName: values.motherName,
            },
            workflowName
          );
          trackEvent("parent_details_submit");
          navigate(
            getPathForWorkflowStep(workflowName, responseData.nextStep),
            {
              replace: true,
            }
          );
        })}
      >
        {(formState) => {
          return (
            <Form id="parent-details-form">
              <FormPage
                title="Parents' details"
                description="Please enter parents' details to verify KYC"
                footer={
                  <Button
                    type="submit"
                    form="parent-details-form"
                    loading={formState.isSubmitting}
                    disabled={formState.isSubmitting}
                  >
                    Proceed
                  </Button>
                }
              >
                <FormFields {...formState} />
              </FormPage>
            </Form>
          );
        }}
      </Formik>
    </>
  );
}
