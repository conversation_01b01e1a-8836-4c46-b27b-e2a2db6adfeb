import { ErrorResponseSchema } from "@/clients/gen/broking/Common_pb";
import { trackEvent } from "@/utils/analytics";
import { fromBinary } from "@bufbuild/protobuf";

export class HttpCallException extends Error {
  constructor(
    public response: Response,
    public request: Request
  ) {
    super(
      `HTTP call failed with status code ${response.status} for ${response.url}`
    );
    this.reportError();
  }

  async getError() {
    if (this.response.headers.get("content-type") === "application/json") {
      const errorResponseData = await this.response.clone().json();
      return {
        statusCode: errorResponseData.errorCode,
        message: errorResponseData.errorMessage || "Oops! Something went wrong",
        errorMessage: errorResponseData.errorMessage || "",
        errorCode: errorResponseData.errorCode || "",
      };
    }
    try {
      const errorResponseData = fromBinary(
        ErrorResponseSchema,
        new Uint8Array(await this.response.clone().arrayBuffer())
      );
      return {
        statusCode: errorResponseData.errorCode,
        message: errorResponseData.errorMessage || "Oops! Something went wrong",
        errorMessage: errorResponseData.errorMessage || "",
        errorCode: errorResponseData.errorCode || "",
      };
    } catch {
      return {
        statusCode: "500",
        message: "Oops! Something went wrong",
        errorMessage: "Oops! Something went wrong",
        errorCode: "500",
      };
    }
  }

  async reportError() {
    const { message } = await this.getError();
    trackEvent("api_call_failed", {
      endpoint: this.request.url,
      method: this.request.method,
      statusCode: this.response.status,
      message,
    });
  }
}
