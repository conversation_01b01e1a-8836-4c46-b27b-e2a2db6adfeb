import type {
  DataK<PERSON>,
  DataKeyVariableType,
} from "@/clients/gen/personalization_api";
import { formatCurrency } from "@/utils/format";
import dayjs from "dayjs";
import { useTranslation as useI18n } from "react-i18next";

export default function useDataKey(key?: DataKey) {
  const { t } = useI18n();
  if (!key) return "";
  const namedArgs = getNameArgsFromContextVariables(key);
  return t(key.key, namedArgs);
}

/**
 * Extracts context variables from a data key and formats them based on their type
 * @param dataKey The data key containing context variables
 * @returns An object with variable names as keys and formatted values as values
 */
function getNameArgsFromContextVariables(
  dataKey: DataKey
): Record<string, string> {
  const result: Record<string, string> = {};

  if (dataKey.contextVariables) {
    for (const variable of dataKey.contextVariables) {
      result[variable.name] = getValueBasedOnDataType(
        variable.type,
        variable.value,
        "User name" // Default user name if needed
      );
    }
  }

  return result;
}

/**
 * Formats a value based on its data type
 * @param dataType The type of the data
 * @param value The value to format
 * @param userFirstName Optional user first name for user_first_name type
 * @returns The formatted value as a string
 */
function getValueBasedOnDataType(
  dataType: DataKeyVariableType,
  value: string,
  userFirstName?: string
): string {
  switch (dataType) {
    case "currency":
      return formatCurrency(Number.parseFloat(value));
    case "date":
      return formatDate(value, "DD MMM, YYYY");
    case "date_time":
      return formatDate(value, "hh:mm A");
    case "string":
      return value;
    case "user_first_name":
      return userFirstName ?? "";
    case "percent":
      return formatPercent(value, false);
    case "2f_percent":
      return formatPercent(value, true);
    case "short_currency":
      return formatCurrency(Number.parseFloat(value), {
        maximumFractionDigits: 2,
      });
    case "number":
      return value;
    default:
      return value;
  }
}

/**
 * Formats a date string according to the specified pattern
 * @param value The date string to format
 * @param pattern The date format pattern
 * @returns The formatted date string
 */
function formatDate(value: string, pattern: string): string {
  try {
    const date = new Date(value);
    return dayjs(date).format(pattern);
  } catch (error) {
    console.error("Error formatting date:", error);
    return value;
  }
}

/**
 * Formats a number as a percentage
 * @param value The value to format as a percentage
 * @param withDecimal Whether to include decimal places
 * @returns The formatted percentage string
 */
function formatPercent(value: string, withDecimal: boolean): string {
  const parsedValue = Number.parseFloat(value);
  return `${parsedValue.toFixed(withDecimal ? 2 : 0)}%`;
}
