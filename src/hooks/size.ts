import useResizeObserver from "@react-hook/resize-observer";
import { useLayoutEffect, useState } from "react";

export function useSize(target: React.RefObject<HTMLElement | null>) {
  const [size, setSize] = useState<DOMRect | null>(null);

  useLayoutEffect(() => {
    setSize(target.current?.getBoundingClientRect() ?? null);
  }, [target]);

  useResizeObserver(target, (entry) => setSize(entry.contentRect));
  return size;
}
