import { useEffect, useRef, useCallback } from "react";

/**
 * ScrollStyle hook for React
 *
 * This hook applies styles to an element based on scroll position and direction.
 * It's a port of the ScrollStyle Svelte action from sk-broking-web.
 */

export interface OffsetStyle<K extends keyof CSSStyleDeclaration> {
  threshold: number;
  before: CSSStyleDeclaration[K];
  after: CSSStyleDeclaration[K];
}

export interface DirectionStyle<K extends keyof CSSStyleDeclaration> {
  forward: CSSStyleDeclaration[K];
  backward: CSSStyleDeclaration[K];
}

export interface ScrollStyleParams<
  O extends keyof CSSStyleDeclaration = keyof CSSStyleDeclaration,
  D extends keyof CSSStyleDeclaration = keyof CSSStyleDeclaration,
> {
  /**
   * Styles to apply based on scroll offset
   */
  offsetStyles?: Partial<Record<O, OffsetStyle<O>>>;

  /**
   * Styles to apply based on scroll direction
   */
  directionStyles?: Partial<Record<D, DirectionStyle<D>>>;

  /**
   * Scroll orientation
   */
  orientation?: "horizontal" | "vertical";

  /**
   * CSS selector for the scrollable element
   * If not provided, window will be used
   */
  scroller?: string;
}

/**
 * React hook that applies styles to an element based on scroll position and direction
 *
 * @param params - Configuration parameters
 * @returns A ref to attach to the target element
 */
export function useScrollStyle<
  O extends keyof CSSStyleDeclaration,
  D extends keyof CSSStyleDeclaration,
>(params?: ScrollStyleParams<O, D>) {
  const elementRef = useRef<HTMLElement>(null);
  const lastScrollAmountRef = useRef(0);
  const scrollableRef = useRef<Window | Element | null>(null);
  const scrollCheckElementRef = useRef<Element | null>(null);

  const {
    offsetStyles = {},
    directionStyles = {},
    orientation = "vertical",
    scroller = "",
  } = params || {};

  // Get the scrollable element
  const getScrollable = useCallback((): Window | Element | null => {
    if (!scroller) {
      return window;
    }

    const element = document.querySelector(scroller);
    if (!element) {
      console.warn(`ScrollStyle: No element found for selector "${scroller}"`);
      return window;
    }

    return element;
  }, [scroller]);

  // Get the element to check for scroll position
  const getScrollCheckElement = useCallback((): Element | null => {
    if (scroller) {
      return document.querySelector(scroller);
    }
    return document.documentElement;
  }, [scroller]);

  // Handle scroll events
  const handleScroll = useCallback(() => {
    const node = elementRef.current;
    const scrollCheckElement = scrollCheckElementRef.current;

    if (!node || !scrollCheckElement) return;

    const scrollAmount =
      orientation === "horizontal"
        ? (scrollCheckElement?.scrollLeft ?? 0)
        : (scrollCheckElement?.scrollTop ?? 0);

    // Apply offset-based styles
    for (const [key, value] of Object.entries(offsetStyles) as [
      O,
      OffsetStyle<O>,
    ][]) {
      if (scrollAmount > value.threshold) {
        node.style[key] = value.after;
      } else {
        node.style[key] = value.before;
      }
    }

    // Apply direction-based styles
    if (directionStyles) {
      const direction =
        scrollAmount > lastScrollAmountRef.current ? "forward" : "backward";
      for (const [key, value] of Object.entries(directionStyles) as [
        D,
        DirectionStyle<D>,
      ][]) {
        node.style[key] = value[direction];
      }
    }

    lastScrollAmountRef.current = scrollAmount;
  }, [offsetStyles, directionStyles, orientation]);

  // Initialize and cleanup
  useEffect(() => {
    const scrollable = getScrollable();
    const scrollCheckElement = getScrollCheckElement();

    scrollableRef.current = scrollable;
    scrollCheckElementRef.current = scrollCheckElement;

    if (scrollable) {
      scrollable.addEventListener("scroll", handleScroll);
      handleScroll(); // Apply initial styles
    }

    return () => {
      if (scrollableRef.current) {
        scrollableRef.current.removeEventListener("scroll", handleScroll);
      }
    };
  }, [getScrollable, getScrollCheckElement, handleScroll]);

  return elementRef;
}
