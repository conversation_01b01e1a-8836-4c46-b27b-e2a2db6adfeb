import { useState, useEffect, useCallback } from "react";

export interface CountdownTimer {
  timeRemaining: number;
  formattedTime: string;
  isRunning: boolean;
  isFinished: boolean;
  start: () => void;
  pause: () => void;
  reset: () => void;
  stop: () => void;
}

export function useCountdownTimer(
  initialDuration: number,
  onFinish?: () => void,
  autoStart: boolean = false
): CountdownTimer {
  const [timeRemaining, setTimeRemaining] = useState(initialDuration);
  const [isRunning, setIsRunning] = useState(autoStart);
  const [isFinished, setIsFinished] = useState(false);

  const formatTime = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, "0")}:${remainingSeconds
      .toString()
      .padStart(2, "0")}`;
  }, []);

  const formattedTime = formatTime(timeRemaining);

  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isRunning && timeRemaining > 0) {
      intervalId = setInterval(() => {
        setTimeRemaining((prev) => {
          const newTime = prev - 1;
          if (newTime <= 0) {
            setIsRunning(false);
            setIsFinished(true);
            onFinish?.();
            return 0;
          }
          return newTime;
        });
      }, 1000);
    }

    return () => {
      if (intervalId) {
        clearInterval(intervalId);
      }
    };
  }, [isRunning, timeRemaining, onFinish]);

  const start = useCallback(() => {
    if (timeRemaining > 0) {
      setIsRunning(true);
      setIsFinished(false);
    }
  }, [timeRemaining]);

  const pause = useCallback(() => {
    setIsRunning(false);
  }, []);

  const reset = useCallback(() => {
    setTimeRemaining(initialDuration);
    setIsRunning(false);
    setIsFinished(false);
  }, [initialDuration]);

  const stop = useCallback(() => {
    setTimeRemaining(initialDuration);
    setIsRunning(false);
    setIsFinished(false);
  }, [initialDuration]);

  return {
    timeRemaining,
    formattedTime,
    isRunning,
    isFinished,
    start,
    pause,
    reset,
    stop,
  };
}
