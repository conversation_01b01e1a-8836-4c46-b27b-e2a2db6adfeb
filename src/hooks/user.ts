import { authQueryOptions } from "@/queries/auth";
import { getProfileQueryOptions } from "@/queries/profile";
import { setUserId } from "@/utils/sentry";
import { useQuery } from "@tanstack/react-query";

export function useUserQuery() {
  const authTokenQuery = useQuery(authQueryOptions());
  const profileQuery = useQuery({
    ...getProfileQueryOptions(),
    enabled: !!authTokenQuery.data,
  });
  const userId = profileQuery.data?.data?.id;
  if (userId) {
    setUserId(userId);
    import("mixpanel-browser").then(({ default: mixpanel }) => {
      mixpanel.identify(userId);
    });
  }
  return profileQuery;
}
