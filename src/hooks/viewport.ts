import { useState, useEffect } from "react";

export function useViewportScaling() {
  const [scalingFactor, setScalingFactor] = useState(1);
  function setScalingFactorState() {
    const clampedFactor =
      document.body.clientWidth > 768
        ? 1
        : Math.min(Math.max(document.body.clientWidth / 360, 1), 1.2);
    setScalingFactor(clampedFactor);
  }
  useEffect(() => {
    window.addEventListener("resize", setScalingFactorState);
    return () => window.removeEventListener("resize", setScalingFactorState);
  }, []);

  return scalingFactor;
}
