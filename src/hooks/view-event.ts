import { useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { trackEvent } from "@/utils/analytics";

type UseViewEventOptions = {
  threshold?: number;
  triggerOnce?: boolean;
  eventName: string;
  eventProperties?: Record<string, unknown>;
};

export function useViewEvent({
  threshold = 0.1,
  triggerOnce = true,
  eventName,
  eventProperties,
}: UseViewEventOptions) {
  const { ref, inView } = useInView({
    threshold,
    triggerOnce,
  });

  useEffect(() => {
    if (inView) {
      trackEvent(eventName, eventProperties || {});
    }
  }, [inView, eventName, eventProperties]);

  return { ref, inView };
}
