import { useEffect } from "react";
import { isHotwireNative } from "@/utils/routing";
import { getClientOS } from "@/clients/http-context";
import { kebabCase } from "change-case";

/**
 * Hook to add OS name (ios/android) and hotwire-native classes to body element
 * This helps with platform-specific styling and feature detection
 */
export function useBodyClasses() {
  useEffect(() => {
    const body = document.body;
    const classes: string[] = [];

    // Add hotwire-native class if running in Hotwire Native
    if (isHotwireNative()) {
      classes.push("hotwire-native");
    }

    if (window.flutter_inappwebview) {
      classes.push("flutter");
    }

    // Add OS-specific class
    const osName = getClientOS()?.toLowerCase();
    if (osName) {
      classes.push(kebabCase(osName));
    }

    // Add all classes to body
    if (classes.length > 0) {
      body.classList.add(...classes);
    }

    // Cleanup function to remove classes when component unmounts
    return () => {
      if (classes.length > 0) {
        body.classList.remove(...classes);
      }
    };
  }, []);
}
