import { useEffect, useRef } from "react";
import { useLocation } from "react-router";
import { trackEvent } from "@/utils/analytics";

export function usePageViewTracking() {
  const location = useLocation();
  const previousPath = useRef<string | null>(null);

  useEffect(() => {
    const currentPath = location.pathname + location.search;

    if (previousPath.current !== currentPath) {
      trackEvent("navigation.navigated", {
        from: previousPath.current ?? document.referrer,
        to: currentPath,
      });
      previousPath.current = currentPath;
    }
  }, [location.pathname, location.search]);
}
