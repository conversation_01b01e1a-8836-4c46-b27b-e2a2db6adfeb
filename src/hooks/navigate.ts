import {
  type NavigateOptions,
  type To,
  useNavigate as useReactRouterNavigate,
} from "react-router";
import { isHotwireNative } from "@/utils/routing.ts";

export default function useNavigate() {
  const navigate = useReactRouterNavigate();
  if (isHotwireNative()) {
    return function hotwireNavigator(to: To, options?: NavigateOptions) {
      const destination = new URL(
        typeof to === "string" ? to : to.pathname + (to.search || ""),
        window.location.origin
      );
      window.HotwireNavigator.visitProposedToLocation(destination, {
        action: options?.replace ? "replace" : "advance",
      });
    };
  }
  return navigate;
}
