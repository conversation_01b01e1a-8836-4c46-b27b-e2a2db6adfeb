import type { SVGProps } from "react";

export default function Refresh(props: SVGProps<SVGSVGElement>) {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g clipPath="url(#clip0_1038_5734)">
        <path
          d="M8.00016 2.66675C10.9455 2.66675 13.3335 5.05475 13.3335 8.00008C13.3335 10.9454 10.9455 13.3334 8.00016 13.3334C5.05483 13.3334 2.66683 10.9454 2.66683 8.00008C2.66683 6.52675 3.28016 5.19608 4.26683 4.26675"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M5.33333 2.66675L4.26666 4.26675L5.86666 5.33341"
          stroke="currentColor"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_1038_5734">
          <rect width="16" height="16" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
