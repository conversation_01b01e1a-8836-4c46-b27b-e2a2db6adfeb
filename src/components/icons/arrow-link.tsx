import type { JSX } from "react";

export default function ArrowLink(props: JSX.IntrinsicElements["svg"]) {
  return (
    <svg
      width="10"
      height="10"
      viewBox="0 0 10 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M9.1 7.58009L9.1 7.68009L9 7.68009L8.04968 7.68009L7.94933 7.68009L7.94968 7.57974L7.96611 2.85303L1.76193 9.07063L1.69203 9.14068L1.62126 9.07152L0.930113 8.3962L0.857932 8.32567L0.929117 8.25414L7.11794 2.03507L2.4514 2.03507L2.3514 2.03507L2.3514 1.93507L2.3514 1L2.3514 0.900001L2.4514 0.900001L9 0.900001L9.1 0.900001L9.1 1L9.1 7.58009Z"
        fill="currentColor"
        stroke="currentColor"
        strokeWidth="0.2"
      />
    </svg>
  );
}
