import clsx from "clsx";
import type { SVGProps } from "react";

export default function CaretDown({
  dropDownOpen,
}: SVGProps<SVGSVGElement> & { dropDownOpen?: boolean }) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={clsx(
        "text-black-40 h-4 w-4",
        dropDownOpen ? "rotate-270" : "rotate-90"
      )}
    >
      <path
        d="M8 5L15 12L8 19"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
