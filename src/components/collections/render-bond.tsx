import {
  CollectionResponse_DisplayType,
  type CollectionItem,
} from "@/clients/gen/broking/Collection_pb";
import { formatAmount } from "@/utils/format";
import { formatToInr } from "@/utils/number";
import LabelValue from "./label-value";

type RenderBondProps = {
  item: CollectionItem;
  displayType: CollectionResponse_DisplayType;
};

export default function RenderBond({ item, displayType }: RenderBondProps) {
  const formattedXirr = `${formatAmount(item.xirr)}%`;
  const formattedTenure = item.tenure;
  const formattedMinInvestment = formatToInr(
    parseFloat(item.minimumInvestment.toString())
  );
  const formattedSoldOut = `${item.soldOutPercentage}%`;

  if (
    displayType === CollectionResponse_DisplayType.SHORT_TERM_XIRR ||
    displayType === CollectionResponse_DisplayType.DEFAULT ||
    displayType === CollectionResponse_DisplayType.DISPLAY_TYPE_UNKNOWN
  ) {
    return (
      <>
        <LabelValue label="YTM" value={formattedXirr} />
        <LabelValue label="Tenure" value={formattedTenure} />
      </>
    );
  } else if (
    displayType === CollectionResponse_DisplayType.MINIMUM_INVESTMENT
  ) {
    return (
      <>
        <LabelValue label="Min. amount" value={formattedMinInvestment} />
        <LabelValue label="YTM" value={formattedXirr} />
      </>
    );
  } else if (displayType === CollectionResponse_DisplayType.SHORT_TERM) {
    return (
      <>
        <LabelValue label="Tenure" value={formattedTenure} />
        <LabelValue label="YTM" value={formattedXirr} />
      </>
    );
  } else if (displayType === CollectionResponse_DisplayType.SELLING_OUT_SOON) {
    return (
      <>
        <LabelValue label="Sold out" value={formattedSoldOut} />
        <LabelValue label="YTM" value={formattedXirr} />
      </>
    );
  }

  return null;
}
