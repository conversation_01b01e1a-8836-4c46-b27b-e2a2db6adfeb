import { type HTMLAttributes } from "react";
import Card from "./card";
import RichText from "@/components/ui/rich-text/rich-text";
import useDataKey from "@/hooks/data-key";
import BulletList from "@/components/ui/bullet-list/bullet-list";
import BulletListItem from "@/components/ui/bullet-list/bullet-list-item";
import type {
  BondsMarketingHighlightsProps,
  DataKey,
} from "@/clients/gen/personalization_api";

type BondsMarketingHighlightsComponentProps = BondsMarketingHighlightsProps &
  HTMLAttributes<HTMLDivElement>;

function MarketingHighlightItem({ highlight }: { highlight: DataKey }) {
  const text = useDataKey(highlight);
  return (
    <BulletListItem>
      <RichText text={text} />
    </BulletListItem>
  );
}

export default function BondsMarketingHighlights({
  marketingHighlightsMap = [],
  ...rest
}: BondsMarketingHighlightsComponentProps) {
  return (
    <div className="mb-6 px-5" data-widget="BondsMarketingHighlights" {...rest}>
      <Card>
        <BulletList>
          {marketingHighlightsMap.map((highlight, index) => (
            <MarketingHighlightItem key={index} highlight={highlight} />
          ))}
        </BulletList>
      </Card>
    </div>
  );
}
