import { type HTMLAttributes } from "react";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import Card from "./card";
import { useTranslation } from "react-i18next";
import useDataKey from "@/hooks/data-key";
import type {
  BondsDisclosureDetailsProps,
  TableDataItem,
} from "@/clients/gen/personalization_api";

type BondsDisclosureDetailsComponentProps = BondsDisclosureDetailsProps &
  HTMLAttributes<HTMLDivElement>;

function DisclosureItem({ disclosure }: { disclosure: TableDataItem }) {
  const labelText = useDataKey(disclosure.labelDataKey);
  const valueText = useDataKey(disclosure.valueDataKey);

  return (
    <div className="px-4 py-3">
      <div className="flex items-center justify-between">
        {disclosure.labelDataKey && (
          <span className="text-black-50 text-sm">{labelText}</span>
        )}

        {disclosure.valueDataKey && (
          <span className="text-right text-base font-medium">{valueText}</span>
        )}
      </div>
    </div>
  );
}

export default function BondsDisclosureDetails({
  title,
  disclosures,
  ...rest
}: BondsDisclosureDetailsComponentProps) {
  const { t } = useTranslation();

  return (
    <div className="space-y-4" {...rest}>
      {title && (
        <div className="pl-5">
          <SectionHeading title={t(title)} separator />
        </div>
      )}

      <div className="px-5">
        <Card>
          <div className="divide-y">
            {disclosures?.data?.map((disclosure, index) => (
              <DisclosureItem key={index} disclosure={disclosure} />
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}
