import { useMemo, type HTMLAttributes, type ReactNode } from "react";
import clsx from "clsx";
import Surface from "@/components/ui/surface/surface";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import { useTranslation } from "react-i18next";
import type {
  CardProps,
  Card as CardWidget,
} from "@/clients/gen/personalization_api";
import {
  CardContentSpacingType,
  TitleType,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";

type CardComponentProps = CardProps & {
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export default function Card({
  children,
  title,
  contentSpacingType,
  isInnerPadding,
  isAttachedWidget = true,
  titleType,
  className,
  ...rest
}: CardComponentProps) {
  const { t } = useTranslation();
  const wrapperClasses = useMemo(
    () =>
      clsx(
        "space-y-4",
        {
          "px-5": !isAttachedWidget,
        },
        className
      ),
    [isAttachedWidget, className]
  );

  const spacingClass = useMemo(() => {
    switch (contentSpacingType) {
      case CardContentSpacingType.smallSpacing:
        return "p-2";
      case CardContentSpacingType.mediumSpacing:
        return "p-4";
      case CardContentSpacingType.largeSpacing:
        return "p-6";
      case CardContentSpacingType.noSpacing:
        return "p-0";
      default:
        return isInnerPadding ? "p-4" : "p-0";
    }
  }, [contentSpacingType, isInnerPadding]);

  return (
    <div className={wrapperClasses} data-widget="Card" {...rest}>
      {title && (
        <SectionHeading
          title={t(title)}
          size={titleType === TitleType.mainHeader ? "medium" : "small"}
          separator={titleType === TitleType.centeredWithDivider}
        />
      )}

      <Surface elevation="md">
        <div className={spacingClass}>{children}</div>
      </Surface>
    </div>
  );
}

export function buildCard(widget: CardWidget) {
  const { props, id, slots } = widget;

  return (
    <Card {...props} id={id} key={id}>
      {slots?.content && buildPersonalizationWidget(slots.content)}
    </Card>
  );
}
