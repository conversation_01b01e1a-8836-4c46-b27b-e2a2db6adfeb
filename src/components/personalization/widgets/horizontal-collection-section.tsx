import { type <PERSON>actN<PERSON>, type H<PERSON>LAttributes } from "react";
import {
  TitleType,
  type HorizontalCollectionSectionProps,
  type HorizontalCollectionSection as HorizontalCollectionSectionWidget,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";
import SectionHeading from "@/components/ui/section-heading/section-heading";

type HorizontalCollectionSectionComponentProps =
  HorizontalCollectionSectionProps & {
    children?: ReactNode;
  } & HTMLAttributes<HTMLDivElement>;

export default function HorizontalCollectionSection({
  id,
  title,
  titleType,
  children,
  ...rest
}: HorizontalCollectionSectionComponentProps) {
  return (
    <div {...rest} data-widget="HorizontalCollectionSection">
      {title && (
        <SectionHeading
          title={title}
          size={titleType === TitleType.mainHeader ? "medium" : "small"}
        />
      )}
      <div
        className="scrollbar-none flex items-start gap-3 overflow-scroll px-5"
        data-scroll-id={`horizontal-collection-section-${id}`}
      >
        {children}
      </div>
    </div>
  );
}

export function buildHorizontalCollectionSection(
  widget: HorizontalCollectionSectionWidget
) {
  const { props, id, slots } = widget;

  return (
    <HorizontalCollectionSection {...props} id={id} key={id}>
      {slots?.items?.map((item) => buildPersonalizationWidget(item))}
    </HorizontalCollectionSection>
  );
}
