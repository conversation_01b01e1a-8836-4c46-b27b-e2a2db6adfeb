import { type HTMLAttributes } from "react";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import useDataKey from "@/hooks/data-key";
import type {
  DataTableProps,
  TableDataItem,
} from "@/clients/gen/personalization_api";
import Surface from "@/components/ui/surface/surface";

type DataTableComponentProps = DataTableProps & HTMLAttributes<HTMLDivElement>;

function DataTableRow({ row }: { row: TableDataItem }) {
  const labelText = useDataKey(row.labelDataKey);
  const valueText = useDataKey(row.valueDataKey);
  const subValueText = useDataKey(row.subValueDataKey);

  return (
    <div className="px-4 py-3">
      <div className="flex items-center justify-between">
        {row.labelDataKey && (
          <span className="text-black-50 text-sm">{labelText}</span>
        )}

        {row.valueDataKey && (
          <span className="text-base font-medium">{valueText}</span>
        )}
      </div>

      {row.subValueDataKey && (
        <div className="text-black-50 mt-1 text-sm">{subValueText}</div>
      )}
    </div>
  );
}

export default function DataTable({
  title,
  tableData,
  ...rest
}: DataTableComponentProps) {
  return (
    <div className="space-y-4 px-5" data-widget="DataTable" {...rest}>
      {title && <SectionHeading title={title} separator />}

      {tableData?.data?.length && (
        <Surface elevation="md">
          <div className="divide-y">
            {tableData.data.map((row, index) => (
              <DataTableRow key={index} row={row} />
            ))}
          </div>
        </Surface>
      )}
    </div>
  );
}
