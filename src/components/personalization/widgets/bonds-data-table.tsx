import { type HTMLAttributes } from "react";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import { useTranslation } from "react-i18next";
import useDataKey from "@/hooks/data-key";
import type {
  DataTableProps,
  TableDataItem,
} from "@/clients/gen/personalization_api";
import Surface from "@/components/ui/surface/surface";

type BondsDataTableComponentProps = DataTableProps &
  HTMLAttributes<HTMLDivElement>;

function BondsDataTableItem({
  row,
  index,
}: {
  row: TableDataItem;
  index: number;
}) {
  const labelText = useDataKey(row.labelDataKey);
  const valueText = useDataKey(row.valueDataKey);
  const subValueText = useDataKey(row.subValueDataKey);

  return (
    <AccordionItem
      key={index}
      id={`data-table-item-${index}`}
      label={
        <div className="flex w-full items-center justify-between">
          <span className="text-black-50">{labelText}</span>
          <span className="text-black-80">{valueText}</span>
        </div>
      }
    >
      {row.subValueDataKey && (
        <div className="text-black-60 text-body2">{subValueText}</div>
      )}
    </AccordionItem>
  );
}

export default function BondsDataTable({
  title,
  tableData,
  ...rest
}: BondsDataTableComponentProps) {
  const { t } = useTranslation();

  if (!tableData?.data?.length) {
    return null;
  }
  return (
    <div className="space-y-4 px-5" data-widget="BondsDataTable" {...rest}>
      {title && <SectionHeading title={t(title)} separator />}

      {tableData?.data?.length && (
        <Surface elevation="md">
          <Accordion multiple className="px-5">
            {tableData.data.map((row, index) => (
              <BondsDataTableItem key={index} row={row} index={index} />
            ))}
          </Accordion>
        </Surface>
      )}
    </div>
  );
}
