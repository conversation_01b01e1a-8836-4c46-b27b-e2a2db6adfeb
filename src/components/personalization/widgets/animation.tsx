import Animation from "@/components/ui/animation/animation";
import { rewritePath } from "@/utils/routing";
import type { Animation as AnimationWidget } from "@/clients/gen/personalization_api";
import Anchor from "@/components/functional/anchor";

export function buildAnimation(widget: AnimationWidget) {
  const { props, id } = widget;
  const {
    url,
    loop,
    type,
    videoControlsPosition,
    fit,
    playOnVisibility,
    width,
    height,
    aspectRatio,
    redirectLink,
  } = props;

  // Map video controls position from API format to component format
  const mappedControlPosition = (() => {
    if (!videoControlsPosition) return undefined;

    switch (videoControlsPosition) {
      case "topLeft":
        return "top-left";
      case "topRight":
        return "top-right";
      case "bottomLeft":
        return "bottom-left";
      case "bottomRight":
        return "bottom-right";
      default:
        return "top-left";
    }
  })();

  // Map animation type from API format to component format
  const mappedType =
    type === "dotLottie" ? "lottie" : (type as "lottie" | "video" | "gif");

  // Map media fit type to supported values
  const mappedFit = fit === "cover" || fit === "contain" ? fit : undefined;

  const animationElement = (
    <Animation
      src={url}
      loop={loop}
      type={mappedType}
      controlPosition={mappedControlPosition}
      mediaFitType={mappedFit}
      playOnVisibility={playOnVisibility}
      width={width}
      height={height}
      aspectRatio={aspectRatio}
    />
  );

  if (redirectLink) {
    return (
      <Anchor
        href={rewritePath(redirectLink.path)}
        aria-label="Animation link"
        id={id}
        key={id}
        data-widget={widget.widget}
        target={redirectLink.pathType === "external" ? "_blank" : "_self"}
      >
        {animationElement}
      </Anchor>
    );
  }

  return (
    <div id={id} key={id} data-widget={widget.widget}>
      {animationElement}
    </div>
  );
}
