import { type HTMLAttributes } from "react";
import type { BondsTnCAndKYCFooterProps } from "@/clients/gen/personalization_api";
import Anchor from "@/components/functional/anchor";

type BondsTnCAndKYCFooterComponentProps = BondsTnCAndKYCFooterProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsTnCAndKYCFooter({
  ...rest
}: BondsTnCAndKYCFooterComponentProps) {
  return (
    <div
      className="text-body2 text-black-40 space-y-2 pb-8 text-center"
      data-widget="BondsTnCAndKYCFooter"
      {...rest}
    >
      <p>
        Stable Broking Pvt Ltd.
        <br />
        SEBI registration number - INZ000314637 | NSE
        <br />
        member code - 90363 | BSE member code - 6829
      </p>
      <p className="divide-x">
        <Anchor
          className="px-2"
          href="/compliance/term-and-condition"
          target="_blank"
          rel="noopener noreferrer"
        >
          <span className="inline-block border-b">Terms and conditions</span>
        </Anchor>

        <Anchor
          className="px-2"
          href="/compliance/privacy-policy"
          target="_blank"
          rel="noopener noreferrer"
        >
          <span className="inline-block border-b">Privacy Policy</span>
        </Anchor>
      </p>
    </div>
  );
}
