import { type HTMLAttributes } from "react";
import LinkButton from "@/components/ui/button/link-button";

import { trackEvent } from "@/utils/analytics";
import { useTranslation } from "react-i18next";
import type { BondsDisclosureButtonProps } from "@/clients/gen/personalization_api";

type BondsDisclosureButtonComponentProps = BondsDisclosureButtonProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsDisclosureButton({
  label = "View disclosures",
  bondId = "",
  analyticsEventProps,
  ...rest
}: BondsDisclosureButtonComponentProps) {
  const { t } = useTranslation();
  const handleClick = () => {
    trackEvent(
      "bond_disclosure_page",
      Object.fromEntries(
        Object.entries(analyticsEventProps ?? {}).map(([key, value]) => [
          key,
          typeof value === "string" ? value : JSON.stringify(value),
        ])
      )
    );
  };

  return (
    <div className="flex justify-center px-5" {...rest}>
      {label && (
        <LinkButton
          href={`/dynamic/bonds-disclosure?params=${encodeURIComponent(
            JSON.stringify({ bond_id: bondId })
          )}`}
          onClick={handleClick}
        >
          {t(label)}
        </LinkButton>
      )}
    </div>
  );
}
