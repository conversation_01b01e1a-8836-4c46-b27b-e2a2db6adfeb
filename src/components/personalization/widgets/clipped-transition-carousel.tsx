import Carousel from "@/components/ui/clipped-transition-carousel/carousel";
import { rewritePath } from "@/utils/routing";
import type {
  ClippedTransitionCarousel as ClippedTransitionCarouselWidget,
  Image,
} from "@/clients/gen/personalization_api";
import type { CarouselItem } from "@/components/ui/clipped-transition-carousel/types";

export function buildClippedTransitionCarousel(
  widget: ClippedTransitionCarouselWidget
) {
  const { props, slots, id } = widget;

  // Transform widget slots into carousel items
  const items: CarouselItem[] =
    slots.children?.map((item) => {
      const stackImage = item.slots.stacked as Image;
      const clipImage = item.slots.clipped as Image;

      return {
        stackImage: stackImage.props.url,
        clipImage: clipImage.props.url,
        link: rewritePath(item.props?.redirectLink?.path ?? ""),
        backgroundColor: item.props?.backgroundColor,
        alt: "Carousel image",
      };
    }) ?? [];

  return (
    <Carousel
      {...props}
      height={props?.height ?? 200}
      items={items}
      id={id}
      key={id}
      data-widget={widget.widget}
    />
  );
}
