import AppBar from "@/components/ui/app-bar/app-bar";
import type { ScrollStyleParams } from "@/hooks/scroll-styles";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import { buildPersonalizationWidget } from "../builder";
import type { AppBar as AppBarWidget } from "@/clients/gen/personalization_api";
import i18next from "i18next";
import HamburgerMenu from "@/pages/home/<USER>/hamburger-menu";
import menu from "@/assets/images/icons/cil-menu.svg";
import { isHotwireNative } from "@/utils/routing";

export function buildAppBar(widget: AppBarWidget) {
  const { props, slots, id } = widget;
  const { title, ...rest } = props;

  // Configure scroll style options based on widget props
  const scrollStyleOptions: ScrollStyleParams = {
    offsetStyles: {
      backgroundColor: {
        threshold: 20,
        before: "transparent",
        after: "var(--color-bg)",
      },
      borderBottomWidth: {
        threshold: 20,
        before: 0,
        after: "var(--border-w-sm)",
      },
    },
    directionStyles: props.isPinned
      ? undefined
      : {
          visibility: { forward: "hidden", backward: "visible" },
        },
  };

  // Build left content with BackAppbarAction and slot widgets
  const leftContent = (
    <div className="flex items-center gap-2">
      <BackAppbarAction />
      {slots?.left?.map((item) => (
        <div key={item.id}>{buildPersonalizationWidget(item)}</div>
      ))}
    </div>
  );

  // Build right content from slot widgets
  const rightContent = slots?.right ? (
    <div className="flex items-center gap-2">
      {slots.right.map((item) => (
        <div key={item.id}>{buildPersonalizationWidget(item)}</div>
      ))}
      {isHotwireNative() || window.flutter_inappwebview ? null : (
        <HamburgerMenu>
          <button className="flex size-9 items-center justify-center rounded-full bg-white">
            <img src={menu} alt="hamburger" className="h-4 w-4 invert" />
          </button>
        </HamburgerMenu>
      )}
    </div>
  ) : undefined;

  return (
    <AppBar
      {...rest}
      id={id}
      key={id}
      scrollStyleOptions={scrollStyleOptions}
      left={leftContent}
      right={rightContent}
    >
      {title ? i18next.t(title) : undefined}
    </AppBar>
  );
}
