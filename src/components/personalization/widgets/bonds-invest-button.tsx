import { useMemo, type HTMLAttributes } from "react";
import { useParams } from "react-router-dom";
import { useSuspenseQuery } from "@tanstack/react-query";
import Button from "@/components/ui/button/button";
import { useTranslation } from "react-i18next";

import { getBondDetailsQueryOptions } from "@/queries/bonds";
import { trackEvent } from "@/utils/analytics";
import { toaster } from "@/components/ui/toast/store";
import type { BondsInvestButtonProps } from "@/clients/gen/personalization_api";

type BondsInvestButtonComponentProps = BondsInvestButtonProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsInvestButton({
  label = "Start investing",
  completeKYCLabel = "Complete KYC",
  soldOutLabel = "Sold out",
  isKYCCompleted = true,
  isBondSoldOut = false,
  ...rest
}: BondsInvestButtonComponentProps) {
  const { t } = useTranslation();
  const params = useParams() as { bond_id: string };
  const bondId = params.bond_id as string;
  const { data: bondDetails } = useSuspenseQuery(
    getBondDetailsQueryOptions(bondId)
  );

  const issuer = useMemo(
    () => bondDetails?.aboutTheInstitution?.slug ?? "unknown",
    [bondDetails]
  );

  // Determine the button text based on conditions
  const buttonText = useMemo(() => {
    if (isBondSoldOut) {
      return t(soldOutLabel);
    }
    if (!isKYCCompleted) {
      return t(completeKYCLabel);
    }
    return t(label);
  }, [t, isBondSoldOut, isKYCCompleted, soldOutLabel, completeKYCLabel, label]);

  const handleSoldOutButtonClicked = () => {
    trackEvent("bond_notify_clicked", {
      bondName: bondDetails?.bondName,
      bondId: bondDetails?.id,
    });
    toaster.create({
      title: `Sure, we'll notify you once this bond is available again`,
      type: "info",
      duration: 3000,
    });
  };

  const handleInvestNowButtonClicked = () => {
    trackEvent("bond_page_invest_now_clicked", {
      bondId: bondDetails?.id,
      bondName: bondDetails?.bondName,
    });
  };

  return (
    <div className="px-5 md:hidden" data-widget="BondsInvestButton" {...rest}>
      {isBondSoldOut ? (
        <Button onClick={handleSoldOutButtonClicked}>{buttonText}</Button>
      ) : (
        <Button
          href={`/bonds/${issuer}/${bondId}/calculator`}
          onClick={handleInvestNowButtonClicked}
        >
          {buttonText}
        </Button>
      )}
    </div>
  );
}
