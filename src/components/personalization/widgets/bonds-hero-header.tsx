import { useRef, useMemo, type HTMLAttributes } from "react";
import clsx from "clsx";
import type {
  BondsHeroHeaderProps,
  BondsHeroHeader as BondsHeroHeaderWidget,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";
import classes from "./bonds-hero-header.module.css";

type BondsHeroHeaderComponentProps = BondsHeroHeaderProps &
  HTMLAttributes<HTMLDivElement> & { id?: string };

export default function BondsHeroHeader({
  backgroundColor,
  children,
  className,
  ...rest
}: BondsHeroHeaderComponentProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const style = useMemo(() => {
    const styles: React.CSSProperties = {};

    if (backgroundColor) {
      styles.backgroundColor = backgroundColor;
    }

    return styles;
  }, [backgroundColor]);

  return (
    <div
      ref={containerRef}
      style={style}
      {...rest}
      className={clsx(className, classes.heroHeader)}
      data-widget="BondsHeroHeader"
    >
      {children}
    </div>
  );
}

export function buildBondsHeroHeader(widget: BondsHeroHeaderWidget) {
  const { props, id, slots } = widget;
  return (
    <BondsHeroHeader
      {...props}
      isContentExpandable={props?.isContentExpandable ?? false}
      id={id}
      key={id}
    >
      {slots?.content && buildPersonalizationWidget(slots.content)}
    </BondsHeroHeader>
  );
}
