import { type HTMLAttributes } from "react";
import Card from "./card";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import { useTranslation } from "react-i18next";
import { trackEvent } from "@/utils/analytics";
import type { BondsSupportedDocumentsProps } from "@/clients/gen/personalization_api";
import ArrowLink from "@/components/icons/arrow-link";

type BondsSupportedDocumentsComponentProps = BondsSupportedDocumentsProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsSupportedDocuments({
  title,
  supportedDocuments = [],
  bondId,
  ...rest
}: BondsSupportedDocumentsComponentProps) {
  const { t } = useTranslation();
  const handleDocumentClick = (documentName: string) => {
    trackEvent("important_document_clicked", {
      document_name: documentName,
      bond_id: bondId,
    });
  };

  if (!supportedDocuments.length) {
    return null;
  }

  return (
    <div className="space-y-4" {...rest}>
      {title && (
        <div className="pl-5">
          <SectionHeading title={t(title)} separator />
        </div>
      )}

      <div className="px-5">
        <Card>
          <div className="divide-y">
            {supportedDocuments.map((document, index) => (
              <a
                key={document.url || index}
                href={document.url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-between px-4 py-3 hover:bg-gray-50"
                onClick={() =>
                  document.name && handleDocumentClick(document.name)
                }
              >
                <span className="text-body1">{document.name}</span>
                <ArrowLink className="size-2.5" />
              </a>
            ))}
          </div>
        </Card>
      </div>
    </div>
  );
}
