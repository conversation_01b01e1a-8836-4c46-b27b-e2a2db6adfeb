import { type HTMLAttributes } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  TitleType,
  type SingleCategoryFAQProps,
} from "@/clients/gen/personalization_api";
import QueryRenderer from "@/components/functional/query-renderer";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import Surface from "@/components/ui/surface/surface";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import { getFAQsQueryOptions } from "@/queries/business";

type SingleCategoryFAQComponentProps = SingleCategoryFAQProps &
  HTMLAttributes<HTMLDivElement>;

export default function SingleCategoryFAQ({
  faqCategory,
  faqNamespaceIdentifier,
  title,
  titleType,
  isDarkMode = false,
  ...rest
}: SingleCategoryFAQComponentProps) {
  const query = useQuery(
    getFAQsQueryOptions({
      namespace: faqCategory,
      identifier: faqNamespaceIdentifier,
    })
  );

  return (
    <QueryRenderer query={query}>
      {(response) => {
        if (!response.faqs.length) {
          return null;
        }

        return (
          <div className="space-y-4 px-5" {...rest}>
            <SectionHeading
              title={title ?? "Frequently asked questions"}
              size={titleType === TitleType.mainHeader ? "medium" : "small"}
              separator={titleType === TitleType.centeredWithDivider}
              isDarkMode={isDarkMode}
            />
            <Surface elevation="md" isDarkMode={isDarkMode}>
              <Accordion isDarkMode={isDarkMode}>
                {response.faqs.map((faq) => (
                  <AccordionItem
                    key={faq.question}
                    id={faq.faqId}
                    label={faq.question}
                    isDarkMode={isDarkMode}
                  >
                    {faq.answer}
                  </AccordionItem>
                ))}
              </Accordion>
            </Surface>
          </div>
        );
      }}
    </QueryRenderer>
  );
}
