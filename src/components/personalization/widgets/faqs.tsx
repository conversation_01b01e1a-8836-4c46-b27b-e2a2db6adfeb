import { useState, useMemo, type HTMLAttributes } from "react";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import ChipItem from "@/components/ui/chip/chip-item";
import ChipGroup from "@/components/ui/chip/chip-group";
import { trackEvent } from "@/utils/analytics";
import type { FaqProps } from "@/clients/gen/personalization_api";
import { Field, Form, Formik } from "formik";
import Input from "@/components/ui/input/input";
import searchImage from "@/assets/images/icons/search.svg";

type FaqsComponentProps = FaqProps & HTMLAttributes<HTMLDivElement>;

export default function Faqs({
  faqs = [],
  categoryList = [],
  ...rest
}: FaqsComponentProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [searchText, setSearchText] = useState<string>("");

  const filteredFaqs = useMemo(() => {
    let filtered = faqs;

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter((faq) =>
        faq.externalTags.includes(selectedCategory)
      );
    }

    // Filter by search text
    if (searchText.trim()) {
      const searchLower = searchText.toLowerCase().trim();
      filtered = filtered.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchLower) ||
          faq.answer.toLowerCase().includes(searchLower)
      );
    }

    return filtered;
  }, [faqs, selectedCategory, searchText]);

  const handleFaqClick = (faq: { question: string; answer: string }) => {
    trackEvent("faq_clicked", {
      tag: selectedCategory,
      faq_question: faq?.question,
      search_text: searchText,
    });
  };

  const handleSearch = (searchValue: string) => {
    setSearchText(searchValue);
    if (searchValue.trim()) {
      trackEvent("faq_search", {
        search_text: searchValue,
        category: selectedCategory,
      });
    }
  };

  return (
    <div {...rest}>
      <div className="px-4">
        <Formik
          initialValues={{
            searchText: "",
          }}
          onSubmit={(values) => {
            handleSearch(values.searchText);
          }}
        >
          {({ errors, setFieldValue }) => {
            return (
              <Form id="faq-search-form">
                <Field
                  name="searchText"
                  label="Search here"
                  as={Input}
                  error={errors.searchText}
                  prefix={<img src={searchImage} />}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    const value = e.target.value;
                    setFieldValue("searchText", value);
                    handleSearch(value);
                  }}
                />
              </Form>
            );
          }}
        </Formik>
      </div>
      {categoryList.length > 0 && (
        <ChipGroup
          name="category"
          value={selectedCategory}
          onChange={(event) =>
            setSelectedCategory((event.target as HTMLInputElement).value)
          }
          className="scrollbar-none my-5 space-x-2 overflow-x-scroll px-5 whitespace-nowrap"
        >
          {categoryList.map((category) => (
            <ChipItem key={category} value={category} size="large">
              {category}
            </ChipItem>
          ))}
        </ChipGroup>
      )}

      {filteredFaqs.length > 0 ? (
        <Accordion>
          {filteredFaqs.map((faq) => (
            <AccordionItem
              key={faq.question}
              id={faq.faqId}
              onClick={() => handleFaqClick(faq)}
              label={
                <div className="flex">
                  {faq.userImage && (
                    <img
                      decoding="sync"
                      src={faq.userImage}
                      alt={faq.userName}
                      className="h-10 w-10"
                    />
                  )}
                  <div className="flex flex-col space-y-1 px-4">
                    <p className="text-body2 text-black-50">
                      {faq.userName}
                      {faq.location ? ` from ${faq.location}` : ""} asks:
                    </p>
                    <p className="text-heading4">{faq.question}</p>
                  </div>
                </div>
              }
            >
              <p className="text-body2 text-black-50">{faq.answer}</p>
            </AccordionItem>
          ))}
        </Accordion>
      ) : (
        <div className="px-4 py-8 text-center">
          <p className="text-body1 text-black-50">
            {searchText.trim()
              ? `No FAQs found for "${searchText}"${selectedCategory ? ` in ${selectedCategory}` : ""}`
              : selectedCategory
                ? `No FAQs found in ${selectedCategory}`
                : "No FAQs available"}
          </p>
        </div>
      )}
    </div>
  );
}
