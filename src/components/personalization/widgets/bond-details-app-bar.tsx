import { type ReactNode, type HTMLAttributes } from "react";
import { useParams } from "react-router-dom";
import { useSuspenseQuery } from "@tanstack/react-query";
import AppBar from "@/components/ui/app-bar/app-bar";
import { useScrollStyle } from "@/hooks/scroll-styles";
import type { ScrollStyleParams } from "@/hooks/scroll-styles";
import { getBondDetailsQueryOptions } from "@/queries/bonds";
import BackAppbarAction from "@/components/functional/back-appbar-action";
import type { AppBarProps } from "@/clients/gen/personalization_api";
import SupportAppbarAction from "@/components/functional/support-appbar-action";

type BondDetailsAppBarComponentProps = AppBarProps &
  HTMLAttributes<HTMLElement> & {
    left?: ReactNode;
    right?: ReactNode;
  };

export default function BondDetailsAppBar({
  ...rest
}: BondDetailsAppBarComponentProps) {
  const params = useParams();
  const bond_id = params.bond_id as string;
  const { data: bondDetails } = useSuspenseQuery(
    getBondDetailsQueryOptions(bond_id)
  );

  const scrollStyleOptions: ScrollStyleParams = {
    offsetStyles: {
      backgroundColor: {
        threshold: 20,
        before: "transparent",
        after: "var(--color-bg)",
      },
      boxShadow: {
        threshold: 20,
        before: "none",
        after: "0 2px 4px rgba(0, 0, 0, 0.1)",
      },
    },
  };

  const titleRef = useScrollStyle({
    offsetStyles: {
      opacity: { threshold: 20, before: "0", after: "1" },
    },
  });

  return (
    <AppBar
      scrollStyleOptions={scrollStyleOptions}
      className={"fixed! right-0 left-0 md:hidden"}
      left={<BackAppbarAction />}
      right={<SupportAppbarAction />}
      {...rest}
    >
      <span ref={titleRef}>{bondDetails?.displayTitle}</span>
    </AppBar>
  );
}
