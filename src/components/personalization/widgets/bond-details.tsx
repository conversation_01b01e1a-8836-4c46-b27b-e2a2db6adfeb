import { type ReactNode, type HTMLAttributes } from "react";
import { useSuspenseQuery } from "@tanstack/react-query";
import { useMediaQuery } from "@react-hook/media-query";
import Surface from "@/components/ui/surface/surface";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { getBondDetailsQueryOptions } from "@/queries/bonds";
import { useViewEvent } from "@/hooks/view-event";
import type {
  BondDetailsProps,
  BondDetailsPropsViewEventProps,
  BondDetails as BondDetailsWidget,
} from "@/clients/gen/personalization_api";
import type { BondDetailItem } from "@/clients/gen/broking/BondDetails_pb";
import { buildPersonalizationWidget } from "../builder";
import { useParams } from "react-router";
import BondDetailsAppBar from "./bond-details-app-bar";
import clsx from "clsx";

type BondDetailsComponentProps = BondDetailsProps &
  HTMLAttributes<HTMLDivElement> & {
    viewEventProps?: BondDetailsPropsViewEventProps;
    children?: ReactNode;
    header?: ReactNode;
    heroHeader?: ReactNode;
    bottomButton?: ReactNode;
  };

type BondDetailsContentProps = {
  bondDetails: BondDetailItem;
  tags?: string[];
  name?: string;
  children?: ReactNode;
};

function BondDetailsContent({
  bondDetails,
  tags,
  name,
  children,
}: BondDetailsContentProps) {
  return (
    <div className="bg-bg">
      <div className="mb-6 space-y-1.5 px-5 pt-10">
        <h3 className="text-heading3 font-medium">
          {bondDetails?.aboutTheInstitution?.title || name || "Bond Details"}
        </h3>

        {((bondDetails?.bondTags && bondDetails?.bondTags.length > 0) ||
          (tags && tags.length > 0)) && (
          <div className="text-title-all-caps text-black-50 flex items-center gap-2 font-medium">
            {bondDetails?.bondTags && bondDetails?.bondTags.length > 0
              ? bondDetails.bondTags.map((tag, index) => (
                  <span key={tag.name}>
                    {tag.name}
                    {index < bondDetails.bondTags.length - 1 && (
                      <span className="bg-black-50 ml-2 inline-block h-1 w-1 rounded-full"></span>
                    )}
                  </span>
                ))
              : tags?.map((tag, index) => (
                  <span key={tag}>
                    {tag}
                    {index < tags.length - 1 && (
                      <span className="bg-black-50 ml-2 inline-block h-1 w-1 rounded-full"></span>
                    )}
                  </span>
                ))}
          </div>
        )}
      </div>

      <div className="space-y-8 pb-8">{children}</div>
    </div>
  );
}

export default function BondDetails({
  tags = [],
  name,
  logo,
  logoBg,
  viewEventProps,
  children: pageChildren,
  header,
  heroHeader,
  bottomButton,
  className,
  ...rest
}: BondDetailsComponentProps) {
  const params = useParams();
  const bond_id = params.bond_id as string;
  const { data: bondDetails } = useSuspenseQuery(
    getBondDetailsQueryOptions(bond_id)
  );
  const isDesktop = useMediaQuery("(min-width: 768px)");

  const { ref } = useViewEvent({
    eventName: "dynamic_base_page",
    eventProperties: viewEventProps,
  });

  return (
    <>
      {header}
      <div
        ref={ref}
        className={clsx(className, "floating-footer-margin")}
        {...rest}
      >
        {/* Hero header */}
        {heroHeader && (
          <div className="fixed top-0 right-0 left-0 overflow-hidden md:relative md:rounded-t-xl">
            {heroHeader}
          </div>
        )}

        <div className="relative mt-[30dvh] md:mt-0">
          <div className="absolute -top-7 left-5 z-10">
            <EntityLogo
              url={bondDetails?.aboutTheInstitution?.logo || logo || ""}
              size="large"
              color={bondDetails?.bgColor || logoBg || "#f5f5f5"}
              seamless={true}
              elevation="md"
            />
          </div>

          {isDesktop ? (
            <BondDetailsContent
              bondDetails={bondDetails}
              tags={tags}
              name={name}
            >
              {pageChildren}
            </BondDetailsContent>
          ) : (
            <Surface elevation="md">
              <BondDetailsContent
                bondDetails={bondDetails}
                tags={tags}
                name={name}
              >
                {pageChildren}
              </BondDetailsContent>
            </Surface>
          )}
        </div>

        {bottomButton && !isDesktop && (
          <FloatingFooterContent
            className="-mx-5"
            scrollStyleParams={{
              offsetStyles: {
                display: { threshold: 700, before: "none", after: "block" },
              },
            }}
          >
            {bottomButton}
          </FloatingFooterContent>
        )}
      </div>
    </>
  );
}

export function buildBondDetails(widget: BondDetailsWidget) {
  const { props, slots, id } = widget;
  const appBar = slots?.header?.widget === "AppBar" ? slots.header : undefined;
  const header = appBar ? <BondDetailsAppBar {...appBar.props} /> : undefined;

  const childrenContent =
    slots?.children?.map((child) => buildPersonalizationWidget(child)) || [];
  const heroHeaderContent = slots?.heroHeader
    ? buildPersonalizationWidget(slots.heroHeader)
    : undefined;
  const bottomButtonContent = slots?.bottomButton
    ? buildPersonalizationWidget(slots.bottomButton)
    : undefined;

  return (
    <BondDetails
      {...props}
      id={id}
      key={id}
      viewEventProps={{ ...props?.viewEventProps, path: "bond-details" }}
      header={header}
      heroHeader={heroHeaderContent}
      bottomButton={bottomButtonContent}
    >
      {childrenContent}
    </BondDetails>
  );
}
