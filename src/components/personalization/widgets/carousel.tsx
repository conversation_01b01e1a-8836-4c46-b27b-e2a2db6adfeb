import { type ReactNode, type HTMLAttributes, useMemo } from "react";
import type {
  CarouselProps,
  Carousel as CarouselWidget,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";

type CarouselComponentProps = CarouselProps & {
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export default function Carousel({
  height,
  heightPercentage,
  children,
  className,
  style,
  ...rest
}: CarouselComponentProps) {
  // Note: showIndicators logic removed as it's not used in this component

  // Calculate the style for height
  const carouselStyle = useMemo(() => {
    const baseStyle = style || {};

    if (height) {
      return { ...baseStyle, height: `${height}px` };
    } else if (heightPercentage) {
      return { ...baseStyle, height: `${heightPercentage * 100}vh` };
    }

    return baseStyle;
  }, [height, heightPercentage, style]);

  return (
    <div style={carouselStyle} className={className} {...rest}>
      {children}
    </div>
  );
}

export function buildCarousel(widget: CarouselWidget) {
  const { props, id, slots } = widget;
  const defaultHeight = props?.height ?? 200;

  return (
    <Carousel {...props} height={defaultHeight} id={id} key={id}>
      {slots?.children?.map((child) => buildPersonalizationWidget(child))}
    </Carousel>
  );
}
