import { useMemo, type ReactNode, type HTMLAttributes } from "react";
import type {
  MarketingListWithCoverProps,
  MarketingListWithCover as MarketingListWithCoverWidget,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";

type MarketingListWithCoverComponentProps = MarketingListWithCoverProps & {
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export default function MarketingListWithCover({
  url,
  aspectRatio,
  height,
  width,
  children,
  id,
  ...rest
}: MarketingListWithCoverComponentProps) {
  // Generate styles for the container
  const containerStyle = useMemo(() => {
    const styles: React.CSSProperties = {
      backgroundImage: `url(${url})`,
      aspectRatio: `${aspectRatio ?? 1}`,
    };

    if (height) {
      styles.height = `${height}px`;
    }
    if (width) {
      styles.width = `${width}px`;
    }

    return styles;
  }, [url, aspectRatio, height, width]);

  return (
    <div
      className="marketing-list-with-cover relative bg-cover bg-center"
      style={containerStyle}
      id={id}
      data-widget="MarketingListWithCover"
      {...rest}
    >
      <div
        className="scrollbar-none absolute right-0 bottom-5 left-0 space-x-2.5 overflow-x-auto px-5 py-5 whitespace-nowrap [&_img]:aspect-[17/28] [&_img]:!h-full"
        data-scroll-id={`marketing-list-with-cover-${id}`}
      >
        {children}
      </div>
    </div>
  );
}

export function buildMarketingListWithCover(
  widget: MarketingListWithCoverWidget
) {
  const { props, id, slots } = widget;
  const sliderHeight = props.sliderHeight ?? 200;

  return (
    <MarketingListWithCover {...props} id={id} key={id}>
      {slots?.children?.map((item) => (
        <div
          key={item.id}
          className="inline-block"
          style={{ height: `${sliderHeight - 20}px` }}
        >
          {buildPersonalizationWidget(item)}
        </div>
      ))}
    </MarketingListWithCover>
  );
}
