import { type ReactNode, type H<PERSON>LAttributes } from "react";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import type {
  HorizontalListSectionProps,
  HorizontalListSection as HorizontalListSectionWidget,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";

type HorizontalListSectionComponentProps = HorizontalListSectionProps & {
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export default function HorizontalListSection({
  title,
  children,
  id,
  className,
  ...rest
}: HorizontalListSectionComponentProps) {
  // Note: Item styles are calculated in the builder function

  return (
    <div id={id} className={className} {...rest}>
      {title && <SectionHeading title={title} size="medium" className="mx-5" />}
      <div
        className="scrollbar-none -mt-6 -mb-10 space-x-2.5 overflow-x-auto px-5 py-10 whitespace-nowrap"
        data-scroll-id={`horizontal-list-section-${id}`}
      >
        {children}
      </div>
    </div>
  );
}

export function buildHorizontalListSection(
  widget: HorizontalListSectionWidget
) {
  const { props, id, slots } = widget;

  // Calculate item styles for children
  const itemStyle: React.CSSProperties = {};

  if (props.height) {
    itemStyle.height = `${props.height}px`;
  }

  if (props.addItemShadow) {
    itemStyle.filter = "drop-shadow(0 0 25px rgba(0, 0, 0, 0.05))";
  }

  itemStyle.width = `${(props.viewPort ?? 0.8) * 100}vw`;

  return (
    <HorizontalListSection {...props} id={id} key={id}>
      {slots?.children?.map((item) => (
        <div key={item.id} className="inline-block" style={itemStyle}>
          {buildPersonalizationWidget(item)}
        </div>
      ))}
    </HorizontalListSection>
  );
}
