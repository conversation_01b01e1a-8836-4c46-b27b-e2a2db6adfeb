import { type ReactNode, type HTMLAttributes } from "react";

import { trackEvent } from "@/utils/analytics";
import type { HorizontalCollectionItem as HorizontalCollectionItemWidget } from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";
import Anchor from "@/components/functional/anchor";

type HorizontalCollectionItemComponentProps = {
  collectionName?: string;
  title?: string;
  children?: ReactNode;
} & HTMLAttributes<HTMLAnchorElement>;

export default function HorizontalCollectionItem({
  collectionName,
  title,
  children,
  className,
  ...rest
}: HorizontalCollectionItemComponentProps) {
  const handleClick = () => {
    trackEvent("usecase_collection_clicked", {
      collectionName: collectionName,
      collectionItem: title,
    });
  };

  // Create a simple route function for collections
  const getCollectionUrl = (collection: string) => {
    return `/collections/${collection}?displayType=userBased`;
  };

  return (
    <Anchor
      href={getCollectionUrl(collectionName!)}
      onClick={handleClick}
      className={className}
      {...rest}
    >
      <div className="flex min-w-17 flex-col items-center justify-center gap-2">
        <div className="flex h-12 w-12 flex-col gap-1">{children}</div>
        <p className="text-body1 text-black-80 text-center">{title}</p>
      </div>
    </Anchor>
  );
}

export function buildHorizontalCollectionItem(
  widget: HorizontalCollectionItemWidget
) {
  const { props, id } = widget;

  return (
    <HorizontalCollectionItem
      collectionName={props.collectionName}
      title={props.title}
      id={id}
      key={id}
    >
      {props.icon && buildPersonalizationWidget(props.icon)}
    </HorizontalCollectionItem>
  );
}
