import { type ReactNode, type H<PERSON>LAttributes } from "react";
import type {
  BondsSingleMediaProps,
  BondsSingleMedia as BondsSingleMediaWidget,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";

type BondsSingleMediaComponentProps = BondsSingleMediaProps & {
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export default function BondsSingleMedia({
  children,
  ...rest
}: BondsSingleMediaComponentProps) {
  return (
    <div className="space-y-4" {...rest}>
      {children}
    </div>
  );
}

export function buildBondsSingleMedia(widget: BondsSingleMediaWidget) {
  const { props, id, slots } = widget;

  if (!slots?.content) {
    return null;
  }
  return (
    <BondsSingleMedia
      {...props}
      section={props?.section ?? ""}
      id={id}
      key={id}
      data-widget="BondsSingleMedia"
    >
      {slots?.content && buildPersonalizationWidget(slots.content)}
    </BondsSingleMedia>
  );
}
