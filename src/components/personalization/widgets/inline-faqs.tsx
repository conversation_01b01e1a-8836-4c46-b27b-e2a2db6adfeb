import { type HTMLAttributes } from "react";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import Surface from "@/components/ui/surface/surface";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import LinkButton from "@/components/ui/button/link-button";
import type { FaqProps } from "@/clients/gen/personalization_api";
import clsx from "clsx";
import { useTranslation } from "react-i18next";

type InlineFaqsComponentProps = FaqProps & HTMLAttributes<HTMLDivElement>;

export default function InlineFaqs({
  faqs = [],
  className,
  title,
  path,
  ...rest
}: InlineFaqsComponentProps) {
  const { t } = useTranslation();

  return (
    <div
      className={clsx("space-y-4 px-5", className)}
      data-widget="InlineFaqs"
      {...rest}
    >
      {title ? <SectionHeading title={t(title)} size="medium" /> : null}

      <Surface elevation="md">
        <Accordion>
          {faqs.map((faq) => (
            <AccordionItem
              key={faq.faqId}
              id={faq.faqId}
              label={
                <div className="flex">
                  {faq.userImage && (
                    <img
                      decoding="sync"
                      src={faq.userImage}
                      alt={faq.userName}
                      className="h-10 w-10"
                    />
                  )}
                  <div className="flex flex-col space-y-1 pl-4">
                    <p className="text-body2 text-black-50">
                      {faq.userName}
                      {faq.location ? ` from ${faq.location}` : ""} asks:
                    </p>
                    <h4 className="text-heading4">{faq.question}</h4>
                  </div>
                </div>
              }
            >
              <p className="text-body2 text-black-50">{faq.answer}</p>
            </AccordionItem>
          ))}
        </Accordion>
      </Surface>

      <div className="mt-2 flex justify-center">
        <LinkButton href={`/dynamic/${path}`}>View all</LinkButton>
      </div>
    </div>
  );
}
