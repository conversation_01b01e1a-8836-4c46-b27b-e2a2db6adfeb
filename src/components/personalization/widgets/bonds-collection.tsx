import { useState, type HTMLAttributes } from "react";
import { useQuery } from "@tanstack/react-query";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import LinkButton from "@/components/ui/button/link-button";
import QueryRenderer from "@/components/functional/query-renderer";
import CollectionItem from "@/components/bonds/collection-item";
import IndexedCollectionItem from "@/components/bonds/indexed-collection-item";
import { createCollectionQueryOptions } from "@/queries/bonds";

import {
  BondsCollectionCardType,
  type BondsCollectionProps,
} from "@/clients/gen/personalization_api";

type BondsCollectionComponentProps = BondsCollectionProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsCollection({
  collectionName,
  cardType,
  ...rest
}: BondsCollectionComponentProps) {
  const query = useQuery(createCollectionQueryOptions(collectionName));
  const [scrolled, setScrolled] = useState(false);

  const handleScroll = (e: React.UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    setScrolled(target.scrollLeft > 0);
  };

  const loader = (
    <div className="min-h-[320px] animate-pulse space-y-4 p-5">
      <div className="bg-black-5 h-5 w-48 rounded-lg"></div>
      <div className="scrollbar-none space-x-3 overflow-x-auto whitespace-nowrap">
        <div className="bg-black-5 inline-block h-[260px] w-[160px] rounded-xl"></div>
        <div className="bg-black-5 inline-block h-[260px] w-[160px] rounded-xl"></div>
        <div className="bg-black-5 inline-block h-[260px] w-[160px] rounded-xl"></div>
      </div>
    </div>
  );

  return (
    <div {...rest} data-widget="BondsCollection">
      <QueryRenderer query={query} loader={loader}>
        {(collection) => (
          <>
            <SectionHeading
              size="medium"
              title={collection.title}
              className="relative pr-2 pl-5"
            >
              {scrolled && (
                <LinkButton
                  href={`/collections/${collectionName}?displayType=collection`}
                >
                  View all
                </LinkButton>
              )}
            </SectionHeading>
            <div
              className="scrollbar-none -mt-6 -mb-10 space-x-3 overflow-x-auto px-5 py-10 whitespace-nowrap"
              data-scroll-id={`collection-scroller-${collectionName}`}
              onScroll={handleScroll}
            >
              {collection.collectionItem.map((item, index) => {
                if (cardType === BondsCollectionCardType.indexedCard) {
                  return (
                    <div key={item.id} className="inline-block">
                      <IndexedCollectionItem item={item} rank={index} />
                    </div>
                  );
                } else {
                  return (
                    <CollectionItem
                      key={item.id}
                      item={item}
                      collectionName={collectionName}
                      rank={index}
                      className="w-[160px]"
                    />
                  );
                }
              })}
            </div>
          </>
        )}
      </QueryRenderer>
    </div>
  );
}
