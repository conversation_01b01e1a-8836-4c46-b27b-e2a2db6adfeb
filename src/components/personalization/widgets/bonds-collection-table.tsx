import { useState, useMemo, type HTMLAttributes } from "react";
import { useQuery } from "@tanstack/react-query";
import QueryRenderer from "@/components/functional/query-renderer";
import Table from "@/components/ui/table/table";
import TableHead from "@/components/ui/table/table-head";
import TableRow from "@/components/ui/table/table-row";
import TableHeadCell from "@/components/ui/table/table-head-cell";
import TableBody from "@/components/ui/table/table-body";
import TableCell from "@/components/ui/table/table-cell";
import EntityLogo from "@/components/ui/entity-logo/entity-logo";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import Anchor from "@/components/functional/anchor";
import Card from "./card";
import { createCollectionQueryOptions } from "@/queries/bonds";
import { makeBondUrl } from "@/utils/routing";
import { getBondDetailsEventProps, trackEvent } from "@/utils/analytics";
import type { BondsCollectionTableProps } from "@/clients/gen/personalization_api";
import type { CollectionItem } from "@/clients/gen/broking/Collection_pb";
import { formatAmount } from "@/utils/number";

type BondsCollectionTableComponentProps = BondsCollectionTableProps &
  HTMLAttributes<HTMLDivElement>;

type SortName = "tenure" | "ytm";
type SortDirection = "asc" | "desc";

export default function BondsCollectionTable({
  collectionName,
  ...rest
}: BondsCollectionTableComponentProps) {
  const query = useQuery(createCollectionQueryOptions(collectionName));
  const [sortName, setSortName] = useState<SortName>("tenure");
  const [sortDirection, setSortDirection] = useState<SortDirection>("asc");

  const getSortedData = useMemo(() => {
    return (items: CollectionItem[]) => {
      return items.sort((a, b) => {
        if (sortName === "tenure") {
          return sortDirection === "asc"
            ? a.tenureInDays - b.tenureInDays
            : b.tenureInDays - a.tenureInDays;
        } else {
          return sortDirection === "asc" ? a.xirr - b.xirr : b.xirr - a.xirr;
        }
      });
    };
  }, [sortName, sortDirection]);

  const sortByTenure = () => {
    setSortName("tenure");
    setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const sortByYtm = () => {
    setSortName("ytm");
    setSortDirection((prev) => (prev === "asc" ? "desc" : "asc"));
  };

  const handleBondsTableClicked = (item: CollectionItem) => {
    trackEvent("bond_table_collection_clicked", getBondDetailsEventProps(item));
  };

  const loader = (
    <TableRow>
      <TableCell>
        <div className="bg-black-5 h-5 w-16 animate-pulse rounded-lg"></div>
      </TableCell>
      <TableCell>
        <div className="bg-black-5 h-5 w-16 animate-pulse rounded-lg"></div>
      </TableCell>
      <TableCell>
        <div className="bg-black-5 h-5 w-32 animate-pulse rounded-lg"></div>
      </TableCell>
    </TableRow>
  );

  return (
    <QueryRenderer query={query} loader={loader}>
      {(collection) => (
        <div className="space-y-4 px-5" {...rest}>
          <SectionHeading title={collection.title} size="medium" />
          <Card>
            <Table variant="secondary" horizontal vertical>
              <TableHead>
                <TableRow>
                  <TableHeadCell
                    onSort={sortByTenure}
                    sortDirection={
                      sortName === "tenure" ? sortDirection : undefined
                    }
                    className="p-4"
                  >
                    Tenure
                  </TableHeadCell>
                  <TableHeadCell
                    onSort={sortByYtm}
                    sortDirection={
                      sortName === "ytm" ? sortDirection : undefined
                    }
                    className="p-4"
                  >
                    YTM
                  </TableHeadCell>
                  <TableHeadCell className="p-4">Bond</TableHeadCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {getSortedData(collection.collectionItem).map((item) => (
                  <TableRow key={item.isinCode}>
                    <TableCell className="flex w-28">
                      <Anchor
                        href={makeBondUrl(item)}
                        onClick={() => handleBondsTableClicked(item)}
                        className="block shrink-0 px-4 py-3"
                      >
                        <span className="text-black-60">{item.tenure}</span>
                      </Anchor>
                    </TableCell>
                    <TableCell>
                      <Anchor
                        href={makeBondUrl(item)}
                        className="block shrink-0 px-4 py-3"
                        onClick={() => handleBondsTableClicked(item)}
                      >
                        <span className="inline-block border-b border-dashed pb-0.25">
                          {formatAmount(item.xirr)}%
                        </span>
                      </Anchor>
                    </TableCell>
                    <TableCell>
                      <Anchor
                        href={makeBondUrl(item)}
                        className="flex items-center gap-1 px-4 py-3"
                        onClick={() => handleBondsTableClicked(item)}
                      >
                        <EntityLogo
                          size="small"
                          url={item.aboutTheInstitution?.logo ?? ""}
                          elevation="none"
                        />
                        <span className="line-clamp-1 text-ellipsis">
                          {item.displayTitle}
                        </span>
                      </Anchor>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </Card>
        </div>
      )}
    </QueryRenderer>
  );
}
