import { useMemo, type HTMLAttributes } from "react";
import Card from "./card";
import type { BondsDisclosureIssuerDetailsProps } from "@/clients/gen/personalization_api";

type BondsDisclosureIssuerDetailsComponentProps =
  BondsDisclosureIssuerDetailsProps & HTMLAttributes<HTMLDivElement>;

export default function BondsDisclosureIssuerDetails({
  bondName,
  bondIssuerDescription,
  bondLogo,
  bondLogoBgColor,
  tags = [],
  ...rest
}: BondsDisclosureIssuerDetailsComponentProps) {
  const gradientStyle = useMemo(
    () => ({
      backgroundImage: `linear-gradient(to top right, #FFFFFF, ${bondLogoBgColor || "#f5f5f5"})`,
    }),
    [bondLogoBgColor]
  );

  return (
    <div className="px-5" {...rest}>
      <Card>
        <div className="m-0.5 rounded-xl" style={gradientStyle}>
          <div className="space-y-6 p-5">
            <div className="flex w-full justify-between">
              <div className="space-y-1">
                <div>
                  {bondName && (
                    <h1 className="text-heading4 text-black-80 tracking-[-0.2px]">
                      {bondName}
                    </h1>
                  )}
                </div>
                {tags && tags.length > 0 && (
                  <div className="text-black-50 text-title-all-caps flex items-center gap-2 font-medium">
                    {tags.map((tag, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <span>{tag}</span>
                        {index < tags.length - 1 && (
                          <span className="bg-black-50 inline-block h-1 w-1 rounded-full"></span>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
              {bondLogo && (
                <img
                  decoding="sync"
                  src={bondLogo}
                  alt={bondName ? `${bondName} logo` : "Bond logo"}
                  width="40"
                />
              )}
            </div>
            {bondIssuerDescription && (
              <p className="text-body2 text-black-50 tracking-0">
                {bondIssuerDescription}
              </p>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}
