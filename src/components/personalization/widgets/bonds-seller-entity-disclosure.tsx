import { type HTMLAttributes } from "react";
import type { BondsSellerEntityDisclosureProps } from "@/clients/gen/personalization_api";

type BondsSellerEntityDisclosureComponentProps =
  BondsSellerEntityDisclosureProps & HTMLAttributes<HTMLDivElement>;

export default function BondsSellerEntityDisclosure({
  sellerEntityDisclosure,
  ...rest
}: BondsSellerEntityDisclosureComponentProps) {
  return (
    <div className="pt-2 pb-8" {...rest}>
      {sellerEntityDisclosure && (
        <p className="text-black-40 text-body2 px-15 pb-3 text-center">
          {sellerEntityDisclosure}
        </p>
      )}
    </div>
  );
}
