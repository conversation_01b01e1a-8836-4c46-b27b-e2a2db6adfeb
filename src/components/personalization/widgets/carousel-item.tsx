import { type ReactN<PERSON>, type H<PERSON><PERSON>ttributes } from "react";
import type {
  CarouselItemProps,
  CarouselItem as CarouselItemWidget,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";

type CarouselItemComponentProps = CarouselItemProps & {
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export default function CarouselItem({
  children,
  ...rest
}: CarouselItemComponentProps) {
  return <div {...rest}>{children}</div>;
}

export function buildCarouselItem(widget: CarouselItemWidget) {
  const { props, id, slots } = widget;

  return (
    <CarouselItem {...props} id={id} key={id}>
      {slots?.content && buildPersonalizationWidget(slots.content)}
    </CarouselItem>
  );
}
