import { type ReactNode, type HTMLAttributes, useEffect } from "react";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import type {
  BasePageProps,
  BasePage as BasePageWidget,
} from "@/clients/gen/personalization_api";
import clsx from "clsx";
import { buildPersonalizationWidget } from "../builder";
import { useViewEvent } from "@/hooks/view-event";

type BasePageComponentProps = HTMLAttributes<HTMLDivElement> &
  BasePageProps & {
    header?: ReactNode;
    children?: ReactNode;
    emptyState?: ReactNode;
    footer?: ReactNode;
  };

export default function BasePage({
  extendBodyBehindAppBar,
  isEmptyState,
  viewEventProps,
  header,
  children,
  emptyState,
  footer,
  className,
  backgroundColor,
  ...rest
}: BasePageComponentProps) {
  const { ref } = useViewEvent({
    eventName: "dynamic_base_page",
    eventProperties: {
      ...viewEventProps,
    },
  });

  useEffect(() => {
    if (backgroundColor) {
      document.body.style.setProperty("--color-bg", backgroundColor);
    }

    return () => {
      if (backgroundColor) {
        document.body.style.removeProperty("--color-bg");
      }
    };
  }, [backgroundColor]);

  return (
    <div
      ref={ref}
      className={clsx("mx-auto max-w-3xl", className)}
      data-widget="BasePage"
      {...rest}
    >
      {header}
      <div
        className={clsx("floating-footer-padding flex flex-col gap-8", {
          "body-behind-appbar relative": extendBodyBehindAppBar,
        })}
      >
        {isEmptyState && emptyState ? emptyState : children}
      </div>

      {footer && <FloatingFooterContent>{footer}</FloatingFooterContent>}
    </div>
  );
}

export function buildBasePage(widget: BasePageWidget) {
  const { props, slots, id } = widget;

  // Build slot content using buildPersonalizationWidget
  const headerContent = slots.header
    ? buildPersonalizationWidget(slots.header)
    : undefined;
  const bodyContent =
    slots.body?.map((child) => buildPersonalizationWidget(child)) || [];
  const emptyStateContent = slots.emptyState
    ? buildPersonalizationWidget(slots.emptyState)
    : undefined;
  const footerContent = slots.footer
    ? buildPersonalizationWidget(slots.footer)
    : undefined;

  return (
    <BasePage
      {...props}
      id={id}
      key={id}
      viewEventProps={{ path: widget.id, ...props.viewEventProps }}
      header={headerContent}
      emptyState={emptyStateContent}
      footer={footerContent}
    >
      {bodyContent}
    </BasePage>
  );
}
