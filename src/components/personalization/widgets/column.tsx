import { type <PERSON>actNode, type H<PERSON>LAttributes } from "react";
import type {
  ColumnProps,
  Column as ColumnWidget,
  SpacingType,
} from "@/clients/gen/personalization_api";
import { buildPersonalizationWidget } from "../builder";
import clsx from "clsx";

type ColumnComponentProps = ColumnProps & {
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export default function Column({
  spacingType,
  children,
  className,
  ...rest
}: ColumnComponentProps) {
  // Map spacing to Tailwind CSS classes
  const getSpacingClass = (spacing?: SpacingType): string => {
    switch (spacing) {
      case "small":
        return "gap-2";
      case "medium":
        return "gap-4";
      case "large":
        return "gap-6";
      case "extraLarge":
        return "gap-8";
      default:
        return "";
    }
  };

  const spacingClass = getSpacingClass(spacingType);

  return (
    <div className={clsx("flex flex-col", spacingClass, className)} {...rest}>
      {children}
    </div>
  );
}

export function buildColumn(widget: ColumnWidget) {
  const { props, id, slots } = widget;

  return (
    <Column {...props} id={id} key={id}>
      {slots?.items?.map((item, index) => (
        <div key={item.id || index}>{buildPersonalizationWidget(item)}</div>
      ))}
    </Column>
  );
}
