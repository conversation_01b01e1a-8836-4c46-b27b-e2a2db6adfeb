import { useState, useMemo, useRef, type HTMLAttributes } from "react";
import clsx from "clsx";
import { rewritePath } from "@/utils/routing";
import { trackEvent } from "@/utils/analytics";
import type { ImageProps } from "@/clients/gen/personalization_api";
import Anchor from "@/components/functional/anchor";

type ImageComponentProps = ImageProps & HTMLAttributes<HTMLElement>;

export default function Image({
  url,
  className,
  srcSet = [],
  redirectLink,
  width,
  height,
  aspectRatio,
  fit,
  widthPercentage,
  type,
  analyticsEventProps,
  ...rest
}: ImageComponentProps) {
  const [loaded, setLoaded] = useState(false);
  const pictureRef = useRef<HTMLPictureElement>(null);

  // Generate styles for the image
  const imgStyles: React.CSSProperties = useMemo(() => {
    const styles: React.CSSProperties = {};

    if (aspectRatio) {
      styles.aspectRatio = `${aspectRatio}`;
    }

    if (fit) {
      styles.objectPosition = "center";
    }

    if (widthPercentage) {
      styles.width = `${widthPercentage * 100}%`;
    }

    if (width) {
      styles.width = `calc(${width}px * var(--sd-scale-factor, 1))`;
    }

    if (height) {
      styles.height = `calc(${height}px * var(--sd-scale-factor, 1))`;
    }

    return styles;
  }, [aspectRatio, fit, widthPercentage, width, height]);

  // Map fit to Tailwind classes
  const fitClass = useMemo(() => {
    if (!fit) return "";

    switch (fit.toLowerCase()) {
      case "cover":
        return "object-cover";
      case "contain":
        return "object-contain";
      case "fill":
        return "object-fill";
      case "none":
        return "object-none";
      case "scale-down":
        return "object-scale-down";
      default:
        return "";
    }
  }, [fit]);

  // Function to generate media query width
  function generateWidth(screenType: string): number {
    switch (screenType) {
      case "mobile":
        return 480;
      case "tablet":
        return 768;
      case "desktop":
        return 1024;
      default:
        return 0;
    }
  }

  function handleLoad() {
    setLoaded(true);
    pictureRef.current?.dispatchEvent(new CustomEvent("image:load"));
  }

  function handleImageClick() {
    trackEvent("dynamic_image_tapped", {
      url: url,
      type: type,
      ...analyticsEventProps,
    });
  }

  const pictureElement = (
    <picture ref={pictureRef} className={clsx(className, { loaded })} {...rest}>
      {srcSet?.map((src) => (
        <source
          key={src.url}
          srcSet={src.url}
          media={`(min-width: ${generateWidth(src.screenType)}px)`}
        />
      ))}
      <img
        decoding="sync"
        src={url}
        alt=""
        className={fitClass}
        style={imgStyles}
        onLoad={handleLoad}
      />
    </picture>
  );

  if (redirectLink) {
    return (
      <Anchor
        href={rewritePath(redirectLink.path)}
        className="contents-display no-underline"
        aria-label="Image link"
        target={redirectLink.pathType === "external" ? "_blank" : "_self"}
        onClick={handleImageClick}
      >
        {pictureElement}
      </Anchor>
    );
  }

  return pictureElement;
}
