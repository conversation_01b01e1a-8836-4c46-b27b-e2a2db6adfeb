import { useState, useMemo, useCallback, type HTMLAttributes } from "react";
import { useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import type { BondsReturnsCalculatorProps } from "@/clients/gen/personalization_api";
import SectionHeading from "@/components/ui/section-heading/section-heading";
import { useTranslation } from "react-i18next";
import Surface from "@/components/ui/surface/surface";
import Anchor from "@/components/functional/anchor";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import QueryRenderer from "@/components/functional/query-renderer";
import { formatToInr } from "@/utils/number";
import { trackEvent } from "@/utils/analytics";
import { isHotwireNative } from "@/utils/routing";
import {
  getBondDetailsQueryOptions,
  getBondCalculationQueryOptions,
} from "@/queries/bonds";
import BondsReturnSchedule from "@/components/bonds/bonds-return-schedule";

type BondsReturnsCalculatorComponentProps = BondsReturnsCalculatorProps &
  HTMLAttributes<HTMLDivElement>;

export default function BondsReturnsCalculator({
  title,
  initialUnits = 1,
  className,
  ...rest
}: BondsReturnsCalculatorComponentProps) {
  const { t } = useTranslation();
  const params = useParams();
  const bondId = params.bond_id as string;

  const [quantity, setQuantity] = useState(initialUnits);

  const bondDetailsQuery = useQuery(getBondDetailsQueryOptions(bondId));
  const bondDetails = bondDetailsQuery.data;

  const isHotWire = useMemo(() => isHotwireNative(), []);
  const issuer = useMemo(
    () => bondDetails?.aboutTheInstitution?.slug ?? "unknown",
    [bondDetails]
  );

  const calculationQuery = useQuery({
    ...getBondCalculationQueryOptions(bondDetails?.id || "", quantity),
    enabled: !!bondDetails?.id && !!quantity,
  });

  const decrease = useCallback(() => {
    const newQuantity = quantity - 1;
    setQuantity(newQuantity);

    trackEvent("returns_calculator_quantity_reduced", {
      bond_id: bondId,
      bond_name: bondDetails?.aboutTheInstitution?.bondInstitutionName,
      bond_type: bondDetails?.investabilityStatus,
      bond_xirr: bondDetails?.xirr,
      units: newQuantity,
    });
  }, [quantity, bondDetails, bondId]);

  const increase = useCallback(() => {
    const newQuantity = quantity + 1;
    setQuantity(newQuantity);

    trackEvent("returns_calculator_quantity_increased", {
      bond_id: bondId,
      bond_name: bondDetails?.aboutTheInstitution?.bondInstitutionName,
      bond_type: bondDetails?.investabilityStatus,
      bond_xirr: bondDetails?.xirr,
      units: newQuantity,
    });
  }, [quantity, bondDetails, bondId]);

  return (
    <div className={`space-y-4 px-5 pt-[1px] ${className || ""}`} {...rest}>
      {title && <SectionHeading title={t(title)} separator />}

      <Surface elevation="md">
        <QueryRenderer query={bondDetailsQuery}>
          {(bondDetails) => (
            <>
              <div className="relative flex flex-col gap-7 p-5">
                <p className="text-heading4">
                  Quickly check <br /> your overall returns
                </p>
                <img
                  src="https://web-assets.stablebonds.in/assets/money_coin-h2BcX4_b.webp"
                  alt="mc"
                  width="85"
                  className="absolute top-5 right-0"
                />
                <div className="text-heading4 flex items-center justify-between gap-4">
                  <div className="text-heading4 flex-1 text-right">
                    {calculationQuery.data
                      ? formatToInr(calculationQuery.data.totalConsideration)
                      : "--"}
                  </div>
                  <div className="flex w-fit items-center">
                    <hr className="border-black-10 w-5 flex-shrink-0" />
                    <div className="text-body2 text-black-50 border-black-10 bg-black-3 w-20 flex-shrink-0 rounded-lg border p-2 text-center font-medium">
                      <span>{bondDetails.xirr}% YTM</span>
                      <br />
                      <span>{bondDetails.timeLeftToMaturity}</span>
                    </div>
                    <hr className="border-black-10 w-5 flex-shrink-0" />
                  </div>
                  <div className="text-green text-heading4 flex-1 font-medium">
                    {calculationQuery.data
                      ? formatToInr(calculationQuery.data.maturityAmount)
                      : "--"}
                  </div>
                </div>
                <div className="text-body1 text-purple text-center">
                  {isHotWire ? (
                    <Anchor
                      href={`/bonds/${issuer}/${bondId}/returns-schedule`}
                      onClick={() => {
                        trackEvent("returns_calculator_view_payout", {
                          bond_id: bondId,
                          bond_name:
                            bondDetails.aboutTheInstitution
                              ?.bondInstitutionName,
                          bond_type: bondDetails.investabilityStatus,
                          bond_xirr: bondDetails.xirr,
                          units: quantity,
                        });
                      }}
                      className="space-x-1"
                    >
                      <span className="border-purple border-b">
                        View all payouts
                      </span>
                      <svg
                        width="10"
                        height="10"
                        viewBox="0 0 10 10"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="inline-block size-2 align-middle"
                      >
                        <path
                          d="M9.1 7.58009L9.1 7.68009L9 7.68009L8.04968 7.68009L7.94933 7.68009L7.94968 7.57974L7.96611 2.85303L1.76193 9.07063L1.69203 9.14068L1.62126 9.07152L0.930113 8.3962L0.857932 8.32567L0.929117 8.25414L7.11794 2.03507L2.4514 2.03507L2.3514 2.03507L2.3514 1.93507L2.3514 1L2.3514 0.900001L2.4514 0.900001L9 0.900001L9.1 0.900001L9.1 1L9.1 7.58009Z"
                          fill="currentColor"
                          stroke="currentColor"
                          strokeWidth="0.2"
                        />
                      </svg>
                    </Anchor>
                  ) : (
                    <AdaptiveModal
                      trigger={
                        <button className="cursor-pointer space-x-1">
                          <span className="border-purple border-b">
                            View all payouts
                          </span>
                          <svg
                            width="10"
                            height="10"
                            viewBox="0 0 10 10"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            className="inline-block size-2 align-middle"
                          >
                            <path
                              d="M9.1 7.58009L9.1 7.68009L9 7.68009L8.04968 7.68009L7.94933 7.68009L7.94968 7.57974L7.96611 2.85303L1.76193 9.07063L1.69203 9.14068L1.62126 9.07152L0.930113 8.3962L0.857932 8.32567L0.929117 8.25414L7.11794 2.03507L2.4514 2.03507L2.3514 2.03507L2.3514 1.93507L2.3514 1L2.3514 0.900001L2.4514 0.900001L9 0.900001L9.1 0.900001L9.1 1L9.1 7.58009Z"
                              fill="currentColor"
                              stroke="currentColor"
                              strokeWidth="0.2"
                            />
                          </svg>
                        </button>
                      }
                      href={`/bonds/${bondDetails.aboutTheInstitution?.slug ?? "unknown"}/${bondDetails.isinCode}/returns-schedule?units=${quantity}`}
                    >
                      {() => <BondsReturnSchedule quantity={quantity} />}
                    </AdaptiveModal>
                  )}
                </div>
              </div>
              <div className="p-0.75">
                <div className="from-black-3 to-bg mt-2 rounded-b-lg bg-linear-to-b">
                  <div className="flex items-center justify-center gap-2 py-5">
                    <button
                      aria-label="Decrease"
                      disabled={
                        quantity <= (bondDetails.bondQuantity?.minCount ?? 1)
                      }
                      onClick={decrease}
                      className="border-black-10 flex h-7 w-7 cursor-pointer items-center justify-center rounded-lg border bg-white"
                    >
                      <svg
                        width="12"
                        height="2"
                        viewBox="0 0 12 2"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="size-3"
                      >
                        <path
                          d="M10.8605 0.0283203H1.13824C0.601294 0.0283203 0.166016 0.463599 0.166016 1.00054C0.166016 1.53749 0.601294 1.97277 1.13824 1.97277H10.8605C11.3974 1.97277 11.8327 1.53749 11.8327 1.00054C11.8327 0.463599 11.3974 0.0283203 10.8605 0.0283203Z"
                          fill="currentColor"
                        />
                      </svg>
                    </button>
                    <div className="border-black-10 text-heading3 flex w-[102px] items-center justify-center rounded-lg border bg-white py-2">
                      {quantity} units
                    </div>
                    <button
                      aria-label="Increase"
                      onClick={increase}
                      className="border-black-10 flex h-7 w-7 cursor-pointer items-center justify-center rounded-lg border bg-white"
                    >
                      <svg
                        width="12"
                        height="12"
                        viewBox="0 0 12 12"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="size-3"
                      >
                        <path
                          d="M10.8605 5.02908H6.97157V1.14019C6.97157 0.882342 6.86914 0.635053 6.68681 0.452726C6.50449 0.270399 6.2572 0.167969 5.99935 0.167969C5.7415 0.167969 5.49421 0.270399 5.31188 0.452726C5.12956 0.635053 5.02713 0.882342 5.02713 1.14019V5.02908H1.13824C0.880389 5.02908 0.6331 5.13151 0.450773 5.31384C0.268446 5.49616 0.166016 5.74345 0.166016 6.0013C0.166016 6.25915 0.268446 6.50644 0.450773 6.68877C0.6331 6.87109 0.880389 6.97353 1.13824 6.97353H5.02713V10.8624C5.02713 11.1203 5.12956 11.3676 5.31188 11.5499C5.49421 11.7322 5.7415 11.8346 5.99935 11.8346C6.2572 11.8346 6.50449 11.7322 6.68681 11.5499C6.86914 11.3676 6.97157 11.1203 6.97157 10.8624V6.97353H10.8605C11.1183 6.97353 11.3656 6.87109 11.5479 6.68877C11.7303 6.50644 11.8327 6.25915 11.8327 6.0013C11.8327 5.74345 11.7303 5.49616 11.5479 5.31384C11.3656 5.13151 11.1183 5.02908 10.8605 5.02908Z"
                          fill="currentColor"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            </>
          )}
        </QueryRenderer>
      </Surface>
    </div>
  );
}
