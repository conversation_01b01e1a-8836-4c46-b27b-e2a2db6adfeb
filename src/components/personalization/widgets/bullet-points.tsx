import { type HTMLAttributes } from "react";
import useD<PERSON><PERSON><PERSON> from "@/hooks/data-key";
import type {
  BulletPointsWidgetProps,
  DataKey,
} from "@/clients/gen/personalization_api";

type BulletPointsComponentProps = BulletPointsWidgetProps &
  HTMLAttributes<HTMLDivElement>;

function BulletPointItem({ point }: { point: DataKey }) {
  const text = useDataKey(point);
  return <li>{text}</li>;
}

export default function BulletPoints({
  pointsList = [],
  ...rest
}: BulletPointsComponentProps) {
  return (
    <div className="p-4" {...rest}>
      <ul className="list-disc space-y-2 pl-5">
        {pointsList.map((point, index) => (
          <BulletPointItem key={index} point={point} />
        ))}
      </ul>
    </div>
  );
}
