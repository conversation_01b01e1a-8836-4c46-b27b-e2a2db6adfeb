import { useMediaQuery } from "@react-hook/media-query";
import BackAppbarAction from "../functional/back-appbar-action";
import SupportAppbarAction from "../functional/support-appbar-action";
import AppBar from "../ui/app-bar/app-bar";

export default function WorkflowShell({
  children,
}: {
  children: React.ReactNode;
}) {
  const isDesktop = useMediaQuery("(min-width: 768px)");

  if (isDesktop) {
    return (
      <div className="mx-auto flex h-screen max-w-[400px] flex-col items-center justify-center">
        <img
          src="https://assets.stablemoney.in/web-frontend/stable_bonds_black_logo.webp"
          alt="stable_bonds_black_logo"
          className="mx-auto my-8 h-8"
        />
        <div className="relative w-full">
          <BackAppbarAction className="absolute top-0 -left-12" />
          <div className="bg-bg border-black-20 flex max-h-[80dvh] flex-col gap-6 rounded-xl border">
            {children}
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
      <AppBar
        scrollStyleOptions={{
          offsetStyles: {
            backgroundColor: {
              threshold: 20,
              before: "transparent",
              after: "var(--color-bg)",
            },
            borderBottomWidth: {
              threshold: 20,
              before: 0,
              after: "var(--border-w-sm)",
            },
          },
        }}
        left={<BackAppbarAction />}
        right={<SupportAppbarAction />}
      />
      {children}
    </>
  );
}
