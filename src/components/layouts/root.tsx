import {
  Outlet,
  ScrollRestoration,
  useNavigate,
  useNavigation,
} from "react-router";
import { useUpdate<PERSON><PERSON><PERSON> } from "@/machines/update-checker";
import { usePageViewTracking } from "@/hooks/page-view-tracking";
import { useBodyClasses } from "@/hooks/body-classes";
import UrlBar from "../ui/url-bar";
import Toaster from "../ui/toast/toaster";
import { useEffect } from "react";
import loadingImage from "@/assets/images/icons/loader.svg";
import { useSpinDelay } from "spin-delay";
import { useUserQuery } from "@/hooks/user";

export default function Root() {
  useUpdateChecker();
  usePageViewTracking();
  useUserQuery();
  useBodyClasses();
  const navigate = useNavigate();
  const navigation = useNavigation();
  const showSpinner = useSpinDelay(!!navigation.location, {
    delay: 500,
    minDuration: 300,
  });

  useEffect(() => {
    window.HotwireNavigator.setStartVisitHandler(
      async (location, _restorationIdentifier, options) => {
        await navigate(location.pathname + location.search, {
          replace: options.action === "replace",
          preventScrollReset: options.action === "restore",
        });
        await new Promise((resolve) => setTimeout(resolve, 300));
      }
    );
  }, [navigate]);

  return (
    <>
      <ScrollRestoration />
      {showSpinner ? (
        <div className="fixed inset-0 flex items-center justify-center">
          <img
            src={loadingImage}
            alt="loader"
            className="size-10 animate-spin"
          />
        </div>
      ) : (
        <>
          <Outlet />
          <Toaster />
        </>
      )}
      <UrlBar />
    </>
  );
}
