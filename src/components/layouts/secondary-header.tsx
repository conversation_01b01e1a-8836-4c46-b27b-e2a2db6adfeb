import { useLocation } from "react-router";
import Button from "@/components/ui/button/button";
import clsx from "clsx";
import Anchor from "../functional/anchor";
import { useUserQuery } from "@/hooks/user";
import { logout } from "@/clients/auth";

interface SecondaryHeaderProps {
  hideHeader?: boolean;
  className?: string;
}

export default function SecondaryHeader({
  hideHeader = false,
  className,
}: SecondaryHeaderProps) {
  const location = useLocation();
  const userQuery = useUserQuery();
  const isLoggedIn = !!userQuery.data;

  return (
    <div
      className={clsx(
        "border-black-40 sticky top-0 z-20 flex h-16 w-full items-center justify-center border-b bg-white px-5",
        hideHeader && "hidden md:flex",
        className
      )}
    >
      <div className="container flex w-full items-center justify-between">
        <Anchor href="/">
          <img
            src="https://assets.stablemoney.in/web-frontend/stable_bonds_black_logo.webp"
            alt="logo"
            className="h-6 md:h-8"
          />
        </Anchor>
        {!isLoggedIn ? (
          <Button size="small" href="/authentication/mobile-number">
            Login/Register
          </Button>
        ) : (
          <div className="flex gap-2">
            {location.pathname !== "/profile" && (
              <Button
                size="small"
                href="/profile"
                className="hidden shrink-0 md:flex"
              >
                Profile
              </Button>
            )}
            {location.pathname !== "/investments" && (
              <Button size="small" href="/investments" className="shrink-0">
                My investments
              </Button>
            )}
            <Button size="small" className="shrink-0" onClick={logout}>
              Log out
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
