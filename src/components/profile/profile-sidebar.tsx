import { useLocation } from "react-router";
import clsx from "clsx";
import Anchor from "../functional/anchor";

const menusData = [
  { label: "User Details", link: "/profile" },
  { label: "Bank", link: "/profile/bank" },
  { label: "Demat", link: "/profile/demat" },
  { label: "KYC", link: "/profile/kyc" },
  { label: "Reports", link: "/profile/reports" },
];

export default function ProfileSidebar() {
  const location = useLocation();

  return (
    <aside>
      <div className="mb-4 flex flex-row md:flex-col md:space-y-4">
        {menusData.map((menu) => (
          <Anchor
            key={menu.label}
            href={menu.link}
            className={clsx(
              "text-body1 md:text-heading4 px-4 py-2 font-medium",
              location.pathname === menu.link && "bg-purple/10 rounded-sm"
            )}
          >
            {menu.label}
          </Anchor>
        ))}
      </div>
    </aside>
  );
}
