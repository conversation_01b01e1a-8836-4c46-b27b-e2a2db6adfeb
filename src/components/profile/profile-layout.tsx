import ProfileSidebar from "./profile-sidebar";
import Surface from "@/components/ui/surface/surface";
import SecondaryHeader from "@/components/layouts/secondary-header";
import type { ReactNode } from "react";

interface ProfileLayoutProps {
  title: string;
  children: ReactNode;
}

export default function ProfileLayout({ title, children }: ProfileLayoutProps) {
  return (
    <div className="min-h-screen bg-white">
      <SecondaryHeader />
      <div className="mx-auto flex max-w-[1206px] flex-col space-x-4 px-5 py-10 md:flex-row">
        <ProfileSidebar />
        <div className="flex-1">
          <Surface elevation="md">
            <div className="p-4">
              <h1 className="text-heading2 border-black-10 mb-4 border-b pb-4 font-medium">
                {title}
              </h1>
              {children}
            </div>
          </Surface>
        </div>
      </div>
    </div>
  );
}
