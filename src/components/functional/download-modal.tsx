import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import type { ReactNode } from "react";

interface DownloadModalProps {
  trigger: ReactNode;
  isInvestedUser?: boolean;
}

export default function DownloadModal({
  trigger,
  isInvestedUser = false,
}: DownloadModalProps) {
  return (
    <AdaptiveModal trigger={trigger} size="large" href="/download">
      {() => (
        <div className="flex flex-col items-center gap-8 p-7">
          <p className="text-foreground/80 text-center text-[22px] leading-[32px] tracking-[-0.5px]">
            {isInvestedUser
              ? "Track your investments with ease on the Stable Money app"
              : "Scan to download the Stable Money app"}
          </p>
          <div className="border-bg-background/10 rounded-[12px] border p-5">
            <img
              width={126}
              height={126}
              src="https://assets.stablemoney.in/web-frontend/website/stablemoney_qr.webp"
              alt="QR code"
              loading="lazy"
              decoding="async"
            />
          </div>
          <div className="space-y-4">
            <p className="text-foreground/50 text-center text-[15px] leading-[22px] tracking-[-0.2px]">
              Trusted by <span className="text-foreground/80"> 15 lakh+ </span>{" "}
              Indians
            </p>
            <div className="flex gap-4">
              <img
                src="https://assets.stablemoney.in/web-frontend/website/appstore-black.webp"
                alt="App Store"
                className="h-[42px]"
                loading="lazy"
                decoding="async"
              />
              <img
                src="https://assets.stablemoney.in/web-frontend/website/playstore-black.webp"
                alt="Play Store"
                className="h-[42px]"
                loading="lazy"
                decoding="async"
              />
            </div>
          </div>
        </div>
      )}
    </AdaptiveModal>
  );
}
