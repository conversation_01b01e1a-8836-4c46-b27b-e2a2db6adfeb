import React from "react";
import { useFormikContext } from "formik";
import { trackEvent } from "@/utils/analytics";

type Props = {
  id: string;
  analyticsProperties?: Record<string, unknown>;
} & React.FormHTMLAttributes<HTMLFormElement>;

/**
 * A functional form component that extends Formik's Form with analytics tracking.
 *
 * Features:
 * - Tracks `form.submitted` event on form submission
 * - Tracks errors as part of submit event when validation errors occur
 * - Maintains full compatibility with Formik's Form component
 *
 * @example
 * ```tsx
 * <Formik
 *   initialValues={{ email: "" }}
 *   validationSchema={schema}
 *   onSubmit={handleSubmit}
 * >
 *   <Form id="login" analyticsProperties={{ module: "auth" }}>
 *     <Field name="email" component={Input} />
 *     <Button type="submit">Submit</Button>
 *   </Form>
 * </Formik>
 * ```
 */
export default function Form({
  id,
  analyticsProperties = {},
  children,
  ...formProps
}: Props) {
  const formik = useFormikContext();

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    formik.submitForm();
    trackEvent(`form.submitted`, {
      id,
      submitCount: formik.submitCount + 1,
      errors: formik.errors,
      ...analyticsProperties,
    });
  };

  return (
    <form id={id} {...formProps} onSubmit={handleSubmit}>
      {children}
    </form>
  );
}
