import { UnauthenticatedException } from "@/exceptions/unauthenticated";
import { useEffect } from "react";
import { useRouteError, useNavigate } from "react-router-dom";
import { captureException } from "@sentry/react";
import ErrorPage from "./error-page";
import WorkflowShell from "../layouts/workflow-shell";
import { logout } from "@/clients/auth";

export default function ErrorBoundary() {
  const error = useRouteError();
  const navigate = useNavigate();

  useEffect(() => {
    if (error instanceof UnauthenticatedException) {
      logout().then(() => {
        navigate("/authentication/mobile-number");
      });
    } else if (error instanceof Error) {
      // Report error to Sentry (URL is automatically captured)
      captureException(error);
    }
  }, [error, navigate]);

  return (
    <WorkflowShell>
      <ErrorPage error={error} />
    </WorkflowShell>
  );
}
