import loadingImage from "@/assets/images/icons/loader.svg";
import type { ReactNode } from "react";
import LoadingPageContainer from "./loading-page-container";

type LoadingPageProps = {
  title?: ReactNode;
  description?: ReactNode;
};

export default function LoadingPage({ title, description }: LoadingPageProps) {
  return (
    <LoadingPageContainer>
      <img src={loadingImage} className="size-10 animate-spin" />
      <div>
        <div className="text-heading3 text-black-80 mb-2 empty:hidden">
          {title}
        </div>
        <div className="text-body1 text-black-50 empty:hidden">
          {description}
        </div>
      </div>
    </LoadingPageContainer>
  );
}
