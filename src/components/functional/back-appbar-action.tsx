import { type ButtonHTMLAttributes } from "react";
import backImage from "@/assets/images/icons/back.webp";
import { goBack } from "@/utils/routing";

export default function BackAppbarAction(
  props: ButtonHTMLAttributes<HTMLButtonElement>
) {
  return (
    <button {...props} onClick={goBack} name="back">
      <img decoding="sync" src={backImage} alt="Back" className="h-9 w-9" />
    </button>
  );
}
