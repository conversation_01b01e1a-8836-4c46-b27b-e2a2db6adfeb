import { useMediaQuery } from "@react-hook/media-query";

export default function LoadingPageContainer({
  children,
}: {
  children: React.ReactNode;
}) {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  if (isDesktop) {
    return (
      <div className="flex flex-col items-center justify-center gap-6 p-5 text-center md:pt-10">
        {children}
      </div>
    );
  }

  return (
    <div className="app-bar-height-top-padding floating-footer-padding fixed inset-0 flex flex-col items-center justify-center gap-6 p-5 text-center">
      {children}
    </div>
  );
}
