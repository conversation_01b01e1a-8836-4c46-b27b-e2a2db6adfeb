import { useEffect, type ReactNode } from "react";
import type { UseQueryResult } from "@tanstack/react-query";
import { HttpCallException } from "@/exceptions/http-call-exception";
import { logout } from "@/clients/auth";

type QueryRendererProps<T> = {
  query: UseQueryResult<T, Error>;
  children?: (data: T) => ReactNode;
  loader?: ReactNode;
  error?: (error: Error) => ReactNode;
  empty?: ReactNode;
};

export default function QueryRenderer<T>({
  query,
  children,
  loader,
  error,
  empty,
}: QueryRendererProps<T>) {
  useEffect(() => {
    if (
      query.error &&
      query.error instanceof HttpCallException &&
      query.error.response.status === 401
    ) {
      logout();
      window.location.href = "/authentication/mobile-number";
    }
  }, [query.error]);

  if (query.data) {
    return children?.(query.data);
  }

  if (query.isLoading) {
    return loader;
  }

  if (query.error) {
    return error?.(query.error);
  }

  return empty;
}
