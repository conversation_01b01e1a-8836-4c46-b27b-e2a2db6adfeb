import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import Surface from "@/components/ui/surface/surface";
import UnitSelector from "@/components/ui/unit-selector/unit-selector";
import Accordion from "@/components/ui/accordion/accordion";
import AccordionItem from "@/components/ui/accordion/accordion-item";
import Panel from "@/components/ui/panel/panel";
import AdaptiveModal from "@/components/ui/adaptive-modal/adaptive-modal";
import BondsReturnSchedule from "./bonds-return-schedule";
import OrderLimitMessage from "./order-limit-message";
import { getBondCalculationQueryOptions } from "@/queries/bonds";
import { formatToInr } from "@/utils/number";
import type { BondDetailItem } from "@/clients/gen/broking/BondDetails_pb";
import PurchaseButton from "./purchase-button";
import loadingIcon from "@/assets/images/icons/small-loader.svg";

type BondCalculatorProps = {
  details: BondDetailItem;
};

export default function BondCalculator({ details }: BondCalculatorProps) {
  const minUnits = details.bondQuantity?.minCount ?? 1;
  const maxUnits = details.bondQuantity?.maxCount ?? 1;
  const [quantity, setQuantity] = useState(details.defaultQuantity ?? 1);

  const calculationQuery = useQuery({
    ...getBondCalculationQueryOptions(details.id, quantity),
    enabled: !!details.id && !!quantity,
  });

  return (
    <>
      <Surface borderWidth="md">
        <div className="md:py-4">
          <div className="space-y-3 md:bg-white">
            <p className="text-heading4 text-black-50 text-center">Units</p>
            <UnitSelector
              className="mx-auto w-fit px-5"
              value={quantity.toString()}
              min={minUnits}
              max={maxUnits}
              isSoldOut={details.bondQuantity?.availableCount === 0}
              onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
              autoFocus
            />
          </div>

          <div className="px-5">
            <OrderLimitMessage
              investmentAmount={calculationQuery.data?.totalConsideration ?? 0}
              availableUnits={details.bondQuantity?.availableCount ?? 0}
              units={quantity}
              pricePerUnit={details.pricePerBond}
            />

            <Panel
              footer={
                calculationQuery.data &&
                calculationQuery.data.maturityAmount > 0 ? (
                  <div className="text-center">
                    <div className="text-body1 md:text-body2 inline">
                      <AdaptiveModal
                        size="large"
                        trigger={
                          <button className="text-body1 cursor-pointer space-x-1">
                            <span className="border-purple border-b">
                              View all payouts
                            </span>
                            <svg
                              width="10"
                              height="10"
                              viewBox="0 0 10 10"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="inline-block align-middle"
                            >
                              <path
                                d="M9.1 7.58009L9.1 7.68009L9 7.68009L8.04968 7.68009L7.94933 7.68009L7.94968 7.57974L7.96611 2.85303L1.76193 9.07063L1.69203 9.14068L1.62126 9.07152L0.930113 8.3962L0.857932 8.32567L0.929117 8.25414L7.11794 2.03507L2.4514 2.03507L2.3514 2.03507L2.3514 1.93507L2.3514 1L2.3514 0.900001L2.4514 0.900001L9 0.900001L9.1 0.900001L9.1 1L9.1 7.58009Z"
                                fill="currentColor"
                                stroke="currentColor"
                                strokeWidth="0.2"
                              />
                            </svg>
                          </button>
                        }
                        href={`/bonds/${details.aboutTheInstitution?.slug ?? "unknown"}/${details.isinCode}/returns-schedule?units=${quantity}`}
                      >
                        {() => <BondsReturnSchedule quantity={quantity} />}
                      </AdaptiveModal>
                    </div>
                  </div>
                ) : (
                  calculationQuery.isFetching && (
                    <div className="flex h-6 items-center justify-center">
                      <img
                        src={loadingIcon}
                        alt="loader"
                        className="size-3 animate-spin"
                      />
                    </div>
                  )
                )
              }
            >
              <Accordion>
                <AccordionItem
                  label={
                    <div className="flex w-full justify-between">
                      <span>Investment amount</span>
                      <span>
                        {calculationQuery.data
                          ? formatToInr(
                              calculationQuery.data.totalConsideration
                            )
                          : calculationQuery.isFetching && (
                              <img
                                src={loadingIcon}
                                alt="loader"
                                className="size-3 animate-spin"
                              />
                            )}
                      </span>
                    </div>
                  }
                >
                  <div className="flex justify-between">
                    <span className="text-black-50">Principal</span>
                    {calculationQuery.data && (
                      <span>
                        {formatToInr(calculationQuery.data.purchaseAmount)}
                      </span>
                    )}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-black-50">Accrued interest</span>
                    {calculationQuery.data && (
                      <span>
                        {formatToInr(calculationQuery.data.accruedInterest)}
                      </span>
                    )}
                  </div>
                  {calculationQuery.data &&
                    calculationQuery.data.stampDuty > 0 && (
                      <div className="flex justify-between">
                        <span className="text-black-50">Stamp duty</span>
                        <span>
                          {formatToInr(calculationQuery.data.stampDuty)}
                        </span>
                      </div>
                    )}
                </AccordionItem>

                <AccordionItem
                  label={
                    <div className="flex w-full justify-between">
                      <span>Total returns</span>
                      <span>
                        {calculationQuery.data &&
                        calculationQuery.data.maturityAmount > 0 ? (
                          <span className="text-green">
                            {formatToInr(calculationQuery.data.maturityAmount)}
                          </span>
                        ) : (
                          calculationQuery.isFetching && (
                            <img
                              src={loadingIcon}
                              alt="loader"
                              className="size-3 animate-spin"
                            />
                          )
                        )}
                      </span>
                    </div>
                  }
                >
                  <div className="flex justify-between">
                    <span className="text-black-50">Total gains</span>
                    {calculationQuery.data &&
                      calculationQuery.data.maturityAmount > 0 && (
                        <span>
                          {formatToInr(
                            calculationQuery.data.maturityAmount -
                              calculationQuery.data.totalConsideration
                          )}
                        </span>
                      )}
                  </div>
                  <div className="flex justify-between">
                    <span className="text-black-50">Investment amount</span>
                    {calculationQuery.data &&
                      calculationQuery.data.maturityAmount > 0 && (
                        <span>
                          {formatToInr(
                            calculationQuery.data.totalConsideration
                          )}
                        </span>
                      )}
                  </div>
                </AccordionItem>
              </Accordion>
            </Panel>

            <div className="pt-5">
              <PurchaseButton bondDetailId={details.id} quantity={quantity} />
            </div>
          </div>
        </div>
      </Surface>
    </>
  );
}
