import type { ReactNode } from "react";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import { useMediaQuery } from "@react-hook/media-query";

interface FormPageProps {
  children?: ReactNode;
  title?: ReactNode;
  description?: ReactNode;
  footer: ReactNode;
}

export default function FormPage({
  title,
  description,
  footer,
  children,
}: FormPageProps) {
  const desktop = useMediaQuery("(min-width: 768px)");
  if (desktop) {
    return (
      <div className="flex max-h-[80dvh] flex-col">
        {(title || description) && (
          <div className="space-y-2 p-5">
            <h1 className="text-heading1">{title}</h1>
            {description && (
              <p className="text-black-50 text-body1">{description}</p>
            )}
          </div>
        )}
        <div className="min-h-[200px] flex-1 space-y-3 overflow-y-auto px-5 pb-5">
          {children}
        </div>
        <FloatingFooterContent>{footer}</FloatingFooterContent>
      </div>
    );
  }
  return (
    <>
      <div className="floating-footer-margin space-y-6 p-5">
        {(title || description) && (
          <div className="space-y-2">
            <h1 className="text-heading1">{title}</h1>
            {description && (
              <p className="text-black-50 text-body1">{description}</p>
            )}
          </div>
        )}
        <div className="space-y-3">{children}</div>
      </div>
      <FloatingFooterContent>{footer}</FloatingFooterContent>
    </>
  );
}
