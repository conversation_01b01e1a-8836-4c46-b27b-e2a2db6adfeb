import { useMemo } from "react";
import { useParams, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { getBondDetailsQueryOptions } from "@/queries/bonds";
import QueryRenderer from "@/components/functional/query-renderer";
import { formatToInr } from "@/utils/number";
import type { InterestPaymentSchedule } from "@/clients/gen/broking/BondDetails_pb";
import Table from "@/components/ui/table/table";
import TableHead from "@/components/ui/table/table-head";
import TableRow from "@/components/ui/table/table-row";
import TableHeadCell from "@/components/ui/table/table-head-cell";
import TableBody from "@/components/ui/table/table-body";
import TableCell from "@/components/ui/table/table-cell";
import dayjs from "dayjs";

function checkTransactionType(type: string) {
  if (type === "INTEREST_PAYMENT") {
    return "Interest";
  } else if (type === "PRINCIPAL_REPAYMENT") {
    return "Principal";
  } else {
    return "Principal + Interest";
  }
}

function renderReturn(row: InterestPaymentSchedule, bondUnit: number) {
  let str = "";
  if (row.principalAmount > 0) {
    str += formatToInr(row.principalAmount * bondUnit) ?? "";
  }
  if (row.principalAmount > 0 && row.interestAmount > 0) {
    str += " + ";
  }
  if (row.interestAmount > 0) {
    str += formatToInr((row.interestAmount * bondUnit * 100) / 100) ?? "";
  }
  return str;
}

function totalReturn(
  bondUnit: number,
  payoutScheduleData: InterestPaymentSchedule[]
) {
  let total = 0;
  for (const element of payoutScheduleData) {
    const interest = element.interestAmount ?? 0;
    const principal = element.principalAmount ?? 0;
    const interestEarned = interest * bondUnit;
    total += interestEarned + principal * bondUnit;
  }
  return total;
}

interface BondsReturnScheduleProps {
  quantity?: number;
}

export default function BondsReturnSchedule({
  quantity,
}: BondsReturnScheduleProps) {
  const params = useParams();
  const [searchParams] = useSearchParams();

  const bondId = params.bond_id as string;
  const units = useMemo(
    () => parseInt(searchParams.get("units") || (quantity ?? 1).toString(), 10),
    [searchParams, quantity]
  );

  const bondDetailsQuery = useQuery(getBondDetailsQueryOptions(bondId));

  return (
    <QueryRenderer query={bondDetailsQuery}>
      {(bondDetails) => (
        <div className="py-5 text-center">
          <div className="space-y-1 pb-8">
            <h1 className="text-heading3">Returns schedule</h1>
            <p className="text-black-50 text-body1">
              {dayjs(bondDetails.issueDate).format("MMM D, YYYY")} to{" "}
              {dayjs(bondDetails.maturityDate).format("MMM D, YYYY")}
            </p>
          </div>
          <div className="border-black-10 border">
            <Table horizontal vertical variant="secondary">
              <TableHead>
                <TableRow>
                  <TableHeadCell alignment="center" className="px-4 py-3">
                    Date
                  </TableHeadCell>
                  <TableHeadCell alignment="center" className="px-4 py-3">
                    Amount
                  </TableHeadCell>
                  <TableHeadCell alignment="center" className="px-4 py-3">
                    Payout
                  </TableHeadCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bondDetails.interestPaymentAndPrincipalRepaymentSchedule.map(
                  (schedule, index) => (
                    <TableRow key={index}>
                      <TableCell
                        alignment="center"
                        className="text-body1 px-4 py-3"
                      >
                        {dayjs(schedule.date, "DD-MM-YYYY").format(
                          "MMM D, YYYY"
                        )}
                      </TableCell>
                      <TableCell
                        alignment="center"
                        className="text-body1 text-green px-4 py-3"
                      >
                        {renderReturn(schedule, units)}
                      </TableCell>
                      <TableCell
                        alignment="center"
                        className="text-body1 px-4 py-3"
                      >
                        {checkTransactionType(schedule.type)}
                      </TableCell>
                    </TableRow>
                  )
                )}
              </TableBody>
            </Table>
            <p className="border-black-10 text-body1 text-black-80 border-t py-2 tracking-[-0.2px]">
              Total returns:{" "}
              {formatToInr(
                totalReturn(
                  units,
                  bondDetails.interestPaymentAndPrincipalRepaymentSchedule
                )
              )}
            </p>
          </div>
        </div>
      )}
    </QueryRenderer>
  );
}
