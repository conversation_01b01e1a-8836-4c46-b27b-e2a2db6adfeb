import { maxInvestmentLimit } from "@/config/limits";
import { formatToInr } from "@/utils/number";

type OrderLimitMessageProps = {
  investmentAmount: number;
  availableUnits?: number;
  units?: number;
  pricePerUnit: number;
};

export default function OrderLimitMessage({
  availableUnits = 1,
  units = 1,
  pricePerUnit,
}: OrderLimitMessageProps) {
  const maxUnits = Math.floor((maxInvestmentLimit ?? 0) / (pricePerUnit ?? 1));
  const hasInvestmentLimitExceeded = units > maxUnits;
  const shouldShowStockMessage = units > (availableUnits ?? 1);
  const hasError = hasInvestmentLimitExceeded || shouldShowStockMessage;

  if (hasError) {
    if (hasInvestmentLimitExceeded) {
      if (shouldShowStockMessage) {
        return (
          <p className="text-body1 text-red pt-3 pb-12 text-center">
            We only have {availableUnits ?? 1} units in stock at the moment
          </p>
        );
      } else {
        return (
          <p className="text-body1 text-red pt-3 pb-12 text-center">
            You can invest up to {maxUnits} units per transaction
          </p>
        );
      }
    } else {
      return (
        <p className="text-body1 text-red pt-3 pb-12 text-center">
          We only have {availableUnits ?? 1} units in stock at the moment
        </p>
      );
    }
  } else {
    return (
      <p className="text-heading4 text-black-50 pt-3 pb-12 text-center">
        {formatToInr(pricePerUnit)} per unit
      </p>
    );
  }
}
