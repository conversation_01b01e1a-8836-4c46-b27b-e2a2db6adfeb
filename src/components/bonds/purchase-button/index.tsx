import useNavigate from "@/hooks/navigate";
import Button from "@/components/ui/button/button";
import { useMachine } from "@xstate/react";
import { purchaseButtonMachine } from "./machine";
import { useCallback, useEffect } from "react";
import type { SnapshotFrom } from "xstate";
import { getPathForWorkflowStep } from "@/utils/workflow-routes";
import { WorkflowName } from "@/clients/gen/broking/WorkflowStep_pb";
import { createInspector } from "@/utils/xstate";
import { getPathForNextAlphaStep } from "@/utils/onboarding-routes";

type ProceedButtonProps = {
  bondDetailId: string;
  quantity: number;
};

function getButtonText(state: SnapshotFrom<typeof purchaseButtonMachine>) {
  switch (state.value) {
    case "loggedOut":
      return "⚡ Complete one-time KYC";
    case "needsAlphaOnboarding":
    case "needsBrokingOnboarding":
    case "resumeAlphaOnboarding":
    case "resumeBrokingOnboarding":
      return "⚡ Continue one-time KYC";
    default:
      return "Proceed";
  }
}

function isLoading(state: SnapshotFrom<typeof purchaseButtonMachine>) {
  return (
    [
      "addingToCart",
      "placingOrder",
      "checkingAlphaOnboardingStatus",
      "checkingBrokingOnboardingStatus",
      "checkingLogin",
      "gettingBondDetails",
    ] as (typeof state.value)[]
  ).includes(state.value);
}

export default function PurchaseButton({
  bondDetailId,
  quantity,
}: ProceedButtonProps) {
  const navigate = useNavigate();
  const [state, send] = useMachine(purchaseButtonMachine, {
    input: { bondId: bondDetailId, quantity },
    inspect: createInspector(purchaseButtonMachine.id),
  });
  useEffect(() => {
    send({ type: "SET_QUANTITY", quantity });
  }, [quantity, send]);
  useEffect(() => {
    send({ type: "SET_BOND_ID", bondId: bondDetailId });
  }, [bondDetailId, send]);

  useEffect(() => {
    if (state.value === "pay") {
      navigate(`/checkout/${state.context.orderId}/pay`);
      return;
    }
    if (state.value === "login") {
      navigate("/authentication/mobile-number");
      return;
    }
    if (state.value === "resumeAlphaOnboarding") {
      navigate(getPathForNextAlphaStep(state.context.alphaNextStep!));
      return;
    }
    if (state.value === "resumeBrokingOnboarding") {
      navigate(
        getPathForWorkflowStep(
          WorkflowName.ONBOARDING_INITIATION,
          state.context.continueWorkflowResponse
        )
      );
      return;
    }
  }, [
    state.value,
    navigate,
    state.context.continueWorkflowResponse,
    state.context.orderId,
    state.context.alphaNextStep,
  ]);

  const handleProceed = useCallback(() => {
    send({ type: "CONTINUE" });
  }, [send]);

  return (
    <Button onClick={handleProceed} loading={isLoading(state)}>
      {getButtonText(state)}
    </Button>
  );
}
