import { getAuthToken } from "@/clients/auth";
import {
  InvestabilityStatus,
  type BondDetailItem,
} from "@/clients/gen/broking/BondDetails_pb";
import { CartAction } from "@/clients/gen/broking/Cart_pb";
import type { PlaceOrderResponseV2 } from "@/clients/gen/broking/Order_pb";
import type { UserProfileResponse } from "@/clients/gen/broking/Profile_pb";
import {
  WorkflowName,
  type ContinueWorkflowResponse,
} from "@/clients/gen/broking/WorkflowStep_pb";
import type { OnboardingState } from "@/clients/gen/platform/public/models/identity/Onboarding_pb";
import { toaster } from "@/components/ui/toast/store";
import { createBondDetailsQueryOptions } from "@/queries/bonds";
import { queryClient } from "@/queries/client";
import { getOnboardingStatus as getAlphaOnboardingStatus } from "@/queries/identity";
import { buyNow, updateCart } from "@/queries/orders";
import { getProfileQueryOptions } from "@/queries/profile";
import { getWorkflowStatus } from "@/queries/workflow";
import { trackEvent } from "@/utils/analytics";
import { getErrorMessage } from "@/utils/errors";
import { assign, fromPromise, setup, type DoneActorEvent } from "xstate";

type Context = {
  bondId: string;
  quantity: number;
  bondDetails?: BondDetailItem;
  alphaNextStep?: OnboardingState["next"];
  continueWorkflowResponse?: ContinueWorkflowResponse;
  orderId?: string;
  error?: unknown;
};
type Event =
  | { type: "SET_QUANTITY"; quantity: number }
  | { type: "SET_BOND_ID"; bondId: string }
  | { type: "CONTINUE" }
  | DoneActorEvent<OnboardingState>
  | DoneActorEvent<UserProfileResponse>
  | DoneActorEvent<ContinueWorkflowResponse>
  | DoneActorEvent<{ token: string }>
  | DoneActorEvent<PlaceOrderResponseV2>
  | DoneActorEvent<BondDetailItem>;
type Input = {
  bondId: string;
  quantity: number;
};

function isLoggedIn({ event }: { event: Event }) {
  return "output" in event && "token" in event.output && !!event.output.token;
}

function needsAlphaOnboarding({ event }: { event: Event }) {
  return "output" in event && "next" in event.output && !!event.output.next;
}

function needsBrokingOnboarding({ event }: { event: Event }) {
  return (
    "output" in event &&
    "lifeTimeStatus" in event.output &&
    ![
      "KYC_COMPLETED",
      "ORDER_INITIATED",
      "BOND_PURCHASED",
      "PAYMENT_DONE",
    ].includes(event.output.lifeTimeStatus)
  );
}

function isSoldOut({ event }: { event: Event }) {
  return (
    "output" in event &&
    "isinCode" in event.output &&
    event.output.investabilityStatus === InvestabilityStatus.SOLD_OUT
  );
}

async function addToCart({ input }: { input: Input }) {
  await updateCart({ action: CartAction.RESET });
  await updateCart({
    action: CartAction.ADD,
    bondDetailId: input.bondId,
    units: input.quantity,
  });
}

async function placeOrder({ input }: { input: Input }) {
  return buyNow({ bondDetailId: input.bondId, quantity: input.quantity });
}

function notifyMe({
  context: { bondDetails },
}: {
  event: Event;
  context: Context;
}) {
  trackEvent("bond_notify_clicked", {
    bondName: bondDetails?.bondName,
    bondId: bondDetails?.id,
  });
  toaster.create({
    title: `Sure, we'll notify you once this bond is available again`,
    type: "info",
    duration: 3000,
  });
}

export const purchaseButtonMachine = setup({
  actors: {
    getAuthToken: fromPromise(() =>
      getAuthToken().then((token) => ({ token }))
    ),
    getAlphaOnboardingStatus: fromPromise(getAlphaOnboardingStatus),
    getUserStatus: fromPromise(() =>
      queryClient.ensureQueryData(getProfileQueryOptions())
    ),
    getWorkflowStatus: fromPromise(() =>
      getWorkflowStatus(WorkflowName.ONBOARDING_INITIATION)
    ),
    addToCart: fromPromise(addToCart),
    placeOrder: fromPromise(placeOrder),
    getBondDetails: fromPromise(({ input }: { input: Input }) =>
      queryClient.ensureQueryData(createBondDetailsQueryOptions(input.bondId))
    ),
  },
  types: {
    context: {} as Context,
    events: {} as Event,
    input: {} as Input,
  },
  guards: {
    isLoggedIn,
    needsAlphaOnboarding,
    needsBrokingOnboarding,
    isSoldOut,
  },
  actions: {
    assignAlphaNextStep: assign(
      ({ event, context }: { event: Event; context: Context }) => {
        if ("output" in event && "next" in event.output) {
          return {
            ...context,
            alphaNextStep: event.output.next,
          };
        }
        return context;
      }
    ),
    assignContinueWorkflowResponse: assign(
      ({ event, context }: { event: Event; context: Context }) => {
        if ("output" in event && "nextStep" in event.output) {
          return {
            ...context,
            continueWorkflowResponse: event.output,
          };
        }
        return context;
      }
    ),
    assignOrderId: assign(
      ({ event, context }: { event: Event; context: Context }) => {
        if ("output" in event && "orderId" in event.output) {
          return {
            ...context,
            orderId: event.output.orderId,
          };
        }
        return context;
      }
    ),
    assignError: assign({
      error: ({ event }) => {
        if ("error" in event) {
          return event.error;
        }
      },
    }),
    async displayErrorToast({ context }) {
      toaster.create({
        description: await getErrorMessage(context.error),
        type: "error",
        duration: 3000,
      });
    },
    notifyMe,
  },
}).createMachine({
  id: "purchase-button",
  context({ input }) {
    return {
      bondId: input.bondId,
      quantity: input.quantity,
    };
  },
  initial: "checkingLogin",
  on: {
    SET_QUANTITY: {
      actions: assign(
        ({ event, context }: { event: Event; context: Context }) => {
          if ("quantity" in event) {
            return {
              ...context,
              quantity: event.quantity,
            };
          }
          return context;
        }
      ),
    },
  },
  states: {
    checkingLogin: {
      invoke: {
        src: "getAuthToken",
        onDone: [
          { target: "checkingAlphaOnboardingStatus", guard: "isLoggedIn" },
          { target: "loggedOut" },
        ],
        onError: { target: "error", actions: "assignError" },
      },
    },
    loggedOut: {
      on: {
        CONTINUE: "login",
      },
    },
    checkingAlphaOnboardingStatus: {
      invoke: {
        src: "getAlphaOnboardingStatus",
        onDone: [
          {
            target: "needsAlphaOnboarding",
            guard: "needsAlphaOnboarding",
            actions: "assignAlphaNextStep",
          },
          {
            target: "gettingBondDetails",
          },
        ],
        onError: { target: "error", actions: "assignError" },
      },
    },
    checkingBrokingOnboardingStatus: {
      invoke: {
        src: "getUserStatus",
        onDone: [
          {
            target: "fetchingNextOnboardingStep",
            guard: "needsBrokingOnboarding",
          },
          { target: "readyToInvest" },
        ],
        onError: { target: "error", actions: "assignError" },
      },
    },
    needsAlphaOnboarding: {
      on: {
        CONTINUE: "resumeAlphaOnboarding",
      },
    },
    fetchingNextOnboardingStep: {
      invoke: {
        src: "getWorkflowStatus",
        onDone: {
          target: "needsBrokingOnboarding",
          actions: "assignContinueWorkflowResponse",
        },
        onError: { target: "error", actions: "assignError" },
      },
    },
    needsBrokingOnboarding: {
      on: {
        CONTINUE: "addingToCart",
      },
    },
    gettingBondDetails: {
      invoke: {
        src: "getBondDetails",
        input: ({ context }) => context,
        onDone: [
          { target: "soldOut", guard: "isSoldOut" },
          { target: "checkingBrokingOnboardingStatus" },
        ],
        onError: { target: "error", actions: "assignError" },
      },
    },
    soldOut: {
      on: {
        CONTINUE: {
          actions: "notifyMe",
        },
      },
    },
    readyToInvest: {
      on: {
        CONTINUE: "placingOrder",
      },
    },
    placingOrder: {
      invoke: {
        src: "placeOrder",
        input: ({ context }) => context,
        onDone: { target: "pay", actions: "assignOrderId" },
        onError: { target: "error", actions: "assignError" },
      },
    },
    addingToCart: {
      invoke: {
        src: "addToCart",
        input: ({ context }) => context,
        onDone: { target: "resumeBrokingOnboarding" },
        onError: { target: "error", actions: "assignError" },
      },
    },
    login: {
      type: "final",
    },
    resumeBrokingOnboarding: {
      type: "final",
    },
    resumeAlphaOnboarding: {
      type: "final",
    },
    pay: {
      type: "final",
    },
    error: {
      entry: "displayErrorToast",
    },
  },
});
