import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import ProgressBar from "./progress-bar";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof ProgressBar> = {
  title: "Components/Progress Bar",
  component: ProgressBar,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    bgColor: { control: "color" },
    barColor: { control: "color" },
    progress: {
      control: { type: "range", min: 0, max: 100, step: 1 },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    bgColor: "#D9D9D9",
    barColor: "#000000",
    progress: 80,
  },
};

export const CustomColors: Story = {
  args: {
    bgColor: "#F5F5F5",
    barColor: "#12BE57",
    progress: 60,
  },
};

export const LowProgress: Story = {
  args: {
    bgColor: "#D9D9D9",
    barColor: "#FF5722",
    progress: 20,
  },
};

export const HighProgress: Story = {
  args: {
    bgColor: "#E0E0E0",
    barColor: "#2196F3",
    progress: 95,
  },
};

export const ZeroProgress: Story = {
  args: {
    bgColor: "#D9D9D9",
    barColor: "#000000",
    progress: 0,
  },
};

export const FullProgress: Story = {
  args: {
    bgColor: "#D9D9D9",
    barColor: "#4CAF50",
    progress: 100,
  },
};

export const MultipleProgressBars: Story = {
  render: () => (
    <div className="space-y-4">
      <div>
        <p className="text-body1 text-black-60 mb-1">Downloads: 25%</p>
        <ProgressBar bgColor="#E3F2FD" barColor="#2196F3" progress={25} />
      </div>
      <div>
        <p className="text-body1 text-black-60 mb-1">Uploads: 75%</p>
        <ProgressBar bgColor="#E8F5E8" barColor="#4CAF50" progress={75} />
      </div>
      <div>
        <p className="text-body1 text-black-60 mb-1">Processing: 50%</p>
        <ProgressBar bgColor="#FFF3E0" barColor="#FF9800" progress={50} />
      </div>
      <div>
        <p className="text-body1 text-black-60 mb-1">Error Rate: 10%</p>
        <ProgressBar bgColor="#FFEBEE" barColor="#F44336" progress={10} />
      </div>
    </div>
  ),
};

export const WithCustomStyling: Story = {
  render: () => (
    <div className="bg-black-5 rounded-lg p-4">
      <h3 className="text-heading3 mb-2">Investment Progress</h3>
      <p className="text-body2 text-black-50 mb-3">₹8,000 of ₹10,000 goal</p>
      <ProgressBar
        bgColor="rgba(0, 0, 0, 0.1)"
        barColor="#916CFF"
        progress={80}
        className="mb-2"
      />
      <p className="text-caption text-black-40">80% complete</p>
    </div>
  ),
};
