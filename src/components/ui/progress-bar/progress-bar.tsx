import clsx from "clsx";
import type { ProgressBarProps } from "./types";

export default function ProgressBar({
  barColor = "#000000",
  bgColor = "#D9D9D9",
  progress = 80,
  className,
  ...rest
}: ProgressBarProps) {
  return (
    <div className={clsx("block", className)} {...rest}>
      <div
        className="h-[3px] w-full overflow-hidden rounded-full md:h-[6px]"
        style={{ backgroundColor: bgColor }}
      >
        <div
          className="h-[3px] rounded-md transition-all duration-500 ease-out md:h-[6px]"
          style={{
            width: `${progress}%`,
            backgroundColor: barColor,
          }}
        />
      </div>
    </div>
  );
}
