import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Surface from "./surface";
import Button from "../button/button";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Surface> = {
  title: "Components/Surface",
  component: Surface,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    elevation: {
      control: { type: "select" },
      options: ["none", "md"],
    },
    borderWidth: {
      control: { type: "select" },
      options: ["sm", "md"],
    },
    isDarkMode: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    elevation: "md",
    borderWidth: "md",
    isDarkMode: false,
  },
  render: (args) => (
    <Surface
      elevation={args.elevation}
      borderWidth={args.borderWidth}
      isDarkMode={args.isDarkMode}
    >
      <div className="p-5">Content here</div>
    </Surface>
  ),
};

export const WithComplexContent: Story = {
  args: {
    elevation: "md",
    borderWidth: "md",
    isDarkMode: false,
  },
  render: (args) => (
    <Surface
      elevation={args.elevation}
      borderWidth={args.borderWidth}
      isDarkMode={args.isDarkMode}
    >
      <div className="p-5">
        <h3 className="text-heading3 mt-0 mb-2.5 font-bold">Card Title</h3>
        <p className="text-body1 text-black-60 mb-4">
          This is a surface component that can contain any content. It provides
          elevation and border styling to create card-like UI elements.
        </p>
        <div className="flex justify-end">
          <Button size="small">Action</Button>
        </div>
      </div>
    </Surface>
  ),
};

export const DarkMode: Story = {
  args: {
    elevation: "md",
    borderWidth: "md",
    isDarkMode: true,
  },
  render: (args) => (
    <div className="bg-black p-8">
      <Surface
        elevation={args.elevation}
        borderWidth={args.borderWidth}
        isDarkMode={args.isDarkMode}
      >
        <div className="p-5">
          <h3 className="text-heading3 mt-0 mb-2.5 font-bold text-white">
            Dark Mode Card
          </h3>
          <p className="text-body1 mb-4 text-white/80">
            This is a surface component in dark mode with black background.
          </p>
        </div>
      </Surface>
    </div>
  ),
};
