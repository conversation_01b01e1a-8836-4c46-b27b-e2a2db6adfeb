import clsx from "clsx";
import type { SurfaceProps } from "./types";

export default function Surface({
  elevation = "none",
  borderWidth = "sm",
  isDarkMode = false,
  className,
  children,
  ...rest
}: SurfaceProps) {
  return (
    <div
      className={clsx(
        "overflow-hidden rounded-xl border",
        borderWidth === "md" ? "border" : "border-[0.5px]",
        isDarkMode ? "border-white/10 bg-black" : "border-black-10 bg-white",
        className,
        { "shadow-md": elevation === "md" }
      )}
      {...rest}
    >
      {children}
    </div>
  );
}
