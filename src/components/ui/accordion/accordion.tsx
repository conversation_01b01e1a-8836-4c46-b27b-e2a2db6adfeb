import { normalizeProps, useMachine } from "@zag-js/react";
import type { AccordionProps } from "./types";
import * as accordion from "@zag-js/accordion";
import { useId } from "react";
import { AccordionContext } from "./context";
import clsx from "clsx";

export default function Accordion({
  children,
  multiple,
  className,
  defaultValue,
  isDarkMode = false,
  ...rest
}: AccordionProps) {
  const service = useMachine(accordion.machine, {
    id: useId(),
    multiple,
    collapsible: true,
    defaultValue,
  });
  const api = accordion.connect(service, normalizeProps);

  return (
    <div
      className={clsx(
        "divide-y px-5 py-1.5",
        {
          "divide-black-10": !isDarkMode,
          "divide-white/10 text-white": isDarkMode,
        },
        className
      )}
      {...rest}
      {...api.getRootProps()}
    >
      <AccordionContext.Provider value={api}>
        {children}
      </AccordionContext.Provider>
    </div>
  );
}
