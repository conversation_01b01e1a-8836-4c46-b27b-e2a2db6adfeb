import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Accordion from "./accordion";
import AccordionItem from "./accordion-item";

const meta: Meta<typeof Accordion> = {
  title: "Components/Accordion",
  component: Accordion,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  render: () => (
    <Accordion>
      <AccordionItem id="item1" label="What is Stable Money?">
        Stable Money is a high-yield savings account that offers up to 9%
        interest rate, which is much higher than traditional savings accounts.
      </AccordionItem>
      <AccordionItem id="item2" label="How does Stable Money work?">
        Stable Money works by investing your money in fixed deposits across
        multiple banks, which allows us to offer higher interest rates than
        traditional savings accounts.
      </AccordionItem>
      <AccordionItem id="item3" label="Is my money safe with Stable Money?">
        Yes, your money is safe with Stable Money. We invest your money in fixed
        deposits across multiple banks, which are covered by deposit insurance
        up to ₹5 lakhs per bank.
      </AccordionItem>
    </Accordion>
  ),
};
