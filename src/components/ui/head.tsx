import { Helmet } from "@dr.pogodin/react-helmet";
import { useLocation } from "react-router";

type HeadProps = {
  title?: string;
  description?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  canonical?: string;
  noindex?: boolean;
  children?: React.ReactNode;
};

export default function Head({
  title,
  description,
  ogTitle,
  ogDescription,
  ogImage = "https://assets.stablemoney.in/web-frontend/bonds-preview.jpg",
  ogUrl,
  twitterTitle,
  twitterDescription,
  twitterImage,
  noindex = false,
  children,
}: HeadProps) {
  const fullTitle = title
    ? `${title} | Stable Bonds`
    : "Invest in Bonds - High Returns | StableBonds";
  const location = useLocation();

  const defaultDescription =
    "Invest in government and corporate bonds with Stable Bonds. Secure, transparent, and regulated bond investments with competitive returns.";

  const metaDescription = description || defaultDescription;
  const metaOgTitle =
    ogTitle || title || "Invest in Bonds - High Returns | StableBonds";
  const metaOgDescription = ogDescription || description || defaultDescription;
  const metaTwitterTitle =
    twitterTitle ||
    ogTitle ||
    title ||
    "Invest in Bonds - High Returns | StableBonds";
  const metaTwitterDescription =
    twitterDescription || ogDescription || description || defaultDescription;
  const metaTwitterImage = twitterImage || ogImage;

  return (
    <Helmet>
      <title>{fullTitle}</title>
      <meta name="description" content={metaDescription} />

      {/* Open Graph / Facebook */}
      <meta property="og:type" content="website" />
      <meta property="og:title" content={metaOgTitle} />
      <meta property="og:description" content={metaOgDescription} />
      <meta property="og:image" content={ogImage} />
      {ogUrl && <meta property="og:url" content={ogUrl} />}

      {/* Twitter */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={metaTwitterTitle} />
      <meta name="twitter:description" content={metaTwitterDescription} />
      <meta name="twitter:image" content={metaTwitterImage} />

      {/* SEO */}
      <link
        rel="canonical"
        href={`https://stablebonds.in${location.pathname + location.search}`}
      />
      {noindex && <meta name="robots" content="noindex,nofollow" />}

      {children}
    </Helmet>
  );
}
