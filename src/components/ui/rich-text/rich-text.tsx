import { useMemo, type HTMLAttributes } from "react";
import { parseRichText } from "@/utils/rich-text";

type RichTextProps = {
  text?: string;
} & HTMLAttributes<HTMLSpanElement>;

export default function RichText({ text = "", ...rest }: RichTextProps) {
  const parsedText = useMemo(() => parseRichText(text), [text]);

  return (
    <span {...rest} dangerouslySetInnerHTML={{ __html: parsedText.html }} />
  );
}
