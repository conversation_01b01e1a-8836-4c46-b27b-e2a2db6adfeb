import * as dialog from "@zag-js/dialog";
import type { PropTypes } from "@zag-js/react";

export type ModalProps = {
  dismissible?: boolean;
  size?: "small" | "medium" | "large";
  trigger?: (props: PropTypes["button"]) => React.ReactNode;
  children?: (api: { dismiss: () => void }) => React.ReactNode;
  header?: React.ReactNode;
  footer?: React.ReactNode;
} & Omit<dialog.Props, "closeOnInteractOutside" | "closeOnEscape" | "id">;
