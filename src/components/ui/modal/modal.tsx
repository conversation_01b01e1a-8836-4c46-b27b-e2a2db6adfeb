import * as dialog from "@zag-js/dialog";
import { useMachine, normalizeProps, Portal } from "@zag-js/react";
import { useId } from "react";
import type { ModalProps } from "./types";
import Surface from "../surface/surface";
import clsx from "clsx";
import closeIcon from "@/assets/images/icons/close.svg";

export default function Modal({
  trigger,
  children,
  size = "medium",
  dismissible = true,
  header,
  footer,
  ...props
}: ModalProps) {
  const id = useId();
  const service = useMachine(dialog.machine, {
    ...props,
    id,
    closeOnEscape: dismissible,
    closeOnInteractOutside: dismissible,
  });
  const api = dialog.connect(service, normalizeProps);

  return (
    <>
      {trigger?.(api.getTriggerProps())}
      <Portal>
        <div
          {...api.getBackdropProps()}
          className="bg-black-80 fixed inset-0 z-90"
        />
        <div
          {...api.getPositionerProps()}
          className="fixed inset-0 z-90 flex items-center justify-center"
        >
          <Surface className="relative" {...api.getContentProps()}>
            {dismissible ? (
              <button
                {...api.getCloseTriggerProps()}
                className="absolute top-5 right-5 cursor-pointer outline-none"
              >
                <img src={closeIcon} alt="Close" className="size-6" />
              </button>
            ) : null}
            {header ? (
              <div {...api.getTitleProps()} className="text-center">
                {header}
              </div>
            ) : null}
            <div
              className={clsx("max-h-[80dvh] overflow-y-auto", {
                "max-w-sm": size === "small",
                "max-w-md": size === "medium",
                "max-w-lg": size === "large",
              })}
            >
              {children?.({ dismiss: () => api.setOpen(false) })}
            </div>
            {footer ? <footer>{footer}</footer> : null}
          </Surface>
        </div>
      </Portal>
    </>
  );
}
