import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import <PERSON><PERSON> from "./modal";
import Button from "../button/button";
import ModalHeader from "./modal-header";

const meta: Meta<typeof Modal> = {
  title: "Components/Modal",
  component: Modal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "select" },
      options: ["small", "medium", "large"],
    },
    open: {
      control: { type: "boolean" },
    },
    dismissible: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Basic modal with trigger button
export const Default: Story = {
  args: {
    size: "medium",
    dismissible: true,
    trigger: (props) => <Button {...props}>Open Modal</Button>,
    children: ({ dismiss }) => (
      <>
        <ModalHeader>Modal title</ModalHeader>
        <p className="text-body1 text-black-50">
          This is a basic modal dialog. You can put any content here.
        </p>
        <div className="h-[150px]" />
        <Button onClick={dismiss}>Close</Button>
      </>
    ),
  },
};

export const Controlled: Story = {
  args: {
    size: "medium",
    open: false,
    dismissible: false,
    children: ({ dismiss }) => (
      <>
        <ModalHeader>Modal title</ModalHeader>
        <p className="text-body1 text-black-50">
          This is dialog opens and closes based on props set externally.
        </p>
        <Button onClick={dismiss}>Close</Button>
      </>
    ),
  },
};
