import clsx from "clsx";
import type { FieldProps } from "./types";

export default function Field({
  error,
  label,
  children,
  className,
  ...rest
}: FieldProps) {
  return (
    <div className={className} {...rest}>
      <div className={clsx("form-field", { "has-error": error })}>
        {label && (
          <h5 className="text-black-40 text-body2 pointer-events-none block pb-1 font-normal">
            {label}
          </h5>
        )}
        {children}
      </div>
      {error && <div className="text-red pt-1">{error}</div>}
    </div>
  );
}
