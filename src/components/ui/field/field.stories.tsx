import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Field from "./field";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Field> = {
  title: "Components/Field",
  component: Field,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    label: { control: "text" },
    error: { control: "text" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  render: () => (
    <Field label="Name">
      <div className="border-black-10 rounded-xl border p-3">
        Your custom field here
      </div>
    </Field>
  ),
};

export const WithError: Story = {
  render: () => (
    <Field label="Name" error="This field is required">
      <div className="border-black-10 rounded-xl border p-3">
        Your custom field here
      </div>
    </Field>
  ),
};

export const WithoutLabel: Story = {
  render: () => (
    <Field>
      <div className="border-black-10 rounded-xl border p-3">
        Your custom field here
      </div>
    </Field>
  ),
};

export const WithDifferentContent: Story = {
  render: () => (
    <Field label="Select an option">
      <div className="flex gap-2">
        <button className="bg-black-5 cursor-pointer rounded border-none px-4 py-2">
          Option 1
        </button>
        <button className="bg-black-5 cursor-pointer rounded border-none px-4 py-2">
          Option 2
        </button>
        <button className="bg-black-5 cursor-pointer rounded border-none px-4 py-2">
          Option 3
        </button>
      </div>
    </Field>
  ),
};
