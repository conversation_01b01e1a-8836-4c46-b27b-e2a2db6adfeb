import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Panel from "./panel";

const meta: Meta<typeof Panel> = {
  title: "Components/Panel",
  component: Panel,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: { type: "text" },
    },
    footer: {
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Primary story with content and footer
export const Primary: Story = {
  args: {
    children: "Here is content, content is great",
    footer: "That's ₹401 in average monthly interest",
  },
};

// Panel without footer
export const WithoutFooter: Story = {
  args: {
    children: "Panel content without a footer",
  },
};
