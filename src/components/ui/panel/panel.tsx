import clsx from "clsx";
import type { PanelProps } from "./types";

export default function Panel({
  children,
  footer,
  className,
  ...rest
}: PanelProps) {
  const hasFooter = !!footer;

  return (
    <div
      className={clsx(
        "border-black-10 bg-black-5 rounded-xl border",
        className
      )}
      {...rest}
    >
      <div className="border-black-10 overflow-hidden rounded-xl border bg-white">
        {children}
      </div>
      {hasFooter && (
        <div className="text-purple px-4 py-2 text-center">{footer}</div>
      )}
    </div>
  );
}
