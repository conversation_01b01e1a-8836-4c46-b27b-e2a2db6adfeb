import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import <PERSON>ti<PERSON><PERSON><PERSON> from "./entity-logo";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof EntityLogo> = {
  title: "Components/Entity Logo",
  component: EntityLogo,
  tags: ["autodocs"],
  parameters: {
    layout: "centered",
  },
  argTypes: {
    url: { control: "text" },
    color: { control: "color" },
    size: {
      control: { type: "select" },
      options: ["large", "medium", "small"],
    },
    seamless: { control: "boolean" },
    elevation: {
      control: { type: "select" },
      options: ["none", "md"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    size: "small",
    url: "https://assets.stablemoney.in/bank-logos/Unity.png",
    color: "#FFF6EE",
    seamless: true,
    elevation: "md",
  },
};
