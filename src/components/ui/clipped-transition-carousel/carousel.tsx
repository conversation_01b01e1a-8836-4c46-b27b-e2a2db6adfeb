import { useRef, useState } from "react";
import { useInView } from "react-intersection-observer";
import type { CarouselItem } from "./types";
import clsx from "clsx";
import type { ClippedTransitionCarouselProps } from "@/clients/gen/personalization_api";
import type { HTMLAttributes } from "react";
import { Swiper, SwiperSlide } from "swiper/react";
// @ts-expect-error Not typed well enough
import "swiper/css";
import Anchor from "@/components/functional/anchor";
import { trackEvent } from "@/utils/analytics";

interface CarouselProps
  extends ClippedTransitionCarouselProps,
    HTMLAttributes<HTMLDivElement> {
  items?: CarouselItem[];
}

// Component for individual slide with intersection observer
function SlideWithObserver({
  item,
  index,
  onInView,
  stackTrackElement,
}: {
  item: CarouselItem;
  index: number;
  onInView: (index: number) => void;
  stackTrackElement: HTMLDivElement | null;
}) {
  const { ref } = useInView({
    threshold: 0.5,
    root: stackTrackElement,
    onChange: (inView) => {
      if (inView) {
        onInView(index);
      }
    },
  });

  return (
    <Anchor
      ref={ref}
      href={item.link}
      className="block pt-[200px]"
      onClick={() => {
        trackEvent("bonds_home_masthead_tapped", {
          link: item.link,
          image_url: item.stackImage,
          slide_index: index,
        });
      }}
    >
      <img
        src={item.stackImage}
        alt={item.alt || ""}
        loading={index === 0 ? "eager" : "lazy"}
      />
    </Anchor>
  );
}

export default function Carousel({ items = [], ...rest }: CarouselProps) {
  const [activeIndex, setActiveIndex] = useState(0);
  const stackTrackElementRef = useRef<HTMLDivElement>(null);
  const slidesContainerRef = useRef<HTMLDivElement>(null);

  function handleBulletClick(index: number) {
    setActiveIndex(index);
    if (slidesContainerRef.current) {
      const slideWidth = slidesContainerRef.current.clientWidth;
      slidesContainerRef.current.scrollTo({
        left: slideWidth * index,
        behavior: "smooth",
      });
    }
  }

  function handleSlideInView(index: number) {
    setActiveIndex(index);
  }

  return (
    <div
      className="relative w-full"
      role="group"
      aria-roledescription="carousel"
      {...rest}
    >
      <ul className="grid">
        {items.map((item, index) => (
          <li
            key={item.link}
            className={clsx(
              "col-span-full row-span-full w-full bg-(--slide-bg-color) pt-(--app-bar-height) opacity-0 transition-opacity duration-800",
              { "opacity-100": index === activeIndex }
            )}
            style={
              {
                "--slide-bg-color": item.backgroundColor || "transparent",
              } as React.CSSProperties
            }
          >
            <img
              decoding="sync"
              src={item.clipImage}
              alt={item.alt || ""}
              loading={index === 0 ? "eager" : "lazy"}
            />
            <div className="to-bg h-[160px] bg-linear-to-b from-(--slide-bg-color)"></div>
          </li>
        ))}
      </ul>
      <div
        className="absolute inset-0 flex flex-col justify-end"
        ref={stackTrackElementRef}
      >
        <Swiper slidesPerView={1} freeMode loop className="w-full">
          {items.map((item, index) => (
            <SwiperSlide key={item.link}>
              <SlideWithObserver
                key={item.link}
                item={item}
                index={index}
                onInView={handleSlideInView}
                stackTrackElement={stackTrackElementRef.current}
              />
            </SwiperSlide>
          ))}
        </Swiper>
        <div
          className="inline-flex items-center justify-center gap-0.75 p-4"
          aria-atomic="false"
          aria-live="off"
        >
          {items.map((item, index) => (
            <button
              key={item.link}
              className={clsx(
                "pointer size-1.5 rounded-full leading-none transition-colors duration-300",
                index === activeIndex ? "bg-black-80" : "bg-black-40"
              )}
              onClick={() => handleBulletClick(index)}
              aria-label={`Go to slide ${index + 1}`}
            ></button>
          ))}
        </div>
      </div>
    </div>
  );
}
