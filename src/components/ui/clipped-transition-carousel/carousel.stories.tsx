import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Carousel from "./carousel";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Carousel> = {
  title: "Components/Carousel",
  component: Carousel,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    autoPlayDurationInMillis: {
      control: { type: "number" },
    },
    loop: {
      control: { type: "boolean" },
    },
    disableAppBarPadding: {
      control: { type: "boolean" },
    },
    height: {
      control: { type: "number" },
    },
    heightPercentage: {
      control: { type: "number" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

const carouselItems = [
  {
    stackImage:
      "https://assets.stablemoney.in/app/06_bonds_masthead_krazybee_25_feb_v1.webp",
    clipImage:
      "https://assets.stablemoney.in/app/06_bonds_masthead_image_krazybee_25_feb_v1.webp",
    link: "/bond_details/07aad06c-5bac-4c73-b2de-1b647b1248b5",
    backgroundColor: "#FFF6EE",
    alt: "KrazyBee bond",
  },
  {
    stackImage:
      "http://assets.stablemoney.in/app/01_bonds_masthead_iifl_25_feb_v1.webp",
    clipImage:
      "https://assets.stablemoney.in/app/01_bonds_masthead_image_iifl_25_feb_v1.webp",
    link: "/bond_details/44a0ca66-08b6-4fff-b533-85987739787a",
    backgroundColor: "#F5F5F5",
    alt: "IIFL bond",
  },
  {
    stackImage:
      "https://assets.stablemoney.in/app/05_bonds_masthead_navi_25_feb_v1.webp",
    clipImage:
      "https://assets.stablemoney.in/app/05_bonds_masthead_image_navi_25_feb_v1.webp",
    link: "/bond_details/aecdcbcb-a7e9-488b-aed7-4d3ca12bb5fd",
    backgroundColor: "#F0F7FF",
    alt: "Navi bond",
  },
  {
    stackImage:
      "https://assets.stablemoney.in/app/02_bonds_masthead_vivriti_25_feb_v1.webp",
    clipImage:
      "https://assets.stablemoney.in/app/02_bonds_masthead_image_vivriti_25_feb_v1.webp",
    link: "/bond_details/06d05d83-4d70-4566-9577-7f481e86899e",
    backgroundColor: "#F5F5F5",
    alt: "Vivriti bond",
  },
];

export const Regular: Story = {
  args: {
    autoPlayDurationInMillis: 2000,
    loop: true,
    disableAppBarPadding: true,
    height: 500,
    heightPercentage: 53,
    items: carouselItems,
  },
  render: (args) => (
    <Carousel
      autoPlayDurationInMillis={args.autoPlayDurationInMillis}
      loop={args.loop}
      disableAppBarPadding={args.disableAppBarPadding}
      height={args.height ?? 0}
      heightPercentage={args.heightPercentage}
      items={args.items}
    />
  ),
};
