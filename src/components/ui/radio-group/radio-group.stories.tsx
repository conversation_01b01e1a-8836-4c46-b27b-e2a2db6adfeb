import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import RadioGroup from "./radio-group";
import RadioItem from "./radio-item";

const meta: Meta<typeof RadioGroup> = {
  title: "Components/Radio Group",
  component: RadioGroup,
  tags: ["autodocs"],
  argTypes: {
    name: { control: "text" },
    value: { control: "text" },
    disabled: { control: "boolean" },
    orientation: {
      control: { type: "select" },
      options: ["horizontal", "vertical"],
    },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Vertical: Story = {
  args: {
    name: "plan",
    value: "basic",
    orientation: "vertical",
  },
  render: (args) => (
    <RadioGroup
      name={args.name!}
      orientation={args.orientation}
      disabled={args.disabled}
      onChange={args.onChange as () => void}
    >
      <RadioItem value="basic">Basic Plan - ₹199/month</RadioItem>
      <RadioItem value="standard">Standard Plan - ₹499/month</RadioItem>
      <RadioItem value="premium">Premium Plan - ₹999/month</RadioItem>
    </RadioGroup>
  ),
};
