.radio-group {
  display: flex;
}

.radio-group.vertical {
  flex-direction: column;
}

.radio-group.horizontal {
  flex-direction: row;
}

.radio-item {
  display: flex;
  align-items: center;
  gap: calc(var(--spacing) * 2);
  cursor: pointer;
  font-size: var(--text-heading4);
  line-height: var(--body1-line-height);
  letter-spacing: var(--body1-letter-spacing);
  color: var(--color-black-50);
}

.radio-item.checked {
  color: var(--color-black-80);
}

.radio-item.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.radio-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: calc(var(--spacing) * 4);
  height: calc(var(--spacing) * 4);
  border-radius: 50%;
  border: var(--border-w-md) solid var(--color-black-20);
  background-color: var(--color-white);
  transition: all 0.2s ease;
}

.radio-item:hover .radio-indicator {
  border-color: var(--color-black-40);
}

.radio-item.checked .radio-indicator {
  color: var(--color-black-80);
  border-color: var(--color-black);
}

.radio-indicator-dot {
  width: calc(var(--spacing) * 2);
  height: calc(var(--spacing) * 2);
  border-radius: 50%;
  background-color: var(--color-black);
}

.radio-label {
  flex: 1;
}
