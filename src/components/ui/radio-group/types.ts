import type { HTMLAttributes, ReactNode } from "react";

export type RadioGroupProps = {
  name: string;
  value?: string;
  disabled?: boolean;
  orientation?: "horizontal" | "vertical";
  onChange?: (value: string) => void;
  children?: ReactNode;
} & Omit<HTMLAttributes<HTMLDivElement>, "onChange">;

export type RadioItemProps = {
  value: string;
  disabled?: boolean;
  children?: ReactNode;
  isChecked?: boolean;
} & HTMLAttributes<HTMLLabelElement>;
