import { useRadioGroupContext } from "./context";
import clsx from "clsx";
import type { RadioItemProps } from "./types";

export default function RadioItem({
  value,
  disabled = false,
  children,
  className,
  ...rest
}: RadioItemProps) {
  const api = useRadioGroupContext();

  if (!api) {
    throw new Error("RadioItem must be used within a RadioGroup");
  }

  const isChecked = api.value === value;
  const isDisabled = disabled;

  const classes = clsx(
    "radio-item",
    {
      checked: isChecked,
      disabled: isDisabled,
    },
    className
  );

  return (
    <>
      <input {...api.getItemHiddenInputProps({ value, disabled })} />
      <label
        {...api.getItemProps({ value, disabled })}
        {...rest}
        className={classes}
      >
        <div className="radio-indicator">
          {isChecked && <div className="radio-indicator-dot" />}
        </div>
        <div className="radio-label">{children}</div>
      </label>
    </>
  );
}
