import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import BottomSheet from "./bottom-sheet";
import type { BottomSheetProps } from "./types";
import Button from "../button/button";

const meta: Meta<BottomSheetProps> = {
  title: "Components/BottomSheet",
  component: BottomSheet,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    direction: {
      control: { type: "select" },
      options: ["top", "bottom", "left", "right"],
    },
    dismissible: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<BottomSheetProps>;

// Basic bottom sheet with trigger button
export const Default: Story = {
  args: {
    trigger: <Button>Open Bottom Sheet</Button>,
    children: ({ dismiss }) => (
      <div className="space-y-4 p-4">
        <p className="text-body1 text-black-60">
          This is the main content of the bottom sheet. You can put any content
          here.
        </p>
        <div className="bg-black-5 flex h-32 items-center justify-center rounded-lg">
          <span className="text-black-40">Content Area</span>
        </div>
        <p className="text-body2 text-black-50">
          The bottom sheet can be dismissed by dragging down, clicking the close
          button, or clicking outside (unless persistent mode is enabled).
        </p>
        <Button onClick={dismiss}>Close Bottom Sheet</Button>
      </div>
    ),
  },
};
