import type { HTMLAttributes } from "react";
import type { Api } from "@zag-js/select";
import type { SelectItem } from "./types";
import clsx from "clsx";

export default function SelectedValue({
  className,
  item,
  api,
  ...rest
}: { item: SelectItem; api: Api } & HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      {...api.getItemProps({ item })}
      {...rest}
      key={item.value}
      className={clsx("pt-4", className)}
    >
      {item.label}
    </div>
  );
}
