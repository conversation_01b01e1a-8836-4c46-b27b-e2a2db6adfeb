import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { Select } from "./index";
import Button from "../button/button";

const meta: Meta<typeof Select> = {
  title: "Components/Select",
  component: Select,
  tags: ["autodocs"],
  argTypes: {
    label: { control: "text" },
    name: { control: "text" },
    value: { control: "text" },
    error: { control: "text" },
    help: { control: "text" },
    disabled: { control: "boolean" },
    required: { control: "boolean" },
    readOnly: { control: "boolean" },
    multiple: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    label: "Tenure",
    name: "select",
  },
  render: (args) => (
    <form>
      <Select
        {...args}
        items={[
          { label: "First option", value: "first" },
          { label: "Second option", value: "second" },
          { label: "Third option", value: "third" },
        ]}
      />
      <Button type="submit">Submit</Button>
    </form>
  ),
};
