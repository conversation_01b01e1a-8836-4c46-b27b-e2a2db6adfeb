import { useId, useMemo } from "react";
import * as select from "@zag-js/select";
import { useMachine, normalizeProps, Portal } from "@zag-js/react";
import type { SelectItem, SelectProps } from "./types";
import clsx from "clsx";
import SelectedValue from "./selected-value";
import SelectOption from "./select-option";
import CaretDown from "@/components/icons/caret-down";

function defaultOptionRenderer<Item extends SelectItem>(
  item: Item,
  api: select.Api
) {
  return <SelectOption item={item} api={api} key={item.value} />;
}

function defaultValueRenderer<Item extends SelectItem>(
  items: Item[],
  api: select.Api
) {
  // The default renderer will only support a single value
  return <SelectedValue item={items[0]} api={api} />;
}

export default function Select<Item extends SelectItem>({
  error,
  label,
  prefix,
  help,
  className,
  id,
  readOnly,
  renderOption = defaultOptionRenderer,
  renderValue = defaultValueRenderer,
  items,
  groupBy,
  groupSort,
  itemToString = (item: Item) => item.label,
  itemToValue = (item: Item) => item.value,
  isItemDisabled = (item: Item) => item.disabled || false,
  ...props
}: SelectProps<Item>) {
  const defaultId = useId();

  // Create collection from options
  const collection = useMemo(
    () =>
      select.collection({
        items,
        groupBy,
        groupSort,
        itemToString,
        itemToValue,
        isItemDisabled,
      }),
    [items, groupBy, groupSort, itemToString, itemToValue, isItemDisabled]
  );

  const service = useMachine(select.machine, {
    id: id || defaultId,
    collection,
    readOnly,
    ...props,
    positioning: {
      strategy: "fixed",
    },
  });

  const api = select.connect(service, normalizeProps);
  const selectedItems = collection.items.filter((item) =>
    api.value.includes(item.value)
  );
  const pristine = !api.value.length;

  const borderColor = error ? "#ee4950" : "#0000001a";

  return (
    <>
      <div className="bg-black-5 rounded-xl border" style={{ borderColor }}>
        <div
          className="relative flex items-center gap-3 overflow-hidden rounded-xl border bg-white px-3"
          style={{ borderColor }}
          {...api.getControlProps()}
        >
          <label
            className={clsx(
              "text-black-40 pointer-events-none absolute transition-all duration-300 ease-in-out",
              pristine
                ? "text-heading3 top-1/2 translate-y-[-50%]"
                : "text-body2 top-2"
            )}
            {...api.getLabelProps()}
          >
            {label}
          </label>
          <select {...api.getHiddenSelectProps()}>
            {collection.items.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
          <button
            type="button"
            className={clsx(
              className,
              "text-heading3 block min-h-14 w-full cursor-pointer text-left outline-none",
              {
                "text-transparent": pristine,
                "cursor-not-allowed": readOnly,
              }
            )}
            {...api.getTriggerProps()}
          >
            {prefix}
            {selectedItems.length > 0 ? renderValue(selectedItems, api) : null}
          </button>
          <CaretDown dropDownOpen={api.open} />
        </div>
        {help && <div className="px-4 py-2 empty:hidden">{help}</div>}
      </div>
      {error && <div className="text-red">{error}</div>}
      <Portal>
        <div {...api.getPositionerProps()} className="!z-20">
          <div
            className="overflow-hidden rounded-xl border bg-white"
            style={{ borderColor }}
          >
            <ul
              {...api.getContentProps()}
              className="[&_[data-part='item'][data-highlighted]]:bg-black-5 max-h-(--available-height) w-(--reference-width) overflow-y-auto"
            >
              {collection.items.map((item) =>
                renderOption(
                  { ...item, selected: api.value.includes(item.value) },
                  api
                )
              )}
            </ul>
          </div>
        </div>
      </Portal>
    </>
  );
}
