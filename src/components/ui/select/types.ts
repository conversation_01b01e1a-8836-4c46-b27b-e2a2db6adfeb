import type { HTMLAttributes, ReactNode } from "react";
import type { CollectionOptions, Props, Api } from "@zag-js/select";

export type SelectProps<Item extends SelectItem> = Omit<
  Props<Item>,
  "collection" | "positioning"
> &
  CollectionOptions<Item> & {
    error?: string;
    label?: string;
    renderOption?: (option: Item, api: Api) => ReactNode;
    renderValue?: (options: Item[], api: Api) => ReactNode;
    prefix?: ReactNode;
    help?: ReactNode;
  } & HTMLAttributes<HTMLDivElement>;

export type SelectItem = { label: string; value: string; disabled?: boolean };
export type ValueComponentProps = {
  item: SelectItem;
  api: Api;
} & HTMLAttributes<HTMLLIElement>;
export type OptionComponentProps = {
  item: SelectItem;
  api: Api;
} & HTMLAttributes<HTMLLIElement>;
