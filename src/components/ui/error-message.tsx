import { getErrorMessage } from "@/utils/errors";
import clsx from "clsx";
import { useEffect, useState, type HTMLAttributes } from "react";

type ErrorMessageProps = {
  error: Error;
} & HTMLAttributes<HTMLParagraphElement>;

export default function ErrorMessage({ error, className }: ErrorMessageProps) {
  const [message, setMessage] = useState("");
  useEffect(() => {
    getErrorMessage(error).then(setMessage);
  }, [error]);

  return <p className={clsx(className, "text-red empty:hidden")}>{message}</p>;
}
