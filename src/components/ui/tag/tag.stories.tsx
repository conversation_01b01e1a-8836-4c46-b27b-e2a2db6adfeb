import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Tag from "./tag";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Tag> = {
  title: "Components/Tag",
  component: Tag,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    color: { control: "color" },
    backgroundColor: { control: "color" },
    shimmerColor: { control: "color" },
    borderColor: { control: "color" },
    hasShimmer: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Regular: Story = {
  args: {
    color: "#916CFF",
    backgroundColor: "#F8F5FF",
    shimmerColor: "#916CFF",
    borderColor: "#916CFF33",
    hasShimmer: true,
  },
  render: (args) => (
    <Tag
      color={args.color}
      backgroundColor={args.backgroundColor}
      shimmerColor={args.shimmerColor}
      borderColor={args.borderColor}
      hasShimmer={args.hasShimmer}
      leading={
        <svg
          width="13"
          height="7"
          viewBox="0 0 13 7"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <mask
            id="path-1-outside-1_4438_5622"
            maskUnits="userSpaceOnUse"
            x="0"
            y="-0.826214"
            width="13"
            height="8"
            fill="black"
          >
            <rect fill="white" y="-0.826214" width="13" height="8" />
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M0.5 0.173786C0.5 0.173786 0.5 0.173786 0.5 0.173786C0.5 0.173786 0.5 0.173786 0.5 0.173786H12.5C12.5 0.173786 12.5 0.173786 12.5 0.173786C12.5 0.173786 12.5 0.173786 12.5 0.173786V6.17379C12.5 6.17379 12.5 6.17379 12.5 6.17379C12.5 6.17379 12.5 6.17379 12.5 6.17379H0.5C0.5 6.17379 0.5 6.17379 0.5 6.17379C0.5 6.17379 0.5 6.17379 0.5 6.17379V0.173786Z"
            />
          </mask>
          <path
            fillRule="evenodd"
            clipRule="evenodd"
            d="M0.5 0.173786C0.5 0.173786 0.5 0.173786 0.5 0.173786C0.5 0.173786 0.5 0.173786 0.5 0.173786H12.5C12.5 0.173786 12.5 0.173786 12.5 0.173786C12.5 0.173786 12.5 0.173786 12.5 0.173786V6.17379C12.5 6.17379 12.5 6.17379 12.5 6.17379C12.5 6.17379 12.5 6.17379 12.5 6.17379H0.5C0.5 6.17379 0.5 6.17379 0.5 6.17379C0.5 6.17379 0.5 6.17379 0.5 6.17379V0.173786Z"
            fill="currentColor"
          />
          <path
            d="M0.5 0.173786V-0.826214H-0.5V0.173786H0.5ZM0.5 0.173786H-0.5V0.173786H0.5ZM0.5 0.173786H1.5V-0.826214H0.5V0.173786ZM12.5 0.173786V-0.826214H11.5V0.173786H12.5ZM12.5 0.173786H11.5V0.173786H12.5ZM12.5 0.173786H13.5V-0.826214H12.5V0.173786ZM12.5 6.17379V7.17379H13.5V6.17379H12.5ZM12.5 6.17379H13.5V6.17379H12.5ZM12.5 6.17379H11.5V7.17379H12.5V6.17379ZM0.5 6.17379V7.17379H1.5V6.17379H0.5ZM0.5 6.17379H1.5V6.17379H0.5ZM0.5 6.17379H-0.5V7.17379H0.5V6.17379ZM0.5 1.17379V0.173786H-1.5V1.17379H0.5ZM0.5 0.173786V1.17379H1.5V0.173786H0.5ZM0.5 -0.826214C-0.0522847 -0.826214 -0.5 -0.378499 -0.5 0.173786H1.5C1.5 -1.37607 0.0522847 -2.82621 -0.5 -2.82621V-0.826214ZM0.5 0.173786C-0.5 0.173786 -0.5 0.173786 -0.5 0.173786H1.5C1.5 0.173786 1.5 0.173786 1.5 0.173786H0.5ZM12.5 -0.826214H0.5V1.17379H12.5V-0.826214ZM11.5 0.173786C11.5 0.173786 11.5 0.173786 11.5 0.173786V-1.82621C10.9477 -1.82621 10.5 -1.37849 10.5 -0.826214L11.5 0.173786ZM13.5 0.173786C13.5 -0.378499 13.0523 -0.826214 12.5 -0.826214V1.17379C13.0523 1.17379 13.5 0.726071 13.5 0.173786ZM12.5 7.17379C13.0523 7.17379 13.5 6.72607 13.5 6.17379H11.5C11.5 5.6215 10.9477 5.17379 10.5 5.17379L12.5 7.17379ZM12.5 6.17379C12.5 6.17379 12.5 6.17379 12.5 6.17379H10.5C10.5 6.17379 10.5 6.17379 10.5 6.17379H12.5ZM0.5 7.17379H12.5V5.17379H0.5V7.17379ZM1.5 6.17379C1.5 6.17379 1.5 6.17379 1.5 6.17379V8.17379C2.05228 8.17379 2.5 7.72607 2.5 7.17379H1.5ZM-0.5 6.17379C-0.5 6.72607 -0.0522847 7.17379 0.5 7.17379V5.17379C-0.0522847 5.17379 -0.5 5.6215 -0.5 6.17379ZM0.5 5.17379C-0.0522847 5.17379 -0.5 5.6215 -0.5 6.17379H1.5C1.5 7.72607 0.0522847 8.17379 -0.5 8.17379V5.17379ZM0.5 6.17379C0.5 6.17379 0.5 6.17379 0.5 6.17379H-1.5C-1.5 6.17379 -1.5 6.17379 -1.5 6.17379H0.5ZM-0.5 0.173786V6.17379H1.5V0.173786H-0.5Z"
            fill="currentColor"
            mask="url(#path-1-outside-1_4438_5622)"
          />
        </svg>
      }
    >
      <span>Best short term FD</span>
    </Tag>
  ),
};

export const WithTrailingIcon: Story = {
  args: {
    color: "#FF5252",
    backgroundColor: "#FFEBEB",
    shimmerColor: "#FF5252",
    borderColor: "#FF525233",
    hasShimmer: true,
  },
  render: (args) => (
    <Tag
      color={args.color}
      backgroundColor={args.backgroundColor}
      shimmerColor={args.shimmerColor}
      borderColor={args.borderColor}
      hasShimmer={args.hasShimmer}
      trailing={
        <svg
          width="12"
          height="12"
          viewBox="0 0 12 12"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M6 1V11"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M11 6L1 6"
            stroke="currentColor"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      }
    >
      <span>Limited time offer</span>
    </Tag>
  ),
};

export const WithBothIcons: Story = {
  args: {
    color: "#4CAF50",
    backgroundColor: "#E8F5E8",
    shimmerColor: "#4CAF50",
    borderColor: "#4CAF5033",
    hasShimmer: true,
  },
  render: (args) => (
    <Tag
      color={args.color}
      backgroundColor={args.backgroundColor}
      shimmerColor={args.shimmerColor}
      borderColor={args.borderColor}
      hasShimmer={args.hasShimmer}
      leading={
        <svg
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M20 6L9 17L4 12"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      }
      trailing={
        <svg
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      }
    >
      <span>Verified Best</span>
    </Tag>
  ),
};
