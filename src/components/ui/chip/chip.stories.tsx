import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Chip from "./chip";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Chip> = {
  title: "Components/Chip",
  component: Chip,
  tags: ["autodocs"],
  argTypes: {
    selected: {
      control: { type: "boolean" },
    },
    variant: {
      control: { type: "select" },
      options: ["default", "outlined"],
    },
    size: {
      control: { type: "select" },
      options: ["large", "medium"],
    },
    caption: { control: "text" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Regular: Story = {
  args: {
    selected: false,
    variant: "default",
    caption: "Hello",
  },
  render: (args) => <Chip {...args}>Fixed Deposits</Chip>,
};

export const WithIcons: Story = {
  args: {
    selected: false,
  },
  render: (args) => (
    <Chip selected={args.selected}>
      <svg
        width="20"
        height="20"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 16V12"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M12 8H12.01"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
      Continue
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8 5L15 12L8 19"
          stroke="black"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </Chip>
  ),
};
