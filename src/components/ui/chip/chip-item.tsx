import Chip from "./chip";
import { useChipGroupContext } from "./context";
import type { ChipItemProps } from "./types";

export default function ChipItem({
  value,
  variant,
  size,
  children,
  caption,
  ...rest
}: ChipItemProps) {
  const api = useChipGroupContext();
  const itemProps = api?.getItemProps({ value });
  const { onClick, ...chipProps } = itemProps || {};

  return (
    <>
      <input {...api?.getItemHiddenInputProps({ value })} {...rest} />
      <Chip
        {...chipProps}
        selected={api?.value === value}
        variant={variant}
        size={size}
        caption={caption}
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        onClick={(_isSelected) => {
          if (onClick) {
            const syntheticEvent = new MouseEvent("click", {
              bubbles: true,
              cancelable: true,
            });
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            onClick(syntheticEvent as any);
          }
        }}
      >
        {children}
      </Chip>
    </>
  );
}
