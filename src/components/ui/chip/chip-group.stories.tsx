import type { <PERSON>a, StoryObj } from "@storybook/react";
import ChipGroup from "./chip-group";
import ChipItem from "./chip-item";

type CustomArgs = {
  onChange: (value: string) => void;
};

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof ChipGroup & CustomArgs> = {
  title: "Components/Chip Group",
  component: ChipGroup,
  tags: ["autodocs"],
  argTypes: {
    name: { control: "text" },
    value: { control: "text" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Basic: Story = {
  args: {
    name: "category",
  },
  render: (args) => (
    <ChipGroup {...args} defaultValue="rewards">
      <ChipItem value="rewards" caption="popular" variant="outlined">
        Rewards
      </ChipItem>
      <ChipItem value="stable-money">Stable money</ChipItem>
      <ChipItem value="fd-booking">FD booking</ChipItem>
      <ChipItem value="fd-insurance">FD insurance</ChipItem>
      <ChipItem value="transactions">Transactions</ChipItem>
      <ChipItem value="kyc">KYC</ChipItem>
    </ChipGroup>
  ),
};
