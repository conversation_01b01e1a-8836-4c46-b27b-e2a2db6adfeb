import type { ReactNode } from "react";
import type { HTMLAttributes } from "react";

export type ChipProps = {
  selected?: boolean;
  variant?: "default" | "outlined";
  size?: "large" | "medium";
  children?: ReactNode;
  caption?: ReactNode;
  onClick?: (isSelected: boolean) => void;
} & Omit<HTMLAttributes<HTMLSpanElement>, "size">;

export type ChipGroupProps = {
  name: string;
  value?: string;
  children?: ReactNode;
} & HTMLAttributes<HTMLDivElement>;

export type ChipItemProps = {
  value: string;
} & Omit<ChipProps, "selected">;
