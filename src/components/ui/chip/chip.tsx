import clsx from "clsx";
import type { ChipProps } from "./types";

export default function Chip({
  selected = false,
  children,
  caption,
  variant = "default",
  size = "medium",
  className,
  onClick,
  ...rest
}: ChipProps) {
  const contentClasses = clsx("flex items-center justify-center gap-2", {
    "text-heading-4 py-2.5 px-4": size === "large",
    "text-body-1 py-1.5 px-3": size === "medium",
  });

  const borderColor =
    selected && variant === "outlined" ? "#000000" : "#0000001A";

  return (
    <label
      className={clsx(
        "inline-block cursor-pointer overflow-hidden rounded-sm border align-top",
        selected && variant === "default"
          ? "bg-black-80 text-white"
          : "text-black-60",
        className
      )}
      style={{ borderColor }}
      onClick={() => {
        onClick?.(!selected);
      }}
      {...rest}
    >
      <div className={contentClasses}>{children}</div>
      {caption ? (
        <div
          className="text-caption text-green flex items-center justify-center rounded-b-sm px-2 py-0.5 font-bold"
          style={{ backgroundColor: "#12BE571A" }}
        >
          {caption}
        </div>
      ) : null}
    </label>
  );
}
