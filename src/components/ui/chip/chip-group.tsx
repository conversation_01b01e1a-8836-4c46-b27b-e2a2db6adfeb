import type { ChipGroupProps } from "./types";
import * as radio from "@zag-js/radio-group";
import { useMachine, normalizeProps } from "@zag-js/react";
import { ChipGroupContext } from "./context";
import clsx from "clsx";

export default function ChipGroup({
  children,
  value,
  defaultValue,
  name,
  className,
  ...rest
}: ChipGroupProps) {
  const service = useMachine(radio.machine, {
    id: name,
    defaultValue: defaultValue as string,
    value,
  });
  const api = radio.connect(service, normalizeProps);
  const classes = clsx(className, "space-x-2");

  return (
    <div {...rest} {...api.getRootProps()} className={classes}>
      <ChipGroupContext.Provider value={api}>
        {children}
      </ChipGroupContext.Provider>
    </div>
  );
}
