import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import <PERSON><PERSON> from "./button";
import LinkButton from "./link-button";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Button> = {
  title: "Components/Button",
  component: Button,
  tags: ["autodocs"],
  argTypes: {
    children: {
      name: "Label",
      control: "text",
    },
    loading: { control: "boolean" },
    disabled: { control: "boolean" },
    size: {
      control: { type: "select" },
      options: ["medium", "small"],
    },
    type: {
      control: { type: "select" },
      options: ["submit", "reset", "button"],
    },
    href: { control: "text" },
    hasShimmer: { control: "boolean" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    children: "Primary Button",
    loading: false,
    disabled: false,
    size: "medium",
    type: "button",
    hasShimmer: true,
    href: "",
  },
  render: (args) => (
    <Button
      title="This is title"
      loading={args.loading}
      type={args.type}
      disabled={args.disabled}
      href={args.href || undefined}
      size={args.size}
      hasShimmer={args.hasShimmer}
    >
      {args.children || "Button"}
    </Button>
  ),
};

type LinkButtonStory = StoryObj<Meta<typeof LinkButton>>;

export const LinkedButton: LinkButtonStory = {
  args: {
    children: "Link Text",
    loading: false,
    disabled: false,
    type: "button",
    href: "https://example.com",
  },
  render: (args) => (
    <LinkButton
      title="This is title"
      loading={args.loading}
      type={args.type}
      disabled={args.disabled}
      href={args.href}
    >
      {args.children || "Button"}
    </LinkButton>
  ),
};
