import clsx from "clsx";
import type { LinkButtonProps } from "./types";
import type { HTMLAttributes } from "react";
import Anchor from "@/components/functional/anchor";

export default function LinkButton({
  loading = false,
  disabled = false,
  onClick,
  className,
  children,
  ...props
}: LinkButtonProps) {
  const classes = clsx(
    "inline-flex text-black-50 text-body1 gap-0.5 items-center justify-center cursor-pointer link-button underline underline-offset-4",
    {
      "cursor-default": disabled,
      loading: loading,
    },
    className
  );

  function handleClick(event: React.MouseEvent<HTMLAnchorElement>) {
    if (disabled) return;
    onClick?.(event);
  }

  const content = (
    <>
      <span>{children}</span>
      {!loading && (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="size-4"
        >
          <path
            d="M8 5L15 12L8 19"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      )}
      {loading && (
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="size-4 animate-spin"
        >
          <path
            opacity="0.4"
            d="M21.9 12C21.9 10.6999 21.6439 9.41256 21.1464 8.21144C20.6489 7.01031 19.9197 5.91894 19.0004 4.99964C18.0811 4.08034 16.9897 3.35111 15.7886 2.85359C14.5874 2.35607 13.3001 2.1 12 2.1C10.6999 2.1 9.41256 2.35607 8.21144 2.85359C7.01031 3.35111 5.91894 4.08034 4.99964 4.99964C4.08034 5.91894 3.35111 7.01031 2.85359 8.21143C2.35607 9.41256 2.1 10.6999 2.1 12C2.1 13.3001 2.35607 14.5874 2.85359 15.7886C3.35111 16.9897 4.08034 18.0811 4.99964 19.0004C5.91894 19.9197 7.01031 20.6489 8.21143 21.1464C9.41256 21.6439 10.6999 21.9 12 21.9C13.3001 21.9 14.5874 21.6439 15.7886 21.1464C16.9897 20.6489 18.0811 19.9197 19.0004 19.0004C19.9197 18.0811 20.6489 16.9897 21.1464 15.7886C21.6439 14.5874 21.9 13.3001 21.9 12L21.9 12Z"
            stroke="currentColor"
            strokeOpacity="0.4"
            strokeWidth="1.8"
          />
          <path
            d="M21.9 12C21.9 10.3134 21.4691 8.65484 20.6483 7.18153C19.8274 5.70822 18.6437 4.46904 17.2096 3.58155C15.7754 2.69405 14.1383 2.18765 12.4535 2.11039C10.7687 2.03313 9.09212 2.38758 7.58275 3.14009C6.07338 3.89261 4.78129 5.01826 3.82902 6.41026C2.87676 7.80227 2.2959 9.41447 2.14155 11.0939C1.98719 12.7734 2.26446 14.4645 2.94705 16.0068C3.62964 17.549 4.69491 18.8913 6.04183 19.9063"
            stroke="currentColor"
            strokeOpacity="0.8"
            strokeWidth="1.8"
          />
        </svg>
      )}
    </>
  );

  if ("href" in props) {
    return (
      <Anchor
        className={classes}
        onClick={handleClick}
        {...(props as HTMLAttributes<HTMLAnchorElement>)}
      >
        {content}
      </Anchor>
    );
  }

  return (
    <button
      className={classes}
      disabled={disabled}
      onClick={onClick as () => void}
      {...(props as HTMLAttributes<HTMLButtonElement>)}
    >
      {content}
    </button>
  );
}
