import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import HorizontalList from "./horizontal-list";
import HorizontalListItem from "./horizontal-list-item";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof HorizontalListItem> = {
  title: "Components/Horizontal List",
  component: HorizontalListItem,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    alignment: {
      control: { type: "select" },
      options: ["center", "left", "right"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  render: () => (
    <HorizontalList>
      <HorizontalListItem alignment="center" label="YTM" value="11.50%" />
      <HorizontalListItem alignment="center" label="Tenure" value="1Y 11M" />
      <HorizontalListItem alignment="center" label="Sold" value="10.61%" />
    </HorizontalList>
  ),
};

export const LeftAligned: Story = {
  render: () => (
    <HorizontalList>
      <HorizontalListItem alignment="left" label="YTM" value="11.50%" />
      <HorizontalListItem alignment="left" label="Tenure" value="1Y 11M" />
      <HorizontalListItem alignment="left" label="Sold" value="10.61%" />
    </HorizontalList>
  ),
};

export const RightAligned: Story = {
  render: () => (
    <HorizontalList>
      <HorizontalListItem alignment="right" label="YTM" value="11.50%" />
      <HorizontalListItem alignment="right" label="Tenure" value="1Y 11M" />
      <HorizontalListItem alignment="right" label="Sold" value="10.61%" />
    </HorizontalList>
  ),
};

export const MixedAlignment: Story = {
  render: () => (
    <HorizontalList>
      <HorizontalListItem alignment="left" label="YTM" value="11.50%" />
      <HorizontalListItem alignment="center" label="Tenure" value="1Y 11M" />
      <HorizontalListItem alignment="right" label="Sold" value="10.61%" />
    </HorizontalList>
  ),
};

export const WithComplexContent: Story = {
  render: () => (
    <HorizontalList>
      <HorizontalListItem
        alignment="center"
        label={<span className="text-purple">Annual Return</span>}
        value={<strong className="text-green">11.50%</strong>}
      />
      <HorizontalListItem
        alignment="center"
        label="Investment Period"
        value={
          <div className="flex flex-col">
            <span>1Y 11M</span>
            <span className="text-caption text-black-40">Until Mar 2025</span>
          </div>
        }
      />
      <HorizontalListItem
        alignment="center"
        label="Availability"
        value={
          <div className="flex items-center gap-1">
            <div className="bg-green h-2 w-2 rounded-full"></div>
            <span>10.61%</span>
          </div>
        }
      />
    </HorizontalList>
  ),
};

export const TwoItems: Story = {
  render: () => (
    <HorizontalList>
      <HorizontalListItem
        alignment="center"
        label="Current Price"
        value="₹99,850"
      />
      <HorizontalListItem
        alignment="center"
        label="Face Value"
        value="₹1,00,000"
      />
    </HorizontalList>
  ),
};

export const FourItems: Story = {
  render: () => (
    <HorizontalList>
      <HorizontalListItem alignment="center" label="YTM" value="11.50%" />
      <HorizontalListItem alignment="center" label="Tenure" value="1Y 11M" />
      <HorizontalListItem alignment="center" label="Sold" value="10.61%" />
      <HorizontalListItem alignment="center" label="Rating" value="CRISIL A+" />
    </HorizontalList>
  ),
};
