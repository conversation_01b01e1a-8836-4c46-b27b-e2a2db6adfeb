import clsx from "clsx";
import type { HorizontalListItemProps } from "./types";

export default function HorizontalListItem({
  alignment = "center",
  label,
  value,
  className,
  ...rest
}: HorizontalListItemProps) {
  const classes = clsx("flex flex-col gap-0.5 px-2", {
    "items-start": alignment === "left",
    "items-center": alignment === "center",
    "items-end": alignment === "right",
  });

  return (
    <div className={clsx("flex-1", className)} {...rest}>
      <div className={classes}>
        <dt className="text-body1 text-black-50">{label}</dt>
        <dd className="text-heading3 font-medium">{value}</dd>
      </div>
    </div>
  );
}
