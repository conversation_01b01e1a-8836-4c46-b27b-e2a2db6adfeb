.stepper {
  display: flex;
  align-items: flex-start;
}

.step-detail-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 24px;
}

.step-detail-wrapper p {
  margin: 0;
}

.stepper-container {
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 24px;
}

.step {
  display: flex;
  align-items: center;
  position: relative;
}

.icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.line {
  width: 4px;
  height: 90px;
  position: absolute;
  left: 16px;
  top: 20px;
}

.dashed {
  border-left: none;
  background-image: linear-gradient(to bottom, #e9e9e9 50%, transparent 50%);
  background-size: 1px 12px;
  background-repeat: repeat-y;
  background-position: left;
}

.label {
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.2px;
  color: var(--black-8, rgba(0, 0, 0, 0.8));
}

.caption {
  font-size: 16px;
  line-height: 24px;
  letter-spacing: -0.2px;
  color: var(--color-black-50, rgba(0, 0, 0, 0.5));
}
