import type { VerticalStepperProps } from "./types";
import "./vertical-stepper.css";

export default function VerticalStepper({ steps }: VerticalStepperProps) {
  return (
    <div className="stepper">
      <div className="stepper-container">
        {steps.map((step, index) => (
          <div key={step.id || index} className="step">
            <div className="icon">
              <img src={step.icon} alt="step-icon" className="size-4" />
            </div>
            <div className="step-detail-wrapper">
              <p className="label">{step.label}</p>
              <p className="caption">{step.caption}</p>
            </div>
            {index !== steps.length - 1 && <div className="line dashed"></div>}
          </div>
        ))}
      </div>
    </div>
  );
}
