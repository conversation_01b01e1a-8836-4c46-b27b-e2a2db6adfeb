import * as avatar from "@zag-js/avatar";
import { useMachine, normalizeProps } from "@zag-js/react";
import { useId } from "react";

export interface AvatarProps extends avatar.Props {
  size?: "small" | "medium" | "large";
  src?: string;
  srcSet?: string;
  name: string;
}
export default function Avatar(props: AvatarProps) {
  const [machineProps, localProps] = avatar.splitProps(props);

  const service = useMachine(avatar.machine, {
    id: useId(),
    ...machineProps,
  });

  const api = avatar.connect(service, normalizeProps);

  return (
    <div {...api.getRootProps()}>
      <span {...api.getFallbackProps()}>{getInitials(localProps.name)}</span>
      <img
        alt={localProps.name}
        src={localProps.src}
        srcSet={localProps.srcSet}
        width={
          localProps.size === "small"
            ? 24
            : localProps.size === "medium"
              ? 32
              : 40
        }
        height={
          localProps.size === "small"
            ? 24
            : localProps.size === "medium"
              ? 32
              : 40
        }
        className="rounded-full"
        {...api.getImageProps()}
      />
    </div>
  );
}

function getInitials(name: string) {
  return name
    .split(" ")
    .map((word) => word[0])
    .join("");
}
