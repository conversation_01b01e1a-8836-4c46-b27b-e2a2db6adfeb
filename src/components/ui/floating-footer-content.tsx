import { type ReactNode, type H<PERSON>LAttributes } from "react";
import { createPortal } from "react-dom";
import { useMediaQuery } from "@react-hook/media-query";
import { useScrollStyle, type ScrollStyleParams } from "@/hooks/scroll-styles";
import clsx from "clsx";

type Props = HTMLAttributes<HTMLDivElement> & {
  children?: ReactNode;
  scrollStyleParams?: ScrollStyleParams;
  className?: string;
};

export default function FloatingFooterContent({
  children,
  scrollStyleParams,
  className,
  ...props
}: Props) {
  const desktop = useMediaQuery("(min-width: 768px)");
  const mobileRef = useScrollStyle(scrollStyleParams);

  if (desktop) {
    return (
      <div
        className={clsx(
          "bg-bg border-black-20 space-y-3 self-stretch rounded-b-xl border-t p-5",
          className
        )}
        {...props}
      >
        {children}
      </div>
    );
  }

  // Mobile version - render into portal
  const portalTarget =
    typeof document !== "undefined"
      ? document.querySelector("#floating-footer-content")
      : null;

  if (!portalTarget) {
    return null;
  }

  return createPortal(
    <div
      ref={mobileRef as React.RefObject<HTMLDivElement>}
      className={clsx("bg-bg border-x border-t p-5 empty:hidden", className)}
      {...props}
    >
      {children}
    </div>,
    portalTarget
  );
}
