import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import AppBar from "./app-bar";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof AppBar> = {
  title: "Components/App Bar",
  component: AppBar,
  tags: ["autodocs"],
  argTypes: {
    children: {
      name: "Title",
      control: { type: "text" },
    },
  },
  parameters: {
    layout: "fullscreen",
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    children: "Stable money",
  },
  render: (args) => (
    <AppBar
      {...args}
      left={
        <svg
          width="36"
          height="36"
          viewBox="0 0 36 36"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="36" height="36" rx="18" fill="white" />
          <rect
            x="0.25"
            y="0.25"
            width="35.5"
            height="35.5"
            rx="17.75"
            stroke="#8A8A8A"
            strokeOpacity="0.31"
            strokeWidth="0.5"
          />
          <path
            d="M20.625 12.75L15.375 18L20.625 23.25"
            stroke="black"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      }
      right={
        <svg
          width="36"
          height="36"
          viewBox="0 0 36 36"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="36" height="36" rx="18" fill="white" />
          <rect
            x="0.25"
            y="0.25"
            width="35.5"
            height="35.5"
            rx="17.75"
            stroke="#8A8A8A"
            strokeOpacity="0.31"
            strokeWidth="0.5"
          />
          <path
            d="M23.8328 10.5078H13.1666C11.696 10.5078 10.5 11.7038 10.5 13.1744V21.174C10.5 22.6447 11.696 23.8406 13.1666 23.8406H15.1151L17.6144 25.9445C17.869 26.1712 18.1883 26.2838 18.5057 26.2832C18.8177 26.2832 19.1263 26.1752 19.367 25.9605L21.9315 23.8406H23.8334C25.3041 23.8406 26.5 22.6447 26.5 21.174V13.1744C26.5 11.7038 25.3034 10.5078 23.8328 10.5078ZM25.1661 21.174C25.1661 21.9093 24.5681 22.5073 23.8328 22.5073H21.6915C21.5369 22.5073 21.3862 22.5613 21.2669 22.66L18.4863 24.9366L15.7878 22.664C15.6671 22.5627 15.5151 22.5073 15.3578 22.5073H13.1659C12.4306 22.5073 11.8326 21.9093 11.8326 21.174V13.1744C11.8326 12.4391 12.4306 11.8411 13.1659 11.8411H23.8321C24.5674 11.8411 25.1654 12.4391 25.1654 13.1744L25.1661 21.174ZM19.4996 21.5074C19.4996 22.0593 19.0516 22.5073 18.4997 22.5073C17.9477 22.5073 17.4997 22.0593 17.4997 21.5074C17.4997 20.9554 17.9477 20.5074 18.4997 20.5074C19.0516 20.5074 19.4996 20.9554 19.4996 21.5074ZM21.0756 15.1416C21.4022 16.4069 20.7896 17.7195 19.6183 18.2615C19.459 18.3355 19.1656 18.6881 19.1656 19.0408V19.1741C19.1656 19.5428 18.8677 19.8408 18.499 19.8408C18.1303 19.8408 17.8324 19.5428 17.8324 19.1741V19.0408C17.8324 18.1182 18.4637 17.3275 19.0576 17.0522C19.5456 16.8262 19.9809 16.2362 19.7849 15.4756C19.6716 15.0383 19.3023 14.669 18.8657 14.5563C18.437 14.4437 18.0184 14.5276 17.6844 14.7863C17.355 15.0416 17.1664 15.4263 17.1664 15.8416C17.1664 16.2102 16.8684 16.5082 16.4998 16.5082C16.1311 16.5082 15.8331 16.2102 15.8331 15.8416C15.8331 15.0103 16.2098 14.2417 16.8684 13.7323C17.5257 13.2217 18.3763 13.051 19.1996 13.2657C20.1049 13.499 20.8422 14.235 21.0756 15.1416Z"
            fill="black"
            fillOpacity="0.8"
          />
        </svg>
      }
    >
      <div>
        <div style={{ textAlign: "center" }}>{args.children}</div>
        <div style={{ textTransform: "none" }}>11.25% XIRR for 14 months</div>
      </div>
    </AppBar>
  ),
};
