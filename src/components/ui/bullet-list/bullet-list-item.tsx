export default function BulletListItem({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex gap-3 py-1.5">
      <svg
        viewBox="0 0 14 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className="mt-0.5 size-4 flex-shrink-0"
      >
        <rect width="14" height="14" rx="7" fill="currentColor" />
        <mask id="path-2-inside-1_5527_6" fill="white">
          <path
            fill-rule="evenodd"
            clip-rule="evenodd"
            d="M6.24481 7.79935L9.06986 4.86915C9.33016 4.59916 9.76256 4.59916 10.0229 4.86915C10.27 5.12554 10.2701 5.53156 10.0229 5.78795L7.16423 8.75299L7.16986 8.75883C6.91165 9.02665 6.56569 9.1568 6.22219 9.14929C5.89378 9.14491 5.56673 9.01471 5.3199 8.75869L5.32527 8.75312L4.26876 7.65758C4.0215 7.40119 4.0214 6.99513 4.26854 6.73862C4.52888 6.46839 4.96154 6.46831 5.22198 6.73845L6.24481 7.79935Z"
          />
        </mask>
        <path
          fill-rule="evenodd"
          clip-rule="evenodd"
          d="M6.24481 7.79935L9.06986 4.86915C9.33016 4.59916 9.76256 4.59916 10.0229 4.86915C10.27 5.12554 10.2701 5.53156 10.0229 5.78795L7.16423 8.75299L7.16986 8.75883C6.91165 9.02665 6.56569 9.1568 6.22219 9.14929C5.89378 9.14491 5.56673 9.01471 5.3199 8.75869L5.32527 8.75312L4.26876 7.65758C4.0215 7.40119 4.0214 6.99513 4.26854 6.73862C4.52888 6.46839 4.96154 6.46831 5.22198 6.73845L6.24481 7.79935Z"
          fill="white"
        />
        <path
          d="M6.24481 7.79935L6.04324 7.99369L6.24481 8.20276L6.44638 7.99369L6.24481 7.79935ZM9.06986 4.86915L8.86828 4.67481V4.67481L9.06986 4.86915ZM10.0229 4.86915L10.2244 4.67481V4.67481L10.0229 4.86915ZM10.0229 5.78795L9.82129 5.59362V5.59362L10.0229 5.78795ZM7.16423 8.75299L6.96266 8.55866L6.77529 8.753L6.96266 8.94733L7.16423 8.75299ZM7.16986 8.75883L7.37143 8.95317L7.55879 8.75883L7.37142 8.56449L7.16986 8.75883ZM6.22219 9.14929L6.22831 8.86935L6.22592 8.86932L6.22219 9.14929ZM5.3199 8.75869L5.11832 8.56436L4.93097 8.75869L5.11833 8.95303L5.3199 8.75869ZM5.32527 8.75312L5.52684 8.94746L5.71423 8.75309L5.52681 8.55876L5.32527 8.75312ZM4.26876 7.65758L4.06721 7.85194H4.06721L4.26876 7.65758ZM4.26854 6.73862L4.47018 6.93288H4.47018L4.26854 6.73862ZM5.22198 6.73845L5.02041 6.93278L5.22198 6.73845ZM6.44638 7.99369L9.27143 5.06349L8.86828 4.67481L6.04324 7.60501L6.44638 7.99369ZM9.27143 5.06349C9.42161 4.90771 9.6711 4.90771 9.82129 5.06349L10.2244 4.67481C9.85402 4.29061 9.2387 4.29061 8.86828 4.67481L9.27143 5.06349ZM9.82129 5.06349C9.96391 5.21142 9.96391 5.44568 9.82129 5.59362L10.2244 5.98229C10.5762 5.61744 10.5762 5.03966 10.2244 4.67481L9.82129 5.06349ZM9.82129 5.59362L6.96266 8.55866L7.3658 8.94733L10.2244 5.98229L9.82129 5.59362ZM6.96266 8.94733L6.96829 8.95317L7.37142 8.56449L7.3658 8.55865L6.96266 8.94733ZM6.96828 8.56449C6.76665 8.77363 6.49708 8.87524 6.22831 8.86936L6.21606 9.42922C6.6343 9.43837 7.05665 9.27966 7.37143 8.95317L6.96828 8.56449ZM6.22592 8.86932C5.96906 8.8659 5.71427 8.76433 5.52147 8.56435L5.11833 8.95303C5.4192 9.2651 5.81849 9.42393 6.21846 9.42926L6.22592 8.86932ZM5.52147 8.95303L5.52684 8.94746L5.12369 8.55879L5.11832 8.56436L5.52147 8.95303ZM5.52681 8.55876L4.4703 7.46321L4.06721 7.85194L5.12372 8.94749L5.52681 8.55876ZM4.4703 7.46321C4.32761 7.31525 4.32756 7.08091 4.47018 6.93288L4.0669 6.54435C3.71525 6.90934 3.71539 7.48712 4.06721 7.85194L4.4703 7.46321ZM4.47018 6.93288C4.62042 6.77693 4.87011 6.77689 5.02041 6.93278L5.42355 6.54411C5.05297 6.15973 4.43734 6.15984 4.0669 6.54435L4.47018 6.93288ZM5.02041 6.93278L6.04324 7.99369L6.44638 7.60501L5.42355 6.54411L5.02041 6.93278Z"
          fill="white"
          mask="url(#path-2-inside-1_5527_6)"
        />
      </svg>
      <div className="text-body1 flex-1">{children}</div>
    </div>
  );
}
