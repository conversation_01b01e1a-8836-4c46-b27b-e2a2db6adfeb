import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import <PERSON><PERSON>List from "./bullet-list";
import BulletListItem from "./bullet-list-item";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof BulletList> = {
  title: "Components/Bullet List",
  component: BulletList,
  tags: ["autodocs"],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  render: () => (
    <BulletList>
      <BulletListItem>
        You can withdraw anytime after 7 days You can withdraw anytime after 7
        days You can withdraw anytime after 7 days
      </BulletListItem>
      <BulletListItem>You can withdraw anytime after 7 days</BulletListItem>
    </BulletList>
  ),
};

export const MultipleItems: Story = {
  render: () => (
    <BulletList>
      <BulletListItem>
        First bullet point with important information
      </BulletListItem>
      <BulletListItem>
        Second bullet point with additional details
      </BulletListItem>
      <BulletListItem>Third bullet point explaining benefits</BulletListItem>
      <BulletListItem>Fourth bullet point with final notes</BulletListItem>
    </BulletList>
  ),
};

export const LongContent: Story = {
  render: () => (
    <BulletList>
      <BulletListItem>
        This is a very long bullet point that demonstrates how the component
        handles text wrapping for longer content. It should wrap nicely and
        maintain proper alignment with the bullet icon. The spacing between
        lines should be consistent and the overall appearance should be clean
        and readable.
      </BulletListItem>
      <BulletListItem>
        Another bullet point with enough text to demonstrate wrapping behavior.
      </BulletListItem>
    </BulletList>
  ),
};
