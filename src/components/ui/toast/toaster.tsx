import { normalizeProps, useMachine } from "@zag-js/react";
import * as toast from "@zag-js/toast";
import { useId } from "react";
import Toast from "./toast";
import { toaster } from "./store";
import { useMediaQuery } from "@react-hook/media-query";
import { createPortal } from "react-dom";

export default function Toaster() {
  const service = useMachine(toast.group.machine, {
    id: useId(),
    store: toaster,
  });
  const api = toast.group.connect(service, normalizeProps);
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const container = isDesktop
    ? document.body
    : document.getElementById("toaster-root")!;
  return createPortal(
    <div {...api.getGroupProps()}>
      {api
        .getToasts()
        .reverse()
        .map((toast, index) => (
          <Toast key={toast.id} actor={toast} parent={service} index={index} />
        ))}
    </div>,
    container
  );
}
