import type { ReactNode } from "react";

interface SeparatorProps {
  children?: ReactNode;
  className?: string;
}

export default function Separator({ children, className }: SeparatorProps) {
  return <div className={`separator ${className || ""}`}>{children}</div>;
}

// Add the CSS styles
const styles = `
.separator {
  display: flex;
  align-items: center;
  text-align: center;
}

.separator::before,
.separator::after {
  content: '';
  flex: 1;
  border-bottom: 1px solid var(--color-black-10, rgba(0, 0, 0, 0.1));
  margin: 0 16px;
}
`;

// Inject styles if in browser
if (typeof document !== "undefined") {
  const styleSheet = document.createElement("style");
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);
}
