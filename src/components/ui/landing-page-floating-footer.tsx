import { type ReactNode, type HTMLAttributes } from "react";
import { createPortal } from "react-dom";
import clsx from "clsx";

type Props = HTMLAttributes<HTMLDivElement> & {
  children?: ReactNode;
  className?: string;
};

export default function LandingPageFloatingFooter({
  children,
  className,
  ...props
}: Props) {
  const portalTarget =
    typeof document !== "undefined"
      ? document.querySelector("#landing-page-floating-footer")
      : null;

  if (!portalTarget) {
    return null;
  }

  return createPortal(
    <div
      className={clsx(
        "bg-bg rounded-t-sm border-x border-t p-5 empty:hidden",
        className
      )}
      {...props}
    >
      {children}
    </div>,
    portalTarget
  );
}
