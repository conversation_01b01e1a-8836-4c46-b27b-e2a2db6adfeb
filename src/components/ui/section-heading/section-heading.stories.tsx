import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import SectionHeading from "./section-heading";
import LinkButton from "../button/link-button";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof SectionHeading> = {
  title: "Components/Section Heading",
  component: SectionHeading,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    title: { control: "text" },
    separator: { control: "boolean" },
    size: {
      control: { type: "select" },
      options: ["small", "medium"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    title: "Section heading",
    separator: true,
    size: "medium",
  },
  render: (args) => (
    <SectionHeading
      title={args.title}
      separator={args.separator}
      size={args.size}
    >
      <LinkButton href="https://www.google.com" target="_blank">
        View all
      </LinkButton>
    </SectionHeading>
  ),
};
