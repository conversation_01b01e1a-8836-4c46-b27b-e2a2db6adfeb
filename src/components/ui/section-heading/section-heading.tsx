import clsx from "clsx";
import type { SectionHeadingProps } from "./types";

export default function SectionHeading({
  size = "small",
  separator = false,
  title,
  children,
  className,
  isDarkMode = false,
  ...rest
}: SectionHeadingProps) {
  return (
    <div
      className={clsx("flex items-center justify-between gap-2", className)}
      {...rest}
    >
      <h4
        className={clsx(
          size === "small"
            ? "text-title-all-caps text-black-40 font-medium uppercase"
            : "text-heading3",
          {
            "text-white": isDarkMode,
          }
        )}
      >
        {title}
      </h4>
      <div className={clsx("flex flex-1 items-center justify-end gap-1")}>
        {separator && <hr className="border-black-5 flex-1 border-t" />}
        {children}
      </div>
    </div>
  );
}
