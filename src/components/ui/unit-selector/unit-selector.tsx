import { useState, useMemo, useCallback, useEffect } from "react";
import type { UnitSelectorProps } from "./types";
import plusImage from "@/assets/images/icons/plus.svg";
import minusImage from "@/assets/images/icons/minus.svg";
import clsx from "clsx";

export default function UnitSelector({
  value = "",
  max,
  min,
  className,
  onChange,
  isSoldOut,
  ...rest
}: UnitSelectorProps) {
  const [inputValue, setInputValue] = useState(value);

  // Sync internal state with prop value when it changes
  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const minValue = useMemo(() => parseInt(min?.toString() ?? "0", 10), [min]);
  const maxValue = useMemo(() => parseInt(max?.toString() ?? "0", 10), [max]);

  const handleInput = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const input = e.target;
      let newValue = parseInt(input.value);
      if (newValue > maxValue) newValue = maxValue;
      if (newValue < minValue) newValue = minValue;
      e.target.value = newValue.toString();
      const stringValue = newValue.toString();
      onChange(e);
      setInputValue(stringValue);
      input.value = stringValue;
    },
    [maxValue, minValue, onChange]
  );
  const handleBlur = useCallback(
    (e: React.FocusEvent<HTMLInputElement>) => {
      const input = e.target;
      if (input.value === "") {
        const stringValue = minValue.toString();
        input.value = stringValue;
        setInputValue(stringValue);

        if (onChange) {
          const syntheticEvent = {
            target: { value: stringValue },
          } as React.ChangeEvent<HTMLInputElement>;
          onChange(syntheticEvent);
        }
        return;
      }
    },
    [minValue, onChange]
  );

  const increment = useCallback(() => {
    const currentValue = parseInt(inputValue ?? "0") || 0;
    if (currentValue < maxValue || isSoldOut) {
      const newValue = (currentValue + 1).toString();
      setInputValue(newValue);

      // Trigger onChange if provided
      if (onChange) {
        const syntheticEvent = {
          target: { value: newValue },
        } as React.ChangeEvent<HTMLInputElement>;
        onChange(syntheticEvent);
      }
    }
  }, [inputValue, maxValue, onChange]);

  const decrement = useCallback(() => {
    const currentValue = parseInt(inputValue ?? "0") || 0;
    if (currentValue > minValue) {
      const newValue = (currentValue - 1).toString();
      setInputValue(newValue);

      // Trigger onChange if provided
      if (onChange) {
        const syntheticEvent = {
          target: { value: newValue },
        } as React.ChangeEvent<HTMLInputElement>;
        onChange(syntheticEvent);
      }
    }
  }, [inputValue, minValue, onChange]);

  return (
    <div className={clsx("flex w-fit items-center gap-4", className)}>
      <button
        aria-label="minus button"
        onClick={decrement}
        type="button"
        className="cursor-pointer"
      >
        <img decoding="sync" src={minusImage} alt="Decrement" />
      </button>
      <input
        type="number"
        className="border-black-20 sm:text-heading2 unit-selector-input h-15 min-w-[3ch] appearance-none border-b border-dashed text-center text-[54px] leading-none outline-none max-sm:w-30"
        inputMode="numeric"
        min={minValue}
        max={maxValue}
        onChange={handleInput}
        onBlur={handleBlur}
        value={inputValue}
        style={{ width: `${inputValue.length}ch` }}
        {...rest}
      />
      <button
        aria-label="plus button"
        onClick={increment}
        type="button"
        className="cursor-pointer"
      >
        <img src={plusImage} alt="Increment" />
      </button>
    </div>
  );
}
