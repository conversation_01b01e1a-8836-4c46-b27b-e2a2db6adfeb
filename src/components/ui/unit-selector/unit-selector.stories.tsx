import type { <PERSON>a, StoryObj } from "@storybook/react";
import UnitSelector from "./unit-selector";

const meta: Meta<typeof UnitSelector> = {
  title: "Components/Unit Selector",
  component: UnitSelector,
  tags: ["autodocs"],
  argTypes: {
    name: { control: "text" },
    value: { control: "text" },
    min: { control: "number" },
    max: { control: "number" },
    readOnly: { control: "boolean" },
    onChange: { action: "changed" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    name: "unit",
    value: "1",
    min: 1,
    max: 10,
    readOnly: false,
  },
  render: (args) => (
    <form onChange={args.onChange as () => void}>
      <UnitSelector {...args} />
    </form>
  ),
};
