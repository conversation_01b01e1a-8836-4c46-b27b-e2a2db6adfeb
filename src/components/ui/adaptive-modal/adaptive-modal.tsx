import { useMediaQuery } from "@react-hook/media-query";
import { isHotwireNative } from "@/utils/routing";
import Modal from "../modal/modal";
import BottomSheet from "../bottom-sheet/bottom-sheet";
import type { AdaptiveModalProps } from "./types";
import { cloneElement, isValidElement, type ButtonHTMLAttributes } from "react";
import useNavigate from "@/hooks/navigate";

export default function AdaptiveModal({
  trigger,
  children,
  href,
  size = "medium",
  ...rest
}: AdaptiveModalProps) {
  const isDesktop = useMediaQuery("(min-width: 768px)");
  const isHotwire = isHotwireNative();
  const navigate = useNavigate();

  // If in Hotwire Native and href is provided, wrap trigger with anchor
  const renderTriggerForHotwire = () => {
    if (isHotwire && href && isValidElement(trigger)) {
      return cloneElement(trigger, {
        onClick: () => navigate(href),
      } as ButtonHTMLAttributes<HTMLButtonElement>);
    }
    return trigger;
  };

  // If in Hotwire Native with href, don't render modal/bottom sheet
  if (isHotwire && href) {
    return renderTriggerForHotwire();
  }

  // Desktop: render Modal
  if (isDesktop) {
    return (
      <Modal
        trigger={(triggerProps) => {
          if (!trigger) return null;
          if (isValidElement(trigger)) {
            return cloneElement(trigger, triggerProps);
          }
          return trigger;
        }}
        size={size}
        {...rest}
      >
        {children}
      </Modal>
    );
  }

  // Mobile: render BottomSheet
  return (
    <BottomSheet trigger={trigger} {...rest}>
      {children}
    </BottomSheet>
  );
}
