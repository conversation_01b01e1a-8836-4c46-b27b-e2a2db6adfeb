import type { ReactNode } from "react";
import type { ModalProps } from "../modal/types";
import type { BottomSheetProps } from "../bottom-sheet/types";

export type AdaptiveModalProps = {
  /**
   * The trigger element that opens the modal/bottom sheet
   */
  trigger?: ReactNode;

  /**
   * The content to display inside the modal/bottom sheet
   * Receives a dismiss function to close the modal/bottom sheet
   */
  children?: (api: { dismiss: () => void }) => ReactNode;

  /**
   * URL for Hotwire Native navigation
   * When provided and in Hotwire Native environment,
   * the trigger will navigate to this URL when clicked
   */
  href?: string;

  /**
   * Modal size (only applies to desktop modal)
   */
  size?: ModalProps["size"];

  /**
   * Whether the modal/bottom sheet can be dismissed by clicking outside or pressing escape
   */
  dismissible?: boolean;

  /**
   * Whether the modal/bottom sheet is open (controlled mode)
   */
  open?: boolean;

  /**
   * Callback when open state changes
   */
  onOpenChange?: (open: boolean) => void;
} & Omit<ModalProps, "trigger" | "children" | "size" | "isPersistent"> &
  Omit<BottomSheetProps, "trigger" | "children">;
