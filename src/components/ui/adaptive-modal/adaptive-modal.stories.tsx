import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import AdaptiveModal from "./adaptive-modal";
import type { AdaptiveModalProps } from "./types";
import Button from "../button/button";

const meta: Meta<AdaptiveModalProps> = {
  title: "Components/Adaptive Modal",
  component: AdaptiveModal,
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
  argTypes: {
    size: {
      control: { type: "select" },
      options: ["small", "medium", "large"],
    },
    dismissible: {
      control: { type: "boolean" },
    },
    href: {
      control: { type: "text" },
    },
  },
};

export default meta;
type Story = StoryObj<AdaptiveModalProps>;

// Basic adaptive modal with trigger button
export const Default: Story = {
  args: {
    trigger: <Button>Open Adaptive Modal</Button>,
    children: ({ dismiss }) => (
      <div className="space-y-4 p-4">
        <h2 className="text-heading3 text-black-80">Adaptive Modal</h2>
        <p className="text-body1 text-black-60">
          This modal adapts to the screen size:
        </p>
        <ul className="text-body2 text-black-50 space-y-1">
          <li>• Desktop (≥768px): Shows as a modal dialog</li>
          <li>• Mobile (&lt;768px): Shows as a bottom sheet</li>
          <li>
            • Hotwire Native: Uses anchor navigation when href is provided
          </li>
        </ul>
        <div className="bg-black-5 flex h-32 items-center justify-center rounded-lg">
          <span className="text-black-40">Content Area</span>
        </div>
        <Button onClick={dismiss}>Close Modal</Button>
      </div>
    ),
  },
};
