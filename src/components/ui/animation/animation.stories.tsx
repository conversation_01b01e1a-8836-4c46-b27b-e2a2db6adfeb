import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import Animation from "./animation";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Animation> = {
  title: "Components/Animation",
  component: Animation,
  tags: ["autodocs"],
  argTypes: {
    src: { control: "text" },
    loop: { control: "boolean" },
    type: {
      control: { type: "select" },
      options: ["lottie", "video", "gif"],
    },
    width: { control: "number" },
    height: { control: "number" },
    controls: { control: "boolean" },
    controlPosition: {
      control: { type: "select" },
      options: ["top-left", "top-right", "bottom-left", "bottom-right"],
    },
    playOnVisibility: { control: "boolean" },
    mediaFitType: {
      control: { type: "select" },
      options: ["cover", "contain"],
    },
    aspectRatio: { control: "number" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Lottie: Story = {
  args: {
    src: "https://assets3.lottiefiles.com/packages/lf20_UJNc2t.json",
    loop: true,
    type: "lottie",
    width: 300,
    height: 300,
    playOnVisibility: true,
  },
};

export const Video: Story = {
  args: {
    src: "http://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",
    loop: true,
    type: "video",
    controlPosition: "top-left",
    width: 300,
    height: 400,
    controls: true,
    playOnVisibility: true,
    mediaFitType: "cover",
  },
};

export const GIF: Story = {
  args: {
    src: "https://media.giphy.com/media/42LVIDips35RnSwKwH/giphy.gif?cid=790b7611vhtg8q2fkr5x1nyq9l7my8055xar2o0ek2ipkrv3&ep=v1_gifs_search&rid=giphy.gif&ct=g",
    loop: false,
    type: "gif",
    width: 300,
    height: 300,
  },
};
