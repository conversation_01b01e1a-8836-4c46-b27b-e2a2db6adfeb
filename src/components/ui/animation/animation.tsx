import clsx from "clsx";
import type { AnimationProps } from "./types";
import { useInView } from "react-intersection-observer";
import { useEffect, useRef, useState } from "react";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import type { DotLottie } from "@lottiefiles/dotlottie-react";

function MuteButton({
  muted,
  onToggle,
  ...rest
}: {
  muted: boolean;
  onToggle: () => void;
} & React.HTMLAttributes<SVGSVGElement>) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      name="mute"
      onClick={onToggle}
      aria-label="mute-button"
      role="button"
      tabIndex={0}
      {...rest}
    >
      {muted ? (
        <>
          <rect width="24" height="24" rx="4" fill="white" fillOpacity="0.3" />
          <path
            d="M13.434 5.41204C13.3207 5.35538 13.179 5.32705 13.0657 5.41204L7.54129 8.38658H4.39662C4.16998 8.38658 4 8.55655 4 8.78318V15.2139C4 15.4122 4.16998 15.6105 4.39662 15.6105H7.56962L13.0374 18.585C13.094 18.6133 13.1507 18.6417 13.2074 18.6417C13.264 18.6417 13.349 18.6133 13.4057 18.585C13.519 18.5283 13.604 18.3867 13.604 18.2451V5.75198C13.604 5.61034 13.5473 5.46869 13.434 5.41204ZM4.76492 9.15146H7.25799V14.8172H4.76492V9.15146ZM12.8391 17.5935L8.02291 14.9872V9.00981L12.8391 6.40355V17.5935Z"
            fill="currentColor"
            fillOpacity="0.8"
          />
          <path
            d="M18.1466 11.9558L19.8145 10.2879C19.978 10.1244 19.978 9.83009 19.8145 9.66658C19.651 9.50307 19.3566 9.50307 19.1931 9.66658L17.5252 11.3344L15.8573 9.66658C15.6938 9.50307 15.3994 9.50307 15.2359 9.66658C15.0724 9.83009 15.0724 10.1244 15.2359 10.2879L16.9038 11.9558L15.2359 13.6236C15.0724 13.7871 15.0724 14.0814 15.2359 14.2449C15.334 14.343 15.4321 14.3757 15.563 14.3757C15.6938 14.3757 15.7919 14.343 15.89 14.2449L17.5579 12.5771L19.2258 14.2449C19.3239 14.343 19.422 14.3757 19.5529 14.3757C19.651 14.3757 19.7818 14.343 19.8799 14.2449C20.0434 14.0814 20.0434 13.7871 19.8799 13.6236L18.1466 11.9558Z"
            fill="currentColor"
            fillOpacity="0.8"
          />
        </>
      ) : (
        <>
          <rect width="24" height="24" rx="4" fill="white" fillOpacity="0.3" />
          <path
            d="M13.4926 5.41204C13.3793 5.35538 13.2376 5.32705 13.1243 5.41204L7.59989 8.38658H4.45522C4.22858 8.38658 4.05859 8.55655 4.05859 8.78318V15.2139C4.05859 15.4122 4.22858 15.6105 4.45522 15.6105H7.62822L13.096 18.585C13.1526 18.6133 13.2093 18.6417 13.266 18.6417C13.3226 18.6417 13.4076 18.6133 13.4643 18.585C13.5776 18.5283 13.6626 18.3867 13.6626 18.2451V5.75198C13.6626 5.61034 13.6059 5.46869 13.4926 5.41204ZM4.82351 9.15146H7.31658V14.8172H4.82351V9.15146ZM12.8977 17.5935L8.0815 14.9872V9.00981L12.8977 6.40355V17.5935Z"
            fill="currentColor"
          />
          <path
            d="M18.3172 8.25498C18.2697 8.20758 18.2134 8.16998 18.1515 8.14433C18.0895 8.11867 18.0231 8.10547 17.956 8.10547C17.889 8.10547 17.8225 8.11867 17.7606 8.14433C17.6986 8.16998 17.6423 8.20758 17.5949 8.25498C17.5475 8.30239 17.5099 8.35866 17.4842 8.42059C17.4585 8.48253 17.4453 8.54891 17.4453 8.61595C17.4453 8.68298 17.4585 8.74936 17.4842 8.8113C17.5099 8.87323 17.5475 8.92951 17.5949 8.97691C18.4458 9.82939 18.9236 10.9844 18.9236 12.1886C18.9236 13.3927 18.4458 14.5478 17.5949 15.4002C17.4991 15.496 17.4453 15.6258 17.4453 15.7612C17.4453 15.8966 17.4991 16.0264 17.5949 16.1222C17.6907 16.2179 17.8206 16.2717 17.956 16.2717C18.0915 16.2717 18.2214 16.2179 18.3172 16.1222C19.3594 15.0781 19.9447 13.6634 19.9447 12.1886C19.9447 10.7137 19.3594 9.29906 18.3172 8.25498Z"
            fill="currentColor"
          />
          <path
            d="M16.9326 9.78616C16.8852 9.73859 16.8289 9.70082 16.7668 9.67501C16.7048 9.64921 16.6383 9.63586 16.5711 9.63574C16.5039 9.63562 16.4374 9.64873 16.3753 9.67432C16.3131 9.69991 16.2567 9.73748 16.2091 9.78488C16.1615 9.83229 16.1237 9.88859 16.0979 9.95059C16.0721 10.0126 16.0587 10.0791 16.0586 10.1462C16.0585 10.2134 16.0716 10.2799 16.0972 10.342C16.1228 10.4041 16.1604 10.4605 16.2078 10.5081C16.6533 10.954 16.9035 11.5585 16.9035 12.1887C16.9035 12.8189 16.6533 13.4233 16.2078 13.8692C16.1604 13.9168 16.1228 13.9732 16.0972 14.0353C16.0716 14.0974 16.0585 14.1639 16.0586 14.2311C16.0587 14.2983 16.0721 14.3647 16.0979 14.4267C16.1237 14.4887 16.1615 14.545 16.2091 14.5924C16.3052 14.6882 16.4354 14.7418 16.5711 14.7416C16.6383 14.7415 16.7048 14.7281 16.7668 14.7023C16.8289 14.6765 16.8852 14.6387 16.9326 14.5912C17.5693 13.9536 17.9269 13.0895 17.9269 12.1887C17.9269 11.2878 17.5693 10.4238 16.9326 9.78616Z"
            fill="currentColor"
          />
        </>
      )}
    </svg>
  );
}

export default function Animation({
  src,
  loop = false,
  width,
  height,
  type = "video",
  controlPosition = "top-left",
  controls = false,
  playOnVisibility = false,
  aspectRatio,
  mediaFitType,
}: AnimationProps) {
  const [ref, inView] = useInView({
    threshold: 1,
  });
  const videoRef = useRef<HTMLVideoElement>(null);
  const lottieRef = useRef<DotLottie | null>(null);
  const [muted, setMuted] = useState(true);

  useEffect(() => {
    if (playOnVisibility) {
      if (inView) {
        videoRef.current?.play();
        lottieRef.current?.play();
      } else {
        videoRef.current?.pause();
        lottieRef.current?.pause();
      }
    }
  }, [inView, playOnVisibility]);

  if (type === "video") {
    return (
      <div
        style={{ width, height, aspectRatio }}
        ref={ref}
        className="relative"
      >
        <MuteButton
          muted={muted}
          onToggle={() => setMuted(!muted)}
          className={clsx("absolute z-1 outline-none", {
            "bottom-0 left-0": controlPosition === "bottom-left",
            "right-0 bottom-0": controlPosition === "bottom-right",
            "top-0 left-0": controlPosition === "top-left",
            "top-0 right-0": controlPosition === "top-right",
          })}
        />
        <video
          ref={videoRef}
          src={src}
          loop={loop}
          controls={controls}
          className={clsx("z-1 block h-full w-full rounded-lg", {
            "object-contain": mediaFitType === "contain",
            "object-cover": mediaFitType === "cover",
          })}
          muted={muted}
          controlsList="nodownload nofullscreen noplaybackrate"
          disablePictureInPicture
          playsInline
        />
      </div>
    );
  }
  if (type === "lottie") {
    return (
      <div ref={ref} style={{ width, height, aspectRatio }}>
        <DotLottieReact
          dotLottieRefCallback={(dotLottie) => {
            lottieRef.current = dotLottie;
          }}
          src={src}
          autoplay={playOnVisibility}
          loop={loop}
          style={{ width, height, aspectRatio }}
        />
      </div>
    );
  }
  if (type === "gif") {
    return (
      <div style={{ width, height, aspectRatio }}>
        <img src={src} alt="gif" className="h-full w-full object-contain" />
      </div>
    );
  }
  return null;
}
