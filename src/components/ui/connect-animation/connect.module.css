.leftEntity {
  transform: translateX(-128px);
}

.rightEntity {
  transform: translateX(128px);
}

@keyframes left-to-right {
  0% {
    transform: translateX(0);
  }

  100% {
    transform: translateX(var(--translate-width));
  }
}

@keyframes slideRight {
  from {
    transform: translateX(-128px);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes slideLeft {
  from {
    transform: translateX(128px);
  }

  to {
    transform: translateX(0);
  }
}

@keyframes shrink {
  from {
    transform: scale(1);
  }

  to {
    transform: scale(0);
  }
}

@keyframes fadeAway {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.slideRightShrinkFadeAnimate {
  animation:
    slideRight 800ms 1s forwards,
    shrink 1s 1800ms forwards,
    fadeAway 1s 1800ms forwards;
}

.slideLeftShrinkFadeAnimate {
  animation:
    slideLeft 800ms 1s forwards,
    shrink 1s 1800ms forwards,
    fadeAway 1s 1800ms forwards;
}
