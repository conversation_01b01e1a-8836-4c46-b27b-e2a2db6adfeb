import type { Meta, StoryObj } from "@storybook/react";
import ConnectAnimation from "./connect-animation";

// Simple EntityLogo placeholder component for the story
function EntityLogo({
  url,
  size = "large",
  color = "#FFFFFF",
}: {
  url: string;
  size?: "large" | "medium" | "small";
  color?: string;
}) {
  const sizeMap = {
    large: { container: 56, image: 32 },
    medium: { container: 40, image: 22 },
    small: { container: 28, image: 16 },
  };

  const dimensions = sizeMap[size];

  return (
    <div
      style={{
        width: dimensions.container,
        height: dimensions.container,
        backgroundColor: color,
        borderRadius: size === "large" ? 12 : size === "medium" ? 8 : 4,
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        border: "1px solid rgba(0, 0, 0, 0.1)",
        filter: "drop-shadow(0 0 25px rgba(0, 0, 0, 0.1))",
      }}
    >
      <img
        src={url}
        alt=""
        style={{
          width: dimensions.image,
          height: dimensions.image,
          objectFit: "contain",
        }}
      />
    </div>
  );
}

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof ConnectAnimation> = {
  title: "Components/Connect Animation",
  component: ConnectAnimation,
  tags: ["autodocs"],
  parameters: {
    layout: "fullscreen",
  },
  argTypes: {
    status: {
      control: "select",
      options: ["connecting", "connected"],
    },
    onCompleted: {
      action: "completed",
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Regular: Story = {
  args: {
    status: "connecting",
  },
  render: (args) => (
    <div
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <ConnectAnimation
        status={args.status}
        onCompleted={args.onCompleted}
        firstEntity={
          <EntityLogo
            url="https://assets.stablemoney.in/bank-logos/Unity.png"
            size="large"
            color="#FFF6EE"
          />
        }
        secondEntity={
          <EntityLogo
            url="https://assets.stablemoney.in/bank-logos/Unity.png"
            size="large"
            color="#FFF6EE"
          />
        }
      >
        <p style={{ margin: 0 }}>
          Connecting you to
          <br />
          Suryoday SF Bank
        </p>
      </ConnectAnimation>
    </div>
  ),
};

export const Connected: Story = {
  args: {
    status: "connected",
  },
  render: (args) => (
    <div
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <ConnectAnimation
        status={args.status}
        onCompleted={args.onCompleted}
        firstEntity={
          <EntityLogo
            url="https://assets.stablemoney.in/bank-logos/Unity.png"
            size="large"
            color="#FFF6EE"
          />
        }
        secondEntity={
          <EntityLogo
            url="https://assets.stablemoney.in/bank-logos/HDFC.png"
            size="large"
            color="#E8F4FD"
          />
        }
      >
        <p style={{ margin: 0 }}>
          Successfully connected to
          <br />
          HDFC Bank
        </p>
      </ConnectAnimation>
    </div>
  ),
};

export const BankConnection: Story = {
  args: {
    status: "connecting",
  },
  render: (args) => (
    <div
      style={{
        height: "100vh",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <ConnectAnimation
        status={args.status}
        onCompleted={args.onCompleted}
        firstEntity={
          <EntityLogo
            url="https://assets.stablemoney.in/app/stable-money-logo.png"
            size="large"
            color="#FFFFFF"
          />
        }
        secondEntity={
          <EntityLogo
            url="https://assets.stablemoney.in/bank-logos/ICICI.png"
            size="large"
            color="#F0F7FF"
          />
        }
      >
        <p style={{ margin: 0 }}>
          Connecting your account with
          <br />
          ICICI Bank
        </p>
      </ConnectAnimation>
    </div>
  ),
};
