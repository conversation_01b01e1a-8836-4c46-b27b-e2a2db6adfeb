import { useEffect, useState } from "react";
import connectionBgAnimation from "@/assets/animations/connect-bg.json?url";
import connectedAnimation from "@/assets/animations/connected.json?url";
import type { ConnectAnimationProps } from "./types";
import clsx from "clsx";
import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import classes from "./connect.module.css";

export default function ConnectAnimation({
  status = "connecting",
  children,
  firstEntity,
  secondEntity,
  onCompleted,
  className,
  ...rest
}: ConnectAnimationProps) {
  const [connectStatus, setConnectStatus] = useState<
    "connecting" | "merging" | "merged" | "completed"
  >("connecting");

  // Effect to handle status changes
  useEffect(() => {
    if (status === "connected") {
      setConnectStatus("merging");
    }
    if (status === "connecting") {
      setConnectStatus("connecting");
    }
  }, [status]);

  // Effect to handle merging state
  useEffect(() => {
    if (connectStatus === "merging") {
      const timeout = setTimeout(() => {
        setConnectStatus("merged");
      }, 1800);
      return () => clearTimeout(timeout);
    }
  }, [connectStatus]);

  // Effect to handle merged state
  useEffect(() => {
    if (connectStatus === "merged") {
      const timeout = setTimeout(() => {
        setConnectStatus("completed");
        onCompleted?.();
      }, 500);
      return () => clearTimeout(timeout);
    }
  }, [connectStatus, onCompleted]);

  return (
    <div
      className={clsx(
        "flex flex-col items-center justify-center gap-8",
        className
      )}
      {...rest}
    >
      <div className="grid h-[344px] w-[344px] items-center justify-items-center">
        <div className="col-span-full row-span-full h-full w-full">
          <DotLottieReact
            src={connectionBgAnimation}
            autoplay
            loop
            className="h-full w-full"
          />
        </div>
        <div
          className={clsx(
            classes.leftEntity,
            "z-10 col-span-full row-span-full h-[56px] w-[56px]",
            {
              [classes.slideRightShrinkFadeAnimate]: [
                "merging",
                "merged",
                "completed",
              ].includes(connectStatus),
            }
          )}
        >
          {firstEntity}
        </div>
        <div
          className={clsx(
            classes.rightEntity,
            "z-20 col-span-full row-span-full h-[56px] w-[56px]",
            {
              [classes.slideLeftShrinkFadeAnimate]: [
                "merging",
                "merged",
                "completed",
              ].includes(connectStatus),
            }
          )}
        >
          {secondEntity}
        </div>
        {(connectStatus === "merged" || connectStatus === "completed") && (
          <div className="z-30 col-span-full row-span-full h-10 w-10">
            <DotLottieReact src={connectedAnimation} autoplay />
          </div>
        )}
      </div>
      <div className="text-heading4 text-black-60 mt-5 text-center">
        {children}
      </div>
    </div>
  );
}
