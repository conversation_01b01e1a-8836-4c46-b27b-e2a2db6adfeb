import clsx from "clsx";
import type { HTMLAttributes } from "react";

type PaginationProps = {
  totalSlides: number;
  activeIndex: number;
} & HTMLAttributes<HTMLDivElement>;

export default function Pagination({
  totalSlides,
  activeIndex,
  className,
}: PaginationProps) {
  return (
    <div className={clsx("flex justify-center gap-2", className)}>
      {Array.from({ length: totalSlides }).map((_, index) => (
        <div
          key={index}
          className={clsx(
            "h-[4px] rounded-full",
            index === activeIndex
              ? "w-6 bg-white md:w-12"
              : "w-2 bg-[#FFFFFF38]"
          )}
        />
      ))}
    </div>
  );
}
