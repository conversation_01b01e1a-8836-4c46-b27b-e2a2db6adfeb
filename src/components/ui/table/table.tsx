import clsx from "clsx";
import type { TableProps } from "./types";

export default function Table({
  horizontal = false,
  vertical = false,
  variant = "primary",
  className,
  children,
  ...rest
}: TableProps) {
  return (
    <div
      className={clsx("w-full overflow-x-auto", {
        "md:px-5": variant === "primary",
      })}
    >
      <table
        className={clsx("w-full border-collapse border-spacing-0", className, {
          // Horizontal borders between rows
          "[&_tbody_tr:not(:last-child)]:border-black-10 [&_tbody_tr:not(:last-child)]:border-b":
            horizontal,
          // Vertical borders between columns
          "[&_th:not(:last-child)]:border-black-10 [&_td:not(:last-child)]:border-black-10 [&_td:not(:last-child)]:border-r [&_th:not(:last-child)]:border-r":
            vertical,
          // Secondary variant styling
          "[&_thead]:bg-black-3": variant === "secondary",
        })}
        {...rest}
      >
        {children}
      </table>
    </div>
  );
}
