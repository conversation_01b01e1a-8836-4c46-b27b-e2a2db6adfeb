import type { <PERSON>a, StoryObj } from "@storybook/react";
import Table from "./table";
import TableHead from "./table-head";
import TableBody from "./table-body";
import TableRow from "./table-row";
import TableHeadCell from "./table-head-cell";
import TableCell from "./table-cell";
import type { SortDirection } from "./types";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Table> = {
  title: "Components/Table",
  component: Table,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    horizontal: { control: "boolean" },
    vertical: { control: "boolean" },
    variant: {
      control: { type: "select" },
      options: ["primary", "secondary"],
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    horizontal: true,
    vertical: false,
    variant: "primary",
  },
  render: (args) => (
    <Table
      horizontal={args.horizontal}
      vertical={args.vertical}
      variant={args.variant}
    >
      <TableHead>
        <TableRow>
          <TableHeadCell>Name</TableHeadCell>
          <TableHeadCell>Age</TableHeadCell>
          <TableHeadCell alignment="right">Balance</TableHeadCell>
        </TableRow>
      </TableHead>
      <TableBody>
        <TableRow>
          <TableCell>John Doe</TableCell>
          <TableCell>32</TableCell>
          <TableCell alignment="right">₹10,000</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Jane Smith</TableCell>
          <TableCell>28</TableCell>
          <TableCell alignment="right">₹15,000</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Bob Johnson</TableCell>
          <TableCell>45</TableCell>
          <TableCell alignment="right">₹8,500</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  ),
};

export const Secondary: Story = {
  args: {
    horizontal: true,
    vertical: false,
    variant: "secondary",
  },
  render: (args) => (
    <Table
      horizontal={args.horizontal}
      vertical={args.vertical}
      variant={args.variant}
    >
      <TableHead>
        <TableRow>
          <TableHeadCell>Product</TableHeadCell>
          <TableHeadCell alignment="center">Quantity</TableHeadCell>
          <TableHeadCell alignment="right">Price</TableHeadCell>
        </TableRow>
      </TableHead>
      <TableBody>
        <TableRow>
          <TableCell>Laptop</TableCell>
          <TableCell alignment="center">1</TableCell>
          <TableCell alignment="right">₹75,000</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Mouse</TableCell>
          <TableCell alignment="center">2</TableCell>
          <TableCell alignment="right">₹1,200</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Keyboard</TableCell>
          <TableCell alignment="center">1</TableCell>
          <TableCell alignment="right">₹2,500</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  ),
};

export const WithSortableColumns: Story = {
  args: {
    horizontal: true,
    vertical: false,
    variant: "primary",
  },
  render: (args) => (
    <Table
      horizontal={args.horizontal}
      vertical={args.vertical}
      variant={args.variant}
    >
      <TableHead>
        <TableRow>
          <TableHeadCell>Stock</TableHeadCell>
          <TableHeadCell
            sortDirection="asc"
            onSort={(direction: SortDirection) => {
              console.log("Sort price:", direction);
            }}
          >
            Price
          </TableHeadCell>
          <TableHeadCell
            alignment="right"
            onSort={(direction: SortDirection) => {
              console.log("Sort change:", direction);
            }}
          >
            Change
          </TableHeadCell>
        </TableRow>
      </TableHead>
      <TableBody>
        <TableRow>
          <TableCell>HDFC Bank</TableCell>
          <TableCell>₹1,650</TableCell>
          <TableCell alignment="right">****%</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Reliance</TableCell>
          <TableCell>₹2,450</TableCell>
          <TableCell alignment="right">****%</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>TCS</TableCell>
          <TableCell>₹3,200</TableCell>
          <TableCell alignment="right">-0.5%</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Infosys</TableCell>
          <TableCell>₹1,450</TableCell>
          <TableCell alignment="right">****%</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  ),
};

export const WithHTMLAttributes: Story = {
  args: {
    horizontal: true,
    vertical: true,
    variant: "primary",
  },
  render: (args) => (
    <Table
      horizontal={args.horizontal}
      vertical={args.vertical}
      variant={args.variant}
      style={{ maxWidth: "800px" }}
      id="financial-table"
    >
      <TableHead style={{ backgroundColor: "#f5f5f5" }}>
        <TableRow>
          <TableHeadCell style={{ width: "120px" }}>Quarter</TableHeadCell>
          <TableHeadCell alignment="right" style={{ width: "120px" }}>
            Revenue
          </TableHeadCell>
          <TableHeadCell alignment="right" style={{ width: "120px" }}>
            Expenses
          </TableHeadCell>
          <TableHeadCell alignment="right" style={{ width: "120px" }}>
            Profit
          </TableHeadCell>
        </TableRow>
      </TableHead>
      <TableBody>
        <TableRow className="bg-black-3">
          <TableCell style={{ fontWeight: 500 }}>Q1 2023</TableCell>
          <TableCell alignment="right">₹50,000</TableCell>
          <TableCell alignment="right">₹30,000</TableCell>
          <TableCell alignment="right">₹20,000</TableCell>
        </TableRow>
        <TableRow>
          <TableCell style={{ fontWeight: 500 }}>Q2 2023</TableCell>
          <TableCell alignment="right">₹65,000</TableCell>
          <TableCell alignment="right">₹35,000</TableCell>
          <TableCell alignment="right">₹30,000</TableCell>
        </TableRow>
        <TableRow>
          <TableCell style={{ fontWeight: 500 }}>Q3 2023</TableCell>
          <TableCell alignment="right">₹70,000</TableCell>
          <TableCell alignment="right">₹40,000</TableCell>
          <TableCell alignment="right">₹30,000</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  ),
};

export const VerticalBorders: Story = {
  args: {
    horizontal: false,
    vertical: true,
    variant: "primary",
  },
  render: (args) => (
    <Table
      horizontal={args.horizontal}
      vertical={args.vertical}
      variant={args.variant}
    >
      <TableHead>
        <TableRow>
          <TableHeadCell>Feature</TableHeadCell>
          <TableHeadCell alignment="center">Basic</TableHeadCell>
          <TableHeadCell alignment="center">Pro</TableHeadCell>
          <TableHeadCell alignment="center">Enterprise</TableHeadCell>
        </TableRow>
      </TableHead>
      <TableBody>
        <TableRow>
          <TableCell>Storage</TableCell>
          <TableCell alignment="center">10GB</TableCell>
          <TableCell alignment="center">100GB</TableCell>
          <TableCell alignment="center">Unlimited</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Users</TableCell>
          <TableCell alignment="center">1</TableCell>
          <TableCell alignment="center">10</TableCell>
          <TableCell alignment="center">Unlimited</TableCell>
        </TableRow>
        <TableRow>
          <TableCell>Support</TableCell>
          <TableCell alignment="center">Email</TableCell>
          <TableCell alignment="center">Email + Chat</TableCell>
          <TableCell alignment="center">24/7 Phone</TableCell>
        </TableRow>
      </TableBody>
    </Table>
  ),
};
