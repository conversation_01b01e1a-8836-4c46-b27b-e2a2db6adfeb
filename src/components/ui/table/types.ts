import type {
  ReactNode,
  HTMLAttributes,
  TableHTMLAttributes,
  TdHTMLAttributes,
  ThHTMLAttributes,
} from "react";

export type TableProps = {
  horizontal?: boolean;
  vertical?: boolean;
  variant?: "primary" | "secondary";
  children?: ReactNode;
} & TableHTMLAttributes<HTMLTableElement>;

export type TableHeadProps = {
  children?: ReactNode;
} & HTMLAttributes<HTMLTableSectionElement>;

export type TableBodyProps = {
  children?: ReactNode;
} & HTMLAttributes<HTMLTableSectionElement>;

export type TableRowProps = {
  children?: ReactNode;
} & HTMLAttributes<HTMLTableRowElement>;

export type SortDirection = "asc" | "desc";

export type TableHeadCellProps = {
  alignment?: "left" | "center" | "right";
  sortDirection?: SortDirection;
  onSort?: (direction: SortDirection) => void;
  children?: ReactNode;
} & ThHTMLAttributes<HTMLTableCellElement>;

export type TableCellProps = {
  alignment?: "left" | "center" | "right";
  children?: ReactNode;
} & TdHTMLAttributes<HTMLTableCellElement>;
