import clsx from "clsx";
import type { TableCellProps } from "./types";

export default function TableCell({
  alignment = "left",
  className,
  children,
  ...rest
}: TableCellProps) {
  return (
    <td
      className={clsx(
        {
          "text-left": alignment === "left",
          "text-right": alignment === "right",
          "text-center": alignment === "center",
        },
        className
      )}
      {...rest}
    >
      {children}
    </td>
  );
}
