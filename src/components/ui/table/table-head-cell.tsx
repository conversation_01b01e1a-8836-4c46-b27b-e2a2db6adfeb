import clsx from "clsx";
import type { TableHeadCellProps, SortDirection } from "./types";
import SortIcon from "@/assets/images/icons/sort.svg";
import SortedIcon from "@/assets/images/icons/sorted.svg";

export default function TableHeadCell({
  alignment = "left",
  sortDirection,
  onSort,
  className,
  children,
  ...rest
}: TableHeadCellProps) {
  const handleSortClick = () => {
    if (!onSort) return;
    const newDirection: SortDirection =
      sortDirection === "asc" ? "desc" : "asc";
    onSort(newDirection);
  };

  return (
    <th
      className={clsx(
        "text-black-50 text-body1 font-normal",
        {
          "text-left": alignment === "left",
          "text-right": alignment === "right",
          "text-center": alignment === "center",
        },
        className
      )}
      {...rest}
    >
      {onSort ? (
        <button
          className="flex w-full cursor-pointer items-center justify-between gap-1"
          onClick={handleSortClick}
          type="button"
        >
          <span className="block">{children}</span>
          {sortDirection ? (
            <img
              src={SortedIcon}
              alt="Sorted"
              className={clsx({
                "rotate-180": sortDirection === "desc",
              })}
            />
          ) : (
            <img src={SortIcon} alt="Sort" />
          )}
        </button>
      ) : (
        children
      )}
    </th>
  );
}
