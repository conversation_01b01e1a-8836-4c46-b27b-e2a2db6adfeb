import { useMachine, normalizeProps } from "@zag-js/react";
import * as zagSwitch from "@zag-js/switch";
import { useId } from "react";
import clsx from "clsx";
import type { SwitchProps } from "./types";

export default function Switch({
  checked,
  defaultChecked = false,
  density = "default",
  disabled = false,
  name,
  value,
  className,
  ...rest
}: SwitchProps) {
  const id = useId();
  const service = useMachine(zagSwitch.machine, {
    id,
    name,
    value,
    checked,
    defaultChecked,
    disabled,
  });
  const api = zagSwitch.connect(service, normalizeProps);

  // Size classes based on density
  const sizeClasses = {
    container: density === "compact" ? "w-[25px] h-[14px]" : "w-9 h-5", // 36px x 20px for default, 60% for compact
    thumb: density === "compact" ? "w-[9.8px] h-[9.8px]" : "w-3.5 h-3.5", // 14px for default, 60% for compact
    thumbTranslate:
      density === "compact" ? "translate-x-[8.4px]" : "translate-x-3.5", // 14px translate for default, 60% for compact
  };

  return (
    <label
      {...api.getRootProps()}
      className={clsx("relative inline-flex", className)}
      {...rest}
    >
      <input {...api.getHiddenInputProps()} />
      <span
        {...api.getControlProps()}
        className={clsx(
          "relative cursor-pointer rounded-full transition-all duration-400",
          sizeClasses.container,
          {
            "bg-black-20": !api.checked,
            "bg-green": api.checked,
            "cursor-not-allowed opacity-50": api.disabled,
          }
        )}
      >
        <span
          {...api.getThumbProps()}
          className={clsx(
            "absolute rounded-full bg-white transition-all duration-400",
            "top-1/2 -translate-y-1/2",
            sizeClasses.thumb,
            {
              "left-1": !api.checked,
              [`left-1 ${sizeClasses.thumbTranslate}`]: api.checked,
            }
          )}
        />
      </span>
    </label>
  );
}
