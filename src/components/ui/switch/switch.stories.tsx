import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import Switch from "./switch";
import <PERSON>ton from "../button/button";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Switch> = {
  title: "Components/Switch",
  component: Switch,
  tags: ["autodocs"],
  parameters: {
    layout: "padded",
  },
  argTypes: {
    checked: { control: "boolean" },
    defaultChecked: { control: "boolean" },
    disabled: { control: "boolean" },
    density: {
      control: { type: "select" },
      options: ["default", "compact"],
    },
    name: { control: "text" },
    value: { control: "text" },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Primary: Story = {
  args: {
    defaultChecked: true,
    density: "default",
  },
};

export const InForm: Story = {
  render: () => {
    const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
      e.preventDefault();
      const data = new FormData(e.currentTarget);
      const result: Record<string, string> = {};
      for (const [key, value] of data.entries()) {
        result[key] = value.toString();
      }
      alert("Form submitted with data: " + JSON.stringify(result));
    };

    return (
      <div className="space-y-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex items-center gap-2">
            <Switch
              name="notifications"
              value="enabled"
              defaultChecked={true}
              density="default"
            />
            <label className="text-body1">Enable notifications</label>
          </div>

          <div className="flex items-center gap-2">
            <Switch
              name="marketing"
              value="subscribed"
              defaultChecked={false}
              density="default"
            />
            <label className="text-body1">Subscribe to marketing emails</label>
          </div>

          <div className="flex items-center gap-2">
            <Switch
              name="analytics"
              value="allowed"
              defaultChecked={true}
              density="compact"
            />
            <label className="text-body1">
              Allow analytics tracking (compact)
            </label>
          </div>

          <Button type="submit">Submit</Button>
        </form>
      </div>
    );
  },
};
