import type { CheckBoxProps } from "./types";
import * as checkbox from "@zag-js/checkbox";
import { useMachine, normalizeProps } from "@zag-js/react";
import clsx from "clsx";
import { useId } from "react";

export default function Checkbox({
  children,
  onChange,
  className,
  ...props
}: CheckBoxProps) {
  const service = useMachine(checkbox.machine, {
    ...props,
    id: useId(),
  });
  const api = checkbox.connect(service, normalizeProps);

  return (
    <label
      className={clsx("flex items-start gap-2", className)}
      {...api.getRootProps()}
      onChange={onChange}
    >
      <input {...api.getHiddenInputProps()} />
      <div
        style={{
          backgroundImage: `url(${api.checked ? "https://assets.stablemoney.in/web-frontend/checkbox-select.webp" : "https://assets.stablemoney.in/web-frontend/checkbox-unselect.svg"})`,
        }}
        className="mt-0.5 size-4 flex-shrink-0 cursor-pointer bg-contain bg-center bg-no-repeat"
        {...api.getControlProps()}
      />
      <span
        className={clsx("text-body1 text-black-50 cursor-pointer", className)}
      >
        {children}
      </span>
    </label>
  );
}
