import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import Checkbox from "./checkbox";
import Button from "../button/button";

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<typeof Checkbox> = {
  title: "Components/Checkbox",
  component: Checkbox,
  tags: ["autodocs"],
  argTypes: {
    defaultChecked: {
      control: { type: "boolean" },
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

export const WithText: Story = {
  args: {
    defaultChecked: true,
  },
  render: (args) => (
    <Checkbox name="toc" defaultChecked={args.defaultChecked}>
      I agree to the{" "}
      <a
        href="https://stablemoney.in"
        target="_blank"
        rel="noopener noreferrer"
      >
        terms and conditions
      </a>
    </Checkbox>
  ),
};

export const InForm: Story = {
  render: () => (
    <form
      onSubmit={(e) => {
        e.preventDefault();
        const data = new FormData(e.target as HTMLFormElement);
        alert("Form submitted with checkbox value: " + data.get("toc"));
      }}
    >
      <Checkbox name="toc">I agree to the terms and conditions</Checkbox>
      <Button type="submit" className="mt-4">
        Submit
      </Button>
    </form>
  ),
};

export const Reversed: Story = {
  args: {
    defaultChecked: true,
  },
  render: (args) => (
    <Checkbox
      name="toc"
      defaultChecked={args.defaultChecked}
      className="flex-row-reverse justify-between"
    >
      I agree to the{" "}
      <a
        href="https://stablemoney.in"
        target="_blank"
        rel="noopener noreferrer"
      >
        terms and conditions
      </a>
    </Checkbox>
  ),
};
