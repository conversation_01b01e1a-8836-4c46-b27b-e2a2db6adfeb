import CountdownTimer from "./countdown-timer";

export default {
  title: "UI/CountdownTimer",
  component: CountdownTimer,
  parameters: {
    layout: "centered",
  },
};

export const Default = () => (
  <CountdownTimer duration={120} onFinish={() => alert("Timer finished!")} />
);

export const AutoStart = () => (
  <CountdownTimer
    duration={60}
    autoStart={true}
    onFinish={() => console.log("Auto-started timer finished")}
  />
);

export const WithControls = () => (
  <CountdownTimer
    duration={30}
    showControls={true}
    onFinish={() => alert("Timer with controls finished!")}
  />
);

export const NoLabel = () => <CountdownTimer duration={75} showLabel={false} />;

export const CustomLabel = () => (
  <CountdownTimer
    duration={300}
    label="Offer expires in"
    onFinish={() => alert("Offer expired!")}
  />
);
