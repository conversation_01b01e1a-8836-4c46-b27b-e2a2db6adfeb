import { useCountdownTimer } from "@/hooks/countdown-timer";
import { useEffect } from "react";

export interface CountdownTimerProps {
  duration: number;
  onFinish?: () => void;
  autoStart?: boolean;
  className?: string;
  showLabel?: boolean;
  label?: string;
  showControls?: boolean;
  onStart?: () => void;
  onPause?: () => void;
}

export default function CountdownTimer({
  duration,
  onFinish,
  autoStart = false,
  className = "",
  showLabel = true,
  label = "Time remaining",
  showControls = false,
  onStart,
  onPause,
}: CountdownTimerProps) {
  const timer = useCountdownTimer(duration, onFinish, autoStart);

  // Handle control callbacks
  useEffect(() => {
    if (timer.isRunning && onStart) {
      onStart();
    }
  }, [timer.isRunning, onStart]);

  const handleStart = () => {
    timer.start();
    onStart?.();
  };

  const handlePause = () => {
    timer.pause();
    onPause?.();
  };

  const handleReset = () => {
    timer.reset();
  };

  return (
    <div className={`flex flex-col items-center space-y-2 ${className}`}>
      {!timer.isFinished && timer.formattedTime}
      {showLabel && (
        <p className="text-sm text-gray-500">{timer.isFinished ? "" : label}</p>
      )}

      {showControls && (
        <div className="flex space-x-2">
          {!timer.isRunning && !timer.isFinished && (
            <button
              onClick={handleStart}
              className="rounded bg-purple-600 px-3 py-1 text-sm text-white hover:bg-purple-700"
            >
              Start
            </button>
          )}

          {timer.isRunning && (
            <button
              onClick={handlePause}
              className="rounded bg-gray-600 px-3 py-1 text-sm text-white hover:bg-gray-700"
            >
              Pause
            </button>
          )}

          <button
            onClick={handleReset}
            className="rounded bg-gray-400 px-3 py-1 text-sm text-white hover:bg-gray-500"
          >
            Reset
          </button>
        </div>
      )}

      {timer.isFinished && <></>}
    </div>
  );
}
