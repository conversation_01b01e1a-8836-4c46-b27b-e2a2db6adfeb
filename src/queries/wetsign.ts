import { getAuthToken, refreshAuthToken } from "@/clients/auth";
import { request } from "@/clients/broking-api";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  RaaDuration,
} from "@/clients/gen/broking/Kyc_pb";
import { create, toBinary } from "@bufbuild/protobuf";

/**
 * Upload wet signature
 * @param formData The form data with the signature image
 * @param raaDuration The RAA duration
 * @returns The response data
 */
export async function uploadWetSignature(
  formData: FormData,
  raaDuration: RaaDuration
) {
  const response = await fetch(
    `${import.meta.env.VITE_BROKING_BASE_URL}/v1/document/upload`,
    {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${await getAuthToken()}`,
      },
      body: formData,
    }
  );
  if (response.status === 401) {
    await refreshAuthToken();
    return uploadWetSignature(formData, raaDuration);
  }
  const result = await response.json();
  if (result.document_id) {
    return saveWetSignature(result.document_id, raaDuration);
  }

  throw new Error("Wet Signature Upload API call failed");
}

/**
 * Save wet signature
 * @param documentId The document ID
 * @param raaDuration The RAA duration
 * @returns The response data
 */
export async function saveWetSignature(
  documentId: string,
  raaDuration: RaaDuration
) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.WET_SIGNATURE,
    result: {
      case: "wetSignatureRequest",
      value: {
        raaDuration: raaDuration,
        isPep: false,
        isIndianCitizen: true,
        creditReportConsent: true,
        documentId: documentId,
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "wetSignatureResponse") {
    return responseData;
  }

  throw new Error("Wet Signature save API call failed");
}
