import { getAuthToken, refreshAuthToken } from "@/clients/auth";
import { request } from "@/clients/broking-api";
import { GetDematProviderDetailsResponseSchema } from "@/clients/gen/broking/Demat_pb";
import {
  KycFetchNameByPanResponseSchema,
  RaaDuration,
} from "@/clients/gen/broking/Kyc_pb";
import {
  WorkflowName,
  StepName,
  InitiateDigioKycRequestSchema,
  InitiateDigioKycResponseSchema,
  SelfieStatusCheckRequestSchema,
  SelfieStatusCheckResponseSchema,
  GenerateEsignTokenRequestSchema,
  GenerateEsignTokenResponseSchema,
  EsignStatusRequestSchema,
  EsignStatusResponseSchema,
  WetSignatureRequestSchema,
  WetSignatureResponseSchema,
  ContinueWorkflowResponseSchema,
  UserProfileUpdateRequestSchema,
  UserProfileUpdateResponseSchema,
  CheckDigioKycStatusRequestSchema,
  CheckDigioKycStatusResponseSchema,
  SelfieStartRequestSchema,
  SelfieInitiationResponseSchema,
  PanVerificationResponseSchema,
  PanVerificationRequestSchema,
  AadhaarPanMatchResponseSchema,
  AadhaarPanMatchRequestSchema,
  RetryStepResponseSchema,
  RetryStepRequestSchema,
  type NomineeDetails,
  NomineeResponseSchema,
  NomineeRequestSchema,
  DematRequestSchema,
  DematResponseSchema,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { getWorkflowNameStringRepresentation } from "@/utils/workflow-routes";
import { create, toBinary } from "@bufbuild/protobuf";
import { queryOptions } from "@tanstack/react-query";

/**
 * Get workflow status
 */
export async function getWorkflowStatus(workflowName: WorkflowName) {
  return request({
    url: `/v1/workflow/${getWorkflowNameStringRepresentation(workflowName)}/continue`,
    method: "POST",
    responseSchema: ContinueWorkflowResponseSchema,
  });
}

/**
 * Get name from PAN number
 * @param pan The PAN number
 * @returns The full name associated with the PAN
 */
export async function getPanName(pan: string) {
  return request({
    url: `/v1/pan-name`,
    method: "GET",
    responseSchema: KycFetchNameByPanResponseSchema,
    params: { pan },
  });
}

export async function submitPanDetails(
  panData: {
    name: string;
    panNo: string;
    dob: string;
  },
  workflowName: WorkflowName
) {
  const data = create(PanVerificationRequestSchema, {
    fullName: panData.name,
    dob: panData.dob,
    panNumber: panData.panNo,
    workflowName: workflowName,
  });

  const responseData = await request({
    url: `/v2/workflow-step/pan-kyc`,
    method: "POST",
    responseSchema: PanVerificationResponseSchema,
    data: toBinary(PanVerificationRequestSchema, data),
  });

  if (responseData) {
    return responseData;
  }

  throw new Error("Error in Pan CVL");
}

/**
 * Get Digio KYC credential
 */
export async function getDigioCredential(workflowName: WorkflowName) {
  const data = create(InitiateDigioKycRequestSchema, {
    workflow: workflowName,
  });

  return await request({
    url: "/v2/workflow-step/initiate-digio-kyc",
    method: "POST",
    data: toBinary(InitiateDigioKycRequestSchema, data),
    responseSchema: InitiateDigioKycResponseSchema,
  });
}

/**
 * Get Digio KYC status
 */
export async function getDigioStatus(workflowName: WorkflowName) {
  const data = create(CheckDigioKycStatusRequestSchema, {
    workflow: workflowName,
  });

  return request({
    url: "/v2/workflow-step/check-digio-kyc-status",
    method: "POST",
    data: toBinary(CheckDigioKycStatusRequestSchema, data),
    responseSchema: CheckDigioKycStatusResponseSchema,
  });
}

/**
 * Get HyperVerge credential for selfie
 */
export async function getHypervergeCredential(
  workflowName: WorkflowName,
  transactionId: string
) {
  const data = create(SelfieStartRequestSchema, {
    workflow: workflowName,
    transactionId: transactionId,
  });

  const response = await request({
    url: "/v2/workflow-step/start-selfie",
    method: "POST",
    data: toBinary(SelfieStartRequestSchema, data),
    responseSchema: SelfieInitiationResponseSchema,
  });

  return response;
}

/**
 * Get HyperVerge selfie status
 */
export async function getHypervergeSelfieStatus(
  workflowName: WorkflowName,
  transactionId: string
) {
  const data = create(SelfieStatusCheckRequestSchema, {
    workflow: workflowName,
    transactionId,
  });

  return request({
    url: "/v2/workflow-step/check-selfie-status",
    method: "POST",
    data: toBinary(SelfieStatusCheckRequestSchema, data),
    responseSchema: SelfieStatusCheckResponseSchema,
  });
}

/**
 * Submit basic details
 */
export async function submitBasicDetails(
  formData: {
    employmentType: string;
    incomeRange: string;
    tradingExperience: string;
    maritalStatus: string;
    fatherName: string;
    motherName: string;
  },
  workflowName: WorkflowName
) {
  const data = create(UserProfileUpdateRequestSchema, {
    workflow: workflowName,
    employmentType: +formData.employmentType,
    incomeRange: +formData.incomeRange,
    tradingExperience: +formData.tradingExperience,
    maritalStatus: +formData.maritalStatus,
    fatherName: formData.fatherName,
    motherName: formData.motherName,
  });

  const responseData = await request({
    url: "/v2/workflow-step/update-user-profile",
    method: "POST",
    data: toBinary(UserProfileUpdateRequestSchema, data),
    responseSchema: UserProfileUpdateResponseSchema,
  });
  if (responseData) return responseData;
  throw new Error("Error submitting basic details");
}

/**
 * Get eSign credential
 */
export async function getEsignCredential(
  workflowName: WorkflowName,
  stepName: StepName.ESIGN | StepName.ESIGN_WITH_DEMAT
) {
  const data = create(GenerateEsignTokenRequestSchema, {
    stepName,
    workflow: workflowName,
  });

  return request({
    url: "/v2/workflow-step/generate-esign-token",
    method: "POST",
    data: toBinary(GenerateEsignTokenRequestSchema, data),
    responseSchema: GenerateEsignTokenResponseSchema,
  });
}

/**
 * Get eSign status
 */
export async function getEsignStatus(
  workflowName: WorkflowName,
  stepName: StepName.ESIGN | StepName.ESIGN_WITH_DEMAT,
  id: string
) {
  const data = create(EsignStatusRequestSchema, {
    stepName,
    workflow: workflowName,
    id,
  });

  return request({
    url: "/v2/workflow-step/check-esign-status",
    method: "POST",
    data: toBinary(EsignStatusRequestSchema, data),
    responseSchema: EsignStatusResponseSchema,
  });
}

/**
 * Upload wet signature
 */
export async function uploadWetSignature(
  formData: FormData,
  raaDuration: RaaDuration,
  workflowName: WorkflowName
) {
  const response = await fetch(
    `${import.meta.env.VITE_BROKING_BASE_URL}/v1/document/upload`,
    {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${await getAuthToken()}`,
      },
      body: formData,
    }
  );
  if (response.status === 401) {
    await refreshAuthToken();
    return uploadWetSignature(formData, raaDuration, workflowName);
  }
  const result = await response.json();
  if (result.document_id) {
    return saveWetSignature(result.document_id, workflowName);
  }

  throw new Error("Wet signature upload failed");
}

/**
 * Save wet signature
 * @param documentId The document ID
 * @param raaDuration The RAA duration
 * @returns The response data
 */
export async function saveWetSignature(
  documentId: string,
  workflowName: WorkflowName
) {
  const data = create(WetSignatureRequestSchema, {
    workflow: workflowName,
    isPep: false,
    isIndianCitizen: true,
    creditReportConsent: true,
    documentId: documentId,
    isEdis: true,
    isSmsConsent: true,
    isUnlawful: true,
  });

  return request({
    url: `/v2/workflow-step/wet-signature`,
    method: "POST",
    responseSchema: WetSignatureResponseSchema,
    data: toBinary(WetSignatureRequestSchema, data),
  });
}

/**
 * Aadhaar Pan Match signature
 * @param documentId The document ID
 * @param raaDuration The RAA duration
 * @returns The response data
 */
export async function getAadharMatchStatus(workflowName: WorkflowName) {
  const data = create(AadhaarPanMatchRequestSchema, {
    workflow: workflowName,
  });

  return request({
    url: `/v2/workflow-step/aadhaar-pan-match`,
    method: "POST",
    responseSchema: AadhaarPanMatchResponseSchema,
    data: toBinary(AadhaarPanMatchRequestSchema, data),
  });
}

export function getAadharMatchStatusQueryOptions(workflowName: WorkflowName) {
  return queryOptions({
    queryKey: ["aadhar-pan-match", workflowName],
    queryFn: () => getAadharMatchStatus(workflowName),
  });
}

export async function retryPan(workflowName: WorkflowName) {
  const data = create(RetryStepRequestSchema, {
    workflow: workflowName,
    stepName: StepName.PAN_KYC,
  });
  const responseData = await request({
    url: `/v2/workflow-step/retry-step`,
    method: "POST",
    responseSchema: RetryStepResponseSchema,
    data: toBinary(RetryStepRequestSchema, data),
  });

  return responseData;
}

export async function skipNominee(workflowName: WorkflowName) {
  const payloadData = create(NomineeRequestSchema, {
    optOut: true,
    nomineeDetails: [],
    workflow: workflowName,
  });
  return request({
    url: `/v2/workflow-step/nominee`,
    method: "POST",
    responseSchema: NomineeResponseSchema,
    data: toBinary(NomineeRequestSchema, payloadData),
  });
}

export async function saveNomineeDetails(
  payloadData: NomineeDetails[],
  workflowName: WorkflowName
) {
  return request({
    url: `/v2/workflow-step/nominee`,
    method: "POST",
    responseSchema: NomineeResponseSchema,
    data: toBinary(
      NomineeRequestSchema,
      create(NomineeRequestSchema, {
        optOut: false,
        nomineeDetails: payloadData,
        workflow: workflowName,
      })
    ),
  });
}

export async function getDematProviderDetails(dematId: string) {
  const responseData = await request({
    url: "/v1/demat/get-provider-details",
    params: { "partial-demat-account-number": dematId },
    method: "GET",
    responseSchema: GetDematProviderDetailsResponseSchema,
  });

  if (responseData.providerId) {
    return responseData;
  } else {
    return null;
  }
}

export async function addDematAccount(
  workflowName: WorkflowName,
  createAccount: boolean,
  dematAccountNumber: string
) {
  const data = create(DematRequestSchema, {
    createAccount,
    dematAccountNumber,
    workflow: workflowName,
  });

  return request({
    url: `/v2/workflow-step/demat`,
    method: "POST",
    responseSchema: DematResponseSchema,
    data: toBinary(DematRequestSchema, data),
  });
}
