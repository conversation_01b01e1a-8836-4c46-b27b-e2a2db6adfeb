import { request } from "@/clients/broking-api";
import { CollectionResponseSchema } from "@/clients/gen/broking/Collection_pb";
import {
  AllBondsRequestSchema,
  AllBondsResponseSchema,
  BondAmountCalculatorResponseSchema,
  BondDetailsResponseSchema,
} from "@/clients/gen/broking/BondDetails_pb";
import { getPage } from "@/clients/gen/personalization_api";
import { QueryClient, queryOptions } from "@tanstack/react-query";
import {
  BondsNetworthSummaryResponseSchema,
  Filled15GFormListSchema,
} from "@/clients/gen/broking/Order_pb";
import { create, toBinary } from "@bufbuild/protobuf";

export function personalizationPageQuery(
  path: string,
  params: Record<string, unknown> = {}
) {
  return queryOptions({
    queryKey: ["personalization", "page", path, params],
    queryFn: () => getPage({ path, params: JSON.stringify(params) }),
  });
}

export function prefetchPersonalizationPage(
  queryClient: QueryClient,
  path: string,
  params: Record<string, unknown> = {}
) {
  return queryClient.prefetchQuery(personalizationPageQuery(path, params));
}

export function getCollection(
  collectionName: string,
  params?: Record<string, string>
) {
  return request({
    url: `/v1/collection/${collectionName}`,
    method: "GET",
    responseSchema: CollectionResponseSchema,
    params: params,
  });
}

export function createCollectionQueryOptions(
  collectionName: string,
  params?: Record<string, string>
) {
  return queryOptions({
    queryKey: ["collection", collectionName],
    queryFn: () => getCollection(collectionName, params),
  });
}

export async function getBondDetails(bondId: string) {
  const response = await request({
    url: `/v1/bond-details/${bondId}`,
    method: "GET",
    responseSchema: BondDetailsResponseSchema,
  });
  if (!response.bondDetails) {
    throw new Error("Bond Details could not be fetched");
  }
  return response.bondDetails;
}

export function createBondDetailsQueryOptions(bondId: string) {
  return queryOptions({
    queryKey: ["bond", bondId],
    queryFn: () => getBondDetails(bondId),
  });
}

export function prefetchBondDetails(queryClient: QueryClient, bondId: string) {
  return queryClient.prefetchQuery({
    queryKey: ["bond", bondId],
    queryFn: () => getBondDetails(bondId),
  });
}

export function getBondDetailsQueryOptions(bondId: string) {
  return queryOptions({
    queryKey: ["bond", bondId],
    queryFn: () => getBondDetails(bondId),
  });
}

export function getBondsNetworth() {
  return request({
    url: "/v1/order/bonds-net-worth",
    method: "GET",
    responseSchema: BondsNetworthSummaryResponseSchema,
  });
}

export function getBondsNetworthQueryOptions() {
  return queryOptions({
    queryKey: ["bonds", "networth"],
    queryFn: getBondsNetworth,
  });
}

export async function getBondCalculation(bondId: string, quantity: number) {
  const response = await request({
    url: `/v1/bond-amount-calculator`,
    params: { id: bondId, quantity: quantity.toString() },
    method: "GET",
    responseSchema: BondAmountCalculatorResponseSchema,
  });
  return response;
}

export function getBondCalculationQueryOptions(
  bondId: string,
  quantity: number
) {
  return {
    queryKey: ["bond", bondId, "calculation", quantity],
    queryFn: () => getBondCalculation(bondId, quantity),
  };
}

export function getAllBonds(page = 0) {
  return request({
    url: "/v1/bonds",
    method: "POST",
    data: toBinary(
      AllBondsRequestSchema,
      create(AllBondsRequestSchema, {
        pageNumber: page,
        pageSize: 25,
        secretKey: "7944767327472-aak1uq5ydJVM4IoTnhFaBV8C",
      })
    ),
    responseSchema: AllBondsResponseSchema,
  });
}

export function getForm15GData() {
  return request({
    url: `/v1/forms/15g`,
    method: "GET",
    responseSchema: Filled15GFormListSchema,
  });
}

export function getForm15GDataQueryOptions() {
  return queryOptions({
    queryKey: ["form15GData"],
    queryFn: getForm15GData,
  });
}
