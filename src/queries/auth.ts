import { request } from "@/clients/identity-api";
import { create, toBinary } from "@bufbuild/protobuf";
import {
  AuthType,
  InitiateAuthRequestSchema,
  InitiateAuthResponseSchema,
  RespondToAuthChallengeRequestSchema,
  RespondToAuthChallengeResponseSchema,
  InitiateVerifyRequestSchema,
  InitiateVerifyResponseSchema,
  RespondToVerifyChallengeRequestSchema,
  RespondToVerifyChallengeResponseSchema,
} from "@/clients/gen/platform/public/models/identity/Auth_pb";
import { DeviceType } from "@/clients/gen/platform/public/models/identity/Device_pb";
import { nanoid } from "nanoid";
import { getOnboardingStatus } from "@/queries/identity";
import { getAuthToken, setAuthToken, setRefreshToken } from "@/clients/auth";
import { queryClient } from "./client";
import { queryOptions } from "@tanstack/react-query";

/**
 * Initiates mobile number verification
 * @param mobileNumber The mobile number to verify
 * @returns The challenge and userId for OTP verification
 */
export async function initiateAuth(
  mobileNumber: string,
  encryptedMobile: string,
  aesKey: string
) {
  const response = await request({
    url: "/v3/auth/initiate",
    method: "POST",
    data: toBinary(
      InitiateAuthRequestSchema,
      create(InitiateAuthRequestSchema, {
        authType: AuthType.MOBILE_OTP,
        result: {
          case: "mobileLoginRequest",
          value: {
            mobile: mobileNumber,
            countryCode: "91",
            encryptedMobile,
            encryptionKey: aesKey,
          },
        },
        userDevice: {
          id: nanoid(),
          deviceType: DeviceType.WEB,
          osVersion: "UNKNOWN",
          model: "UNKNOWN",
          appVersion: "1.0.0",
          lastActiveTime: new Date().toISOString(),
          notificationToken: "",
          aaId: "",
        },
      })
    ),
    responseSchema: InitiateAuthResponseSchema,
  });

  if (response.result.case === "otpChallenge") {
    return {
      challenge: response.result.value,
      userId: response.userId,
    };
  }

  throw new Error(`Unexpected response: ${response.result.case}`);
}

/**
 * Verifies OTP and authenticates the user
 * @param challengeId The challenge ID from initiateAuth
 * @param userId The user ID from initiateAuth
 * @param answer The OTP entered by the user
 * @returns The authentication result with token and refresh token and next step if any
 * @throws Error if authentication fails
 */
export async function authenticate(
  challengeId: string,
  userId: string,
  answer: string
) {
  const response = await request({
    url: "/v1/auth/authenticate",
    method: "POST",
    data: toBinary(
      RespondToAuthChallengeRequestSchema,
      create(RespondToAuthChallengeRequestSchema, {
        challengeId,
        userId,
        answer,
      })
    ),
    responseSchema: RespondToAuthChallengeResponseSchema,
  });

  if (!response.authenticationResult) {
    throw new Error("Could not authenticate the user");
  }

  await setAuthToken(response.authenticationResult.token);
  await setRefreshToken(response.authenticationResult.refreshToken);
  queryClient.invalidateQueries({ queryKey: ["auth-token"] });

  // Check alpha onboarding status
  const alphaStatus = await getOnboardingStatus();

  return {
    authResult: response.authenticationResult,
    alphaStatus,
  };
}

/**
 * Initiates email verification
 * @param email The email to verify
 * @returns The challenge for OTP verification
 */
export async function initiateEmailVerification(email: string) {
  const response = await request({
    url: "/v1/auth/initiate-verification",
    method: "POST",
    data: toBinary(
      InitiateVerifyRequestSchema,
      create(InitiateVerifyRequestSchema, {
        authType: AuthType.EMAIL_OTP,
        result: { case: "email", value: email },
      })
    ),
    responseSchema: InitiateVerifyResponseSchema,
  });

  if (response.result.case === "otpChallenge") {
    return { challenge: response.result.value };
  }

  throw new Error(`Unexpected response: ${response.result.case}`);
}

/**
 * Verifies email OTP
 * @param challengeId The challenge ID from initiateEmailVerification
 * @param answer The OTP entered by the user
 * @returns The verification response
 */
export async function verifyEmail(challengeId: string, answer: string) {
  const response = await request({
    url: "/v1/auth/verify",
    method: "POST",
    data: toBinary(
      RespondToVerifyChallengeRequestSchema,
      create(RespondToVerifyChallengeRequestSchema, {
        challengeId,
        answer,
      })
    ),
    responseSchema: RespondToVerifyChallengeResponseSchema,
  });

  // Check alpha onboarding status after verification
  const alphaStatus = await getOnboardingStatus();

  return {
    response,
    alphaStatus,
  };
}

export function authQueryOptions() {
  return queryOptions({
    queryKey: ["auth-token"],
    queryFn: () => getAuthToken().then((token) => token || null),
  });
}
