import { request } from "@/clients/broking-api";
import type { AddressProto } from "@/clients/gen/broking/Common_pb";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  PanKycStep,
} from "@/clients/gen/broking/Kyc_pb";
import { GetCurrentAddressResponseSchema } from "@/clients/gen/broking/Profile_pb";
import { create, toBinary } from "@bufbuild/protobuf";
import { queryOptions } from "@tanstack/react-query";

/**
 * Get the current onboarding status
 * @returns The onboarding status with nextStep and kycStatus
 */
export async function getOnboardingStatus() {
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "GET",
    responseSchema: OnboardingResponseSchema,
  });

  return {
    nextStep: responseData.nextStep,
    kycStatus: responseData.kycStatus,
    lifetimeStatus: responseData.lifetimeStatus,
  };
}

/**
 * Get name from PAN number
 * @param pan The PAN number
 * @returns The full name associated with the PAN
 */
export async function getPanName(pan: string) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.PAN_KYC,
    result: {
      case: "panKycRequest",
      value: {
        step: PanKycStep.NAME_FETCH,
        result: { case: "kycFetchNameByPanRequest", value: { pan } },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "panKycResponse") {
    if (responseData.result.value.result.case === "kycFetchNameByPanResponse") {
      return responseData.result.value.result.value;
    }
  }

  throw new Error("Error in Pan Name Fetch");
}

export async function submitPanDetails(panData: {
  name: string;
  panNo: string;
  dob: string;
}) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.PAN_KYC,
    result: {
      case: "panKycRequest",
      value: {
        step: PanKycStep.PAN_STATUS,
        result: {
          case: "kycValidateNameAndGetKycStatusRequest",
          value: {
            dob: panData.dob,
            fullName: panData.name,
            panNumber: panData.panNo,
          },
        },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "panKycResponse") {
    if (
      responseData.result.value.result.case ===
      "kycValidateNameAndGetPanStatusResponse"
    ) {
      return {
        panKycData: responseData.result.value.result.value,
        nextStep: responseData.nextStep,
        kycStatus: responseData.kycStatus,
      };
    }
  }
  throw new Error("Error in Pan CVL");
}

export async function getAddressDetails() {
  const responseData = await request({
    url: "/v1/user/current-address",
    method: "GET",
    responseSchema: GetCurrentAddressResponseSchema,
  });

  if (responseData.currentAddress) {
    return responseData.currentAddress;
  } else {
    return null;
  }
}

export function getAddressDetailsQueryOptions() {
  return queryOptions({
    queryKey: ["address-details"],
    queryFn: getAddressDetails,
  });
}

export async function saveAddress(
  address: Omit<AddressProto, "$typeName">,
  isSameAsPermanentAddress: boolean
) {
  const payload = create(OnboardingRequestSchema, {
    stepName: StepName.CURRENT_ADDRESS,
    result: {
      case: "addCurrentAddressRequest",
      value: {
        isSameAsPermanentAddress,
        addressProto: address,
      },
    },
  });
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, payload),
  });

  if (responseData.result.case === "addCurrentAddressResponse") {
    return responseData;
  }
  throw new Error("Failed to save address");
}

export async function getAadharMatchStatus() {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.AADHAAR_PAN_MATCH,
    result: {
      case: "emptyKycRequest",
      value: {},
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "aadhaarPanMatchResponse") {
    return {
      aadhaarPanMatchResponse: responseData.result.value,
      nextStep: responseData.nextStep,
      kycStatus: responseData.kycStatus,
    };
  }
  throw new Error("Error in Aadhar Pan");
}

export function getAadharMatchStatusQueryOptions() {
  return queryOptions({
    queryKey: ["aadhar-pan-match"],
    queryFn: getAadharMatchStatus,
  });
}

export async function retryPan() {
  const responseData = await request({
    url: `/v1/onboarding/retry-step`,
    method: "POST",
    params: { step: "PAN_KYC" },
    responseSchema: OnboardingResponseSchema,
  });

  return { nextStep: responseData.nextStep, kycStatus: responseData.kycStatus };
}

export async function getCvlPullStatus() {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.CVL_PULL,
    result: {
      case: "emptyKycRequest",
      value: {},
    },
  });
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "cvlPullResponse") {
    if (responseData.result.value.status) return responseData;
  }
  throw new Error("Failed to pull cvl");
}

export async function getCvlPushStatus() {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.CVL_PUSH,
    result: {
      case: "emptyKycRequest",
      value: {},
    },
  });
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "cvlPushResponse") {
    return responseData;
  }
  throw new Error("Failed to push cvl");
}

export async function getCvlPartialFetchStatus() {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.CVL_PARTIAL_FETCH,
    result: {
      case: "emptyKycRequest",
      value: {},
    },
  });
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "cvlPartialFetchResponse") {
    return responseData;
  }
  throw new Error("Failed to get cvl partial fetch");
}
