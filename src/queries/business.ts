import { request } from "@/clients/business-api";
import {
  BusinessUnit,
  GetFaqsRequestSchema,
  GetFaqsResponseSchema,
} from "@/clients/gen/platform/public/models/business/Faq_pb";
import { create, toBinary } from "@bufbuild/protobuf";
import { queryOptions } from "@tanstack/react-query";

export async function getFAQs({
  namespace,
  identifier,
}: {
  namespace: string;
  identifier?: string;
}) {
  return request({
    method: "POST",
    url: "/v1/faq",
    data: toBinary(
      GetFaqsRequestSchema,
      create(GetFaqsRequestSchema, {
        namespace,
        identifier,
        businessUnit: BusinessUnit.BROKING,
      })
    ),
    responseSchema: GetFaqsResponseSchema,
  });
}

export function getFAQsQueryOptions({
  namespace,
  identifier,
}: {
  namespace: string;
  identifier?: string;
}) {
  return queryOptions({
    queryKey: ["faq", namespace, identifier],
    queryFn: () => getFAQs({ namespace, identifier }),
  });
}
