import { UpdateNameRequestSchema } from "@/clients/gen/broking/Profile_pb";
import { CampaignResponseSchema } from "@/clients/gen/platform/public/models/identity/Campaign_pb";
import { ConfigDataResponseSchema } from "@/clients/gen/platform/public/models/identity/Common_pb";
import { OnboardingStateSchema } from "@/clients/gen/platform/public/models/identity/Onboarding_pb";
import { UpdateNameResponseSchema } from "@/clients/gen/platform/public/models/identity/Profile_pb";
import { request } from "@/clients/identity-api";
import { create, toBinary } from "@bufbuild/protobuf";

export async function getOnboardingStatus() {
  return request({
    method: "GET",
    url: `/v1/onboarding/state`,
    params: { module: "APP_ONBOARDING" },
    responseSchema: OnboardingStateSchema,
  });
}

export async function getCampaign(campaignName: string) {
  const responseData = await request({
    url: "/v1/campaign",
    method: "GET",
    params: { campaignType: campaignName },
    responseSchema: CampaignResponseSchema,
  });
  if (responseData.metadata.case === "bondsReferralCampaignMetadata") {
    return responseData.metadata.value;
  }
  throw new Error("Campaign not found");
}

export async function getConfig() {
  return request({
    url: "/v1/config/list",
    method: "POST",
    responseSchema: ConfigDataResponseSchema,
  });
}

export async function postName({
  firstName,
  lastName,
}: {
  firstName: string;
  lastName: string;
}) {
  return request({
    method: "PUT",
    url: "/v1/user/name",
    data: toBinary(
      UpdateNameRequestSchema,
      create(UpdateNameRequestSchema, {
        firstName,
        lastName,
      })
    ),
    responseSchema: UpdateNameResponseSchema,
  });
}
