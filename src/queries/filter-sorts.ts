import { request } from "@/clients/broking-api";
import {
  FilterSortQueryResponseSchema,
  FilterSortQueryRequestSchema,
  type FilterSortQueryRequest,
  type FilterSortConfigResponse,
  type FilterSortQueryResponse,
  FilterSortConfigResponseSchema,
} from "@/clients/gen/broking/FilterSearch_pb";
import { toBinary } from "@bufbuild/protobuf";
import { QueryClient } from "@tanstack/react-query";

export async function getConfig(): Promise<FilterSortConfigResponse> {
  const response = await request({
    url: `/v1/filter-search/config`,
    method: "POST",
    responseSchema: FilterSortConfigResponseSchema,
  });
  if (!response) {
    throw new Error("Bond Filter Config could not be fetched");
  }
  return response;
}

export function prefetchFilterConfig(queryClient: QueryClient) {
  return queryClient.prefetchQuery({
    queryKey: ["filter-config"],
    queryFn: () => getConfig(),
  });
}

export async function getFilterResults(
  filterQueryRequest: FilterSortQueryRequest
): Promise<FilterSortQueryResponse> {
  const response = await request({
    url: `/v1/filter-search/query`,
    method: "POST",
    data: toBinary(FilterSortQueryRequestSchema, filterQueryRequest),
    responseSchema: FilterSortQueryResponseSchema,
  });
  return response;
}
