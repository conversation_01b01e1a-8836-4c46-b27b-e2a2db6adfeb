import { request } from "@/clients/broking-api";
import {
  UserProfileResponseSchema,
  type UserProfileResponse,
} from "@/clients/gen/broking/Profile_pb";
import { NseStatusResponseSchema } from "@/clients/gen/broking/Common_pb";
import { queryOptions } from "@tanstack/react-query";

export async function getProfile() {
  const response = await request({
    url: `/v1/user/profile`,
    method: "GET",
    responseSchema: UserProfileResponseSchema,
  });
  if (!response.profileData || !response.data) {
    throw new Error("Profile data not found");
  }
  return response;
}

export function getProfileQueryOptions() {
  return queryOptions({
    queryKey: ["profile"],
    queryFn: getProfile,
  });
}

export function getNameFromProfile(response: UserProfileResponse) {
  const name =
    response.profileData?.incomeTaxDepartmentName ||
    response.profileData?.kraName ||
    response.data?.name ||
    response.data?.firstName + " " + response.data?.lastName;
  if (!name) {
    console.warn("Name not found in profile");
  }
  return name;
}

export function getNseStatus() {
  return request({
    url: `/v1/user/nse-status`,
    method: "GET",
    responseSchema: NseStatusResponseSchema,
  });
}
