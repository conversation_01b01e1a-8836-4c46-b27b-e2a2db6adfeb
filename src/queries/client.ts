import { UnauthenticatedException } from "@/exceptions/unauthenticated";
import { QueryClient } from "@tanstack/react-query";
import { createSyncStoragePersister } from "@tanstack/query-sync-storage-persister";
import { HttpCallException } from "@/exceptions/http-call-exception";
import { getErrorMessage } from "@/utils/errors";
import { toaster } from "@/components/ui/toast/store";

export const persister = createSyncStoragePersister({
  storage: window.localStorage,
});

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry(failureCount, error) {
        if (error instanceof UnauthenticatedException) {
          return false;
        }
        if (
          error instanceof HttpCallException &&
          [400, 401, 403, 404, 422, 429].includes(error.response.status)
        ) {
          return false;
        }
        return failureCount < 3;
      },
      retryDelay: 1000,
      throwOnError(error) {
        return error instanceof UnauthenticatedException;
      },
    },
    mutations: {
      onError: async (error) => {
        // Show error toast for all mutations
        const message = await getErrorMessage(error);
        toaster.create({
          description: message,
          type: "error",
          duration: 3000,
        });
      },
    },
  },
});
