import { request } from "@/clients/broking-api.js";
import {
  BankStatusResponseSchema,
  BankVerificationType,
  IfscListSchema,
  RpdStatusRequestSchema,
  RpdStatusResponseSchema,
} from "@/clients/gen/broking/BankAccountVerification_pb";
import { create, toBinary } from "@bufbuild/protobuf";
import { getOnboardingStatus } from "./onboarding.js";
import {
  OnboardingRequestSchema,
  OnboardingResponseSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import { getNameFromProfile, getProfile } from "./profile.js";
import { queryOptions } from "@tanstack/react-query";
import { uuid } from "@/utils/strings.js";
import { getOrCreateSessionItem } from "@/utils/session-storage.js";

export async function getBankVerificationStatus() {
  const responseData = await request({
    url: `/v1/bank/status`,
    method: "GET",
    responseSchema: BankStatusResponseSchema,
  });

  if (responseData) {
    const isBankStepCompleted =
      responseData.pdStatus === "completed" ||
      responseData.rpdStatus === "completed";
    const onboardingData = await getOnboardingStatus();
    return { isBankStepCompleted, nextStep: onboardingData.nextStep };
  }
  throw new Error("Bank verification status Api call failed");
}

export async function getRpdUpiLinks() {
  const transactionId = getOrCreateSessionItem("rpd_transaction_id", uuid, 10);

  const data = create(OnboardingRequestSchema, {
    stepName: StepName.BANK_ACCOUNT,
    result: {
      case: "verifyBankRequest",
      value: {
        type: BankVerificationType.REVERSE_PENNY_DROP,
        transactionId: transactionId,
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "verifyBankResponse") {
    if (responseData.result.value.result.case === "initiateRpdResponse") {
      return {
        responseData,
        rpdLinkData: responseData.result.value.result.value,
        transactionId,
      };
    }
  }
  return {
    responseData,
    transactionId,
  };
}

export function getRpdQueryOptions() {
  return queryOptions({
    queryKey: ["bank-rpd-links"],
    queryFn: getRpdUpiLinks,
    refetchInterval: 60000,
  });
}

export async function getIfscDetails(ifscCode: string) {
  try {
    const responseData = await request({
      url: `/v1/bank/ifsc`,
      params: { ifsc: ifscCode.toUpperCase() },
      method: "GET",
      responseSchema: IfscListSchema,
    });
    return responseData;
  } catch {
    return null;
  }
}

export async function submitPennyDropRequest(
  beneficiaryAccountNo: string,
  ifsc: string
) {
  const profile = await getProfile();
  const ifscDetails = await getIfscDetails(ifsc.trim());
  if (!ifscDetails) {
    throw new Error("IFSC details could not be found");
  }
  const requestData = {
    stepName: StepName.BANK_ACCOUNT,
    result: {
      case: "verifyBankRequest" as const,
      value: {
        type: BankVerificationType.PENNY_DROP,
        result: {
          case: "bankAccount" as const,
          value: {
            beneficiaryAccountNo: beneficiaryAccountNo.trim(),
            ifscId: ifscDetails.id,
            beneficiaryName: getNameFromProfile(profile),
          },
        },
      },
    },
  };
  const data = create(OnboardingRequestSchema, requestData);

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (
    responseData.result.case === "verifyBankResponse" &&
    responseData.result.value.result.case === "initiatePdResponse" &&
    responseData.result.value.result.value.verified
  ) {
    return responseData;
  }
  throw new Error("Penny Drop Api call failed");
}

export const maxRpdFetchCount = 40;

export async function getRpdStatus(refId: string) {
  const data = create(RpdStatusRequestSchema, { refId });

  const responseData = await request({
    url: `/v1/bank/rpd-status`,
    method: "POST",
    responseSchema: RpdStatusResponseSchema,
    data: toBinary(RpdStatusRequestSchema, data),
  });

  if (responseData.verified) {
    const { nextStep, kycStatus } = await getOnboardingStatus();
    return {
      verified: responseData.verified,
      nextStep,
      kycStatus,
    };
  }
  return {
    verified: responseData.verified,
  };
}
