import { request } from "@/clients/broking-api.js";
import {
  BankVerificationStatusRequestSchema,
  BankVerificationStatusResponseSchema,
  WorkflowName,
} from "@/clients/gen/broking/WorkflowStep_pb.js";
import { create, toBinary } from "@bufbuild/protobuf";
import {
  InitiateBankVerificationResponseSchema,
  InitiateBankVerificationRequestSchema,
} from "@/clients/gen/broking/WorkflowStep_pb.js";
import { getNameFromProfile, getProfile } from "./profile.js";
import { uuid } from "@/utils/strings.js";
import { getOrCreateSessionItem } from "@/utils/session-storage.js";
import {
  BankVerificationType,
  IfscListSchema,
} from "@/clients/gen/broking/BankAccountVerification_pb.js";

export async function getBankVerificationStatus() {
  const responseData = await request({
    url: `/v2/workflow-step/bank-verification-status`,
    method: "GET",
    responseSchema: BankVerificationStatusResponseSchema,
  });

  if (responseData) {
    const isBankStepCompleted = responseData.verified;
    return { isBankStepCompleted, nextStep: responseData.nextStep };
  }
  throw new Error("Bank verification status Api call failed");
}

export async function getRpdUpiLinks(workflowName: WorkflowName) {
  const transactionId = getOrCreateSessionItem("rpd_transaction_id", uuid, 10);

  const data = create(InitiateBankVerificationRequestSchema, {
    type: BankVerificationType.REVERSE_PENNY_DROP,
    transactionId: transactionId,
    workflow: workflowName,
  });

  return request({
    url: `/v2/workflow-step/initiate-bank-verification`,
    method: "POST",
    responseSchema: InitiateBankVerificationResponseSchema,
    data: toBinary(InitiateBankVerificationRequestSchema, data),
  });
}

export async function getIfscDetails(ifscCode: string) {
  return request({
    url: `/v1/bank/ifsc`,
    params: { ifsc: ifscCode.toUpperCase() },
    method: "GET",
    responseSchema: IfscListSchema,
  });
}

export async function submitPennyDropRequest(
  beneficiaryAccountNo: string,
  ifsc: string,
  workflowName: WorkflowName
) {
  const profile = await getProfile();
  const ifscDetails = await getIfscDetails(ifsc.trim());
  if (!ifscDetails) {
    throw new Error("IFSC details could not be found");
  }

  const data = create(InitiateBankVerificationRequestSchema, {
    type: BankVerificationType.PENNY_DROP,
    result: {
      case: "bankAccount" as const,
      value: {
        beneficiaryAccountNo: beneficiaryAccountNo.trim(),
        ifscId: ifscDetails.id,
        beneficiaryName: getNameFromProfile(profile),
      },
    },
    workflow: workflowName,
  });

  const responseData = await request({
    url: `/v2/workflow-step/initiate-bank-verification`,
    method: "POST",
    responseSchema: InitiateBankVerificationResponseSchema,
    data: toBinary(InitiateBankVerificationRequestSchema, data),
  });

  if (
    responseData.result.case === "initiatePdResponse" &&
    responseData.result.value.verified
  ) {
    return responseData;
  }
  throw new Error("Bank verification was not successful, please retry.");
}

export const maxRpdFetchCount = 40;

export async function getRpdStatus(refId: string, workflow: WorkflowName) {
  const data = create(BankVerificationStatusRequestSchema, { refId, workflow });

  return request({
    url: `/v2/workflow-step/bank-verification-status`,
    method: "POST",
    responseSchema: BankVerificationStatusResponseSchema,
    data: toBinary(BankVerificationStatusRequestSchema, data),
  });
}
