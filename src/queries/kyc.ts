import { request } from "@/clients/broking-api";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  KycStep,
} from "@/clients/gen/broking/Kyc_pb";
import { create, toBinary } from "@bufbuild/protobuf";
import { queryOptions } from "@tanstack/react-query";

/**
 * Get Digio credentials for KYC verification
 * @returns The Digio credentials
 */
export async function getDigioCredential() {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.KYC,
    result: {
      case: "kycRequest",
      value: {
        step: KycStep.GENERATE_TOKEN,
        result: {
          case: "generateTokenRequest",
          value: {},
        },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "kycResponse") {
    if (responseData.result.value.result.case === "generateTokenResponse") {
      return responseData.result.value.result.value;
    }
  }

  throw new Error("Error in Digio");
}

export function getDigioCredentialQueryOptions() {
  return queryOptions({
    queryKey: ["digio-credential"],
    queryFn: getDigioCredential,
  });
}

/**
 * Get Digio status
 * @param id The Digio document ID
 * @returns The Digio status
 */
export async function getDigioStatus(id: string) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.KYC,
    result: {
      case: "kycRequest",
      value: {
        step: KycStep.GET_STATUS,
        result: {
          case: "statusRequest",
          value: { id },
        },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (
    responseData.result.case === "kycResponse" &&
    responseData.result.value.result.case === "statusResponse"
  ) {
    const statusResponse = responseData.result.value.result.value;
    const isApproved =
      statusResponse.kycStatus === "approved" ||
      statusResponse.kycStatus === "approval_pending";
    return {
      isApproved,
      nextStep: responseData.nextStep,
      kycStatus: responseData.kycStatus,
    };
  }

  return {
    nextStep: responseData.nextStep,
    isRetry: true,
    kycStatus: responseData.kycStatus,
  };
}

/**
 * Create a query for Digio status
 */
export function createDigioStatusQuery(id: string) {
  return {
    queryKey: ["digio", "status", id],
    queryFn: () => getDigioStatus(id),
  };
}
