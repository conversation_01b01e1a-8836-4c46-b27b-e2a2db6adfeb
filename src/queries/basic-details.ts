import { request } from "@/clients/broking-api";
import {
  ConfigDataResponseSchema,
  type UpdateProfileRequest,
} from "@/clients/gen/broking/Common_pb";
import {
  OnboardingRequestSchema,
  OnboardingResponseSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import type { NomineeDetails } from "@/clients/gen/broking/Nominee_pb";
import { create, toBinary } from "@bufbuild/protobuf";

export type QuestionOption = {
  label: string;
  value: number;
};

export type QuestionListItem = {
  title: string;
  subtitle: string;
  options: QuestionOption[];
  screenName: string;
  clickEvent: string;
  id: number;
  fieldName: string;
};

export async function getQuestionaireList(): Promise<QuestionListItem[]> {
  const responseData = await request({
    url: "/v1/config/list",
    method: "POST",
    responseSchema: ConfigDataResponseSchema,
  });

  if (responseData.data) {
    const { questionaires } = JSON.parse(responseData.data[0].configValue);
    return questionaires.map((question: QuestionListItem) => ({
      ...question,
      fieldName: getFieldNameFromTitle(question.title),
    }));
  }
  return [];
}

export function getQuestionnaireListQueryOptions() {
  return {
    queryKey: ["questionnaire", "list"],
    queryFn: () => getQuestionaireList(),
  };
}

export async function saveUserDetails(
  payload: Omit<UpdateProfileRequest, "$typeName">
) {
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(
      OnboardingRequestSchema,
      create(OnboardingRequestSchema, {
        stepName: StepName.USER_PROFILE,
        result: {
          case: "updateProfileRequest",
          value: payload,
        },
      })
    ),
  });

  if (responseData.result.case === "updateProfileResponse") {
    return responseData;
  }
  throw new Error("Error saving user details");
}

export async function skipNominee() {
  const payloadData = create(OnboardingRequestSchema, {
    stepName: StepName.NOMINEE,
    result: {
      case: "addNomineeRequest",
      value: { nomineeDetails: [], optOut: true },
    },
  });
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, payloadData),
  });
  if (responseData.result.case === "addNomineeResponse") {
    return responseData;
  }
  throw new Error("Error skipping nominee");
}

export async function saveNomineeDetails(
  payloadData: Omit<NomineeDetails, "$typeName">
) {
  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(
      OnboardingRequestSchema,
      create(OnboardingRequestSchema, {
        stepName: StepName.NOMINEE,
        result: {
          case: "addNomineeRequest",
          value: {
            nomineeDetails: [payloadData],
            optOut: false,
          },
        },
      })
    ),
  });
  if (responseData.result.case === "addNomineeResponse") {
    return responseData;
  }
  throw new Error("Error saving nominee details");
}

/**
 * Helper function to determine field name from question title
 */
function getFieldNameFromTitle(title: string): string {
  if (title.toLowerCase().includes("employment type")) {
    return "employmentType";
  } else if (
    title.toLowerCase().includes("salary range") ||
    title.toLowerCase().includes("income range")
  ) {
    return "incomeRange";
  } else if (title.toLowerCase().includes("trading")) {
    return "tradingExperience";
  } else {
    return "maritalStatus";
  }
}
