import { request } from "@/clients/broking-api";
import { OrdersResponseSchema } from "@/clients/gen/broking/Order_pb";
import { queryOptions } from "@tanstack/react-query";

export function getInvestments() {
  return request({
    url: `/v1/order/my-orders`,
    method: "GET",
    responseSchema: OrdersResponseSchema,
  });
}

export function getInvestmentsQueryOptions() {
  return queryOptions({
    queryKey: ["investments"],
    queryFn: getInvestments,
  });
}
