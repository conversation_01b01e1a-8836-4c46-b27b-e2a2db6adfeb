import { request } from "@/clients/broking-api";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  EsignStep,
} from "@/clients/gen/broking/Kyc_pb";
import { create, toBinary } from "@bufbuild/protobuf";
import { queryOptions } from "@tanstack/react-query";

/**
 * Initiate esign process
 * @returns The esign token data
 */
export async function initiateEsign() {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.ESIGN,
    result: {
      case: "esignRequest",
      value: {
        step: EsignStep.GENERATE_TOKEN,
        result: { case: "generateTokenRequest", value: {} },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "esignResponse") {
    if (responseData.result.value.result.case === "generateTokenResponse") {
      return responseData.result.value.result.value;
    }
  }

  throw new Error("Esign initiate API call failed");
}

export function initiateEsignQueryOptions() {
  return queryOptions({
    queryKey: ["esign-initiate"],
    queryFn: initiateEsign,
    retry: false,
  });
}

/**
 * Get esign status
 * @param id The document ID
 * @returns The esign status
 */
export async function getEsignStatus(id: string) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.ESIGN,
    result: {
      case: "esignRequest",
      value: {
        step: EsignStep.STATUS,
        result: { case: "statusRequest", value: { id } },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "esignResponse") {
    if (responseData.result.value.result.case === "statusResponse") {
      return {
        nextStep: responseData.nextStep,
        kycStatus: responseData.kycStatus,
        esignStatus: responseData.result.value.result.value.kycStatus,
        isApproved:
          responseData.result.value.result.value.kycStatus === "approved" ||
          responseData.result.value.result.value.kycStatus === "signed",
      };
    }
  }

  throw new Error("Esign getKYCStatus API call failed");
}
