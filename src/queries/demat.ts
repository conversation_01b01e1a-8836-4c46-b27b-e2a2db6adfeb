import { request } from "@/clients/broking-api";
import { GetDematProviderDetailsResponseSchema } from "@/clients/gen/broking/Demat_pb";
import {
  OnboardingRequestSchema,
  StepName,
  OnboardingResponseSchema,
} from "@/clients/gen/broking/Kyc_pb";
import { toBinary, create } from "@bufbuild/protobuf";

export async function getDematProviderDetails(dematId: string) {
  const responseData = await request({
    url: "/v1/demat/get-provider-details",
    params: { "partial-demat-account-number": dematId },
    method: "GET",
    responseSchema: GetDematProviderDetailsResponseSchema,
  });

  if (responseData.providerId) {
    return responseData;
  } else {
    return null;
  }
}

export async function addDematAccount(dematAccountNumber: string) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.DEMAT_ACCOUNT,
    result: {
      case: "addDematRequest",
      value: {
        dematAccountNumber: dematAccountNumber,
        dematCmlDocumentId: "",
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (
    responseData.result.case === "addDematResponse" &&
    responseData.result.value.isVerified
  ) {
    return responseData;
  }

  throw new Error("Demat account could not be added");
}
