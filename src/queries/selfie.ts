import { request } from "@/clients/broking-api";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  SelfieKycStep,
} from "@/clients/gen/broking/Kyc_pb";
import { create, toBinary } from "@bufbuild/protobuf";

/**
 * Get Hyperverge credentials for selfie verification
 * @returns The Hyperverge credentials
 */
export async function getHypervergeCredential(transactionId: string) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.SELFIE,
    result: {
      case: "selfieRequest",
      value: {
        step: SelfieKycStep.START_SELFIE_STEP,
        result: {
          case: "startSelfieStepRequest",
          value: {
            transactionId,
          },
        },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (responseData.result.case === "selfieResponse") {
    if (responseData.result.value.result.case === "startSelfieStepResponse") {
      return {
        ...responseData.result.value.result.value,
        nextStep: responseData.nextStep,
        kycStatus: responseData.kycStatus,
      };
    }
  }

  throw new Error("Error in Hyperverge");
}

/**
 * Get Hyperverge selfie status
 * @param transactionId The transaction ID
 * @param status The status
 * @returns The selfie status
 */
export async function getHypervergeSelfieStatus(
  transactionId: string,
  status: string
) {
  const data = create(OnboardingRequestSchema, {
    stepName: StepName.SELFIE,
    result: {
      case: "selfieRequest",
      value: {
        step: SelfieKycStep.SET_STATUS,
        result: {
          case: "setSelfieStatusRequest",
          value: { transactionId, status },
        },
      },
    },
  });

  const responseData = await request({
    url: `/v1/onboarding`,
    method: "POST",
    responseSchema: OnboardingResponseSchema,
    data: toBinary(OnboardingRequestSchema, data),
  });

  if (
    responseData.result.case === "selfieResponse" &&
    responseData.result.value.result.case === "setSelfieStatusResponse"
  ) {
    const success = responseData.result.value.result.value.success;
    return {
      success,
      nextStep: responseData.nextStep,
      kycStatus: responseData.kycStatus,
    };
  }

  return {
    nextStep: responseData.nextStep,
    isRetry: true,
    kycStatus: responseData.kycStatus,
  };
}
