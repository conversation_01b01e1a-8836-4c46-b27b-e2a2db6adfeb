import { request } from "@/clients/broking-api";
import { create, toBinary } from "@bufbuild/protobuf";
import { getOnboardingStatus as getAlphaOnboardingStatus } from "@/queries/identity";
import { getOnboardingStatus } from "@/queries/onboarding";
import {
  getPathForNextAlphaStep,
  getPathForStep,
} from "@/utils/onboarding-routes";
// Note: redirect functionality will need to be handled differently in React Router
import {
  PaymentStatusResponseSchema,
  type PlaceOrderRequestItem,
  PlaceOrderRequestItemSchema,
  PlaceOrderResponseV2Schema,
  type ProcessPaymentRequest,
  ProcessPaymentRequestSchema,
  ProcessPaymentResponseSchema,
} from "@/clients/gen/broking/Order_pb";
import {
  CartAction,
  CartDetailsSchema,
  type UpdateCartRequest,
  UpdateCartRequestSchema,
  UpdateCartResponseSchema,
} from "@/clients/gen/broking/Cart_pb";
import { UserLifetimeStatusResponse_KycStatus } from "@/clients/gen/broking/Common_pb";

export function processPayment(
  payloadData: Omit<ProcessPaymentRequest, "$typeName">
) {
  return request({
    url: `/v1/payment/process-payment`,
    method: "POST",
    responseSchema: ProcessPaymentResponseSchema,
    data: toBinary(
      ProcessPaymentRequestSchema,
      create(ProcessPaymentRequestSchema, payloadData)
    ),
  });
}

export function getPaymentSessionIdQueryOptions(orderId: string) {
  return {
    queryKey: ["payment-session-id", orderId],
    queryFn: () => processPayment({ orderId }),
  };
}

export function getPaymentStatus(orderId: string) {
  return request({
    url: `/v1/payment/status`,
    method: "GET",
    params: { order_id: orderId },
    responseSchema: PaymentStatusResponseSchema,
  });
}

export function getOrderDetails(orderId: string) {
  return request({
    url: `/v1/order/order-details/${orderId}`,
    method: "GET",
    responseSchema: PlaceOrderResponseV2Schema,
  });
}

export function buyNow(payloadData: Omit<PlaceOrderRequestItem, "$typeName">) {
  return request({
    url: `/v1/cart/add-checkout`,
    method: "POST",
    data: toBinary(
      PlaceOrderRequestItemSchema,
      create(PlaceOrderRequestItemSchema, payloadData)
    ),
    responseSchema: PlaceOrderResponseV2Schema,
  });
}

export function updateCart(payloadData: Omit<UpdateCartRequest, "$typeName">) {
  return request({
    url: `/v1/cart/update`,
    method: "POST",
    data: toBinary(
      UpdateCartRequestSchema,
      create(UpdateCartRequestSchema, payloadData)
    ),
    responseSchema: UpdateCartResponseSchema,
  });
}

export function getCart() {
  return request({
    url: `/v1/cart/details`,
    method: "GET",
    responseSchema: CartDetailsSchema,
  });
}

export function checkoutCart() {
  return request({
    url: `/v1/cart/checkout`,
    method: "POST",
    responseSchema: PlaceOrderResponseV2Schema,
  });
}

export function getOrderStatusQueryOptions(orderId: string) {
  return {
    queryKey: ["order", orderId],
    queryFn: () =>
      Promise.all([getOrderDetails(orderId), getPaymentStatus(orderId)]),
  };
}

export function getOrderMutationQueryOptions() {
  return {
    mutationKey: ["new-order"],
    async mutationFn(payloadData: Omit<PlaceOrderRequestItem, "$typeName">) {
      const alphaOnboardingStatus = await getAlphaOnboardingStatus();
      if (alphaOnboardingStatus.next) {
        return getPathForNextAlphaStep(alphaOnboardingStatus.next);
      }
      const onboardingData = await getOnboardingStatus();
      const currentCart = await getCart();
      if (currentCart.cartItems.length > 0) {
        await updateCart({ action: CartAction.RESET });
      }
      if (
        onboardingData.kycStatus !==
        UserLifetimeStatusResponse_KycStatus.COMPLETED
      ) {
        await updateCart({
          action: CartAction.ADD,
          bondDetailId: payloadData.bondDetailId,
          units: payloadData.quantity,
        });
        return getPathForStep(
          onboardingData.nextStep,
          onboardingData.kycStatus
        );
      }
      const order = await buyNow(payloadData);
      return `/checkout/${order.orderId}/pay`;
    },
  };
}
