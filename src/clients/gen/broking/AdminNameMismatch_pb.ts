// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file AdminNameMismatch.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file AdminNameMismatch.proto.
 */
export const file_AdminNameMismatch: GenFile = /*@__PURE__*/
  fileDesc("ChdBZG1pbk5hbWVNaXNtYXRjaC5wcm90bxIbY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nIjkKIUFkbWluVmVyaWZ5QWFkaGFhclBhbk5hbWVNaXNtYXRjaBIUCgxwaG9uZV9udW1iZXIYASABKAkiVAoeQWRtaW5WZXJpZnlCYW5rUGFuTmFtZU1pc21hdGNoEhQKDHBob25lX251bWJlchgBIAEoCRIcChR1c2VyX2JhbmtfYWNjb3VudF9pZBgCIAEoCUIfChtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmdQAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.broking.AdminVerifyAadhaarPanNameMismatch
 */
export type AdminVerifyAadhaarPanNameMismatch = Message<"com.stablemoney.api.broking.AdminVerifyAadhaarPanNameMismatch"> & {
  /**
   * @generated from field: string phone_number = 1;
   */
  phoneNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AdminVerifyAadhaarPanNameMismatch.
 * Use `create(AdminVerifyAadhaarPanNameMismatchSchema)` to create a new message.
 */
export const AdminVerifyAadhaarPanNameMismatchSchema: GenMessage<AdminVerifyAadhaarPanNameMismatch> = /*@__PURE__*/
  messageDesc(file_AdminNameMismatch, 0);

/**
 * @generated from message com.stablemoney.api.broking.AdminVerifyBankPanNameMismatch
 */
export type AdminVerifyBankPanNameMismatch = Message<"com.stablemoney.api.broking.AdminVerifyBankPanNameMismatch"> & {
  /**
   * @generated from field: string phone_number = 1;
   */
  phoneNumber: string;

  /**
   * @generated from field: string user_bank_account_id = 2;
   */
  userBankAccountId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AdminVerifyBankPanNameMismatch.
 * Use `create(AdminVerifyBankPanNameMismatchSchema)` to create a new message.
 */
export const AdminVerifyBankPanNameMismatchSchema: GenMessage<AdminVerifyBankPanNameMismatch> = /*@__PURE__*/
  messageDesc(file_AdminNameMismatch, 1);

