// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Common.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Common.proto.
 */
export const file_Common: GenFile = /*@__PURE__*/
  fileDesc("CgxDb21tb24ucHJvdG8SG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZyIzChBSZWRpcmVjdERlZXBsaW5rEgwKBHBhdGgYASABKAkSEQoJcGF0aF90eXBlGAIgASgJIjoKDUVycm9yUmVzcG9uc2USEgoKZXJyb3JfY29kZRgBIAEoCRIVCg1lcnJvcl9tZXNzYWdlGAIgASgJIiUKElVzZXJDb250ZXh0UmVxdWVzdBIPCgd1c2VyX2lkGAEgASgJIt0CChNVc2VyQ29udGV4dFJlc3BvbnNlEg8KB3VzZXJfaWQYASABKAkSSAoPbGlmZXRpbWVfc3RhdHVzGAMgASgOMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLlVzZXJMaWZldGltZVN0YXR1cxJVCgpreWNfc3RhdHVzGAUgASgOMkEuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLlVzZXJMaWZldGltZVN0YXR1c1Jlc3BvbnNlLkt5Y1N0YXR1cxIYChB0b3RhbF9pbnZlc3RtZW50GAcgASgBEhoKEmZpcnN0X3BheW1lbnRfZGF0ZRgLIAEoCRIZChFsYXN0X3BheW1lbnRfZGF0ZRgNIAEoCRIfChdib25kc19jb25zZW50X3RpbWVzdGFtcBgPIAEoAxIiChpuc2Vfb25ib2FyZGluZ19pbl9wcm9ncmVzcxgQIAEoCCIsChlVc2VyTGlmZXRpbWVTdGF0dXNSZXF1ZXN0Eg8KB3VzZXJfaWQYASABKAkikgMKGlVzZXJMaWZldGltZVN0YXR1c1Jlc3BvbnNlEkgKD2xpZmV0aW1lX3N0YXR1cxgBIAEoDjIvLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5Vc2VyTGlmZXRpbWVTdGF0dXMSVQoKa3ljX3N0YXR1cxgCIAEoDjJBLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5Vc2VyTGlmZXRpbWVTdGF0dXNSZXNwb25zZS5LeWNTdGF0dXMi0gEKCUt5Y1N0YXR1cxIWChJLWUNfU1RBVFVTX1VOS05PV04QABIRCg1OT1RfSU5JVElBVEVEEAESDQoJSU5JVElBVEVEEAISFQoRQ1ZMX1BVU0hfUkVRVUlSRUQQBRIWChJDVkxfUFVTSF9DT01QTEVURUQQChIRCg1DVkxfVkFMSURBVEVEEA8SGgoWT05CT0FSRElOR19PTl9FWENIQU5HRRAUEh4KGkVYQ0hBTkdFX09OQk9BUkRJTkdfRkFJTEVEEBkSDQoJQ09NUExFVEVEEB4iSwoSQ29uZmlnRGF0YVJlc3BvbnNlEjUKBGRhdGEYASADKAsyJy5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQ29uZmlnRGF0YSLoAQoKQ29uZmlnRGF0YRI/Cgtjb25maWdfbmFtZRgBIAEoDjIqLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5BcHBDb25maWdUeXBlEhQKDGNvbmZpZ192YWx1ZRgCIAEoCRJECgtjb25maWdfdHlwZRgDIAEoDjIvLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5BcHBDb25maWdWYWx1ZVR5cGUSEwoLbWluX3ZlcnNpb24YBCABKAUSEwoLbWF4X3ZlcnNpb24YBSABKAUSEwoLZGVzY3JpcHRpb24YBiABKAkiTwoSVXNlckNvbmZpZ1Jlc3BvbnNlEjkKBGRhdGEYASADKAsyKy5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuVXNlckNvbmZpZ0RhdGEi1QEKDlVzZXJDb25maWdEYXRhEkAKC2NvbmZpZ19uYW1lGAEgASgOMisuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLlVzZXJDb25maWdUeXBlEkQKC2NvbmZpZ190eXBlGAIgASgOMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkFwcENvbmZpZ1ZhbHVlVHlwZRIUCgxjb25maWdfdmFsdWUYAyABKAkSEwoLZGVzY3JpcHRpb24YBCABKAkSEAoIc2hvd19jdGEYBSABKAgi4QIKB0RhdGFLZXkSCwoDa2V5GAEgASgJEkgKEWNvbnRleHRfdmFyaWFibGVzGAMgAygLMi0uY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkRhdGFLZXkuVmFyaWFibGUaaAoIVmFyaWFibGUSDAoEbmFtZRgBIAEoCRI/CgR0eXBlGAIgASgOMjEuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkRhdGFLZXkuVmFyaWFibGVUeXBlEg0KBXZhbHVlGAMgASgJIpQBCgxWYXJpYWJsZVR5cGUSCwoHVU5LTk9XThAAEgoKBlNUUklORxABEgwKCENVUlJFTkNZEAISCAoEREFURRADEg0KCURBVEVfVElNRRAEEhMKD1VTRVJfRklSU1RfTkFNRRAFEhIKDlNIT1JUX0NVUlJFTkNZEAYSCwoHUEVSQ0VOVBAHEg4KClBFUkNFTlRfMkYQCCJaCglUYWdDb25maWcSDAoEbmFtZRgBIAEoCRIQCghpY29uX3VybBgCIAEoCRINCgVjb2xvchgDIAEoCRIQCghiZ19jb2xvchgEIAEoCRIMCgR0eXBlGAUgASgJIpQBCgxBZGRyZXNzUHJvdG8SFQoNYWRkcmVzc19saW5lMRgBIAEoCRIVCg1hZGRyZXNzX2xpbmUyGAIgASgJEhUKDWFkZHJlc3NfbGluZTMYAyABKAkSDAoEY2l0eRgEIAEoCRIRCglwb3N0X2NvZGUYBSABKAkSDQoFc3RhdGUYBiABKAkSDwoHY291bnRyeRgHIAEoCSLCBAoUVXBkYXRlUHJvZmlsZVJlcXVlc3QSEAoDZG9iGAEgASgJSACIAQESQwoMaW5jb21lX3JhbmdlGAIgASgOMiguY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkluY29tZVJhbmdlSAGIAQESSQoPZW1wbG95bWVudF90eXBlGAMgASgOMisuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkVtcGxveW1lbnRUeXBlSAKIAQESTwoSdHJhZGluZ19leHBlcmllbmNlGAQgASgOMi4uY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLlRyYWRpbmdFeHBlcmllbmNlSAOIAQESRwoObWFyaXRhbF9zdGF0dXMYBSABKA4yKi5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuTWFyaXRhbFN0YXR1c0gEiAEBEhgKC2ZhdGhlcl9uYW1lGAYgASgJSAWIAQESGAoLbW90aGVyX25hbWUYByABKAlIBogBARI4CgZnZW5kZXIYCCABKA4yIy5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuR2VuZGVySAeIAQFCBgoEX2RvYkIPCg1faW5jb21lX3JhbmdlQhIKEF9lbXBsb3ltZW50X3R5cGVCFQoTX3RyYWRpbmdfZXhwZXJpZW5jZUIRCg9fbWFyaXRhbF9zdGF0dXNCDgoMX2ZhdGhlcl9uYW1lQg4KDF9tb3RoZXJfbmFtZUIJCgdfZ2VuZGVyIjwKFVVwZGF0ZVByb2ZpbGVSZXNwb25zZRIOCgZzdGF0dXMYASABKAgSEwoLZGVzY3JpcHRpb24YAiABKAkimQEKGEFkZEN1cnJlbnRBZGRyZXNzUmVxdWVzdBIkChxpc19zYW1lX2FzX3Blcm1hbmVudF9hZGRyZXNzGAEgASgIEkUKDWFkZHJlc3NfcHJvdG8YAiABKAsyKS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQWRkcmVzc1Byb3RvSACIAQFCEAoOX2FkZHJlc3NfcHJvdG8i+QIKCUFkZHJlc3NEQhIPCgJpZBgBIAEoCUgAiAEBEhoKDWFkZHJlc3NfbGluZTEYAiABKAlIAYgBARIaCg1hZGRyZXNzX2xpbmUyGAMgASgJSAKIAQESGgoNYWRkcmVzc19saW5lMxgEIAEoCUgDiAEBEhEKBGNpdHkYBSABKAlIBIgBARIWCglwb3N0X2NvZGUYBiABKAlIBYgBARISCgVzdGF0ZRgHIAEoCUgGiAEBEhQKB2NvdW50cnkYCCABKAlIB4gBARIVCghsYXRpdHVkZRgJIAEoAUgIiAEBEhYKCWxvbmdpdHVkZRgKIAEoAUgJiAEBQgUKA19pZEIQCg5fYWRkcmVzc19saW5lMUIQCg5fYWRkcmVzc19saW5lMkIQCg5fYWRkcmVzc19saW5lM0IHCgVfY2l0eUIMCgpfcG9zdF9jb2RlQggKBl9zdGF0ZUIKCghfY291bnRyeUILCglfbGF0aXR1ZGVCDAoKX2xvbmdpdHVkZSJRChZBZGRyZXNzREJVcGRhdGVSZXF1ZXN0EjcKB2FkZHJlc3MYASABKAsyJi5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQWRkcmVzc0RCIlIKF0FkZHJlc3NEQlVwZGF0ZVJlc3BvbnNlEjcKB2FkZHJlc3MYASABKAsyJi5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQWRkcmVzc0RCIhsKGUFkZEN1cnJlbnRBZGRyZXNzUmVzcG9uc2UioQEKEU5zZVN0YXR1c1Jlc3BvbnNlEkwKCm5zZV9zdGF0dXMYASABKA4yOC5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuTnNlU3RhdHVzUmVzcG9uc2UuTnNlU3RhdHVzIj4KCU5zZVN0YXR1cxILCgdVTktOT1dOEAASCwoHUEVORElORxABEgsKB1NVQ0NFU1MQAhIKCgZGQUlMRUQQAyIvChFQYWdpbmF0aW9uUmVxdWVzdBIMCgRwYWdlGAEgASgFEgwKBHNpemUYAiABKAUqLAoOQ29sbGVjdGlvblR5cGUSCgoGTUFOVUFMEAASDgoKRVhQUkVTU0lPThABKmwKC01lc3NhZ2VUeXBlEhgKFFVOS05PV05fTUVTU0FHRV9UWVBFEAASFwoTUFJPTU9USU9OQUxfTUVTU0FHRRABEhkKFVRSQU5TQUNUSU9OQUxfTUVTU0FHRRACEg8KC09UUF9NRVNTQUdFEAMqPgoLQ29udGVudFR5cGUSGAoUVU5LTk9XTl9DT05URU5UX1RZUEUQABIICgRURVhUEAESCwoHVU5JQ09ERRACKkQKC09wdGluU3RhdHVzEhgKFFVOS05PV05fT1BUSU5fU1RBVFVTEAASDAoIT1BURURfSU4QARINCglPUFRFRF9PVVQQAiqvBwoNQXBwQ29uZmlnVHlwZRIbChdBUFBfQ09ORklHX1RZUEVfVU5LTk9XThAAEhkKFUJBTktfVkVSSUZJQ0FUSU9OX1JQRBABEhgKFEJBTktfVkVSSUZJQ0FUSU9OX1BEEAISGwoXQkFTSUNfREVUQUlMU19RVUVTVElPTlMQAxIRCg1QQU5fQ1ZMX0NIRUNLEAQSGgoWUlBEX1NVUFBPUlRFRF9VUElfQVBQUxAFEhEKDUNSRURJVF9SRVBPUlQQBhIRCg1NT0JJTEVfUkVTRU5EEAcSEgoOTU9CSUxFX0FUVEVNUFQQCBIVChFNT0JJTEVfT1RQX0xFTkdUSBAJEhQKEEVNQUlMX09UUF9MRU5HVEgQChIQCgxFTUFJTF9SRVNFTkQQCxIRCg1FTUFJTF9BVFRFTVBUEAwSFwoTQU5EUk9JRF9BUFBfVkVSU0lPThANEhMKD0lPU19BUFBfVkVSU0lPThAOEhMKD1dFQl9BUFBfVkVSU0lPThAPEiYKIk1PQklMRV9MT0dJTl9CTE9DS0VEX1RJTUVfSU5fSE9VUlMQEBIsCihFTUFJTF9MT0dJTl9SRVNFTkRfQkxPQ0tFRF9USU1FX0lOX0hPVVJTEBESJQohTU9CSUxFX09UUF9FWFBJUllfVElNRV9JTl9NSU5VVEVTEBISJAogRU1BSUxfT1RQX0VYUElSWV9USU1FX0lOX01JTlVURVMQExI2CjJFTUFJTF9PVFBfUkVHRU5FUkFURV9CRUZPUkVfRVhQSVJZX1RJTUVfSU5fTUlOVVRFUxAUEi0KKUVNQUlMX0xPR0lOX0FUVEVNUFRfQkxPQ0tFRF9USU1FX0lOX0hPVVJTEBUSLgoqTU9CSUxFX0xPR0lOX0FUVEVNUFRfQkxPQ0tFRF9USU1FX0lOX0hPVVJTEBYSLQopTU9CSUxFX0xPR0lOX1JFU0VORF9CTE9DS0VEX1RJTUVfSU5fSE9VUlMQFxI3CjNNT0JJTEVfT1RQX1JFR0VORVJBVEVfQkVGT1JFX0VYUElSWV9USU1FX0lOX01JTlVURVMQGBIlCiFFTUFJTF9PVFBfUkVTRU5EX1RJTUVSX0lOX1NFQ09ORFMQGRImCiJNT0JJTEVfT1RQX1JFU0VORF9USU1FUl9JTl9TRUNPTkRTEBoSGwoXVE9LRU5fVkFMSURJVFlfSU5fSE9VUlMQGxIjCh9SRUZSRVNIX1RPS0VOX1ZBTElESVRZX0lOX0hPVVJTEBwqigEKEkFwcENvbmZpZ1ZhbHVlVHlwZRIhCh1BUFBfQ09ORklHX1ZBTFVFX1RZUEVfVU5LTk9XThAAEg0KCVRFWFRfVFlQRRABEhAKDEJPT0xFQU5fVFlQRRACEg8KC1NUUklOR19UWVBFEAMSDQoJSlNPTl9UWVBFEAQSEAoMSU5URUdFUl9UWVBFEAUqQQoOVXNlckNvbmZpZ1R5cGUSHAoYVU5LTk9XTl9VU0VSX0NPTkZJR19UWVBFEAASEQoNQkFOTkVSX0NPTkZJRxABKsYBChJVc2VyTGlmZXRpbWVTdGF0dXMSGwoXTElGRVRJTUVfU1RBVFVTX1VOS05PV04QABISCg5OT1RfUkVHSVNURVJFRBACEgwKCE5FV19VU0VSEAQSEQoNS1lDX0lOSVRJQVRFRBAGEhAKDE1JTl9LWUNfRE9ORRAHEhEKDUtZQ19DT01QTEVURUQQCBITCg9PUkRFUl9JTklUSUFURUQQChIQCgxQQVlNRU5UX0RPTkUQDBISCg5CT05EX1BVUkNIQVNFRBAQKqkBCgtJbmNvbWVSYW5nZRIYChRVTktOT1dOX0lOQ09NRV9SQU5HRRAAEhAKDExFU1NfVEhBTl8xTBABEhUKEUJFVFdFRU5fMUxfQU5EXzVMEAISFgoSQkVUV0VFTl81TF9BTkRfMTBMEAMSFwoTQkVUV0VFTl8xMExfQU5EXzI1TBAEEhcKE0JFVFdFRU5fMjVMX0FORF8xQ1IQBRINCglBQk9WRV8xQ1IQBiqRAgoORW1wbG95bWVudFR5cGUSGwoXVU5LTk9XTl9FTVBMT1lNRU5UX1RZUEUQABIaChZQUklWQVRFX1NFQ1RPUl9TRVJWSUNFEAESEQoNUFVCTElDX1NFQ1RPUhACEgwKCEJVU0lORVNTEAMSEAoMUFJPRkVTU0lPTkFMEAQSEQoNQUdSSUNVTFRVUklTVBAFEgsKB1JFVElSRUQQBhINCglIT1VTRVdJRkUQBxILCgdTVFVERU5UEAgSEAoMRk9SRVhfREVBTEVSEAkSFgoSR09WRVJOTUVOVF9TRVJWSUNFEAoSGgoWT1RIRVJTX0VNUExPWU1FTlRfVFlQRRALEhEKDVNFTEZfRU1QTE9ZRUQQDCpECgZHZW5kZXISEgoOVU5LTk9XTl9HRU5ERVIQABIICgRNQUxFEAESCgoGRkVNQUxFEAISEAoMT1RIRVJfR0VOREVSEAMqRAoNTWFyaXRhbFN0YXR1cxIaChZVTktOT1dOX01BUklUQUxfU1RBVFVTEAASCgoGU0lOR0xFEAESCwoHTUFSUklFRBACKr4BChFUcmFkaW5nRXhwZXJpZW5jZRIeChpVTktOT1dOX1RSQURJTkdfRVhQRVJJRU5DRRAAEhUKEUxFU1NfVEhBTl8xX01PTlRIEAESHwobQkVUV0VFTl8xX01PTlRIX0FORF82X01PTlRIEAISHgoaQkVUV0VFTl82X01PTlRIX0FORF8xX1lFQVIQAxIeChpCRVRXRUVOXzFfWUVBUl9BTkRfNV9ZRUFSUxAEEhEKDUFCT1ZFXzVfWUVBUlMQBUIfChtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmdQAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.broking.RedirectDeeplink
 */
export type RedirectDeeplink = Message<"com.stablemoney.api.broking.RedirectDeeplink"> & {
  /**
   * @generated from field: string path = 1;
   */
  path: string;

  /**
   * @generated from field: string path_type = 2;
   */
  pathType: string;
};

/**
 * Describes the message com.stablemoney.api.broking.RedirectDeeplink.
 * Use `create(RedirectDeeplinkSchema)` to create a new message.
 */
export const RedirectDeeplinkSchema: GenMessage<RedirectDeeplink> = /*@__PURE__*/
  messageDesc(file_Common, 0);

/**
 * @generated from message com.stablemoney.api.broking.ErrorResponse
 */
export type ErrorResponse = Message<"com.stablemoney.api.broking.ErrorResponse"> & {
  /**
   * @generated from field: string error_code = 1;
   */
  errorCode: string;

  /**
   * @generated from field: string error_message = 2;
   */
  errorMessage: string;
};

/**
 * Describes the message com.stablemoney.api.broking.ErrorResponse.
 * Use `create(ErrorResponseSchema)` to create a new message.
 */
export const ErrorResponseSchema: GenMessage<ErrorResponse> = /*@__PURE__*/
  messageDesc(file_Common, 1);

/**
 * @generated from message com.stablemoney.api.broking.UserContextRequest
 */
export type UserContextRequest = Message<"com.stablemoney.api.broking.UserContextRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UserContextRequest.
 * Use `create(UserContextRequestSchema)` to create a new message.
 */
export const UserContextRequestSchema: GenMessage<UserContextRequest> = /*@__PURE__*/
  messageDesc(file_Common, 2);

/**
 * @generated from message com.stablemoney.api.broking.UserContextResponse
 */
export type UserContextResponse = Message<"com.stablemoney.api.broking.UserContextResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.UserLifetimeStatus lifetime_status = 3;
   */
  lifetimeStatus: UserLifetimeStatus;

  /**
   * @generated from field: com.stablemoney.api.broking.UserLifetimeStatusResponse.KycStatus kyc_status = 5;
   */
  kycStatus: UserLifetimeStatusResponse_KycStatus;

  /**
   * @generated from field: double total_investment = 7;
   */
  totalInvestment: number;

  /**
   * @generated from field: string first_payment_date = 11;
   */
  firstPaymentDate: string;

  /**
   * @generated from field: string last_payment_date = 13;
   */
  lastPaymentDate: string;

  /**
   * @generated from field: int64 bonds_consent_timestamp = 15;
   */
  bondsConsentTimestamp: bigint;

  /**
   * @generated from field: bool nse_onboarding_in_progress = 16;
   */
  nseOnboardingInProgress: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.UserContextResponse.
 * Use `create(UserContextResponseSchema)` to create a new message.
 */
export const UserContextResponseSchema: GenMessage<UserContextResponse> = /*@__PURE__*/
  messageDesc(file_Common, 3);

/**
 * @generated from message com.stablemoney.api.broking.UserLifetimeStatusRequest
 */
export type UserLifetimeStatusRequest = Message<"com.stablemoney.api.broking.UserLifetimeStatusRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UserLifetimeStatusRequest.
 * Use `create(UserLifetimeStatusRequestSchema)` to create a new message.
 */
export const UserLifetimeStatusRequestSchema: GenMessage<UserLifetimeStatusRequest> = /*@__PURE__*/
  messageDesc(file_Common, 4);

/**
 * @generated from message com.stablemoney.api.broking.UserLifetimeStatusResponse
 */
export type UserLifetimeStatusResponse = Message<"com.stablemoney.api.broking.UserLifetimeStatusResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.UserLifetimeStatus lifetime_status = 1;
   */
  lifetimeStatus: UserLifetimeStatus;

  /**
   * @generated from field: com.stablemoney.api.broking.UserLifetimeStatusResponse.KycStatus kyc_status = 2;
   */
  kycStatus: UserLifetimeStatusResponse_KycStatus;
};

/**
 * Describes the message com.stablemoney.api.broking.UserLifetimeStatusResponse.
 * Use `create(UserLifetimeStatusResponseSchema)` to create a new message.
 */
export const UserLifetimeStatusResponseSchema: GenMessage<UserLifetimeStatusResponse> = /*@__PURE__*/
  messageDesc(file_Common, 5);

/**
 * @generated from enum com.stablemoney.api.broking.UserLifetimeStatusResponse.KycStatus
 */
export enum UserLifetimeStatusResponse_KycStatus {
  /**
   * @generated from enum value: KYC_STATUS_UNKNOWN = 0;
   */
  KYC_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: NOT_INITIATED = 1;
   */
  NOT_INITIATED = 1,

  /**
   * @generated from enum value: INITIATED = 2;
   */
  INITIATED = 2,

  /**
   * @generated from enum value: CVL_PUSH_REQUIRED = 5;
   */
  CVL_PUSH_REQUIRED = 5,

  /**
   * @generated from enum value: CVL_PUSH_COMPLETED = 10;
   */
  CVL_PUSH_COMPLETED = 10,

  /**
   * @generated from enum value: CVL_VALIDATED = 15;
   */
  CVL_VALIDATED = 15,

  /**
   * @generated from enum value: ONBOARDING_ON_EXCHANGE = 20;
   */
  ONBOARDING_ON_EXCHANGE = 20,

  /**
   * @generated from enum value: EXCHANGE_ONBOARDING_FAILED = 25;
   */
  EXCHANGE_ONBOARDING_FAILED = 25,

  /**
   * @generated from enum value: COMPLETED = 30;
   */
  COMPLETED = 30,
}

/**
 * Describes the enum com.stablemoney.api.broking.UserLifetimeStatusResponse.KycStatus.
 */
export const UserLifetimeStatusResponse_KycStatusSchema: GenEnum<UserLifetimeStatusResponse_KycStatus> = /*@__PURE__*/
  enumDesc(file_Common, 5, 0);

/**
 * @generated from message com.stablemoney.api.broking.ConfigDataResponse
 */
export type ConfigDataResponse = Message<"com.stablemoney.api.broking.ConfigDataResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.ConfigData data = 1;
   */
  data: ConfigData[];
};

/**
 * Describes the message com.stablemoney.api.broking.ConfigDataResponse.
 * Use `create(ConfigDataResponseSchema)` to create a new message.
 */
export const ConfigDataResponseSchema: GenMessage<ConfigDataResponse> = /*@__PURE__*/
  messageDesc(file_Common, 6);

/**
 * @generated from message com.stablemoney.api.broking.ConfigData
 */
export type ConfigData = Message<"com.stablemoney.api.broking.ConfigData"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.AppConfigType config_name = 1;
   */
  configName: AppConfigType;

  /**
   * @generated from field: string config_value = 2;
   */
  configValue: string;

  /**
   * @generated from field: com.stablemoney.api.broking.AppConfigValueType config_type = 3;
   */
  configType: AppConfigValueType;

  /**
   * @generated from field: int32 min_version = 4;
   */
  minVersion: number;

  /**
   * @generated from field: int32 max_version = 5;
   */
  maxVersion: number;

  /**
   * @generated from field: string description = 6;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.broking.ConfigData.
 * Use `create(ConfigDataSchema)` to create a new message.
 */
export const ConfigDataSchema: GenMessage<ConfigData> = /*@__PURE__*/
  messageDesc(file_Common, 7);

/**
 * @generated from message com.stablemoney.api.broking.UserConfigResponse
 */
export type UserConfigResponse = Message<"com.stablemoney.api.broking.UserConfigResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.UserConfigData data = 1;
   */
  data: UserConfigData[];
};

/**
 * Describes the message com.stablemoney.api.broking.UserConfigResponse.
 * Use `create(UserConfigResponseSchema)` to create a new message.
 */
export const UserConfigResponseSchema: GenMessage<UserConfigResponse> = /*@__PURE__*/
  messageDesc(file_Common, 8);

/**
 * @generated from message com.stablemoney.api.broking.UserConfigData
 */
export type UserConfigData = Message<"com.stablemoney.api.broking.UserConfigData"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.UserConfigType config_name = 1;
   */
  configName: UserConfigType;

  /**
   * @generated from field: com.stablemoney.api.broking.AppConfigValueType config_type = 2;
   */
  configType: AppConfigValueType;

  /**
   * @generated from field: string config_value = 3;
   */
  configValue: string;

  /**
   * @generated from field: string description = 4;
   */
  description: string;

  /**
   * @generated from field: bool show_cta = 5;
   */
  showCta: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.UserConfigData.
 * Use `create(UserConfigDataSchema)` to create a new message.
 */
export const UserConfigDataSchema: GenMessage<UserConfigData> = /*@__PURE__*/
  messageDesc(file_Common, 9);

/**
 * @generated from message com.stablemoney.api.broking.DataKey
 */
export type DataKey = Message<"com.stablemoney.api.broking.DataKey"> & {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.DataKey.Variable context_variables = 3;
   */
  contextVariables: DataKey_Variable[];
};

/**
 * Describes the message com.stablemoney.api.broking.DataKey.
 * Use `create(DataKeySchema)` to create a new message.
 */
export const DataKeySchema: GenMessage<DataKey> = /*@__PURE__*/
  messageDesc(file_Common, 10);

/**
 * @generated from message com.stablemoney.api.broking.DataKey.Variable
 */
export type DataKey_Variable = Message<"com.stablemoney.api.broking.DataKey.Variable"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.broking.DataKey.VariableType type = 2;
   */
  type: DataKey_VariableType;

  /**
   * @generated from field: string value = 3;
   */
  value: string;
};

/**
 * Describes the message com.stablemoney.api.broking.DataKey.Variable.
 * Use `create(DataKey_VariableSchema)` to create a new message.
 */
export const DataKey_VariableSchema: GenMessage<DataKey_Variable> = /*@__PURE__*/
  messageDesc(file_Common, 10, 0);

/**
 * @generated from enum com.stablemoney.api.broking.DataKey.VariableType
 */
export enum DataKey_VariableType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STRING = 1;
   */
  STRING = 1,

  /**
   * @generated from enum value: CURRENCY = 2;
   */
  CURRENCY = 2,

  /**
   * @generated from enum value: DATE = 3;
   */
  DATE = 3,

  /**
   * @generated from enum value: DATE_TIME = 4;
   */
  DATE_TIME = 4,

  /**
   * @generated from enum value: USER_FIRST_NAME = 5;
   */
  USER_FIRST_NAME = 5,

  /**
   * @generated from enum value: SHORT_CURRENCY = 6;
   */
  SHORT_CURRENCY = 6,

  /**
   * @generated from enum value: PERCENT = 7;
   */
  PERCENT = 7,

  /**
   * @generated from enum value: PERCENT_2F = 8;
   */
  PERCENT_2F = 8,
}

/**
 * Describes the enum com.stablemoney.api.broking.DataKey.VariableType.
 */
export const DataKey_VariableTypeSchema: GenEnum<DataKey_VariableType> = /*@__PURE__*/
  enumDesc(file_Common, 10, 0);

/**
 * @generated from message com.stablemoney.api.broking.TagConfig
 */
export type TagConfig = Message<"com.stablemoney.api.broking.TagConfig"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string icon_url = 2;
   */
  iconUrl: string;

  /**
   * @generated from field: string color = 3;
   */
  color: string;

  /**
   * @generated from field: string bg_color = 4;
   */
  bgColor: string;

  /**
   * @generated from field: string type = 5;
   */
  type: string;
};

/**
 * Describes the message com.stablemoney.api.broking.TagConfig.
 * Use `create(TagConfigSchema)` to create a new message.
 */
export const TagConfigSchema: GenMessage<TagConfig> = /*@__PURE__*/
  messageDesc(file_Common, 11);

/**
 * @generated from message com.stablemoney.api.broking.AddressProto
 */
export type AddressProto = Message<"com.stablemoney.api.broking.AddressProto"> & {
  /**
   * @generated from field: string address_line1 = 1;
   */
  addressLine1: string;

  /**
   * @generated from field: string address_line2 = 2;
   */
  addressLine2: string;

  /**
   * @generated from field: string address_line3 = 3;
   */
  addressLine3: string;

  /**
   * @generated from field: string city = 4;
   */
  city: string;

  /**
   * @generated from field: string post_code = 5;
   */
  postCode: string;

  /**
   * @generated from field: string state = 6;
   */
  state: string;

  /**
   * @generated from field: string country = 7;
   */
  country: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AddressProto.
 * Use `create(AddressProtoSchema)` to create a new message.
 */
export const AddressProtoSchema: GenMessage<AddressProto> = /*@__PURE__*/
  messageDesc(file_Common, 12);

/**
 * @generated from message com.stablemoney.api.broking.UpdateProfileRequest
 */
export type UpdateProfileRequest = Message<"com.stablemoney.api.broking.UpdateProfileRequest"> & {
  /**
   * @generated from field: optional string dob = 1;
   */
  dob?: string;

  /**
   * @generated from field: optional com.stablemoney.api.broking.IncomeRange income_range = 2;
   */
  incomeRange?: IncomeRange;

  /**
   * @generated from field: optional com.stablemoney.api.broking.EmploymentType employment_type = 3;
   */
  employmentType?: EmploymentType;

  /**
   * @generated from field: optional com.stablemoney.api.broking.TradingExperience trading_experience = 4;
   */
  tradingExperience?: TradingExperience;

  /**
   * @generated from field: optional com.stablemoney.api.broking.MaritalStatus marital_status = 5;
   */
  maritalStatus?: MaritalStatus;

  /**
   * @generated from field: optional string father_name = 6;
   */
  fatherName?: string;

  /**
   * @generated from field: optional string mother_name = 7;
   */
  motherName?: string;

  /**
   * @generated from field: optional com.stablemoney.api.broking.Gender gender = 8;
   */
  gender?: Gender;
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateProfileRequest.
 * Use `create(UpdateProfileRequestSchema)` to create a new message.
 */
export const UpdateProfileRequestSchema: GenMessage<UpdateProfileRequest> = /*@__PURE__*/
  messageDesc(file_Common, 13);

/**
 * @generated from message com.stablemoney.api.broking.UpdateProfileResponse
 */
export type UpdateProfileResponse = Message<"com.stablemoney.api.broking.UpdateProfileResponse"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;

  /**
   * @generated from field: string description = 2;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateProfileResponse.
 * Use `create(UpdateProfileResponseSchema)` to create a new message.
 */
export const UpdateProfileResponseSchema: GenMessage<UpdateProfileResponse> = /*@__PURE__*/
  messageDesc(file_Common, 14);

/**
 * @generated from message com.stablemoney.api.broking.AddCurrentAddressRequest
 */
export type AddCurrentAddressRequest = Message<"com.stablemoney.api.broking.AddCurrentAddressRequest"> & {
  /**
   * @generated from field: bool is_same_as_permanent_address = 1;
   */
  isSameAsPermanentAddress: boolean;

  /**
   * @generated from field: optional com.stablemoney.api.broking.AddressProto address_proto = 2;
   */
  addressProto?: AddressProto;
};

/**
 * Describes the message com.stablemoney.api.broking.AddCurrentAddressRequest.
 * Use `create(AddCurrentAddressRequestSchema)` to create a new message.
 */
export const AddCurrentAddressRequestSchema: GenMessage<AddCurrentAddressRequest> = /*@__PURE__*/
  messageDesc(file_Common, 15);

/**
 * @generated from message com.stablemoney.api.broking.AddressDB
 */
export type AddressDB = Message<"com.stablemoney.api.broking.AddressDB"> & {
  /**
   * @generated from field: optional string id = 1;
   */
  id?: string;

  /**
   * @generated from field: optional string address_line1 = 2;
   */
  addressLine1?: string;

  /**
   * @generated from field: optional string address_line2 = 3;
   */
  addressLine2?: string;

  /**
   * @generated from field: optional string address_line3 = 4;
   */
  addressLine3?: string;

  /**
   * @generated from field: optional string city = 5;
   */
  city?: string;

  /**
   * @generated from field: optional string post_code = 6;
   */
  postCode?: string;

  /**
   * @generated from field: optional string state = 7;
   */
  state?: string;

  /**
   * @generated from field: optional string country = 8;
   */
  country?: string;

  /**
   * @generated from field: optional double latitude = 9;
   */
  latitude?: number;

  /**
   * @generated from field: optional double longitude = 10;
   */
  longitude?: number;
};

/**
 * Describes the message com.stablemoney.api.broking.AddressDB.
 * Use `create(AddressDBSchema)` to create a new message.
 */
export const AddressDBSchema: GenMessage<AddressDB> = /*@__PURE__*/
  messageDesc(file_Common, 16);

/**
 * @generated from message com.stablemoney.api.broking.AddressDBUpdateRequest
 */
export type AddressDBUpdateRequest = Message<"com.stablemoney.api.broking.AddressDBUpdateRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.AddressDB address = 1;
   */
  address?: AddressDB;
};

/**
 * Describes the message com.stablemoney.api.broking.AddressDBUpdateRequest.
 * Use `create(AddressDBUpdateRequestSchema)` to create a new message.
 */
export const AddressDBUpdateRequestSchema: GenMessage<AddressDBUpdateRequest> = /*@__PURE__*/
  messageDesc(file_Common, 17);

/**
 * @generated from message com.stablemoney.api.broking.AddressDBUpdateResponse
 */
export type AddressDBUpdateResponse = Message<"com.stablemoney.api.broking.AddressDBUpdateResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.AddressDB address = 1;
   */
  address?: AddressDB;
};

/**
 * Describes the message com.stablemoney.api.broking.AddressDBUpdateResponse.
 * Use `create(AddressDBUpdateResponseSchema)` to create a new message.
 */
export const AddressDBUpdateResponseSchema: GenMessage<AddressDBUpdateResponse> = /*@__PURE__*/
  messageDesc(file_Common, 18);

/**
 * @generated from message com.stablemoney.api.broking.AddCurrentAddressResponse
 */
export type AddCurrentAddressResponse = Message<"com.stablemoney.api.broking.AddCurrentAddressResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.AddCurrentAddressResponse.
 * Use `create(AddCurrentAddressResponseSchema)` to create a new message.
 */
export const AddCurrentAddressResponseSchema: GenMessage<AddCurrentAddressResponse> = /*@__PURE__*/
  messageDesc(file_Common, 19);

/**
 * @generated from message com.stablemoney.api.broking.NseStatusResponse
 */
export type NseStatusResponse = Message<"com.stablemoney.api.broking.NseStatusResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.NseStatusResponse.NseStatus nse_status = 1;
   */
  nseStatus: NseStatusResponse_NseStatus;
};

/**
 * Describes the message com.stablemoney.api.broking.NseStatusResponse.
 * Use `create(NseStatusResponseSchema)` to create a new message.
 */
export const NseStatusResponseSchema: GenMessage<NseStatusResponse> = /*@__PURE__*/
  messageDesc(file_Common, 20);

/**
 * @generated from enum com.stablemoney.api.broking.NseStatusResponse.NseStatus
 */
export enum NseStatusResponse_NseStatus {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: SUCCESS = 2;
   */
  SUCCESS = 2,

  /**
   * @generated from enum value: FAILED = 3;
   */
  FAILED = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.NseStatusResponse.NseStatus.
 */
export const NseStatusResponse_NseStatusSchema: GenEnum<NseStatusResponse_NseStatus> = /*@__PURE__*/
  enumDesc(file_Common, 20, 0);

/**
 * @generated from message com.stablemoney.api.broking.PaginationRequest
 */
export type PaginationRequest = Message<"com.stablemoney.api.broking.PaginationRequest"> & {
  /**
   * @generated from field: int32 page = 1;
   */
  page: number;

  /**
   * @generated from field: int32 size = 2;
   */
  size: number;
};

/**
 * Describes the message com.stablemoney.api.broking.PaginationRequest.
 * Use `create(PaginationRequestSchema)` to create a new message.
 */
export const PaginationRequestSchema: GenMessage<PaginationRequest> = /*@__PURE__*/
  messageDesc(file_Common, 21);

/**
 * @generated from enum com.stablemoney.api.broking.CollectionType
 */
export enum CollectionType {
  /**
   * @generated from enum value: MANUAL = 0;
   */
  MANUAL = 0,

  /**
   * @generated from enum value: EXPRESSION = 1;
   */
  EXPRESSION = 1,
}

/**
 * Describes the enum com.stablemoney.api.broking.CollectionType.
 */
export const CollectionTypeSchema: GenEnum<CollectionType> = /*@__PURE__*/
  enumDesc(file_Common, 0);

/**
 * @generated from enum com.stablemoney.api.broking.MessageType
 */
export enum MessageType {
  /**
   * @generated from enum value: UNKNOWN_MESSAGE_TYPE = 0;
   */
  UNKNOWN_MESSAGE_TYPE = 0,

  /**
   * @generated from enum value: PROMOTIONAL_MESSAGE = 1;
   */
  PROMOTIONAL_MESSAGE = 1,

  /**
   * @generated from enum value: TRANSACTIONAL_MESSAGE = 2;
   */
  TRANSACTIONAL_MESSAGE = 2,

  /**
   * @generated from enum value: OTP_MESSAGE = 3;
   */
  OTP_MESSAGE = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType> = /*@__PURE__*/
  enumDesc(file_Common, 1);

/**
 * @generated from enum com.stablemoney.api.broking.ContentType
 */
export enum ContentType {
  /**
   * @generated from enum value: UNKNOWN_CONTENT_TYPE = 0;
   */
  UNKNOWN_CONTENT_TYPE = 0,

  /**
   * @generated from enum value: TEXT = 1;
   */
  TEXT = 1,

  /**
   * @generated from enum value: UNICODE = 2;
   */
  UNICODE = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.ContentType.
 */
export const ContentTypeSchema: GenEnum<ContentType> = /*@__PURE__*/
  enumDesc(file_Common, 2);

/**
 * @generated from enum com.stablemoney.api.broking.OptinStatus
 */
export enum OptinStatus {
  /**
   * @generated from enum value: UNKNOWN_OPTIN_STATUS = 0;
   */
  UNKNOWN_OPTIN_STATUS = 0,

  /**
   * @generated from enum value: OPTED_IN = 1;
   */
  OPTED_IN = 1,

  /**
   * @generated from enum value: OPTED_OUT = 2;
   */
  OPTED_OUT = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.OptinStatus.
 */
export const OptinStatusSchema: GenEnum<OptinStatus> = /*@__PURE__*/
  enumDesc(file_Common, 3);

/**
 * @generated from enum com.stablemoney.api.broking.AppConfigType
 */
export enum AppConfigType {
  /**
   * @generated from enum value: APP_CONFIG_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: BANK_VERIFICATION_RPD = 1;
   */
  BANK_VERIFICATION_RPD = 1,

  /**
   * @generated from enum value: BANK_VERIFICATION_PD = 2;
   */
  BANK_VERIFICATION_PD = 2,

  /**
   * @generated from enum value: BASIC_DETAILS_QUESTIONS = 3;
   */
  BASIC_DETAILS_QUESTIONS = 3,

  /**
   * @generated from enum value: PAN_CVL_CHECK = 4;
   */
  PAN_CVL_CHECK = 4,

  /**
   * @generated from enum value: RPD_SUPPORTED_UPI_APPS = 5;
   */
  RPD_SUPPORTED_UPI_APPS = 5,

  /**
   * @generated from enum value: CREDIT_REPORT = 6;
   */
  CREDIT_REPORT = 6,

  /**
   * @generated from enum value: MOBILE_RESEND = 7;
   */
  MOBILE_RESEND = 7,

  /**
   * @generated from enum value: MOBILE_ATTEMPT = 8;
   */
  MOBILE_ATTEMPT = 8,

  /**
   * @generated from enum value: MOBILE_OTP_LENGTH = 9;
   */
  MOBILE_OTP_LENGTH = 9,

  /**
   * @generated from enum value: EMAIL_OTP_LENGTH = 10;
   */
  EMAIL_OTP_LENGTH = 10,

  /**
   * @generated from enum value: EMAIL_RESEND = 11;
   */
  EMAIL_RESEND = 11,

  /**
   * @generated from enum value: EMAIL_ATTEMPT = 12;
   */
  EMAIL_ATTEMPT = 12,

  /**
   * @generated from enum value: ANDROID_APP_VERSION = 13;
   */
  ANDROID_APP_VERSION = 13,

  /**
   * @generated from enum value: IOS_APP_VERSION = 14;
   */
  IOS_APP_VERSION = 14,

  /**
   * @generated from enum value: WEB_APP_VERSION = 15;
   */
  WEB_APP_VERSION = 15,

  /**
   * @generated from enum value: MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16;
   */
  MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16,

  /**
   * @generated from enum value: EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17;
   */
  EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17,

  /**
   * @generated from enum value: MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18;
   */
  MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18,

  /**
   * @generated from enum value: EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19;
   */
  EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19,

  /**
   * @generated from enum value: EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20;
   */
  EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20,

  /**
   * @generated from enum value: EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21;
   */
  EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21,

  /**
   * @generated from enum value: MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22;
   */
  MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22,

  /**
   * @generated from enum value: MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23;
   */
  MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23,

  /**
   * @generated from enum value: MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24;
   */
  MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24,

  /**
   * @generated from enum value: EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25;
   */
  EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25,

  /**
   * @generated from enum value: MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26;
   */
  MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26,

  /**
   * @generated from enum value: TOKEN_VALIDITY_IN_HOURS = 27;
   */
  TOKEN_VALIDITY_IN_HOURS = 27,

  /**
   * @generated from enum value: REFRESH_TOKEN_VALIDITY_IN_HOURS = 28;
   */
  REFRESH_TOKEN_VALIDITY_IN_HOURS = 28,
}

/**
 * Describes the enum com.stablemoney.api.broking.AppConfigType.
 */
export const AppConfigTypeSchema: GenEnum<AppConfigType> = /*@__PURE__*/
  enumDesc(file_Common, 4);

/**
 * @generated from enum com.stablemoney.api.broking.AppConfigValueType
 */
export enum AppConfigValueType {
  /**
   * @generated from enum value: APP_CONFIG_VALUE_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_VALUE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: TEXT_TYPE = 1;
   */
  TEXT_TYPE = 1,

  /**
   * @generated from enum value: BOOLEAN_TYPE = 2;
   */
  BOOLEAN_TYPE = 2,

  /**
   * @generated from enum value: STRING_TYPE = 3;
   */
  STRING_TYPE = 3,

  /**
   * @generated from enum value: JSON_TYPE = 4;
   */
  JSON_TYPE = 4,

  /**
   * @generated from enum value: INTEGER_TYPE = 5;
   */
  INTEGER_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.AppConfigValueType.
 */
export const AppConfigValueTypeSchema: GenEnum<AppConfigValueType> = /*@__PURE__*/
  enumDesc(file_Common, 5);

/**
 * @generated from enum com.stablemoney.api.broking.UserConfigType
 */
export enum UserConfigType {
  /**
   * @generated from enum value: UNKNOWN_USER_CONFIG_TYPE = 0;
   */
  UNKNOWN_USER_CONFIG_TYPE = 0,

  /**
   * @generated from enum value: BANNER_CONFIG = 1;
   */
  BANNER_CONFIG = 1,
}

/**
 * Describes the enum com.stablemoney.api.broking.UserConfigType.
 */
export const UserConfigTypeSchema: GenEnum<UserConfigType> = /*@__PURE__*/
  enumDesc(file_Common, 6);

/**
 * @generated from enum com.stablemoney.api.broking.UserLifetimeStatus
 */
export enum UserLifetimeStatus {
  /**
   * @generated from enum value: LIFETIME_STATUS_UNKNOWN = 0;
   */
  LIFETIME_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: NOT_REGISTERED = 2;
   */
  NOT_REGISTERED = 2,

  /**
   * @generated from enum value: NEW_USER = 4;
   */
  NEW_USER = 4,

  /**
   * @generated from enum value: KYC_INITIATED = 6;
   */
  KYC_INITIATED = 6,

  /**
   * @generated from enum value: MIN_KYC_DONE = 7;
   */
  MIN_KYC_DONE = 7,

  /**
   * @generated from enum value: KYC_COMPLETED = 8;
   */
  KYC_COMPLETED = 8,

  /**
   * @generated from enum value: ORDER_INITIATED = 10;
   */
  ORDER_INITIATED = 10,

  /**
   * @generated from enum value: PAYMENT_DONE = 12;
   */
  PAYMENT_DONE = 12,

  /**
   * @generated from enum value: BOND_PURCHASED = 16;
   */
  BOND_PURCHASED = 16,
}

/**
 * Describes the enum com.stablemoney.api.broking.UserLifetimeStatus.
 */
export const UserLifetimeStatusSchema: GenEnum<UserLifetimeStatus> = /*@__PURE__*/
  enumDesc(file_Common, 7);

/**
 * @generated from enum com.stablemoney.api.broking.IncomeRange
 */
export enum IncomeRange {
  /**
   * @generated from enum value: UNKNOWN_INCOME_RANGE = 0;
   */
  UNKNOWN_INCOME_RANGE = 0,

  /**
   * @generated from enum value: LESS_THAN_1L = 1;
   */
  LESS_THAN_1L = 1,

  /**
   * @generated from enum value: BETWEEN_1L_AND_5L = 2;
   */
  BETWEEN_1L_AND_5L = 2,

  /**
   * @generated from enum value: BETWEEN_5L_AND_10L = 3;
   */
  BETWEEN_5L_AND_10L = 3,

  /**
   * @generated from enum value: BETWEEN_10L_AND_25L = 4;
   */
  BETWEEN_10L_AND_25L = 4,

  /**
   * @generated from enum value: BETWEEN_25L_AND_1CR = 5;
   */
  BETWEEN_25L_AND_1CR = 5,

  /**
   * @generated from enum value: ABOVE_1CR = 6;
   */
  ABOVE_1CR = 6,
}

/**
 * Describes the enum com.stablemoney.api.broking.IncomeRange.
 */
export const IncomeRangeSchema: GenEnum<IncomeRange> = /*@__PURE__*/
  enumDesc(file_Common, 8);

/**
 * @generated from enum com.stablemoney.api.broking.EmploymentType
 */
export enum EmploymentType {
  /**
   * @generated from enum value: UNKNOWN_EMPLOYMENT_TYPE = 0;
   */
  UNKNOWN_EMPLOYMENT_TYPE = 0,

  /**
   * @generated from enum value: PRIVATE_SECTOR_SERVICE = 1;
   */
  PRIVATE_SECTOR_SERVICE = 1,

  /**
   * @generated from enum value: PUBLIC_SECTOR = 2;
   */
  PUBLIC_SECTOR = 2,

  /**
   * @generated from enum value: BUSINESS = 3;
   */
  BUSINESS = 3,

  /**
   * @generated from enum value: PROFESSIONAL = 4;
   */
  PROFESSIONAL = 4,

  /**
   * @generated from enum value: AGRICULTURIST = 5;
   */
  AGRICULTURIST = 5,

  /**
   * @generated from enum value: RETIRED = 6;
   */
  RETIRED = 6,

  /**
   * @generated from enum value: HOUSEWIFE = 7;
   */
  HOUSEWIFE = 7,

  /**
   * @generated from enum value: STUDENT = 8;
   */
  STUDENT = 8,

  /**
   * @generated from enum value: FOREX_DEALER = 9;
   */
  FOREX_DEALER = 9,

  /**
   * @generated from enum value: GOVERNMENT_SERVICE = 10;
   */
  GOVERNMENT_SERVICE = 10,

  /**
   * @generated from enum value: OTHERS_EMPLOYMENT_TYPE = 11;
   */
  OTHERS_EMPLOYMENT_TYPE = 11,

  /**
   * @generated from enum value: SELF_EMPLOYED = 12;
   */
  SELF_EMPLOYED = 12,
}

/**
 * Describes the enum com.stablemoney.api.broking.EmploymentType.
 */
export const EmploymentTypeSchema: GenEnum<EmploymentType> = /*@__PURE__*/
  enumDesc(file_Common, 9);

/**
 * @generated from enum com.stablemoney.api.broking.Gender
 */
export enum Gender {
  /**
   * @generated from enum value: UNKNOWN_GENDER = 0;
   */
  UNKNOWN_GENDER = 0,

  /**
   * @generated from enum value: MALE = 1;
   */
  MALE = 1,

  /**
   * @generated from enum value: FEMALE = 2;
   */
  FEMALE = 2,

  /**
   * @generated from enum value: OTHER_GENDER = 3;
   */
  OTHER_GENDER = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.Gender.
 */
export const GenderSchema: GenEnum<Gender> = /*@__PURE__*/
  enumDesc(file_Common, 10);

/**
 * @generated from enum com.stablemoney.api.broking.MaritalStatus
 */
export enum MaritalStatus {
  /**
   * @generated from enum value: UNKNOWN_MARITAL_STATUS = 0;
   */
  UNKNOWN_MARITAL_STATUS = 0,

  /**
   * @generated from enum value: SINGLE = 1;
   */
  SINGLE = 1,

  /**
   * @generated from enum value: MARRIED = 2;
   */
  MARRIED = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.MaritalStatus.
 */
export const MaritalStatusSchema: GenEnum<MaritalStatus> = /*@__PURE__*/
  enumDesc(file_Common, 11);

/**
 * @generated from enum com.stablemoney.api.broking.TradingExperience
 */
export enum TradingExperience {
  /**
   * @generated from enum value: UNKNOWN_TRADING_EXPERIENCE = 0;
   */
  UNKNOWN_TRADING_EXPERIENCE = 0,

  /**
   * @generated from enum value: LESS_THAN_1_MONTH = 1;
   */
  LESS_THAN_1_MONTH = 1,

  /**
   * @generated from enum value: BETWEEN_1_MONTH_AND_6_MONTH = 2;
   */
  BETWEEN_1_MONTH_AND_6_MONTH = 2,

  /**
   * @generated from enum value: BETWEEN_6_MONTH_AND_1_YEAR = 3;
   */
  BETWEEN_6_MONTH_AND_1_YEAR = 3,

  /**
   * @generated from enum value: BETWEEN_1_YEAR_AND_5_YEARS = 4;
   */
  BETWEEN_1_YEAR_AND_5_YEARS = 4,

  /**
   * @generated from enum value: ABOVE_5_YEARS = 5;
   */
  ABOVE_5_YEARS = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.TradingExperience.
 */
export const TradingExperienceSchema: GenEnum<TradingExperience> = /*@__PURE__*/
  enumDesc(file_Common, 12);

