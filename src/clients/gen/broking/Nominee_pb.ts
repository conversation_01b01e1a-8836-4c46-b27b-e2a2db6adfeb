// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Nominee.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { AddressProto } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Nominee.proto.
 */
export const file_Nominee: GenFile = /*@__PURE__*/
  fileDesc("Cg1Ob21pbmVlLnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmciugIKDk5vbWluZWVEZXRhaWxzEgwKBG5hbWUYASABKAkSSAoRcmVsYXRpb25zaGlwX3R5cGUYAiABKA4yLS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuUmVsYXRpb25zaGlwVHlwZRILCgNkb2IYAyABKAkSFAoMcmVsYXRpb25zaGlwGAQgASgJEkYKEGd1YXJkaWFuX2RldGFpbHMYBSABKAsyLC5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuR3VhcmRpYW5EZXRhaWxzEjoKB2FkZHJlc3MYBiABKAsyKS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQWRkcmVzc1Byb3RvEh0KFWFsbG9jYXRpb25fcGVyY2VudGFnZRgHIAEoARIKCgJpZBgIIAEoCSK7AQoPR3VhcmRpYW5EZXRhaWxzEgwKBG5hbWUYASABKAkSSAoRcmVsYXRpb25zaGlwX3R5cGUYAiABKA4yLS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuUmVsYXRpb25zaGlwVHlwZRIUCgxyZWxhdGlvbnNoaXAYAyABKAkSOgoHYWRkcmVzcxgEIAEoCzIpLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5BZGRyZXNzUHJvdG8iagoRQWRkTm9taW5lZVJlcXVlc3QSDwoHb3B0X291dBgBIAEoCBJECg9ub21pbmVlX2RldGFpbHMYAiADKAsyKy5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuTm9taW5lZURldGFpbHMiOQoSQWRkTm9taW5lZVJlc3BvbnNlEg4KBnN0YXR1cxgBIAEoCBITCgtkZXNjcmlwdGlvbhgCIAEoCSITChFHZXROb21pbmVlUmVxdWVzdCJaChJHZXROb21pbmVlUmVzcG9uc2USRAoPbm9taW5lZV9kZXRhaWxzGAEgAygLMisuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLk5vbWluZWVEZXRhaWxzIlwKFFVwZGF0ZU5vbWluZWVSZXF1ZXN0EkQKD25vbWluZWVfZGV0YWlscxgCIAEoCzIrLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5Ob21pbmVlRGV0YWlscyqLAQoQUmVsYXRpb25zaGlwVHlwZRIYChRVTktOT1dOX1JFTEFUSU9OU0hJUBAAEgoKBkZBVEhFUhABEgoKBk1PVEhFUhACEgwKCERBVUdIVEVSEAMSBwoDU09OEAQSCgoGU1BPVVNFEAUSCQoFT1RIRVIQBxILCgdCUk9USEVSEAgSCgoGU0lTVEVSEAlCHwobY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nUAFiBnByb3RvMw", [file_Common]);

/**
 * @generated from message com.stablemoney.api.broking.NomineeDetails
 */
export type NomineeDetails = Message<"com.stablemoney.api.broking.NomineeDetails"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.broking.RelationshipType relationship_type = 2;
   */
  relationshipType: RelationshipType;

  /**
   * @generated from field: string dob = 3;
   */
  dob: string;

  /**
   * @generated from field: string relationship = 4;
   */
  relationship: string;

  /**
   * @generated from field: com.stablemoney.api.broking.GuardianDetails guardian_details = 5;
   */
  guardianDetails?: GuardianDetails;

  /**
   * @generated from field: com.stablemoney.api.broking.AddressProto address = 6;
   */
  address?: AddressProto;

  /**
   * @generated from field: double allocation_percentage = 7;
   */
  allocationPercentage: number;

  /**
   * @generated from field: string id = 8;
   */
  id: string;
};

/**
 * Describes the message com.stablemoney.api.broking.NomineeDetails.
 * Use `create(NomineeDetailsSchema)` to create a new message.
 */
export const NomineeDetailsSchema: GenMessage<NomineeDetails> = /*@__PURE__*/
  messageDesc(file_Nominee, 0);

/**
 * @generated from message com.stablemoney.api.broking.GuardianDetails
 */
export type GuardianDetails = Message<"com.stablemoney.api.broking.GuardianDetails"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.broking.RelationshipType relationship_type = 2;
   */
  relationshipType: RelationshipType;

  /**
   * @generated from field: string relationship = 3;
   */
  relationship: string;

  /**
   * @generated from field: com.stablemoney.api.broking.AddressProto address = 4;
   */
  address?: AddressProto;
};

/**
 * Describes the message com.stablemoney.api.broking.GuardianDetails.
 * Use `create(GuardianDetailsSchema)` to create a new message.
 */
export const GuardianDetailsSchema: GenMessage<GuardianDetails> = /*@__PURE__*/
  messageDesc(file_Nominee, 1);

/**
 * @generated from message com.stablemoney.api.broking.AddNomineeRequest
 */
export type AddNomineeRequest = Message<"com.stablemoney.api.broking.AddNomineeRequest"> & {
  /**
   * @generated from field: bool opt_out = 1;
   */
  optOut: boolean;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.NomineeDetails nominee_details = 2;
   */
  nomineeDetails: NomineeDetails[];
};

/**
 * Describes the message com.stablemoney.api.broking.AddNomineeRequest.
 * Use `create(AddNomineeRequestSchema)` to create a new message.
 */
export const AddNomineeRequestSchema: GenMessage<AddNomineeRequest> = /*@__PURE__*/
  messageDesc(file_Nominee, 2);

/**
 * @generated from message com.stablemoney.api.broking.AddNomineeResponse
 */
export type AddNomineeResponse = Message<"com.stablemoney.api.broking.AddNomineeResponse"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;

  /**
   * @generated from field: string description = 2;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AddNomineeResponse.
 * Use `create(AddNomineeResponseSchema)` to create a new message.
 */
export const AddNomineeResponseSchema: GenMessage<AddNomineeResponse> = /*@__PURE__*/
  messageDesc(file_Nominee, 3);

/**
 * @generated from message com.stablemoney.api.broking.GetNomineeRequest
 */
export type GetNomineeRequest = Message<"com.stablemoney.api.broking.GetNomineeRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.GetNomineeRequest.
 * Use `create(GetNomineeRequestSchema)` to create a new message.
 */
export const GetNomineeRequestSchema: GenMessage<GetNomineeRequest> = /*@__PURE__*/
  messageDesc(file_Nominee, 4);

/**
 * @generated from message com.stablemoney.api.broking.GetNomineeResponse
 */
export type GetNomineeResponse = Message<"com.stablemoney.api.broking.GetNomineeResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.NomineeDetails nominee_details = 1;
   */
  nomineeDetails: NomineeDetails[];
};

/**
 * Describes the message com.stablemoney.api.broking.GetNomineeResponse.
 * Use `create(GetNomineeResponseSchema)` to create a new message.
 */
export const GetNomineeResponseSchema: GenMessage<GetNomineeResponse> = /*@__PURE__*/
  messageDesc(file_Nominee, 5);

/**
 * @generated from message com.stablemoney.api.broking.UpdateNomineeRequest
 */
export type UpdateNomineeRequest = Message<"com.stablemoney.api.broking.UpdateNomineeRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.NomineeDetails nominee_details = 2;
   */
  nomineeDetails?: NomineeDetails;
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateNomineeRequest.
 * Use `create(UpdateNomineeRequestSchema)` to create a new message.
 */
export const UpdateNomineeRequestSchema: GenMessage<UpdateNomineeRequest> = /*@__PURE__*/
  messageDesc(file_Nominee, 6);

/**
 * @generated from enum com.stablemoney.api.broking.RelationshipType
 */
export enum RelationshipType {
  /**
   * @generated from enum value: UNKNOWN_RELATIONSHIP = 0;
   */
  UNKNOWN_RELATIONSHIP = 0,

  /**
   * @generated from enum value: FATHER = 1;
   */
  FATHER = 1,

  /**
   * @generated from enum value: MOTHER = 2;
   */
  MOTHER = 2,

  /**
   * @generated from enum value: DAUGHTER = 3;
   */
  DAUGHTER = 3,

  /**
   * @generated from enum value: SON = 4;
   */
  SON = 4,

  /**
   * @generated from enum value: SPOUSE = 5;
   */
  SPOUSE = 5,

  /**
   * @generated from enum value: OTHER = 7;
   */
  OTHER = 7,

  /**
   * @generated from enum value: BROTHER = 8;
   */
  BROTHER = 8,

  /**
   * @generated from enum value: SISTER = 9;
   */
  SISTER = 9,
}

/**
 * Describes the enum com.stablemoney.api.broking.RelationshipType.
 */
export const RelationshipTypeSchema: GenEnum<RelationshipType> = /*@__PURE__*/
  enumDesc(file_Nominee, 0);

