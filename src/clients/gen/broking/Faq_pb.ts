// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Faq.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Faq.proto.
 */
export const file_Faq: GenFile = /*@__PURE__*/
  fileDesc("CglGYXEucHJvdG8SG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZyJFCg9CYW5rRmFxUmVzcG9uc2USMgoIYmFua19mYXEYASADKAsyIC5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuRmFxIjwKA0ZhcRIQCghxdWVzdGlvbhgBIAEoCRIOCgZhbnN3ZXIYAiABKAkSEwoLaHRtbF9hbnN3ZXIYAyABKAkiSAoSU3VwcG9ydEZhcVJlc3BvbnNlEjIKCGJhbmtfZmFxGAEgAygLMiAuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkZhcSJbCgxTdXBwb3J0RmFxVjISLQoDZmFxGAEgASgLMiAuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkZhcRIcChRzdXBwb3J0X2ZhcV9pY29uX3VybBgDIAEoCSKWAQoUU3VwcG9ydEZhcVJlc3BvbnNlVjISEwoLY2F0ZWdvcnlfaWQYASABKAkSFQoNY2F0ZWdvcnlfbmFtZRgCIAEoCRIZChFjYXRlZ29yeV9pY29uX3VybBgDIAEoCRI3CgRmYXFzGAQgAygLMikuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLlN1cHBvcnRGYXFWMiKIAQocU3VwcG9ydEZhcUNhdGVnb3J5UmVzcG9uc2VWMhJPChRjYXRlZ29yeV9zdXBwb3J0X2ZhcRgBIAMoCzIxLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5TdXBwb3J0RmFxUmVzcG9uc2VWMhIXCg90b3BfY2F0ZWdvcnlfaWQYAiABKAkqeAoHRmFxVHlwZRIUChBVTktOT1dOX0ZBUV9UWVBFEAASFAoQREVGQVVMVF9GQVFfVFlQRRABEhMKD1JFV0FSRF9GQVFfVFlQRRACEhUKEVJFRkVSUkFMX0ZBUV9UWVBFEAMSFQoRQ0FURUdPUllfRkFRX1RZUEUQBEIfChtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmdQAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.broking.BankFaqResponse
 */
export type BankFaqResponse = Message<"com.stablemoney.api.broking.BankFaqResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.Faq bank_faq = 1;
   */
  bankFaq: Faq[];
};

/**
 * Describes the message com.stablemoney.api.broking.BankFaqResponse.
 * Use `create(BankFaqResponseSchema)` to create a new message.
 */
export const BankFaqResponseSchema: GenMessage<BankFaqResponse> = /*@__PURE__*/
  messageDesc(file_Faq, 0);

/**
 * @generated from message com.stablemoney.api.broking.Faq
 */
export type Faq = Message<"com.stablemoney.api.broking.Faq"> & {
  /**
   * @generated from field: string question = 1;
   */
  question: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;

  /**
   * @generated from field: string html_answer = 3;
   */
  htmlAnswer: string;
};

/**
 * Describes the message com.stablemoney.api.broking.Faq.
 * Use `create(FaqSchema)` to create a new message.
 */
export const FaqSchema: GenMessage<Faq> = /*@__PURE__*/
  messageDesc(file_Faq, 1);

/**
 * @generated from message com.stablemoney.api.broking.SupportFaqResponse
 */
export type SupportFaqResponse = Message<"com.stablemoney.api.broking.SupportFaqResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.Faq bank_faq = 1;
   */
  bankFaq: Faq[];
};

/**
 * Describes the message com.stablemoney.api.broking.SupportFaqResponse.
 * Use `create(SupportFaqResponseSchema)` to create a new message.
 */
export const SupportFaqResponseSchema: GenMessage<SupportFaqResponse> = /*@__PURE__*/
  messageDesc(file_Faq, 2);

/**
 * @generated from message com.stablemoney.api.broking.SupportFaqV2
 */
export type SupportFaqV2 = Message<"com.stablemoney.api.broking.SupportFaqV2"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.Faq faq = 1;
   */
  faq?: Faq;

  /**
   * @generated from field: string support_faq_icon_url = 3;
   */
  supportFaqIconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.broking.SupportFaqV2.
 * Use `create(SupportFaqV2Schema)` to create a new message.
 */
export const SupportFaqV2Schema: GenMessage<SupportFaqV2> = /*@__PURE__*/
  messageDesc(file_Faq, 3);

/**
 * @generated from message com.stablemoney.api.broking.SupportFaqResponseV2
 */
export type SupportFaqResponseV2 = Message<"com.stablemoney.api.broking.SupportFaqResponseV2"> & {
  /**
   * @generated from field: string category_id = 1;
   */
  categoryId: string;

  /**
   * @generated from field: string category_name = 2;
   */
  categoryName: string;

  /**
   * @generated from field: string category_icon_url = 3;
   */
  categoryIconUrl: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.SupportFaqV2 faqs = 4;
   */
  faqs: SupportFaqV2[];
};

/**
 * Describes the message com.stablemoney.api.broking.SupportFaqResponseV2.
 * Use `create(SupportFaqResponseV2Schema)` to create a new message.
 */
export const SupportFaqResponseV2Schema: GenMessage<SupportFaqResponseV2> = /*@__PURE__*/
  messageDesc(file_Faq, 4);

/**
 * @generated from message com.stablemoney.api.broking.SupportFaqCategoryResponseV2
 */
export type SupportFaqCategoryResponseV2 = Message<"com.stablemoney.api.broking.SupportFaqCategoryResponseV2"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.SupportFaqResponseV2 category_support_faq = 1;
   */
  categorySupportFaq: SupportFaqResponseV2[];

  /**
   * @generated from field: string top_category_id = 2;
   */
  topCategoryId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.SupportFaqCategoryResponseV2.
 * Use `create(SupportFaqCategoryResponseV2Schema)` to create a new message.
 */
export const SupportFaqCategoryResponseV2Schema: GenMessage<SupportFaqCategoryResponseV2> = /*@__PURE__*/
  messageDesc(file_Faq, 5);

/**
 * @generated from enum com.stablemoney.api.broking.FaqType
 */
export enum FaqType {
  /**
   * @generated from enum value: UNKNOWN_FAQ_TYPE = 0;
   */
  UNKNOWN_FAQ_TYPE = 0,

  /**
   * @generated from enum value: DEFAULT_FAQ_TYPE = 1;
   */
  DEFAULT_FAQ_TYPE = 1,

  /**
   * @generated from enum value: REWARD_FAQ_TYPE = 2;
   */
  REWARD_FAQ_TYPE = 2,

  /**
   * @generated from enum value: REFERRAL_FAQ_TYPE = 3;
   */
  REFERRAL_FAQ_TYPE = 3,

  /**
   * @generated from enum value: CATEGORY_FAQ_TYPE = 4;
   */
  CATEGORY_FAQ_TYPE = 4,
}

/**
 * Describes the enum com.stablemoney.api.broking.FaqType.
 */
export const FaqTypeSchema: GenEnum<FaqType> = /*@__PURE__*/
  enumDesc(file_Faq, 0);

