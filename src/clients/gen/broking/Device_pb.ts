// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Device.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Device.proto.
 */
export const file_Device: GenFile = /*@__PURE__*/
  fileDesc("CgxEZXZpY2UucHJvdG8SG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZyLEAQoKVXNlckRldmljZRIKCgJpZBgBIAEoCRI8CgtkZXZpY2VfdHlwZRgCIAEoDjInLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5EZXZpY2VUeXBlEhIKCm9zX3ZlcnNpb24YAyABKAkSDQoFbW9kZWwYBCABKAkSEwoLYXBwX3ZlcnNpb24YBSABKAkSGAoQbGFzdF9hY3RpdmVfdGltZRgGIAEoCRIaChJub3RpZmljYXRpb25fdG9rZW4YByABKAkiUwoTVXBkYXRlRGV2aWNlUmVxdWVzdBI8Cgt1c2VyX2RldmljZRgBIAEoCzInLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5Vc2VyRGV2aWNlIhYKFFVwZGF0ZURldmljZVJlc3BvbnNlKmUKCkRldmljZVR5cGUSFwoTQ0xJRU5UX1RZUEVfVU5LTk9XThAAEgsKB0FORFJPSUQQARIHCgNJT1MQAhIHCgNXRUIQAxIOCgpNT0JJTEVfV0VCEAQSDwoHQVBQX1dFQhAFGgIIASpOCg5EZXZpY2VQbGF0Zm9ybRIUChBQTEFURk9STV9VTktOT1dOEAASFAoQUExBVEZPUk1fRkxVVFRFUhABEhAKDFBMQVRGT1JNX1dFQhACQh8KG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZ1ABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.broking.UserDevice
 */
export type UserDevice = Message<"com.stablemoney.api.broking.UserDevice"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: com.stablemoney.api.broking.DeviceType device_type = 2;
   */
  deviceType: DeviceType;

  /**
   * @generated from field: string os_version = 3;
   */
  osVersion: string;

  /**
   * @generated from field: string model = 4;
   */
  model: string;

  /**
   * @generated from field: string app_version = 5;
   */
  appVersion: string;

  /**
   * @generated from field: string last_active_time = 6;
   */
  lastActiveTime: string;

  /**
   * @generated from field: string notification_token = 7;
   */
  notificationToken: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UserDevice.
 * Use `create(UserDeviceSchema)` to create a new message.
 */
export const UserDeviceSchema: GenMessage<UserDevice> = /*@__PURE__*/
  messageDesc(file_Device, 0);

/**
 * @generated from message com.stablemoney.api.broking.UpdateDeviceRequest
 */
export type UpdateDeviceRequest = Message<"com.stablemoney.api.broking.UpdateDeviceRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.UserDevice user_device = 1;
   */
  userDevice?: UserDevice;
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateDeviceRequest.
 * Use `create(UpdateDeviceRequestSchema)` to create a new message.
 */
export const UpdateDeviceRequestSchema: GenMessage<UpdateDeviceRequest> = /*@__PURE__*/
  messageDesc(file_Device, 1);

/**
 * @generated from message com.stablemoney.api.broking.UpdateDeviceResponse
 */
export type UpdateDeviceResponse = Message<"com.stablemoney.api.broking.UpdateDeviceResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateDeviceResponse.
 * Use `create(UpdateDeviceResponseSchema)` to create a new message.
 */
export const UpdateDeviceResponseSchema: GenMessage<UpdateDeviceResponse> = /*@__PURE__*/
  messageDesc(file_Device, 2);

/**
 * @generated from enum com.stablemoney.api.broking.DeviceType
 */
export enum DeviceType {
  /**
   * @generated from enum value: CLIENT_TYPE_UNKNOWN = 0;
   */
  CLIENT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: ANDROID = 1;
   */
  ANDROID = 1,

  /**
   * @generated from enum value: IOS = 2;
   */
  IOS = 2,

  /**
   * @generated from enum value: WEB = 3;
   */
  WEB = 3,

  /**
   * @generated from enum value: MOBILE_WEB = 4;
   */
  MOBILE_WEB = 4,

  /**
   * @generated from enum value: APP_WEB = 5 [deprecated = true];
   * @deprecated
   */
  APP_WEB = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.DeviceType.
 */
export const DeviceTypeSchema: GenEnum<DeviceType> = /*@__PURE__*/
  enumDesc(file_Device, 0);

/**
 * @generated from enum com.stablemoney.api.broking.DevicePlatform
 */
export enum DevicePlatform {
  /**
   * @generated from enum value: PLATFORM_UNKNOWN = 0;
   */
  PLATFORM_UNKNOWN = 0,

  /**
   * @generated from enum value: PLATFORM_FLUTTER = 1;
   */
  PLATFORM_FLUTTER = 1,

  /**
   * @generated from enum value: PLATFORM_WEB = 2;
   */
  PLATFORM_WEB = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.DevicePlatform.
 */
export const DevicePlatformSchema: GenEnum<DevicePlatform> = /*@__PURE__*/
  enumDesc(file_Device, 1);

