// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Cart.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import { file_Order } from "./Order_pb.js";
import { file_BondDetails } from "./BondDetails_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Cart.proto.
 */
export const file_Cart: GenFile = /*@__PURE__*/
  fileDesc("CgpDYXJ0LnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcimgEKEVVwZGF0ZUNhcnRSZXF1ZXN0EhsKDmJvbmRfZGV0YWlsX2lkGAEgASgJSACIAQESNwoGYWN0aW9uGAIgASgOMicuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkNhcnRBY3Rpb24SEgoFdW5pdHMYAyABKAVIAYgBAUIRCg9fYm9uZF9kZXRhaWxfaWRCCAoGX3VuaXRzIioKElVwZGF0ZUNhcnRSZXNwb25zZRIUCgxjYXJ0X2l0ZW1faWQYASABKAkiZAoIQ2FydEl0ZW0SFgoOYm9uZF9kZXRhaWxfaWQYASABKAkSEAoIcXVhbnRpdHkYAiABKAUSEAoIaW5fc3RvY2sYAyABKAgSHAoUb3V0X29mX3N0b2NrX21lc3NhZ2UYBCABKAkiSAoLQ2FydERldGFpbHMSOQoKY2FydF9pdGVtcxgBIAMoCzIlLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5DYXJ0SXRlbSJCChZBZGRDaGVja291dENhcnRSZXF1ZXN0EhYKDmJvbmRfZGV0YWlsX2lkGAEgASgJEhAKCHF1YW50aXR5GAIgASgFKkUKCkNhcnRBY3Rpb24SFwoTQ0FSVF9BQ1RJT05fVU5LTk9XThAAEgcKA0FERBABEgoKBlJFTU9WRRACEgkKBVJFU0VUEANCHwobY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nUAFiBnByb3RvMw", [file_Order, file_BondDetails]);

/**
 * @generated from message com.stablemoney.api.broking.UpdateCartRequest
 */
export type UpdateCartRequest = Message<"com.stablemoney.api.broking.UpdateCartRequest"> & {
  /**
   * @generated from field: optional string bond_detail_id = 1;
   */
  bondDetailId?: string;

  /**
   * @generated from field: com.stablemoney.api.broking.CartAction action = 2;
   */
  action: CartAction;

  /**
   * @generated from field: optional int32 units = 3;
   */
  units?: number;
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateCartRequest.
 * Use `create(UpdateCartRequestSchema)` to create a new message.
 */
export const UpdateCartRequestSchema: GenMessage<UpdateCartRequest> = /*@__PURE__*/
  messageDesc(file_Cart, 0);

/**
 * @generated from message com.stablemoney.api.broking.UpdateCartResponse
 */
export type UpdateCartResponse = Message<"com.stablemoney.api.broking.UpdateCartResponse"> & {
  /**
   * @generated from field: string cart_item_id = 1;
   */
  cartItemId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.UpdateCartResponse.
 * Use `create(UpdateCartResponseSchema)` to create a new message.
 */
export const UpdateCartResponseSchema: GenMessage<UpdateCartResponse> = /*@__PURE__*/
  messageDesc(file_Cart, 1);

/**
 * @generated from message com.stablemoney.api.broking.CartItem
 */
export type CartItem = Message<"com.stablemoney.api.broking.CartItem"> & {
  /**
   * @generated from field: string bond_detail_id = 1;
   */
  bondDetailId: string;

  /**
   * @generated from field: int32 quantity = 2;
   */
  quantity: number;

  /**
   * @generated from field: bool in_stock = 3;
   */
  inStock: boolean;

  /**
   * @generated from field: string out_of_stock_message = 4;
   */
  outOfStockMessage: string;
};

/**
 * Describes the message com.stablemoney.api.broking.CartItem.
 * Use `create(CartItemSchema)` to create a new message.
 */
export const CartItemSchema: GenMessage<CartItem> = /*@__PURE__*/
  messageDesc(file_Cart, 2);

/**
 * @generated from message com.stablemoney.api.broking.CartDetails
 */
export type CartDetails = Message<"com.stablemoney.api.broking.CartDetails"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.CartItem cart_items = 1;
   */
  cartItems: CartItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.CartDetails.
 * Use `create(CartDetailsSchema)` to create a new message.
 */
export const CartDetailsSchema: GenMessage<CartDetails> = /*@__PURE__*/
  messageDesc(file_Cart, 3);

/**
 * @generated from message com.stablemoney.api.broking.AddCheckoutCartRequest
 */
export type AddCheckoutCartRequest = Message<"com.stablemoney.api.broking.AddCheckoutCartRequest"> & {
  /**
   * @generated from field: string bond_detail_id = 1;
   */
  bondDetailId: string;

  /**
   * @generated from field: int32 quantity = 2;
   */
  quantity: number;
};

/**
 * Describes the message com.stablemoney.api.broking.AddCheckoutCartRequest.
 * Use `create(AddCheckoutCartRequestSchema)` to create a new message.
 */
export const AddCheckoutCartRequestSchema: GenMessage<AddCheckoutCartRequest> = /*@__PURE__*/
  messageDesc(file_Cart, 4);

/**
 * @generated from enum com.stablemoney.api.broking.CartAction
 */
export enum CartAction {
  /**
   * @generated from enum value: CART_ACTION_UNKNOWN = 0;
   */
  CART_ACTION_UNKNOWN = 0,

  /**
   * @generated from enum value: ADD = 1;
   */
  ADD = 1,

  /**
   * @generated from enum value: REMOVE = 2;
   */
  REMOVE = 2,

  /**
   * @generated from enum value: RESET = 3;
   */
  RESET = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.CartAction.
 */
export const CartActionSchema: GenEnum<CartAction> = /*@__PURE__*/
  enumDesc(file_Cart, 0);

