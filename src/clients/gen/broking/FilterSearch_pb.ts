// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file FilterSearch.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { PaginationRequest, TagConfig } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { InvestabilityStatus } from "./BondDetails_pb.js";
import { file_BondDetails } from "./BondDetails_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file FilterSearch.proto.
 */
export const file_FilterSearch: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Common, file_BondDetails]);

/**
 * @generated from message com.stablemoney.api.broking.RangeValue
 */
export type RangeValue = Message<"com.stablemoney.api.broking.RangeValue"> & {
  /**
   * @generated from field: double lower_bound = 1;
   */
  lowerBound: number;

  /**
   * @generated from field: double upper_bound = 2;
   */
  upperBound: number;
};

/**
 * Describes the message com.stablemoney.api.broking.RangeValue.
 * Use `create(RangeValueSchema)` to create a new message.
 */
export const RangeValueSchema: GenMessage<RangeValue> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 0);

/**
 * @generated from message com.stablemoney.api.broking.FilterOptionValue
 */
export type FilterOptionValue = Message<"com.stablemoney.api.broking.FilterOptionValue"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.FilterOptionValue.option_value
   */
  optionValue: {
    /**
     * @generated from field: string string_value = 1;
     */
    value: string;
    case: "stringValue";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.RangeValue range_value = 2;
     */
    value: RangeValue;
    case: "rangeValue";
  } | {
    /**
     * @generated from field: bool bool_value = 3;
     */
    value: boolean;
    case: "boolValue";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.FilterOptionValue.
 * Use `create(FilterOptionValueSchema)` to create a new message.
 */
export const FilterOptionValueSchema: GenMessage<FilterOptionValue> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 1);

/**
 * @generated from message com.stablemoney.api.broking.FilterOption
 */
export type FilterOption = Message<"com.stablemoney.api.broking.FilterOption"> & {
  /**
   * @generated from field: string label = 1;
   */
  label: string;

  /**
   * @generated from field: string icon = 2;
   */
  icon: string;

  /**
   * @generated from field: com.stablemoney.api.broking.FilterOptionValue option_value = 3;
   */
  optionValue?: FilterOptionValue;

  /**
   * @generated from field: bool is_selected = 4;
   */
  isSelected: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.FilterOption.
 * Use `create(FilterOptionSchema)` to create a new message.
 */
export const FilterOptionSchema: GenMessage<FilterOption> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 2);

/**
 * @generated from message com.stablemoney.api.broking.FilterItem
 */
export type FilterItem = Message<"com.stablemoney.api.broking.FilterItem"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.FilterType type = 1;
   */
  type: FilterType;

  /**
   * @generated from field: string label = 2;
   */
  label: string;

  /**
   * @generated from field: string short_label = 3;
   */
  shortLabel: string;

  /**
   * @generated from field: string key = 4;
   */
  key: string;

  /**
   * @generated from field: bool is_collapsible = 5;
   */
  isCollapsible: boolean;

  /**
   * @generated from field: bool is_collapsed = 6;
   */
  isCollapsed: boolean;

  /**
   * @generated from field: bool is_quick_filter = 7;
   */
  isQuickFilter: boolean;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.FilterOption options = 8;
   */
  options: FilterOption[];

  /**
   * @generated from field: optional string info = 9;
   */
  info?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.FilterItem.
 * Use `create(FilterItemSchema)` to create a new message.
 */
export const FilterItemSchema: GenMessage<FilterItem> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 3);

/**
 * @generated from message com.stablemoney.api.broking.FilterConfig
 */
export type FilterConfig = Message<"com.stablemoney.api.broking.FilterConfig"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.FilterItem items = 2;
   */
  items: FilterItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.FilterConfig.
 * Use `create(FilterConfigSchema)` to create a new message.
 */
export const FilterConfigSchema: GenMessage<FilterConfig> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 4);

/**
 * @generated from message com.stablemoney.api.broking.SortItem
 */
export type SortItem = Message<"com.stablemoney.api.broking.SortItem"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.SortType type = 1;
   */
  type: SortType;

  /**
   * @generated from field: string label = 2;
   */
  label: string;
};

/**
 * Describes the message com.stablemoney.api.broking.SortItem.
 * Use `create(SortItemSchema)` to create a new message.
 */
export const SortItemSchema: GenMessage<SortItem> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 5);

/**
 * @generated from message com.stablemoney.api.broking.SortConfig
 */
export type SortConfig = Message<"com.stablemoney.api.broking.SortConfig"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.SortItem items = 2;
   */
  items: SortItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.SortConfig.
 * Use `create(SortConfigSchema)` to create a new message.
 */
export const SortConfigSchema: GenMessage<SortConfig> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 6);

/**
 * @generated from message com.stablemoney.api.broking.FilterSortConfigResponse
 */
export type FilterSortConfigResponse = Message<"com.stablemoney.api.broking.FilterSortConfigResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.FilterConfig filter = 1;
   */
  filter?: FilterConfig;

  /**
   * @generated from field: com.stablemoney.api.broking.SortConfig sort = 2;
   */
  sort?: SortConfig;
};

/**
 * Describes the message com.stablemoney.api.broking.FilterSortConfigResponse.
 * Use `create(FilterSortConfigResponseSchema)` to create a new message.
 */
export const FilterSortConfigResponseSchema: GenMessage<FilterSortConfigResponse> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 7);

/**
 * @generated from message com.stablemoney.api.broking.FilterSortConfigRequest
 */
export type FilterSortConfigRequest = Message<"com.stablemoney.api.broking.FilterSortConfigRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.FilterSortConfigRequest.
 * Use `create(FilterSortConfigRequestSchema)` to create a new message.
 */
export const FilterSortConfigRequestSchema: GenMessage<FilterSortConfigRequest> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 8);

/**
 * @generated from message com.stablemoney.api.broking.BondItem
 */
export type BondItem = Message<"com.stablemoney.api.broking.BondItem"> & {
  /**
   * @generated from field: string tenure = 1;
   */
  tenure: string;

  /**
   * @generated from field: com.stablemoney.api.broking.TagConfig tag = 2;
   */
  tag?: TagConfig;

  /**
   * @generated from field: string logo_url = 3;
   */
  logoUrl: string;

  /**
   * @generated from field: string display_title = 4;
   */
  displayTitle: string;

  /**
   * @generated from field: double ytm = 5;
   */
  ytm: number;

  /**
   * @generated from field: string rating = 6;
   */
  rating: string;

  /**
   * @generated from field: string isin = 7;
   */
  isin: string;

  /**
   * @generated from field: string slug = 8;
   */
  slug: string;

  /**
   * @generated from field: com.stablemoney.api.broking.InvestabilityStatus investability_status = 9;
   */
  investabilityStatus: InvestabilityStatus;
};

/**
 * Describes the message com.stablemoney.api.broking.BondItem.
 * Use `create(BondItemSchema)` to create a new message.
 */
export const BondItemSchema: GenMessage<BondItem> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 9);

/**
 * @generated from message com.stablemoney.api.broking.EmptyState
 */
export type EmptyState = Message<"com.stablemoney.api.broking.EmptyState"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string sub_title = 2;
   */
  subTitle: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.FilterItem suggested_filters = 3;
   */
  suggestedFilters: FilterItem[];

  /**
   * @generated from field: string alternate_title = 4;
   */
  alternateTitle: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.BondItem alternate_items = 5;
   */
  alternateItems: BondItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.EmptyState.
 * Use `create(EmptyStateSchema)` to create a new message.
 */
export const EmptyStateSchema: GenMessage<EmptyState> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 10);

/**
 * @generated from message com.stablemoney.api.broking.FilterSortQueryResponse
 */
export type FilterSortQueryResponse = Message<"com.stablemoney.api.broking.FilterSortQueryResponse"> & {
  /**
   * @generated from field: int32 count = 1;
   */
  count: number;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.BondItem items = 2;
   */
  items: BondItem[];

  /**
   * @generated from field: map<int32, com.stablemoney.api.broking.FilterItem> suggested_filters = 3;
   */
  suggestedFilters: { [key: number]: FilterItem };

  /**
   * @generated from field: com.stablemoney.api.broking.EmptyState empty_state = 4;
   */
  emptyState?: EmptyState;
};

/**
 * Describes the message com.stablemoney.api.broking.FilterSortQueryResponse.
 * Use `create(FilterSortQueryResponseSchema)` to create a new message.
 */
export const FilterSortQueryResponseSchema: GenMessage<FilterSortQueryResponse> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 11);

/**
 * @generated from message com.stablemoney.api.broking.FilterSortQueryRequest
 */
export type FilterSortQueryRequest = Message<"com.stablemoney.api.broking.FilterSortQueryRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.FilterQuery filters = 1;
   */
  filters: FilterQuery[];

  /**
   * @generated from field: com.stablemoney.api.broking.SortType sort = 2;
   */
  sort: SortType;

  /**
   * @generated from field: com.stablemoney.api.broking.PaginationRequest pagination_request = 3;
   */
  paginationRequest?: PaginationRequest;
};

/**
 * Describes the message com.stablemoney.api.broking.FilterSortQueryRequest.
 * Use `create(FilterSortQueryRequestSchema)` to create a new message.
 */
export const FilterSortQueryRequestSchema: GenMessage<FilterSortQueryRequest> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 12);

/**
 * @generated from message com.stablemoney.api.broking.FilterQuery
 */
export type FilterQuery = Message<"com.stablemoney.api.broking.FilterQuery"> & {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.FilterOptionValue options = 2;
   */
  options: FilterOptionValue[];
};

/**
 * Describes the message com.stablemoney.api.broking.FilterQuery.
 * Use `create(FilterQuerySchema)` to create a new message.
 */
export const FilterQuerySchema: GenMessage<FilterQuery> = /*@__PURE__*/
  messageDesc(file_FilterSearch, 13);

/**
 * @generated from enum com.stablemoney.api.broking.FilterType
 */
export enum FilterType {
  /**
   * @generated from enum value: UNKNOWN_FILTER_TYPE = 0;
   */
  UNKNOWN_FILTER_TYPE = 0,

  /**
   * @generated from enum value: RANGE_MULTI_SELECT = 1;
   */
  RANGE_MULTI_SELECT = 1,

  /**
   * @generated from enum value: MULTI_SELECT_WITH_ICON = 2;
   */
  MULTI_SELECT_WITH_ICON = 2,

  /**
   * @generated from enum value: MULTI_SELECT_PILLS = 3;
   */
  MULTI_SELECT_PILLS = 3,

  /**
   * @generated from enum value: BOOLEAN_SELECT = 4;
   */
  BOOLEAN_SELECT = 4,

  /**
   * @generated from enum value: MULTI_SELECT = 5;
   */
  MULTI_SELECT = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.FilterType.
 */
export const FilterTypeSchema: GenEnum<FilterType> = /*@__PURE__*/
  enumDesc(file_FilterSearch, 0);

/**
 * @generated from enum com.stablemoney.api.broking.OptionLogic
 */
export enum OptionLogic {
  /**
   * @generated from enum value: AND = 0;
   */
  AND = 0,

  /**
   * @generated from enum value: OR = 1;
   */
  OR = 1,
}

/**
 * Describes the enum com.stablemoney.api.broking.OptionLogic.
 */
export const OptionLogicSchema: GenEnum<OptionLogic> = /*@__PURE__*/
  enumDesc(file_FilterSearch, 1);

/**
 * @generated from enum com.stablemoney.api.broking.SortType
 */
export enum SortType {
  /**
   * @generated from enum value: UNKNOWN_SORT_TYPE = 0;
   */
  UNKNOWN_SORT_TYPE = 0,

  /**
   * @generated from enum value: YTM_HIGH_TO_LOW = 1;
   */
  YTM_HIGH_TO_LOW = 1,

  /**
   * @generated from enum value: TENURE_HIGH_TO_LOW = 2;
   */
  TENURE_HIGH_TO_LOW = 2,

  /**
   * @generated from enum value: TENURE_LOW_TO_HIGH = 3;
   */
  TENURE_LOW_TO_HIGH = 3,

  /**
   * @generated from enum value: RATING_HIGH_TO_LOW = 4;
   */
  RATING_HIGH_TO_LOW = 4,

  /**
   * @generated from enum value: POPULARITY_HIGH_TO_LOW = 5;
   */
  POPULARITY_HIGH_TO_LOW = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.SortType.
 */
export const SortTypeSchema: GenEnum<SortType> = /*@__PURE__*/
  enumDesc(file_FilterSearch, 2);

