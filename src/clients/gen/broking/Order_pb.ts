// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Order.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { InterestPaymentSchedule } from "./BondDetails_pb.js";
import { file_BondDetails } from "./BondDetails_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Order.proto.
 */
export const file_Order: GenFile = /*@__PURE__*/
  fileDesc("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", [file_BondDetails]);

/**
 * @generated from message com.stablemoney.api.broking.PlaceOrderRequest
 * @deprecated
 */
export type PlaceOrderRequest = Message<"com.stablemoney.api.broking.PlaceOrderRequest"> & {
  /**
   * @generated from field: string bond_offering_details_id = 1;
   */
  bondOfferingDetailsId: string;

  /**
   * @generated from field: int32 quantity = 2;
   */
  quantity: number;
};

/**
 * Describes the message com.stablemoney.api.broking.PlaceOrderRequest.
 * Use `create(PlaceOrderRequestSchema)` to create a new message.
 * @deprecated
 */
export const PlaceOrderRequestSchema: GenMessage<PlaceOrderRequest> = /*@__PURE__*/
  messageDesc(file_Order, 0);

/**
 * @generated from message com.stablemoney.api.broking.PlaceOrderRequestV2
 */
export type PlaceOrderRequestV2 = Message<"com.stablemoney.api.broking.PlaceOrderRequestV2"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.PlaceOrderRequestItem place_order_request_items = 1;
   */
  placeOrderRequestItems: PlaceOrderRequestItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.PlaceOrderRequestV2.
 * Use `create(PlaceOrderRequestV2Schema)` to create a new message.
 */
export const PlaceOrderRequestV2Schema: GenMessage<PlaceOrderRequestV2> = /*@__PURE__*/
  messageDesc(file_Order, 1);

/**
 * @generated from message com.stablemoney.api.broking.PlaceOrderRequestItem
 */
export type PlaceOrderRequestItem = Message<"com.stablemoney.api.broking.PlaceOrderRequestItem"> & {
  /**
   * @generated from field: string bond_detail_id = 1;
   */
  bondDetailId: string;

  /**
   * @generated from field: int32 quantity = 2;
   */
  quantity: number;
};

/**
 * Describes the message com.stablemoney.api.broking.PlaceOrderRequestItem.
 * Use `create(PlaceOrderRequestItemSchema)` to create a new message.
 */
export const PlaceOrderRequestItemSchema: GenMessage<PlaceOrderRequestItem> = /*@__PURE__*/
  messageDesc(file_Order, 2);

/**
 * @generated from message com.stablemoney.api.broking.StatusNode
 */
export type StatusNode = Message<"com.stablemoney.api.broking.StatusNode"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;

  /**
   * @generated from field: string date_time = 2;
   */
  dateTime: string;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.StatusNode.
 * Use `create(StatusNodeSchema)` to create a new message.
 */
export const StatusNodeSchema: GenMessage<StatusNode> = /*@__PURE__*/
  messageDesc(file_Order, 3);

/**
 * @generated from message com.stablemoney.api.broking.OrderData
 */
export type OrderData = Message<"com.stablemoney.api.broking.OrderData"> & {
  /**
   * order level
   *
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * item level
   *
   * @generated from field: string bond_name = 2;
   */
  bondName: string;

  /**
   * item level
   *
   * @generated from field: string bond_issuer_logo = 3;
   */
  bondIssuerLogo: string;

  /**
   * item level
   *
   * @generated from field: int32 quantity = 4;
   */
  quantity: number;

  /**
   * order level
   *
   * @generated from field: double total_consideration = 5;
   */
  totalConsideration: number;

  /**
   * order level
   *
   * @generated from field: string status = 6;
   */
  status: string;

  /**
   * order level
   *
   * @generated from field: string order_type = 7;
   */
  orderType: string;

  /**
   * item level
   *
   * @generated from field: string bond_offering_id = 8;
   */
  bondOfferingId: string;

  /**
   * item level
   *
   * @generated from field: double ytm = 9;
   */
  ytm: number;

  /**
   * item level
   *
   * @generated from field: string bond_issuer_name = 10;
   */
  bondIssuerName: string;

  /**
   * item level
   *
   * @generated from field: string tenure = 11;
   */
  tenure: string;

  /**
   * item level
   *
   * @generated from field: string expected_returns = 12;
   */
  expectedReturns: string;

  /**
   * order  level
   *
   * @generated from field: bool is_order_settled = 13;
   */
  isOrderSettled: boolean;

  /**
   * item level
   *
   * @generated from field: com.stablemoney.api.broking.StatusNode order_placed = 14;
   */
  orderPlaced?: StatusNode;

  /**
   * item level
   *
   * @generated from field: com.stablemoney.api.broking.StatusNode payment = 15;
   */
  payment?: StatusNode;

  /**
   * item level
   *
   * @generated from field: com.stablemoney.api.broking.StatusNode settlement = 16;
   */
  settlement?: StatusNode;

  /**
   * item level
   *
   * @generated from field: string order_receipt_url = 17;
   */
  orderReceiptUrl: string;

  /**
   * item level
   *
   * @generated from field: string deal_sheet_url = 18;
   */
  dealSheetUrl: string;

  /**
   * NSE payment url, item level
   *
   * @generated from field: string payment_url = 19;
   */
  paymentUrl: string;

  /**
   * NSE order number, item level
   *
   * @generated from field: string order_number = 20;
   */
  orderNumber: string;

  /**
   * order level
   *
   * @generated from field: string created_at = 21;
   */
  createdAt: string;

  /**
   * item level
   *
   * @generated from field: repeated com.stablemoney.api.broking.InterestPaymentSchedule repayment_schedule = 22;
   */
  repaymentSchedule: InterestPaymentSchedule[];

  /**
   * order level
   *
   * @generated from field: string payment_gateway = 23;
   */
  paymentGateway: string;

  /**
   * item level
   *
   * @generated from field: com.stablemoney.api.broking.StatusNode trade_placed = 24;
   */
  tradePlaced?: StatusNode;

  /**
   * dd-mm-yyyy
   *
   * @generated from field: string maturity_date = 25;
   */
  maturityDate: string;

  /**
   * item level
   *
   * @generated from field: string heading = 26;
   */
  heading: string;

  /**
   * item level
   *
   * @generated from field: bool is_bond_investable = 27;
   */
  isBondInvestable: boolean;

  /**
   * dd-mm-yyyy
   *
   * @generated from field: string settlement_date = 28;
   */
  settlementDate: string;

  /**
   * item level
   *
   * @generated from field: optional string bond_issuer_id = 29;
   */
  bondIssuerId?: string;

  /**
   * item level
   *
   * @generated from field: optional string bond_issuer_color = 30;
   */
  bondIssuerColor?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.OrderData.
 * Use `create(OrderDataSchema)` to create a new message.
 */
export const OrderDataSchema: GenMessage<OrderData> = /*@__PURE__*/
  messageDesc(file_Order, 4);

/**
 * @generated from message com.stablemoney.api.broking.OrderDataV2
 */
export type OrderDataV2 = Message<"com.stablemoney.api.broking.OrderDataV2"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: double total_consideration = 2;
   */
  totalConsideration: number;

  /**
   * cumilative state of each item
   *
   * @generated from field: string status = 3;
   */
  status: string;

  /**
   * buy / sell
   *
   * @generated from field: string order_type = 4;
   */
  orderType: string;

  /**
   * cumulative settlement state of each item
   *
   * @generated from field: bool is_order_settled = 5;
   */
  isOrderSettled: boolean;

  /**
   * @generated from field: string payment_gateway = 6;
   */
  paymentGateway: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.OrderItemData order_item_data = 7;
   */
  orderItemData: OrderItemData[];
};

/**
 * Describes the message com.stablemoney.api.broking.OrderDataV2.
 * Use `create(OrderDataV2Schema)` to create a new message.
 */
export const OrderDataV2Schema: GenMessage<OrderDataV2> = /*@__PURE__*/
  messageDesc(file_Order, 5);

/**
 * @generated from message com.stablemoney.api.broking.OrderItemData
 */
export type OrderItemData = Message<"com.stablemoney.api.broking.OrderItemData"> & {
  /**
   * @generated from field: string bond_name = 1;
   */
  bondName: string;

  /**
   * @generated from field: string status = 2;
   */
  status: string;

  /**
   * @generated from field: string bond_issuer_logo = 3;
   */
  bondIssuerLogo: string;

  /**
   * @generated from field: int32 quantity = 4;
   */
  quantity: number;

  /**
   * @generated from field: string bond_offering_id = 5;
   */
  bondOfferingId: string;

  /**
   * @generated from field: double ytm = 6;
   */
  ytm: number;

  /**
   * @generated from field: string bond_issuer_name = 7;
   */
  bondIssuerName: string;

  /**
   * @generated from field: string tenure = 8;
   */
  tenure: string;

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode order_placed = 9;
   */
  orderPlaced?: StatusNode;

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode payment = 10;
   */
  payment?: StatusNode;

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode settlement = 11;
   */
  settlement?: StatusNode;

  /**
   * @generated from field: string expected_returns = 12;
   */
  expectedReturns: string;

  /**
   * @generated from field: string order_receipt_url = 13;
   */
  orderReceiptUrl: string;

  /**
   * @generated from field: string deal_sheet_url = 14;
   */
  dealSheetUrl: string;

  /**
   * NSE order number
   *
   * @generated from field: string order_number = 15;
   */
  orderNumber: string;

  /**
   * @generated from field: string created_at = 16;
   */
  createdAt: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.InterestPaymentSchedule repayment_schedule = 17;
   */
  repaymentSchedule: InterestPaymentSchedule[];

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode trade_placed = 18;
   */
  tradePlaced?: StatusNode;

  /**
   * @generated from field: string heading = 19;
   */
  heading: string;

  /**
   * @generated from field: bool is_bond_investable = 20;
   */
  isBondInvestable: boolean;

  /**
   * @generated from field: string bond_issuer_id = 21;
   */
  bondIssuerId: string;

  /**
   * @generated from field: string bond_issuer_color = 22;
   */
  bondIssuerColor: string;

  /**
   * @generated from field: bool is_settled = 23;
   */
  isSettled: boolean;

  /**
   * @generated from field: string maturity_date = 24;
   */
  maturityDate: string;

  /**
   * @generated from field: string settlement_date = 25;
   */
  settlementDate: string;
};

/**
 * Describes the message com.stablemoney.api.broking.OrderItemData.
 * Use `create(OrderItemDataSchema)` to create a new message.
 */
export const OrderItemDataSchema: GenMessage<OrderItemData> = /*@__PURE__*/
  messageDesc(file_Order, 6);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentSummary
 */
export type InvestmentSummary = Message<"com.stablemoney.api.broking.InvestmentSummary"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.OrderData order_data = 1;
   */
  orderData?: OrderData;

  /**
   * @generated from field: com.stablemoney.api.broking.InterestPayoutDetails interest_payout_details = 2;
   */
  interestPayoutDetails?: InterestPayoutDetails;
};

/**
 * Describes the message com.stablemoney.api.broking.InvestmentSummary.
 * Use `create(InvestmentSummarySchema)` to create a new message.
 */
export const InvestmentSummarySchema: GenMessage<InvestmentSummary> = /*@__PURE__*/
  messageDesc(file_Order, 7);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentSummariesRequest
 */
export type InvestmentSummariesRequest = Message<"com.stablemoney.api.broking.InvestmentSummariesRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InvestmentSummariesRequest.
 * Use `create(InvestmentSummariesRequestSchema)` to create a new message.
 */
export const InvestmentSummariesRequestSchema: GenMessage<InvestmentSummariesRequest> = /*@__PURE__*/
  messageDesc(file_Order, 8);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentSummariesResponse
 */
export type InvestmentSummariesResponse = Message<"com.stablemoney.api.broking.InvestmentSummariesResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.InvestmentSummary investment_summaries = 1;
   */
  investmentSummaries: InvestmentSummary[];
};

/**
 * Describes the message com.stablemoney.api.broking.InvestmentSummariesResponse.
 * Use `create(InvestmentSummariesResponseSchema)` to create a new message.
 */
export const InvestmentSummariesResponseSchema: GenMessage<InvestmentSummariesResponse> = /*@__PURE__*/
  messageDesc(file_Order, 9);

/**
 * @generated from message com.stablemoney.api.broking.PlaceOrderResponse
 */
export type PlaceOrderResponse = Message<"com.stablemoney.api.broking.PlaceOrderResponse"> & {
  /**
   * @generated from field: string order_id = 1;
   */
  orderId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.OrderData order = 2;
   */
  order?: OrderData;

  /**
   * @generated from field: string payment_gateway = 4;
   */
  paymentGateway: string;
};

/**
 * Describes the message com.stablemoney.api.broking.PlaceOrderResponse.
 * Use `create(PlaceOrderResponseSchema)` to create a new message.
 */
export const PlaceOrderResponseSchema: GenMessage<PlaceOrderResponse> = /*@__PURE__*/
  messageDesc(file_Order, 10);

/**
 * @generated from message com.stablemoney.api.broking.PlaceOrderResponseV2
 */
export type PlaceOrderResponseV2 = Message<"com.stablemoney.api.broking.PlaceOrderResponseV2"> & {
  /**
   * @generated from field: string order_id = 1;
   */
  orderId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.OrderDataV2 order_data = 2;
   */
  orderData?: OrderDataV2;

  /**
   * @generated from field: string payment_gateway = 4;
   */
  paymentGateway: string;

  /**
   * @generated from field: optional bool is_kyc_done = 5;
   */
  isKycDone?: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.PlaceOrderResponseV2.
 * Use `create(PlaceOrderResponseV2Schema)` to create a new message.
 */
export const PlaceOrderResponseV2Schema: GenMessage<PlaceOrderResponseV2> = /*@__PURE__*/
  messageDesc(file_Order, 11);

/**
 * @generated from message com.stablemoney.api.broking.PendingOrderItem
 */
export type PendingOrderItem = Message<"com.stablemoney.api.broking.PendingOrderItem"> & {
  /**
   * @generated from field: string order_id = 1;
   */
  orderId: string;

  /**
   * @generated from field: double amount = 2;
   */
  amount: number;

  /**
   * @generated from field: int32 quantity = 3;
   */
  quantity: number;

  /**
   * @generated from field: string created_at = 4;
   */
  createdAt: string;

  /**
   * @generated from field: string expire_by = 5;
   */
  expireBy: string;

  /**
   * @generated from field: string bond_id = 6;
   */
  bondId: string;

  /**
   * @generated from field: string bond_logo = 7;
   */
  bondLogo: string;

  /**
   * @generated from field: string bond_name = 8;
   */
  bondName: string;

  /**
   * @generated from field: string payment_gateway = 9;
   */
  paymentGateway: string;
};

/**
 * Describes the message com.stablemoney.api.broking.PendingOrderItem.
 * Use `create(PendingOrderItemSchema)` to create a new message.
 */
export const PendingOrderItemSchema: GenMessage<PendingOrderItem> = /*@__PURE__*/
  messageDesc(file_Order, 12);

/**
 * @generated from message com.stablemoney.api.broking.PendingOrdersResponse
 */
export type PendingOrdersResponse = Message<"com.stablemoney.api.broking.PendingOrdersResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.PendingOrderItem items = 1;
   */
  items: PendingOrderItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.PendingOrdersResponse.
 * Use `create(PendingOrdersResponseSchema)` to create a new message.
 */
export const PendingOrdersResponseSchema: GenMessage<PendingOrdersResponse> = /*@__PURE__*/
  messageDesc(file_Order, 13);

/**
 * @generated from message com.stablemoney.api.broking.ProcessPaymentRequest
 */
export type ProcessPaymentRequest = Message<"com.stablemoney.api.broking.ProcessPaymentRequest"> & {
  /**
   * @generated from field: string order_id = 1;
   */
  orderId: string;

  /**
   * @generated from field: optional com.stablemoney.api.broking.PaymentType payment_type = 2;
   */
  paymentType?: PaymentType;

  /**
   * @generated from field: optional string upi_id = 3;
   */
  upiId?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.ProcessPaymentRequest.
 * Use `create(ProcessPaymentRequestSchema)` to create a new message.
 */
export const ProcessPaymentRequestSchema: GenMessage<ProcessPaymentRequest> = /*@__PURE__*/
  messageDesc(file_Order, 14);

/**
 * @generated from message com.stablemoney.api.broking.ProcessPaymentResponse
 */
export type ProcessPaymentResponse = Message<"com.stablemoney.api.broking.ProcessPaymentResponse"> & {
  /**
   * @generated from field: string status = 1;
   */
  status: string;

  /**
   * @generated from field: string payment_url = 2;
   */
  paymentUrl: string;

  /**
   * @generated from field: string upi_id = 3;
   */
  upiId: string;

  /**
   * @generated from field: string order_id = 4;
   */
  orderId: string;

  /**
   * @generated from field: double payable_amount = 5;
   */
  payableAmount: number;

  /**
   * @generated from field: string payment_session_id = 6;
   */
  paymentSessionId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.ProcessPaymentResponse.
 * Use `create(ProcessPaymentResponseSchema)` to create a new message.
 */
export const ProcessPaymentResponseSchema: GenMessage<ProcessPaymentResponse> = /*@__PURE__*/
  messageDesc(file_Order, 15);

/**
 * @generated from message com.stablemoney.api.broking.PaymentStatusResponse
 */
export type PaymentStatusResponse = Message<"com.stablemoney.api.broking.PaymentStatusResponse"> & {
  /**
   * @generated from field: string order_id = 1;
   */
  orderId: string;

  /**
   * @generated from field: string status = 2;
   */
  status: string;

  /**
   * @generated from field: string bond_name = 3;
   */
  bondName: string;

  /**
   * @generated from field: int32 quantity = 4;
   */
  quantity: number;

  /**
   * @generated from field: double payable_amount = 5;
   */
  payableAmount: number;

  /**
   * @generated from field: string payment_time = 6;
   */
  paymentTime: string;

  /**
   * @generated from field: string settlement_date = 7;
   */
  settlementDate: string;
};

/**
 * Describes the message com.stablemoney.api.broking.PaymentStatusResponse.
 * Use `create(PaymentStatusResponseSchema)` to create a new message.
 */
export const PaymentStatusResponseSchema: GenMessage<PaymentStatusResponse> = /*@__PURE__*/
  messageDesc(file_Order, 16);

/**
 * @generated from message com.stablemoney.api.broking.OrdersResponse
 */
export type OrdersResponse = Message<"com.stablemoney.api.broking.OrdersResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.OrderData orders = 1;
   */
  orders: OrderData[];
};

/**
 * Describes the message com.stablemoney.api.broking.OrdersResponse.
 * Use `create(OrdersResponseSchema)` to create a new message.
 */
export const OrdersResponseSchema: GenMessage<OrdersResponse> = /*@__PURE__*/
  messageDesc(file_Order, 17);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentDetailsRequest
 */
export type InvestmentDetailsRequest = Message<"com.stablemoney.api.broking.InvestmentDetailsRequest"> & {
  /**
   * @generated from field: string order_id = 1;
   */
  orderId: string;

  /**
   * @generated from field: string user_id = 2;
   */
  userId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InvestmentDetailsRequest.
 * Use `create(InvestmentDetailsRequestSchema)` to create a new message.
 */
export const InvestmentDetailsRequestSchema: GenMessage<InvestmentDetailsRequest> = /*@__PURE__*/
  messageDesc(file_Order, 18);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentDetailsResponse
 */
export type InvestmentDetailsResponse = Message<"com.stablemoney.api.broking.InvestmentDetailsResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.OrderData orderData = 1;
   */
  orderData?: OrderData;

  /**
   * @generated from field: optional com.stablemoney.api.broking.InvestmentNomineeDetails nominee_details = 2;
   */
  nomineeDetails?: InvestmentNomineeDetails;

  /**
   * @generated from field: optional com.stablemoney.api.broking.WithdrawalBankAccountDetails withdrawal_bank_account_details = 3;
   */
  withdrawalBankAccountDetails?: WithdrawalBankAccountDetails;

  /**
   * @generated from field: optional com.stablemoney.api.broking.InterestPayoutDetails interest_payout_details = 4;
   */
  interestPayoutDetails?: InterestPayoutDetails;

  /**
   * @generated from field: optional com.stablemoney.api.broking.DematAccountDetails demat_account_details = 5;
   */
  dematAccountDetails?: DematAccountDetails;
};

/**
 * Describes the message com.stablemoney.api.broking.InvestmentDetailsResponse.
 * Use `create(InvestmentDetailsResponseSchema)` to create a new message.
 */
export const InvestmentDetailsResponseSchema: GenMessage<InvestmentDetailsResponse> = /*@__PURE__*/
  messageDesc(file_Order, 19);

/**
 * @generated from message com.stablemoney.api.broking.BondsNetworthSummaryResponse
 */
export type BondsNetworthSummaryResponse = Message<"com.stablemoney.api.broking.BondsNetworthSummaryResponse"> & {
  /**
   * @generated from field: double total_investment = 1;
   */
  totalInvestment: number;

  /**
   * @generated from field: double current_gains = 2;
   */
  currentGains: number;

  /**
   * @generated from field: int32 number_of_orders = 3;
   */
  numberOfOrders: number;

  /**
   * @generated from field: int32 number_of_matured_payments = 4;
   */
  numberOfMaturedPayments: number;

  /**
   * @generated from field: double total_amount_for_matured_payments = 5;
   */
  totalAmountForMaturedPayments: number;
};

/**
 * Describes the message com.stablemoney.api.broking.BondsNetworthSummaryResponse.
 * Use `create(BondsNetworthSummaryResponseSchema)` to create a new message.
 */
export const BondsNetworthSummaryResponseSchema: GenMessage<BondsNetworthSummaryResponse> = /*@__PURE__*/
  messageDesc(file_Order, 20);

/**
 * @generated from message com.stablemoney.api.broking.UserNetworthResponse
 */
export type UserNetworthResponse = Message<"com.stablemoney.api.broking.UserNetworthResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: double total_investment = 2;
   */
  totalInvestment: number;

  /**
   * @generated from field: double current_gains = 3;
   */
  currentGains: number;
};

/**
 * Describes the message com.stablemoney.api.broking.UserNetworthResponse.
 * Use `create(UserNetworthResponseSchema)` to create a new message.
 */
export const UserNetworthResponseSchema: GenMessage<UserNetworthResponse> = /*@__PURE__*/
  messageDesc(file_Order, 21);

/**
 * @generated from message com.stablemoney.api.broking.AllUserNetworthResponse
 */
export type AllUserNetworthResponse = Message<"com.stablemoney.api.broking.AllUserNetworthResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.UserNetworthResponse user_net_worth = 1;
   */
  userNetWorth: UserNetworthResponse[];
};

/**
 * Describes the message com.stablemoney.api.broking.AllUserNetworthResponse.
 * Use `create(AllUserNetworthResponseSchema)` to create a new message.
 */
export const AllUserNetworthResponseSchema: GenMessage<AllUserNetworthResponse> = /*@__PURE__*/
  messageDesc(file_Order, 22);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentNomineeDetails
 */
export type InvestmentNomineeDetails = Message<"com.stablemoney.api.broking.InvestmentNomineeDetails"> & {
  /**
   * @generated from field: string nominee_name = 1;
   */
  nomineeName: string;

  /**
   * @generated from field: string nominee_relation = 2;
   */
  nomineeRelation: string;

  /**
   * @generated from field: string heading = 3;
   */
  heading: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InvestmentNomineeDetails.
 * Use `create(InvestmentNomineeDetailsSchema)` to create a new message.
 */
export const InvestmentNomineeDetailsSchema: GenMessage<InvestmentNomineeDetails> = /*@__PURE__*/
  messageDesc(file_Order, 23);

/**
 * @generated from message com.stablemoney.api.broking.WithdrawalBankAccountDetails
 */
export type WithdrawalBankAccountDetails = Message<"com.stablemoney.api.broking.WithdrawalBankAccountDetails"> & {
  /**
   * @generated from field: string bank_name = 1;
   */
  bankName: string;

  /**
   * @generated from field: string account_number = 2;
   */
  accountNumber: string;

  /**
   * @generated from field: string ifsc_code = 3;
   */
  ifscCode: string;

  /**
   * @generated from field: string heading = 4;
   */
  heading: string;
};

/**
 * Describes the message com.stablemoney.api.broking.WithdrawalBankAccountDetails.
 * Use `create(WithdrawalBankAccountDetailsSchema)` to create a new message.
 */
export const WithdrawalBankAccountDetailsSchema: GenMessage<WithdrawalBankAccountDetails> = /*@__PURE__*/
  messageDesc(file_Order, 24);

/**
 * @generated from message com.stablemoney.api.broking.InterestPayoutDetails
 */
export type InterestPayoutDetails = Message<"com.stablemoney.api.broking.InterestPayoutDetails"> & {
  /**
   * @generated from field: double realised_gains = 1;
   */
  realisedGains: number;

  /**
   * @generated from field: double unrealised_gains = 2;
   */
  unrealisedGains: number;

  /**
   * dd-mm-yyyy
   *
   * @generated from field: string next_payout_date = 3;
   */
  nextPayoutDate: string;

  /**
   * @generated from field: string heading = 4;
   */
  heading: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InterestPayoutDetails.
 * Use `create(InterestPayoutDetailsSchema)` to create a new message.
 */
export const InterestPayoutDetailsSchema: GenMessage<InterestPayoutDetails> = /*@__PURE__*/
  messageDesc(file_Order, 25);

/**
 * @generated from message com.stablemoney.api.broking.DematAccountDetails
 */
export type DematAccountDetails = Message<"com.stablemoney.api.broking.DematAccountDetails"> & {
  /**
   * @generated from field: string demat_number = 1;
   */
  dematNumber: string;

  /**
   * @generated from field: string demat_provider = 2;
   */
  dematProvider: string;

  /**
   * @generated from field: string heading = 3;
   */
  heading: string;
};

/**
 * Describes the message com.stablemoney.api.broking.DematAccountDetails.
 * Use `create(DematAccountDetailsSchema)` to create a new message.
 */
export const DematAccountDetailsSchema: GenMessage<DematAccountDetails> = /*@__PURE__*/
  messageDesc(file_Order, 26);

/**
 * @generated from message com.stablemoney.api.broking.Filled15GFormFile
 */
export type Filled15GFormFile = Message<"com.stablemoney.api.broking.Filled15GFormFile"> & {
  /**
   * @generated from field: string file_name = 1;
   */
  fileName: string;

  /**
   * @generated from field: bytes file_content = 2;
   */
  fileContent: Uint8Array;

  /**
   * @generated from field: string content_type = 3;
   */
  contentType: string;
};

/**
 * Describes the message com.stablemoney.api.broking.Filled15GFormFile.
 * Use `create(Filled15GFormFileSchema)` to create a new message.
 */
export const Filled15GFormFileSchema: GenMessage<Filled15GFormFile> = /*@__PURE__*/
  messageDesc(file_Order, 27);

/**
 * @generated from message com.stablemoney.api.broking.Filled15GFormList
 */
export type Filled15GFormList = Message<"com.stablemoney.api.broking.Filled15GFormList"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.Filled15GFormFile forms = 1;
   */
  forms: Filled15GFormFile[];
};

/**
 * Describes the message com.stablemoney.api.broking.Filled15GFormList.
 * Use `create(Filled15GFormListSchema)` to create a new message.
 */
export const Filled15GFormListSchema: GenMessage<Filled15GFormList> = /*@__PURE__*/
  messageDesc(file_Order, 28);

/**
 * @generated from enum com.stablemoney.api.broking.PaymentType
 */
export enum PaymentType {
  /**
   * @generated from enum value: UNKNOWN_PAYMENT_TYPE = 0;
   */
  UNKNOWN_PAYMENT_TYPE = 0,

  /**
   * @generated from enum value: UPI = 1;
   */
  UPI = 1,

  /**
   * @generated from enum value: NET_BANKING = 2;
   */
  NET_BANKING = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.PaymentType.
 */
export const PaymentTypeSchema: GenEnum<PaymentType> = /*@__PURE__*/
  enumDesc(file_Order, 0);

