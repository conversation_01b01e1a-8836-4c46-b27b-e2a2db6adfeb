// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Kyc.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { AddCurrentAddressRequest, AddCurrentAddressResponse, UpdateProfileRequest, UpdateProfileResponse, UserLifetimeStatus, UserLifetimeStatusResponse_KycStatus } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { InitiateBankVerificationRequest, InitiateBankVerificationResponse } from "./BankAccountVerification_pb.js";
import { file_BankAccountVerification } from "./BankAccountVerification_pb.js";
import type { AddDematAccountRequest, AddDematAccountResponse } from "./Demat_pb.js";
import { file_Demat } from "./Demat_pb.js";
import type { AddNomineeRequest, AddNomineeResponse } from "./Nominee_pb.js";
import { file_Nominee } from "./Nominee_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Kyc.proto.
 */
export const file_Kyc: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Common, file_BankAccountVerification, file_Demat, file_Nominee]);

/**
 * @generated from message com.stablemoney.api.broking.GenerateTokenRequest
 */
export type GenerateTokenRequest = Message<"com.stablemoney.api.broking.GenerateTokenRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.GenerateTokenRequest.
 * Use `create(GenerateTokenRequestSchema)` to create a new message.
 */
export const GenerateTokenRequestSchema: GenMessage<GenerateTokenRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 0);

/**
 * @generated from message com.stablemoney.api.broking.GenerateTokenResponse
 */
export type GenerateTokenResponse = Message<"com.stablemoney.api.broking.GenerateTokenResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string access_token = 2;
   */
  accessToken: string;

  /**
   * @generated from field: string customer_identifier = 3;
   */
  customerIdentifier: string;

  /**
   * @generated from field: bool is_new = 4;
   */
  isNew: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.GenerateTokenResponse.
 * Use `create(GenerateTokenResponseSchema)` to create a new message.
 */
export const GenerateTokenResponseSchema: GenMessage<GenerateTokenResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 1);

/**
 * @generated from message com.stablemoney.api.broking.EmptyKycRequest
 */
export type EmptyKycRequest = Message<"com.stablemoney.api.broking.EmptyKycRequest"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.EmptyKycRequest.
 * Use `create(EmptyKycRequestSchema)` to create a new message.
 */
export const EmptyKycRequestSchema: GenMessage<EmptyKycRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 2);

/**
 * @generated from message com.stablemoney.api.broking.InitiateKycRequest
 */
export type InitiateKycRequest = Message<"com.stablemoney.api.broking.InitiateKycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.StepName kyc_type = 1;
   */
  kycType: StepName;

  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateKycRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.PanKycRequest pan_kyc_request = 2;
     */
    value: PanKycRequest;
    case: "panKycRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.WetSignatureRequest wet_signature_request = 3;
     */
    value: WetSignatureRequest;
    case: "wetSignatureRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.SelfieRequest selfie_request = 4;
     */
    value: SelfieRequest;
    case: "selfieRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.KycRequest kyc_request = 5;
     */
    value: KycRequest;
    case: "kycRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.EsignRequest esign_request = 6;
     */
    value: EsignRequest;
    case: "esignRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.EmptyKycRequest empty_kyc_request = 7;
     */
    value: EmptyKycRequest;
    case: "emptyKycRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateKycRequest.
 * Use `create(InitiateKycRequestSchema)` to create a new message.
 */
export const InitiateKycRequestSchema: GenMessage<InitiateKycRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 3);

/**
 * @generated from message com.stablemoney.api.broking.EsignRequest
 */
export type EsignRequest = Message<"com.stablemoney.api.broking.EsignRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.EsignStep step = 1;
   */
  step: EsignStep;

  /**
   * @generated from oneof com.stablemoney.api.broking.EsignRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.GenerateTokenRequest generate_token_request = 2;
     */
    value: GenerateTokenRequest;
    case: "generateTokenRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.StatusRequest status_request = 3;
     */
    value: StatusRequest;
    case: "statusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.EsignRequest.
 * Use `create(EsignRequestSchema)` to create a new message.
 */
export const EsignRequestSchema: GenMessage<EsignRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 4);

/**
 * @generated from message com.stablemoney.api.broking.KycRequest
 */
export type KycRequest = Message<"com.stablemoney.api.broking.KycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.KycStep step = 1;
   */
  step: KycStep;

  /**
   * @generated from oneof com.stablemoney.api.broking.KycRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.GenerateTokenRequest generate_token_request = 2;
     */
    value: GenerateTokenRequest;
    case: "generateTokenRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.StatusRequest status_request = 3;
     */
    value: StatusRequest;
    case: "statusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.KycRequest.
 * Use `create(KycRequestSchema)` to create a new message.
 */
export const KycRequestSchema: GenMessage<KycRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 5);

/**
 * @generated from message com.stablemoney.api.broking.InitiateKycResponse
 */
export type InitiateKycResponse = Message<"com.stablemoney.api.broking.InitiateKycResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateKycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.PanKycResponse pan_kyc_response = 1;
     */
    value: PanKycResponse;
    case: "panKycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.WetSignatureResponse wet_signature_response = 2;
     */
    value: WetSignatureResponse;
    case: "wetSignatureResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.SelfieResponse selfie_response = 3;
     */
    value: SelfieResponse;
    case: "selfieResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.KycResponse kyc_response = 4;
     */
    value: KycResponse;
    case: "kycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.EsignKycResponse esign_response = 5;
     */
    value: EsignKycResponse;
    case: "esignResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.KraKycResponse kra_kyc_response = 6;
     */
    value: KraKycResponse;
    case: "kraKycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.CvlPullResponse cvl_pull_response = 7;
     */
    value: CvlPullResponse;
    case: "cvlPullResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.CvlPushResponse cvl_push_response = 8;
     */
    value: CvlPushResponse;
    case: "cvlPushResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AadhaarPanMatchResponse aadhaar_pan_match_response = 9;
     */
    value: AadhaarPanMatchResponse;
    case: "aadhaarPanMatchResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.CvlPartialFetchResponse cvl_partial_fetch_response = 10;
     */
    value: CvlPartialFetchResponse;
    case: "cvlPartialFetchResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateKycResponse.
 * Use `create(InitiateKycResponseSchema)` to create a new message.
 */
export const InitiateKycResponseSchema: GenMessage<InitiateKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 6);

/**
 * @generated from message com.stablemoney.api.broking.OnboardingRequest
 */
export type OnboardingRequest = Message<"com.stablemoney.api.broking.OnboardingRequest"> & {
  /**
   * @generated from field: optional com.stablemoney.api.broking.StepName step_name = 1;
   */
  stepName?: StepName;

  /**
   * @generated from oneof com.stablemoney.api.broking.OnboardingRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.PanKycRequest pan_kyc_request = 10;
     */
    value: PanKycRequest;
    case: "panKycRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.SelfieRequest selfie_request = 12;
     */
    value: SelfieRequest;
    case: "selfieRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.KycRequest kyc_request = 14;
     */
    value: KycRequest;
    case: "kycRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiateBankVerificationRequest verify_bank_request = 16;
     */
    value: InitiateBankVerificationRequest;
    case: "verifyBankRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AddDematAccountRequest add_demat_request = 18;
     */
    value: AddDematAccountRequest;
    case: "addDematRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.WetSignatureRequest wet_signature_request = 20;
     */
    value: WetSignatureRequest;
    case: "wetSignatureRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.UpdateProfileRequest update_profile_request = 22;
     */
    value: UpdateProfileRequest;
    case: "updateProfileRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AddCurrentAddressRequest add_current_address_request = 23;
     */
    value: AddCurrentAddressRequest;
    case: "addCurrentAddressRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AddNomineeRequest add_nominee_request = 24;
     */
    value: AddNomineeRequest;
    case: "addNomineeRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.EsignRequest esign_request = 26;
     */
    value: EsignRequest;
    case: "esignRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.EmptyKycRequest empty_kyc_request = 28;
     */
    value: EmptyKycRequest;
    case: "emptyKycRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.OnboardingRequest.
 * Use `create(OnboardingRequestSchema)` to create a new message.
 */
export const OnboardingRequestSchema: GenMessage<OnboardingRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 7);

/**
 * @generated from message com.stablemoney.api.broking.OnboardingResponse
 */
export type OnboardingResponse = Message<"com.stablemoney.api.broking.OnboardingResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.UserLifetimeStatusResponse.KycStatus kyc_status = 1;
   */
  kycStatus: UserLifetimeStatusResponse_KycStatus;

  /**
   * @generated from field: com.stablemoney.api.broking.StepName next_step = 2;
   */
  nextStep: StepName;

  /**
   * @generated from field: com.stablemoney.api.broking.UserLifetimeStatus lifetime_status = 3;
   */
  lifetimeStatus: UserLifetimeStatus;

  /**
   * @generated from oneof com.stablemoney.api.broking.OnboardingResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.EmptyKycResponse empty_kyc_response = 4;
     */
    value: EmptyKycResponse;
    case: "emptyKycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.PanKycResponse pan_kyc_response = 10;
     */
    value: PanKycResponse;
    case: "panKycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.CvlPartialFetchResponse cvl_partial_fetch_response = 12;
     */
    value: CvlPartialFetchResponse;
    case: "cvlPartialFetchResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.SelfieResponse selfie_response = 14;
     */
    value: SelfieResponse;
    case: "selfieResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.KycResponse kyc_response = 16;
     */
    value: KycResponse;
    case: "kycResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AadhaarPanMatchResponse aadhaar_pan_match_response = 18;
     */
    value: AadhaarPanMatchResponse;
    case: "aadhaarPanMatchResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiateBankVerificationResponse verify_bank_response = 20;
     */
    value: InitiateBankVerificationResponse;
    case: "verifyBankResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AddDematAccountResponse add_demat_response = 22;
     */
    value: AddDematAccountResponse;
    case: "addDematResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.WetSignatureResponse wet_signature_response = 24;
     */
    value: WetSignatureResponse;
    case: "wetSignatureResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.UpdateProfileResponse update_profile_response = 26;
     */
    value: UpdateProfileResponse;
    case: "updateProfileResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AddCurrentAddressResponse add_current_address_response = 27;
     */
    value: AddCurrentAddressResponse;
    case: "addCurrentAddressResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AddNomineeResponse add_nominee_response = 28;
     */
    value: AddNomineeResponse;
    case: "addNomineeResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.EsignKycResponse esign_response = 30;
     */
    value: EsignKycResponse;
    case: "esignResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.CvlPullResponse cvl_pull_response = 32;
     */
    value: CvlPullResponse;
    case: "cvlPullResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.CvlPushResponse cvl_push_response = 34;
     */
    value: CvlPushResponse;
    case: "cvlPushResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.OnboardingResponse.
 * Use `create(OnboardingResponseSchema)` to create a new message.
 */
export const OnboardingResponseSchema: GenMessage<OnboardingResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 8);

/**
 * @generated from message com.stablemoney.api.broking.EsignKycResponse
 */
export type EsignKycResponse = Message<"com.stablemoney.api.broking.EsignKycResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.EsignKycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.GenerateTokenResponse generate_token_response = 1;
     */
    value: GenerateTokenResponse;
    case: "generateTokenResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.StatusResponse status_response = 2;
     */
    value: StatusResponse;
    case: "statusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.EsignKycResponse.
 * Use `create(EsignKycResponseSchema)` to create a new message.
 */
export const EsignKycResponseSchema: GenMessage<EsignKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 9);

/**
 * @generated from message com.stablemoney.api.broking.KycResponse
 */
export type KycResponse = Message<"com.stablemoney.api.broking.KycResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.KycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.GenerateTokenResponse generate_token_response = 1;
     */
    value: GenerateTokenResponse;
    case: "generateTokenResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.StatusResponse status_response = 2;
     */
    value: StatusResponse;
    case: "statusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.KycResponse.
 * Use `create(KycResponseSchema)` to create a new message.
 */
export const KycResponseSchema: GenMessage<KycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 10);

/**
 * @generated from message com.stablemoney.api.broking.AadhaarPanMatchResponse
 */
export type AadhaarPanMatchResponse = Message<"com.stablemoney.api.broking.AadhaarPanMatchResponse"> & {
  /**
   * @generated from field: bool aadhaar_pan_match_status = 1;
   */
  aadhaarPanMatchStatus: boolean;

  /**
   * @generated from field: bool aadhaar_pan_dob_match = 2;
   */
  aadhaarPanDobMatch: boolean;

  /**
   * @generated from field: bool aadhaar_pan_name_match = 3;
   */
  aadhaarPanNameMatch: boolean;

  /**
   * @generated from field: com.stablemoney.api.broking.AadhaarPanMatchResponse.AadhaarDetails aadhaar_details = 4;
   */
  aadhaarDetails?: AadhaarPanMatchResponse_AadhaarDetails;

  /**
   * @generated from field: com.stablemoney.api.broking.AadhaarPanMatchResponse.PanDetails pan_details = 5;
   */
  panDetails?: AadhaarPanMatchResponse_PanDetails;
};

/**
 * Describes the message com.stablemoney.api.broking.AadhaarPanMatchResponse.
 * Use `create(AadhaarPanMatchResponseSchema)` to create a new message.
 */
export const AadhaarPanMatchResponseSchema: GenMessage<AadhaarPanMatchResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 11);

/**
 * @generated from message com.stablemoney.api.broking.AadhaarPanMatchResponse.AadhaarDetails
 */
export type AadhaarPanMatchResponse_AadhaarDetails = Message<"com.stablemoney.api.broking.AadhaarPanMatchResponse.AadhaarDetails"> & {
  /**
   * @generated from field: string aadhaar_name = 1;
   */
  aadhaarName: string;

  /**
   * @generated from field: string aadhaar_number = 2;
   */
  aadhaarNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AadhaarPanMatchResponse.AadhaarDetails.
 * Use `create(AadhaarPanMatchResponse_AadhaarDetailsSchema)` to create a new message.
 */
export const AadhaarPanMatchResponse_AadhaarDetailsSchema: GenMessage<AadhaarPanMatchResponse_AadhaarDetails> = /*@__PURE__*/
  messageDesc(file_Kyc, 11, 0);

/**
 * @generated from message com.stablemoney.api.broking.AadhaarPanMatchResponse.PanDetails
 */
export type AadhaarPanMatchResponse_PanDetails = Message<"com.stablemoney.api.broking.AadhaarPanMatchResponse.PanDetails"> & {
  /**
   * @generated from field: string pan_name = 1;
   */
  panName: string;

  /**
   * @generated from field: string pan_number = 2;
   */
  panNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AadhaarPanMatchResponse.PanDetails.
 * Use `create(AadhaarPanMatchResponse_PanDetailsSchema)` to create a new message.
 */
export const AadhaarPanMatchResponse_PanDetailsSchema: GenMessage<AadhaarPanMatchResponse_PanDetails> = /*@__PURE__*/
  messageDesc(file_Kyc, 11, 1);

/**
 * @generated from message com.stablemoney.api.broking.SelfieRequest
 */
export type SelfieRequest = Message<"com.stablemoney.api.broking.SelfieRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.SelfieKycStep step = 1;
   */
  step: SelfieKycStep;

  /**
   * @generated from oneof com.stablemoney.api.broking.SelfieRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.StartSelfieStepRequest start_selfie_step_request = 2;
     */
    value: StartSelfieStepRequest;
    case: "startSelfieStepRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.SetSelfieStatusRequest set_selfie_status_request = 3;
     */
    value: SetSelfieStatusRequest;
    case: "setSelfieStatusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.SelfieRequest.
 * Use `create(SelfieRequestSchema)` to create a new message.
 */
export const SelfieRequestSchema: GenMessage<SelfieRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 12);

/**
 * @generated from message com.stablemoney.api.broking.StartSelfieStepRequest
 */
export type StartSelfieStepRequest = Message<"com.stablemoney.api.broking.StartSelfieStepRequest"> & {
  /**
   * @generated from field: string transaction_id = 1;
   */
  transactionId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.StartSelfieStepRequest.
 * Use `create(StartSelfieStepRequestSchema)` to create a new message.
 */
export const StartSelfieStepRequestSchema: GenMessage<StartSelfieStepRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 13);

/**
 * @generated from message com.stablemoney.api.broking.SetSelfieStatusRequest
 */
export type SetSelfieStatusRequest = Message<"com.stablemoney.api.broking.SetSelfieStatusRequest"> & {
  /**
   * @generated from field: string transaction_id = 1;
   */
  transactionId: string;

  /**
   * @generated from field: string status = 2;
   */
  status: string;
};

/**
 * Describes the message com.stablemoney.api.broking.SetSelfieStatusRequest.
 * Use `create(SetSelfieStatusRequestSchema)` to create a new message.
 */
export const SetSelfieStatusRequestSchema: GenMessage<SetSelfieStatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 14);

/**
 * @generated from message com.stablemoney.api.broking.SelfieResponse
 */
export type SelfieResponse = Message<"com.stablemoney.api.broking.SelfieResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.SelfieResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.StartSelfieStepResponse start_selfie_step_response = 1;
     */
    value: StartSelfieStepResponse;
    case: "startSelfieStepResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.SetSelfieStatusResponse set_selfie_status_response = 2;
     */
    value: SetSelfieStatusResponse;
    case: "setSelfieStatusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.SelfieResponse.
 * Use `create(SelfieResponseSchema)` to create a new message.
 */
export const SelfieResponseSchema: GenMessage<SelfieResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 15);

/**
 * @generated from message com.stablemoney.api.broking.StartSelfieStepResponse
 */
export type StartSelfieStepResponse = Message<"com.stablemoney.api.broking.StartSelfieStepResponse"> & {
  /**
   * @generated from field: string selfie = 1;
   */
  selfie: string;

  /**
   * @generated from field: string workflow_id = 2;
   */
  workflowId: string;

  /**
   * @generated from field: string access_token = 3;
   */
  accessToken: string;

  /**
   * @generated from field: string transaction_id = 4;
   */
  transactionId: string;

  /**
   * @generated from field: bool completed = 5;
   */
  completed: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.StartSelfieStepResponse.
 * Use `create(StartSelfieStepResponseSchema)` to create a new message.
 */
export const StartSelfieStepResponseSchema: GenMessage<StartSelfieStepResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 16);

/**
 * @generated from message com.stablemoney.api.broking.SetSelfieStatusResponse
 */
export type SetSelfieStatusResponse = Message<"com.stablemoney.api.broking.SetSelfieStatusResponse"> & {
  /**
   * @generated from field: bool completed = 1;
   */
  completed: boolean;

  /**
   * @generated from field: bool success = 2;
   */
  success: boolean;

  /**
   * @generated from field: string error_message = 3;
   */
  errorMessage: string;
};

/**
 * Describes the message com.stablemoney.api.broking.SetSelfieStatusResponse.
 * Use `create(SetSelfieStatusResponseSchema)` to create a new message.
 */
export const SetSelfieStatusResponseSchema: GenMessage<SetSelfieStatusResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 17);

/**
 * @generated from message com.stablemoney.api.broking.PanKycResponse
 */
export type PanKycResponse = Message<"com.stablemoney.api.broking.PanKycResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.PanKycStep step = 1;
   */
  step: PanKycStep;

  /**
   * @generated from oneof com.stablemoney.api.broking.PanKycResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.KycFetchNameByPanResponse kyc_fetch_name_by_pan_response = 2;
     */
    value: KycFetchNameByPanResponse;
    case: "kycFetchNameByPanResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.KycValidateNameAndGetPanStatusResponse kyc_validate_name_and_get_pan_status_response = 3;
     */
    value: KycValidateNameAndGetPanStatusResponse;
    case: "kycValidateNameAndGetPanStatusResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.PanKycResponse.
 * Use `create(PanKycResponseSchema)` to create a new message.
 */
export const PanKycResponseSchema: GenMessage<PanKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 18);

/**
 * @generated from message com.stablemoney.api.broking.KraKycResponse
 */
export type KraKycResponse = Message<"com.stablemoney.api.broking.KraKycResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.PanKycStep step = 1;
   */
  step: PanKycStep;

  /**
   * @generated from oneof com.stablemoney.api.broking.KraKycResponse.result
   */
  result: {
    /**
     * @generated from field: string name = 2;
     */
    value: string;
    case: "name";
  } | {
    /**
     * @generated from field: string pan = 3;
     */
    value: string;
    case: "pan";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.KraKycResponse.
 * Use `create(KraKycResponseSchema)` to create a new message.
 */
export const KraKycResponseSchema: GenMessage<KraKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 19);

/**
 * @generated from message com.stablemoney.api.broking.CvlPullResponse
 */
export type CvlPullResponse = Message<"com.stablemoney.api.broking.CvlPullResponse"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.CvlPullResponse.
 * Use `create(CvlPullResponseSchema)` to create a new message.
 */
export const CvlPullResponseSchema: GenMessage<CvlPullResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 20);

/**
 * @generated from message com.stablemoney.api.broking.CvlPushResponse
 */
export type CvlPushResponse = Message<"com.stablemoney.api.broking.CvlPushResponse"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.CvlPushResponse.
 * Use `create(CvlPushResponseSchema)` to create a new message.
 */
export const CvlPushResponseSchema: GenMessage<CvlPushResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 21);

/**
 * @generated from message com.stablemoney.api.broking.CvlPartialFetchResponse
 */
export type CvlPartialFetchResponse = Message<"com.stablemoney.api.broking.CvlPartialFetchResponse"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.CvlPartialFetchResponse.
 * Use `create(CvlPartialFetchResponseSchema)` to create a new message.
 */
export const CvlPartialFetchResponseSchema: GenMessage<CvlPartialFetchResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 22);

/**
 * @generated from message com.stablemoney.api.broking.EmptyKycResponse
 */
export type EmptyKycResponse = Message<"com.stablemoney.api.broking.EmptyKycResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.EmptyKycResponse.
 * Use `create(EmptyKycResponseSchema)` to create a new message.
 */
export const EmptyKycResponseSchema: GenMessage<EmptyKycResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 23);

/**
 * @generated from message com.stablemoney.api.broking.PanKycRequest
 */
export type PanKycRequest = Message<"com.stablemoney.api.broking.PanKycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.PanKycStep step = 1;
   */
  step: PanKycStep;

  /**
   * @generated from oneof com.stablemoney.api.broking.PanKycRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.KycFetchNameByPanRequest kyc_fetch_name_by_pan_request = 2;
     */
    value: KycFetchNameByPanRequest;
    case: "kycFetchNameByPanRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.KycValidateNameAndGetKycStatusRequest kyc_validate_name_and_get_kyc_status_request = 3;
     */
    value: KycValidateNameAndGetKycStatusRequest;
    case: "kycValidateNameAndGetKycStatusRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.PanKycRequest.
 * Use `create(PanKycRequestSchema)` to create a new message.
 */
export const PanKycRequestSchema: GenMessage<PanKycRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 24);

/**
 * @generated from message com.stablemoney.api.broking.KycFetchNameByPanResponse
 */
export type KycFetchNameByPanResponse = Message<"com.stablemoney.api.broking.KycFetchNameByPanResponse"> & {
  /**
   * @generated from field: string pan = 1;
   */
  pan: string;

  /**
   * @generated from field: string full_name = 2;
   */
  fullName: string;
};

/**
 * Describes the message com.stablemoney.api.broking.KycFetchNameByPanResponse.
 * Use `create(KycFetchNameByPanResponseSchema)` to create a new message.
 */
export const KycFetchNameByPanResponseSchema: GenMessage<KycFetchNameByPanResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 25);

/**
 * @generated from message com.stablemoney.api.broking.KycFetchNameByPanRequest
 */
export type KycFetchNameByPanRequest = Message<"com.stablemoney.api.broking.KycFetchNameByPanRequest"> & {
  /**
   * @generated from field: string pan = 1;
   */
  pan: string;
};

/**
 * Describes the message com.stablemoney.api.broking.KycFetchNameByPanRequest.
 * Use `create(KycFetchNameByPanRequestSchema)` to create a new message.
 */
export const KycFetchNameByPanRequestSchema: GenMessage<KycFetchNameByPanRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 26);

/**
 * @generated from message com.stablemoney.api.broking.KycValidateNameAndGetPanStatusRequest
 */
export type KycValidateNameAndGetPanStatusRequest = Message<"com.stablemoney.api.broking.KycValidateNameAndGetPanStatusRequest"> & {
  /**
   * @generated from field: bool is_name_match = 1;
   */
  isNameMatch: boolean;

  /**
   * @generated from field: string dob = 2;
   */
  dob: string;
};

/**
 * Describes the message com.stablemoney.api.broking.KycValidateNameAndGetPanStatusRequest.
 * Use `create(KycValidateNameAndGetPanStatusRequestSchema)` to create a new message.
 */
export const KycValidateNameAndGetPanStatusRequestSchema: GenMessage<KycValidateNameAndGetPanStatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 27);

/**
 * @generated from message com.stablemoney.api.broking.KycValidateNameAndGetPanStatusResponse
 */
export type KycValidateNameAndGetPanStatusResponse = Message<"com.stablemoney.api.broking.KycValidateNameAndGetPanStatusResponse"> & {
  /**
   * @generated from field: bool pan_kyc_status = 1;
   */
  panKycStatus: boolean;

  /**
   * @generated from field: bool name_match_status = 2;
   */
  nameMatchStatus: boolean;

  /**
   * @generated from field: bool dob_match_status = 3;
   */
  dobMatchStatus: boolean;

  /**
   * @generated from field: bool is_pan_valid = 4;
   */
  isPanValid: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.KycValidateNameAndGetPanStatusResponse.
 * Use `create(KycValidateNameAndGetPanStatusResponseSchema)` to create a new message.
 */
export const KycValidateNameAndGetPanStatusResponseSchema: GenMessage<KycValidateNameAndGetPanStatusResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 28);

/**
 * @generated from message com.stablemoney.api.broking.StatusRequest
 */
export type StatusRequest = Message<"com.stablemoney.api.broking.StatusRequest"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;
};

/**
 * Describes the message com.stablemoney.api.broking.StatusRequest.
 * Use `create(StatusRequestSchema)` to create a new message.
 */
export const StatusRequestSchema: GenMessage<StatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 29);

/**
 * @generated from message com.stablemoney.api.broking.StatusResponse
 */
export type StatusResponse = Message<"com.stablemoney.api.broking.StatusResponse"> & {
  /**
   * @generated from field: string kyc_status = 1;
   */
  kycStatus: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.broking.StatusResponse.
 * Use `create(StatusResponseSchema)` to create a new message.
 */
export const StatusResponseSchema: GenMessage<StatusResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 30);

/**
 * @generated from message com.stablemoney.api.broking.WetSignatureRequest
 */
export type WetSignatureRequest = Message<"com.stablemoney.api.broking.WetSignatureRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.RaaDuration raa_duration = 1;
   */
  raaDuration: RaaDuration;

  /**
   * @generated from field: bool is_pep = 2;
   */
  isPep: boolean;

  /**
   * @generated from field: bool is_indian_citizen = 3;
   */
  isIndianCitizen: boolean;

  /**
   * @generated from field: string document_id = 4;
   */
  documentId: string;

  /**
   * @generated from field: bool credit_report_consent = 5;
   */
  creditReportConsent: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.WetSignatureRequest.
 * Use `create(WetSignatureRequestSchema)` to create a new message.
 */
export const WetSignatureRequestSchema: GenMessage<WetSignatureRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 31);

/**
 * @generated from message com.stablemoney.api.broking.WetSignatureResponse
 */
export type WetSignatureResponse = Message<"com.stablemoney.api.broking.WetSignatureResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.WetSignatureResponse.
 * Use `create(WetSignatureResponseSchema)` to create a new message.
 */
export const WetSignatureResponseSchema: GenMessage<WetSignatureResponse> = /*@__PURE__*/
  messageDesc(file_Kyc, 32);

/**
 * @generated from message com.stablemoney.api.broking.KycValidateNameAndGetKycStatusRequest
 */
export type KycValidateNameAndGetKycStatusRequest = Message<"com.stablemoney.api.broking.KycValidateNameAndGetKycStatusRequest"> & {
  /**
   * @generated from field: string full_name = 1;
   */
  fullName: string;

  /**
   * @generated from field: string dob = 2;
   */
  dob: string;

  /**
   * @generated from field: string pan_number = 3;
   */
  panNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.KycValidateNameAndGetKycStatusRequest.
 * Use `create(KycValidateNameAndGetKycStatusRequestSchema)` to create a new message.
 */
export const KycValidateNameAndGetKycStatusRequestSchema: GenMessage<KycValidateNameAndGetKycStatusRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 33);

/**
 * @generated from message com.stablemoney.api.broking.AddSanctionedPeopleRequest
 */
export type AddSanctionedPeopleRequest = Message<"com.stablemoney.api.broking.AddSanctionedPeopleRequest"> & {
  /**
   * @generated from field: string excel_s3_bucket_name = 1;
   */
  excelS3BucketName: string;

  /**
   * @generated from field: string excel_s3_key = 2;
   */
  excelS3Key: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AddSanctionedPeopleRequest.
 * Use `create(AddSanctionedPeopleRequestSchema)` to create a new message.
 */
export const AddSanctionedPeopleRequestSchema: GenMessage<AddSanctionedPeopleRequest> = /*@__PURE__*/
  messageDesc(file_Kyc, 34);

/**
 * @generated from enum com.stablemoney.api.broking.StepName
 */
export enum StepName {
  /**
   * @generated from enum value: KYC_TYPE_UNKNOWN = 0;
   */
  KYC_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: POI = 1;
   */
  POI = 1,

  /**
   * @generated from enum value: KYC = 2;
   */
  KYC = 2,

  /**
   * @generated from enum value: POA = 3;
   */
  POA = 3,

  /**
   * @generated from enum value: SELFIE = 4;
   */
  SELFIE = 4,

  /**
   * @generated from enum value: ESIGN = 5;
   */
  ESIGN = 5,

  /**
   * @generated from enum value: BANK_ACCOUNT = 6;
   */
  BANK_ACCOUNT = 6,

  /**
   * @generated from enum value: DEMAT_ACCOUNT = 7;
   */
  DEMAT_ACCOUNT = 7,

  /**
   * @generated from enum value: NOMINEE = 8;
   */
  NOMINEE = 8,

  /**
   * @generated from enum value: WET_SIGNATURE = 9;
   */
  WET_SIGNATURE = 9,

  /**
   * @generated from enum value: USER_PROFILE = 10;
   */
  USER_PROFILE = 10,

  /**
   * @generated from enum value: PAN_KYC = 11;
   */
  PAN_KYC = 11,

  /**
   * @generated from enum value: NAME = 12;
   */
  NAME = 12,

  /**
   * @generated from enum value: QUESTIONNAIRE = 13;
   */
  QUESTIONNAIRE = 13,

  /**
   * @generated from enum value: EMAIL = 14;
   */
  EMAIL = 14,

  /**
   * @generated from enum value: WHITELIST_CHECK = 15;
   */
  WHITELIST_CHECK = 15,

  /**
   * @generated from enum value: KRA_KYC = 16;
   */
  KRA_KYC = 16,

  /**
   * @generated from enum value: CVL_PUSH = 17;
   */
  CVL_PUSH = 17,

  /**
   * @generated from enum value: CVL_PULL = 18;
   */
  CVL_PULL = 18,

  /**
   * @generated from enum value: ADDRESS_SYNC_CVL_PUSH = 19;
   */
  ADDRESS_SYNC_CVL_PUSH = 19,

  /**
   * @generated from enum value: AADHAAR_PAN_MATCH = 20;
   */
  AADHAAR_PAN_MATCH = 20,

  /**
   * @generated from enum value: CURRENT_ADDRESS = 21;
   */
  CURRENT_ADDRESS = 21,

  /**
   * @generated from enum value: CVL_PARTIAL_FETCH = 22;
   */
  CVL_PARTIAL_FETCH = 22,
}

/**
 * Describes the enum com.stablemoney.api.broking.StepName.
 */
export const StepNameSchema: GenEnum<StepName> = /*@__PURE__*/
  enumDesc(file_Kyc, 0);

/**
 * @generated from enum com.stablemoney.api.broking.ProofType
 */
export enum ProofType {
  /**
   * @generated from enum value: PROOF_TYPE_UNKNOWN = 0;
   */
  PROOF_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: PAN = 1;
   */
  PAN = 1,

  /**
   * @generated from enum value: AADHAR = 2;
   */
  AADHAR = 2,

  /**
   * @generated from enum value: PASSPORT = 3;
   */
  PASSPORT = 3,

  /**
   * @generated from enum value: DRIVING_LICENSE = 4;
   */
  DRIVING_LICENSE = 4,

  /**
   * @generated from enum value: VOTER_ID = 5;
   */
  VOTER_ID = 5,

  /**
   * @generated from enum value: GOVT_ID = 6;
   */
  GOVT_ID = 6,

  /**
   * @generated from enum value: REGULATORY_ID = 7;
   */
  REGULATORY_ID = 7,

  /**
   * @generated from enum value: PSU_ID = 8;
   */
  PSU_ID = 8,

  /**
   * @generated from enum value: BANK_ID = 9;
   */
  BANK_ID = 9,

  /**
   * @generated from enum value: PUBLIC_FINANCIAL_INSTITUTION_ID = 10;
   */
  PUBLIC_FINANCIAL_INSTITUTION_ID = 10,

  /**
   * @generated from enum value: COLLEGE_ID = 11;
   */
  COLLEGE_ID = 11,

  /**
   * @generated from enum value: PROFESSIONAL_BODY_ID = 12;
   */
  PROFESSIONAL_BODY_ID = 12,

  /**
   * @generated from enum value: CREDIT_CARD = 13;
   */
  CREDIT_CARD = 13,

  /**
   * @generated from enum value: OTHER_ID = 16;
   */
  OTHER_ID = 16,

  /**
   * @generated from enum value: BANK_PASSBOOK = 17;
   */
  BANK_PASSBOOK = 17,

  /**
   * @generated from enum value: BANK_ACCOUNT_STATEMENT = 18;
   */
  BANK_ACCOUNT_STATEMENT = 18,

  /**
   * @generated from enum value: RATION_CARD = 19;
   */
  RATION_CARD = 19,

  /**
   * @generated from enum value: LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20;
   */
  LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20,

  /**
   * @generated from enum value: LAND_LINE_TELEPHONE_BILL = 21;
   */
  LAND_LINE_TELEPHONE_BILL = 21,

  /**
   * @generated from enum value: ELECTRICITY_BILL = 22;
   */
  ELECTRICITY_BILL = 22,

  /**
   * @generated from enum value: GAS_BILL = 23;
   */
  GAS_BILL = 23,

  /**
   * @generated from enum value: FLAT_MAINTENANCE_BILL = 24;
   */
  FLAT_MAINTENANCE_BILL = 24,

  /**
   * @generated from enum value: INSURANCE_COPY = 25;
   */
  INSURANCE_COPY = 25,

  /**
   * @generated from enum value: SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26;
   */
  SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26,

  /**
   * @generated from enum value: POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27;
   */
  POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27,

  /**
   * @generated from enum value: POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28;
   */
  POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28,

  /**
   * @generated from enum value: POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29;
   */
  POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29,

  /**
   * @generated from enum value: POA_ISSUED_BY_PARLIAMENT = 30;
   */
  POA_ISSUED_BY_PARLIAMENT = 30,

  /**
   * @generated from enum value: POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31;
   */
  POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31,

  /**
   * @generated from enum value: POA_ISSUED_BY_NOTARY_PUBLIC = 32;
   */
  POA_ISSUED_BY_NOTARY_PUBLIC = 32,

  /**
   * @generated from enum value: POA_ISSUED_BY_GAZETTED_OFFICER = 33;
   */
  POA_ISSUED_BY_GAZETTED_OFFICER = 33,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34;
   */
  ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35;
   */
  ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36;
   */
  ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37;
   */
  ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38;
   */
  ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39;
   */
  ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40;
   */
  ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40,

  /**
   * @generated from enum value: UIN_CARD = 41;
   */
  UIN_CARD = 41,

  /**
   * @generated from enum value: OTHER_ID_MF = 42;
   */
  OTHER_ID_MF = 42,
}

/**
 * Describes the enum com.stablemoney.api.broking.ProofType.
 */
export const ProofTypeSchema: GenEnum<ProofType> = /*@__PURE__*/
  enumDesc(file_Kyc, 1);

/**
 * @generated from enum com.stablemoney.api.broking.KycProvider
 */
export enum KycProvider {
  /**
   * @generated from enum value: KYC_PROVIDER_UNKNOWN = 0;
   */
  KYC_PROVIDER_UNKNOWN = 0,

  /**
   * @generated from enum value: NONE = 1;
   */
  NONE = 1,

  /**
   * @generated from enum value: CDSL = 2;
   */
  CDSL = 2,

  /**
   * @generated from enum value: DIGIO = 3;
   */
  DIGIO = 3,

  /**
   * @generated from enum value: HYPERVERGE = 4;
   */
  HYPERVERGE = 4,

  /**
   * @generated from enum value: TARRAKKI = 5;
   */
  TARRAKKI = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.KycProvider.
 */
export const KycProviderSchema: GenEnum<KycProvider> = /*@__PURE__*/
  enumDesc(file_Kyc, 2);

/**
 * @generated from enum com.stablemoney.api.broking.OnBoardingStatus
 */
export enum OnBoardingStatus {
  /**
   * @generated from enum value: ONBOARDING_STATUS_UNKNOWN = 0;
   */
  ONBOARDING_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: COMPLETE = 2;
   */
  COMPLETE = 2,

  /**
   * @generated from enum value: REJECTED = 3;
   */
  REJECTED = 3,

  /**
   * @generated from enum value: SKIPPED = 4;
   */
  SKIPPED = 4,
}

/**
 * Describes the enum com.stablemoney.api.broking.OnBoardingStatus.
 */
export const OnBoardingStatusSchema: GenEnum<OnBoardingStatus> = /*@__PURE__*/
  enumDesc(file_Kyc, 3);

/**
 * @generated from enum com.stablemoney.api.broking.CvlMode
 */
export enum CvlMode {
  /**
   * @generated from enum value: CVL_MODE_UNKNOWN = 0;
   */
  CVL_MODE_UNKNOWN = 0,

  /**
   * @generated from enum value: VALIDATED_CVL_MODE = 1;
   */
  VALIDATED_CVL_MODE = 1,

  /**
   * @generated from enum value: CREATE_CVL_MODE = 2;
   */
  CREATE_CVL_MODE = 2,

  /**
   * @generated from enum value: UPDATE_CVL_MODE = 3;
   */
  UPDATE_CVL_MODE = 3,

  /**
   * @generated from enum value: WAIT_CVL_MODE = 4;
   */
  WAIT_CVL_MODE = 4,
}

/**
 * Describes the enum com.stablemoney.api.broking.CvlMode.
 */
export const CvlModeSchema: GenEnum<CvlMode> = /*@__PURE__*/
  enumDesc(file_Kyc, 4);

/**
 * @generated from enum com.stablemoney.api.broking.CvlPushStatus
 */
export enum CvlPushStatus {
  /**
   * @generated from enum value: CVL_PUSH_STATUS_UNKNOWN = 0;
   */
  CVL_PUSH_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: CVL_PUSH_INITIATED = 1;
   */
  CVL_PUSH_INITIATED = 1,

  /**
   * @generated from enum value: CVL_PUSH_API_FAILED = 2;
   */
  CVL_PUSH_API_FAILED = 2,

  /**
   * @generated from enum value: CVL_PUSH_API_SUCCESSFUL = 3;
   */
  CVL_PUSH_API_SUCCESSFUL = 3,

  /**
   * @generated from enum value: CVL_SFTP_UPLOAD_FAILED = 4;
   */
  CVL_SFTP_UPLOAD_FAILED = 4,

  /**
   * @generated from enum value: CVL_SFTP_UPLOAD_SUCCESSFUL = 5;
   */
  CVL_SFTP_UPLOAD_SUCCESSFUL = 5,

  /**
   * @generated from enum value: CVL_VALIDATED = 6;
   */
  CVL_VALIDATED = 6,

  /**
   * @generated from enum value: CVL_PUSH_WAITING = 7;
   */
  CVL_PUSH_WAITING = 7,

  /**
   * @generated from enum value: CVL_SOLICIT_PAN_SUCCESSFUL = 8;
   */
  CVL_SOLICIT_PAN_SUCCESSFUL = 8,
}

/**
 * Describes the enum com.stablemoney.api.broking.CvlPushStatus.
 */
export const CvlPushStatusSchema: GenEnum<CvlPushStatus> = /*@__PURE__*/
  enumDesc(file_Kyc, 5);

/**
 * @generated from enum com.stablemoney.api.broking.EsignStep
 */
export enum EsignStep {
  /**
   * @generated from enum value: ESIGN_STEP_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ESIGN_STEP_GENERATE_TOKEN = 1;
   */
  GENERATE_TOKEN = 1,

  /**
   * @generated from enum value: ESIGN_STEP_STATUS = 2;
   */
  STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.EsignStep.
 */
export const EsignStepSchema: GenEnum<EsignStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 6);

/**
 * @generated from enum com.stablemoney.api.broking.PanKycStep
 */
export enum PanKycStep {
  /**
   * @generated from enum value: UNKNOWN_PAN_STEP = 0;
   */
  UNKNOWN_PAN_STEP = 0,

  /**
   * @generated from enum value: NAME_FETCH = 1;
   */
  NAME_FETCH = 1,

  /**
   * @generated from enum value: PAN_STATUS = 2;
   */
  PAN_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.PanKycStep.
 */
export const PanKycStepSchema: GenEnum<PanKycStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 7);

/**
 * @generated from enum com.stablemoney.api.broking.KycStep
 */
export enum KycStep {
  /**
   * @generated from enum value: UNKNOWN_STEP = 0;
   */
  UNKNOWN_STEP = 0,

  /**
   * @generated from enum value: GENERATE_TOKEN = 1;
   */
  GENERATE_TOKEN = 1,

  /**
   * @generated from enum value: GET_STATUS = 2;
   */
  GET_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.KycStep.
 */
export const KycStepSchema: GenEnum<KycStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 8);

/**
 * @generated from enum com.stablemoney.api.broking.SelfieKycStep
 */
export enum SelfieKycStep {
  /**
   * @generated from enum value: UNKNOWN_SELFIE_STEP = 0;
   */
  UNKNOWN_SELFIE_STEP = 0,

  /**
   * @generated from enum value: START_SELFIE_STEP = 1;
   */
  START_SELFIE_STEP = 1,

  /**
   * @generated from enum value: SET_STATUS = 2;
   */
  SET_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.SelfieKycStep.
 */
export const SelfieKycStepSchema: GenEnum<SelfieKycStep> = /*@__PURE__*/
  enumDesc(file_Kyc, 9);

/**
 * @generated from enum com.stablemoney.api.broking.RaaDuration
 */
export enum RaaDuration {
  /**
   * @generated from enum value: UNKNOWN_RAA_DURATION = 0;
   */
  UNKNOWN_RAA_DURATION = 0,

  /**
   * @generated from enum value: RAA_60_DAYS = 1;
   */
  RAA_60_DAYS = 1,

  /**
   * @generated from enum value: RAA_90_DAYS = 2;
   */
  RAA_90_DAYS = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.RaaDuration.
 */
export const RaaDurationSchema: GenEnum<RaaDuration> = /*@__PURE__*/
  enumDesc(file_Kyc, 10);

