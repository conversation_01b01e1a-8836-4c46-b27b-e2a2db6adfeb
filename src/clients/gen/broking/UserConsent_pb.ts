// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file UserConsent.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file UserConsent.proto.
 */
export const file_UserConsent: GenFile = /*@__PURE__*/
  fileDesc("ChFVc2VyQ29uc2VudC5wcm90bxIbY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nImAKB0NvbnNlbnQSPgoMY29uc2VudF90eXBlGAEgASgOMiguY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkNvbnNlbnRUeXBlEhUKDWNvbnNlbnRfdmFsdWUYAiABKAgiTAoSVXNlckNvbnNlbnRSZXF1ZXN0EjYKCGNvbnNlbnRzGAEgAygLMiQuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkNvbnNlbnQiFQoTVXNlckNvbnNlbnRSZXNwb25zZSJQChZHZXRVc2VyQ29uc2VudFJlc3BvbnNlEjYKCGNvbnNlbnRzGAEgAygLMiQuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkNvbnNlbnQqIAoLQ29uc2VudFR5cGUSEQoNQk9ORFNfQ09OU0VOVBAAQh8KG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZ1ABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.broking.Consent
 */
export type Consent = Message<"com.stablemoney.api.broking.Consent"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.ConsentType consent_type = 1;
   */
  consentType: ConsentType;

  /**
   * @generated from field: bool consent_value = 2;
   */
  consentValue: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.Consent.
 * Use `create(ConsentSchema)` to create a new message.
 */
export const ConsentSchema: GenMessage<Consent> = /*@__PURE__*/
  messageDesc(file_UserConsent, 0);

/**
 * @generated from message com.stablemoney.api.broking.UserConsentRequest
 */
export type UserConsentRequest = Message<"com.stablemoney.api.broking.UserConsentRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.Consent consents = 1;
   */
  consents: Consent[];
};

/**
 * Describes the message com.stablemoney.api.broking.UserConsentRequest.
 * Use `create(UserConsentRequestSchema)` to create a new message.
 */
export const UserConsentRequestSchema: GenMessage<UserConsentRequest> = /*@__PURE__*/
  messageDesc(file_UserConsent, 1);

/**
 * @generated from message com.stablemoney.api.broking.UserConsentResponse
 */
export type UserConsentResponse = Message<"com.stablemoney.api.broking.UserConsentResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.UserConsentResponse.
 * Use `create(UserConsentResponseSchema)` to create a new message.
 */
export const UserConsentResponseSchema: GenMessage<UserConsentResponse> = /*@__PURE__*/
  messageDesc(file_UserConsent, 2);

/**
 * @generated from message com.stablemoney.api.broking.GetUserConsentResponse
 */
export type GetUserConsentResponse = Message<"com.stablemoney.api.broking.GetUserConsentResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.Consent consents = 1;
   */
  consents: Consent[];
};

/**
 * Describes the message com.stablemoney.api.broking.GetUserConsentResponse.
 * Use `create(GetUserConsentResponseSchema)` to create a new message.
 */
export const GetUserConsentResponseSchema: GenMessage<GetUserConsentResponse> = /*@__PURE__*/
  messageDesc(file_UserConsent, 3);

/**
 * @generated from enum com.stablemoney.api.broking.ConsentType
 */
export enum ConsentType {
  /**
   * @generated from enum value: BONDS_CONSENT = 0;
   */
  BONDS_CONSENT = 0,
}

/**
 * Describes the enum com.stablemoney.api.broking.ConsentType.
 */
export const ConsentTypeSchema: GenEnum<ConsentType> = /*@__PURE__*/
  enumDesc(file_UserConsent, 0);

