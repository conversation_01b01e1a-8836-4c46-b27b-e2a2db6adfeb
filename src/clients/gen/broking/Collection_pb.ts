// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Collection.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { InstitutionDetails, InvestabilityStatus, MediaItem } from "./BondDetails_pb.js";
import { file_BondDetails } from "./BondDetails_pb.js";
import type { TagConfig } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Collection.proto.
 */
export const file_Collection: GenFile = /*@__PURE__*/
  fileDesc("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", [file_BondDetails, file_Common]);

/**
 * @generated from message com.stablemoney.api.broking.CollectionResponse
 */
export type CollectionResponse = Message<"com.stablemoney.api.broking.CollectionResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string icon_url = 4;
   */
  iconUrl: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.CollectionItem collection_item = 5;
   */
  collectionItem: CollectionItem[];

  /**
   * @generated from field: string name = 6;
   */
  name: string;

  /**
   * @generated from field: bool include_sold_out = 7;
   */
  includeSoldOut: boolean;

  /**
   * @generated from field: com.stablemoney.api.broking.CollectionResponse.DisplayType display_type = 8;
   */
  displayType: CollectionResponse_DisplayType;
};

/**
 * Describes the message com.stablemoney.api.broking.CollectionResponse.
 * Use `create(CollectionResponseSchema)` to create a new message.
 */
export const CollectionResponseSchema: GenMessage<CollectionResponse> = /*@__PURE__*/
  messageDesc(file_Collection, 0);

/**
 * @generated from enum com.stablemoney.api.broking.CollectionResponse.DisplayType
 */
export enum CollectionResponse_DisplayType {
  /**
   * @generated from enum value: DISPLAY_TYPE_UNKNOWN = 0;
   */
  DISPLAY_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: DEFAULT = 1;
   */
  DEFAULT = 1,

  /**
   * @generated from enum value: MINIMUM_INVESTMENT = 2;
   */
  MINIMUM_INVESTMENT = 2,

  /**
   * @generated from enum value: SHORT_TERM = 4;
   */
  SHORT_TERM = 4,

  /**
   * @generated from enum value: SHORT_TERM_XIRR = 6;
   */
  SHORT_TERM_XIRR = 6,

  /**
   * @generated from enum value: SELLING_OUT_SOON = 8;
   */
  SELLING_OUT_SOON = 8,
}

/**
 * Describes the enum com.stablemoney.api.broking.CollectionResponse.DisplayType.
 */
export const CollectionResponse_DisplayTypeSchema: GenEnum<CollectionResponse_DisplayType> = /*@__PURE__*/
  enumDesc(file_Collection, 0, 0);

/**
 * @generated from message com.stablemoney.api.broking.CollectionItemOfferingDetails
 */
export type CollectionItemOfferingDetails = Message<"com.stablemoney.api.broking.CollectionItemOfferingDetails"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: int32 min_lot_size = 2;
   */
  minLotSize: number;
};

/**
 * Describes the message com.stablemoney.api.broking.CollectionItemOfferingDetails.
 * Use `create(CollectionItemOfferingDetailsSchema)` to create a new message.
 */
export const CollectionItemOfferingDetailsSchema: GenMessage<CollectionItemOfferingDetails> = /*@__PURE__*/
  messageDesc(file_Collection, 1);

/**
 * @generated from message com.stablemoney.api.broking.CollectionItem
 */
export type CollectionItem = Message<"com.stablemoney.api.broking.CollectionItem"> & {
  /**
   * @generated from field: string display_title = 1;
   */
  displayTitle: string;

  /**
   * @generated from field: string rating_agency = 2;
   */
  ratingAgency: string;

  /**
   * @generated from field: string rating = 3;
   */
  rating: string;

  /**
   * @generated from field: double xirr = 4;
   */
  xirr: number;

  /**
   * @generated from field: string id = 5;
   */
  id: string;

  /**
   * @generated from field: string bg_color = 6;
   */
  bgColor: string;

  /**
   * @generated from field: com.stablemoney.api.broking.InstitutionDetails about_the_institution = 7;
   */
  aboutTheInstitution?: InstitutionDetails;

  /**
   * @generated from field: string interest_payment = 8;
   */
  interestPayment: string;

  /**
   * @generated from field: string face_value = 9;
   */
  faceValue: string;

  /**
   * @generated from field: string cover_image_url = 10;
   */
  coverImageUrl: string;

  /**
   * @generated from field: com.stablemoney.api.broking.InvestabilityStatus investability_status = 11;
   */
  investabilityStatus: InvestabilityStatus;

  /**
   * @generated from field: string maturity_date = 12;
   */
  maturityDate: string;

  /**
   * @generated from field: string tenure = 13;
   */
  tenure: string;

  /**
   * @generated from field: int32 tenure_in_days = 14;
   */
  tenureInDays: number;

  /**
   * @generated from field: com.stablemoney.api.broking.TagConfig tag_config = 15;
   */
  tagConfig?: TagConfig;

  /**
   * @generated from field: string isin_code = 18;
   */
  isinCode: string;

  /**
   * @generated from field: bool is_tzero_supported = 20;
   */
  isTzeroSupported: boolean;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.CollectionItemOfferingDetails bond_offering_details = 21;
   */
  bondOfferingDetails: CollectionItemOfferingDetails[];

  /**
   * @generated from field: double minimum_investment = 23;
   */
  minimumInvestment: number;

  /**
   * @generated from field: double sold_out_percentage = 24;
   */
  soldOutPercentage: number;

  /**
   * @generated from field: string button_cta = 25;
   */
  buttonCta: string;

  /**
   * @generated from field: double strucken_yield = 26;
   */
  struckenYield: number;

  /**
   * @generated from field: string selling_point = 27;
   */
  sellingPoint: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.MediaItem media_items = 28;
   */
  mediaItems: MediaItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.CollectionItem.
 * Use `create(CollectionItemSchema)` to create a new message.
 */
export const CollectionItemSchema: GenMessage<CollectionItem> = /*@__PURE__*/
  messageDesc(file_Collection, 2);

/**
 * @generated from message com.stablemoney.api.broking.AllCollectionsRequest
 */
export type AllCollectionsRequest = Message<"com.stablemoney.api.broking.AllCollectionsRequest"> & {
  /**
   * @generated from field: string secret_key = 1;
   */
  secretKey: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AllCollectionsRequest.
 * Use `create(AllCollectionsRequestSchema)` to create a new message.
 */
export const AllCollectionsRequestSchema: GenMessage<AllCollectionsRequest> = /*@__PURE__*/
  messageDesc(file_Collection, 3);

/**
 * @generated from message com.stablemoney.api.broking.AllCollectionsResponse
 */
export type AllCollectionsResponse = Message<"com.stablemoney.api.broking.AllCollectionsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.AllCollectionsResponse.Collection collections = 1;
   */
  collections: AllCollectionsResponse_Collection[];
};

/**
 * Describes the message com.stablemoney.api.broking.AllCollectionsResponse.
 * Use `create(AllCollectionsResponseSchema)` to create a new message.
 */
export const AllCollectionsResponseSchema: GenMessage<AllCollectionsResponse> = /*@__PURE__*/
  messageDesc(file_Collection, 4);

/**
 * @generated from message com.stablemoney.api.broking.AllCollectionsResponse.Collection
 */
export type AllCollectionsResponse_Collection = Message<"com.stablemoney.api.broking.AllCollectionsResponse.Collection"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AllCollectionsResponse.Collection.
 * Use `create(AllCollectionsResponse_CollectionSchema)` to create a new message.
 */
export const AllCollectionsResponse_CollectionSchema: GenMessage<AllCollectionsResponse_Collection> = /*@__PURE__*/
  messageDesc(file_Collection, 4, 0);

