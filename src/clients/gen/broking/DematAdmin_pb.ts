// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file DematAdmin.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file DematAdmin.proto.
 */
export const file_DematAdmin: GenFile = /*@__PURE__*/
  fileDesc("ChBEZW1hdEFkbWluLnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmciUQobQWRtaW5BZGREZW1hdEFjY291bnRSZXF1ZXN0EhQKDHBob25lX251bWJlchgBIAEoCRIcChRkZW1hdF9hY2NvdW50X251bWJlchgCIAEoCSJiChlBZG1pbkRlbWF0QWNjb3VudFJlc3BvbnNlEhwKFGRlbWF0X2FjY291bnRfbnVtYmVyGAEgASgJEhIKCmlzX2RlZmF1bHQYAiABKAgSEwoLaXNfdmVyaWZpZWQYAyABKAgibwodQWRtaW5EZW1hdEFjY291bnRSZXNwb25zZUxpc3QSTgoOZGVtYXRfYWNjb3VudHMYASADKAsyNi5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQWRtaW5EZW1hdEFjY291bnRSZXNwb25zZUIfChtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmdQAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.broking.AdminAddDematAccountRequest
 */
export type AdminAddDematAccountRequest = Message<"com.stablemoney.api.broking.AdminAddDematAccountRequest"> & {
  /**
   * @generated from field: string phone_number = 1;
   */
  phoneNumber: string;

  /**
   * @generated from field: string demat_account_number = 2;
   */
  dematAccountNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AdminAddDematAccountRequest.
 * Use `create(AdminAddDematAccountRequestSchema)` to create a new message.
 */
export const AdminAddDematAccountRequestSchema: GenMessage<AdminAddDematAccountRequest> = /*@__PURE__*/
  messageDesc(file_DematAdmin, 0);

/**
 * @generated from message com.stablemoney.api.broking.AdminDematAccountResponse
 */
export type AdminDematAccountResponse = Message<"com.stablemoney.api.broking.AdminDematAccountResponse"> & {
  /**
   * @generated from field: string demat_account_number = 1;
   */
  dematAccountNumber: string;

  /**
   * @generated from field: bool is_default = 2;
   */
  isDefault: boolean;

  /**
   * @generated from field: bool is_verified = 3;
   */
  isVerified: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.AdminDematAccountResponse.
 * Use `create(AdminDematAccountResponseSchema)` to create a new message.
 */
export const AdminDematAccountResponseSchema: GenMessage<AdminDematAccountResponse> = /*@__PURE__*/
  messageDesc(file_DematAdmin, 1);

/**
 * @generated from message com.stablemoney.api.broking.AdminDematAccountResponseList
 */
export type AdminDematAccountResponseList = Message<"com.stablemoney.api.broking.AdminDematAccountResponseList"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.AdminDematAccountResponse demat_accounts = 1;
   */
  dematAccounts: AdminDematAccountResponse[];
};

/**
 * Describes the message com.stablemoney.api.broking.AdminDematAccountResponseList.
 * Use `create(AdminDematAccountResponseListSchema)` to create a new message.
 */
export const AdminDematAccountResponseListSchema: GenMessage<AdminDematAccountResponseList> = /*@__PURE__*/
  messageDesc(file_DematAdmin, 2);

