// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file BankAccountVerification.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file BankAccountVerification.proto.
 */
export const file_BankAccountVerification: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message com.stablemoney.api.broking.InitiateBankVerificationRequest
 */
export type InitiateBankVerificationRequest = Message<"com.stablemoney.api.broking.InitiateBankVerificationRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.BankVerificationType type = 1;
   */
  type: BankVerificationType;

  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateBankVerificationRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.BankAccount bank_account = 2;
     */
    value: BankAccount;
    case: "bankAccount";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.Cheque cheque = 3;
     */
    value: Cheque;
    case: "cheque";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: string transaction_id = 4;
   */
  transactionId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateBankVerificationRequest.
 * Use `create(InitiateBankVerificationRequestSchema)` to create a new message.
 */
export const InitiateBankVerificationRequestSchema: GenMessage<InitiateBankVerificationRequest> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 0);

/**
 * @generated from message com.stablemoney.api.broking.BankAccount
 */
export type BankAccount = Message<"com.stablemoney.api.broking.BankAccount"> & {
  /**
   * @generated from field: string beneficiary_account_no = 1;
   */
  beneficiaryAccountNo: string;

  /**
   * @generated from field: string ifsc_id = 2;
   */
  ifscId: string;

  /**
   * @generated from field: string beneficiary_name = 3;
   */
  beneficiaryName: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BankAccount.
 * Use `create(BankAccountSchema)` to create a new message.
 */
export const BankAccountSchema: GenMessage<BankAccount> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 1);

/**
 * @generated from message com.stablemoney.api.broking.InitiateBankVerificationResponse
 */
export type InitiateBankVerificationResponse = Message<"com.stablemoney.api.broking.InitiateBankVerificationResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateBankVerificationResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiatePdResponse initiate_pd_response = 2;
     */
    value: InitiatePdResponse;
    case: "initiatePdResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiateRpdResponse initiate_rpd_response = 3;
     */
    value: InitiateRpdResponse;
    case: "initiateRpdResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiateChequeResponse initiate_cheque_response = 4;
     */
    value: InitiateChequeResponse;
    case: "initiateChequeResponse";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateBankVerificationResponse.
 * Use `create(InitiateBankVerificationResponseSchema)` to create a new message.
 */
export const InitiateBankVerificationResponseSchema: GenMessage<InitiateBankVerificationResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 2);

/**
 * @generated from message com.stablemoney.api.broking.InitiatePdResponse
 */
export type InitiatePdResponse = Message<"com.stablemoney.api.broking.InitiatePdResponse"> & {
  /**
   * @generated from field: bool verified = 1;
   */
  verified: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.InitiatePdResponse.
 * Use `create(InitiatePdResponseSchema)` to create a new message.
 */
export const InitiatePdResponseSchema: GenMessage<InitiatePdResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 3);

/**
 * @generated from message com.stablemoney.api.broking.InitiateRpdResponse
 */
export type InitiateRpdResponse = Message<"com.stablemoney.api.broking.InitiateRpdResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.InitiateRpdResponse.PaymentOption payment_options = 1;
   */
  paymentOptions: InitiateRpdResponse_PaymentOption[];

  /**
   * @generated from field: string valid_upto = 2;
   */
  validUpto: string;

  /**
   * @generated from field: string ref_id = 3;
   */
  refId: string;

  /**
   * @generated from field: string qr_code = 4;
   */
  qrCode: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateRpdResponse.
 * Use `create(InitiateRpdResponseSchema)` to create a new message.
 */
export const InitiateRpdResponseSchema: GenMessage<InitiateRpdResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 4);

/**
 * @generated from message com.stablemoney.api.broking.InitiateRpdResponse.PaymentOption
 */
export type InitiateRpdResponse_PaymentOption = Message<"com.stablemoney.api.broking.InitiateRpdResponse.PaymentOption"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.InitiateRpdResponse.PaymentApp app = 1;
   */
  app: InitiateRpdResponse_PaymentApp;

  /**
   * @generated from field: string logo_url = 2;
   */
  logoUrl: string;

  /**
   * @generated from field: string upi_intent = 3;
   */
  upiIntent: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateRpdResponse.PaymentOption.
 * Use `create(InitiateRpdResponse_PaymentOptionSchema)` to create a new message.
 */
export const InitiateRpdResponse_PaymentOptionSchema: GenMessage<InitiateRpdResponse_PaymentOption> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 4, 0);

/**
 * @generated from enum com.stablemoney.api.broking.InitiateRpdResponse.PaymentApp
 */
export enum InitiateRpdResponse_PaymentApp {
  /**
   * @generated from enum value: UNKNOWN_APP = 0;
   */
  UNKNOWN_APP = 0,

  /**
   * @generated from enum value: PAYTM = 1;
   */
  PAYTM = 1,

  /**
   * @generated from enum value: BHIM = 2;
   */
  BHIM = 2,

  /**
   * @generated from enum value: GPAY = 3;
   */
  GPAY = 3,

  /**
   * @generated from enum value: PHONEPE = 4;
   */
  PHONEPE = 4,

  /**
   * @generated from enum value: MISC = 5;
   */
  MISC = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.InitiateRpdResponse.PaymentApp.
 */
export const InitiateRpdResponse_PaymentAppSchema: GenEnum<InitiateRpdResponse_PaymentApp> = /*@__PURE__*/
  enumDesc(file_BankAccountVerification, 4, 0);

/**
 * @generated from message com.stablemoney.api.broking.RpdStatusRequest
 */
export type RpdStatusRequest = Message<"com.stablemoney.api.broking.RpdStatusRequest"> & {
  /**
   * @generated from field: string ref_id = 1;
   */
  refId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.RpdStatusRequest.
 * Use `create(RpdStatusRequestSchema)` to create a new message.
 */
export const RpdStatusRequestSchema: GenMessage<RpdStatusRequest> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 5);

/**
 * @generated from message com.stablemoney.api.broking.RpdStatusResponse
 */
export type RpdStatusResponse = Message<"com.stablemoney.api.broking.RpdStatusResponse"> & {
  /**
   * @generated from field: string ref_id = 1;
   */
  refId: string;

  /**
   * @generated from field: string status = 3;
   */
  status: string;

  /**
   * @generated from field: bool verified = 4;
   */
  verified: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.RpdStatusResponse.
 * Use `create(RpdStatusResponseSchema)` to create a new message.
 */
export const RpdStatusResponseSchema: GenMessage<RpdStatusResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 6);

/**
 * @generated from message com.stablemoney.api.broking.AdditionalData
 */
export type AdditionalData = Message<"com.stablemoney.api.broking.AdditionalData"> & {
  /**
   * @generated from field: string ref_id = 1;
   */
  refId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AdditionalData.
 * Use `create(AdditionalDataSchema)` to create a new message.
 */
export const AdditionalDataSchema: GenMessage<AdditionalData> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 7);

/**
 * @generated from message com.stablemoney.api.broking.BankListResponse
 */
export type BankListResponse = Message<"com.stablemoney.api.broking.BankListResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.BankList data = 2;
   */
  data: BankList[];
};

/**
 * Describes the message com.stablemoney.api.broking.BankListResponse.
 * Use `create(BankListResponseSchema)` to create a new message.
 */
export const BankListResponseSchema: GenMessage<BankListResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 8);

/**
 * @generated from message com.stablemoney.api.broking.BankList
 */
export type BankList = Message<"com.stablemoney.api.broking.BankList"> & {
  /**
   * @generated from field: string bank_name = 1;
   */
  bankName: string;

  /**
   * @generated from field: string ifsc = 2;
   */
  ifsc: string;

  /**
   * @generated from field: string logo = 3;
   */
  logo: string;

  /**
   * @generated from field: bool is_popular = 4;
   */
  isPopular: boolean;

  /**
   * @generated from field: string id = 5;
   */
  id: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BankList.
 * Use `create(BankListSchema)` to create a new message.
 */
export const BankListSchema: GenMessage<BankList> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 9);

/**
 * @generated from message com.stablemoney.api.broking.AddBankAccountRequest
 */
export type AddBankAccountRequest = Message<"com.stablemoney.api.broking.AddBankAccountRequest"> & {
  /**
   * @generated from field: string phone_no = 1;
   */
  phoneNo: string;

  /**
   * @generated from field: string beneficiary_account_no = 2;
   */
  beneficiaryAccountNo: string;

  /**
   * @generated from field: string ifsc_code = 3;
   */
  ifscCode: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AddBankAccountRequest.
 * Use `create(AddBankAccountRequestSchema)` to create a new message.
 */
export const AddBankAccountRequestSchema: GenMessage<AddBankAccountRequest> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 10);

/**
 * @generated from message com.stablemoney.api.broking.AddBankAccountResponse
 */
export type AddBankAccountResponse = Message<"com.stablemoney.api.broking.AddBankAccountResponse"> & {
  /**
   * @generated from field: bool is_verified = 1;
   */
  isVerified: boolean;

  /**
   * @generated from field: string is_added_to_nse = 2;
   */
  isAddedToNse: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AddBankAccountResponse.
 * Use `create(AddBankAccountResponseSchema)` to create a new message.
 */
export const AddBankAccountResponseSchema: GenMessage<AddBankAccountResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 11);

/**
 * @generated from message com.stablemoney.api.broking.BankAccountDB
 */
export type BankAccountDB = Message<"com.stablemoney.api.broking.BankAccountDB"> & {
  /**
   * @generated from field: optional string id = 1;
   */
  id?: string;

  /**
   * @generated from field: optional string account_number = 2;
   */
  accountNumber?: string;

  /**
   * @generated from field: optional bool is_verified = 3;
   */
  isVerified?: boolean;

  /**
   * @generated from field: optional com.stablemoney.api.broking.BankVerificationProvider verification_provider = 4;
   */
  verificationProvider?: BankVerificationProvider;

  /**
   * @generated from field: optional com.stablemoney.api.broking.BankVerificationType verification_type = 5;
   */
  verificationType?: BankVerificationType;

  /**
   * @generated from field: optional string beneficiary_name_with_bank = 6;
   */
  beneficiaryNameWithBank?: string;

  /**
   * @generated from field: optional string ifsc_code = 7;
   */
  ifscCode?: string;

  /**
   * @generated from field: optional string metadata = 8;
   */
  metadata?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BankAccountDB.
 * Use `create(BankAccountDBSchema)` to create a new message.
 */
export const BankAccountDBSchema: GenMessage<BankAccountDB> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 12);

/**
 * @generated from message com.stablemoney.api.broking.BankAccountDBUpdateRequest
 */
export type BankAccountDBUpdateRequest = Message<"com.stablemoney.api.broking.BankAccountDBUpdateRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.BankAccountDB bank_account = 1;
   */
  bankAccount?: BankAccountDB;
};

/**
 * Describes the message com.stablemoney.api.broking.BankAccountDBUpdateRequest.
 * Use `create(BankAccountDBUpdateRequestSchema)` to create a new message.
 */
export const BankAccountDBUpdateRequestSchema: GenMessage<BankAccountDBUpdateRequest> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 13);

/**
 * @generated from message com.stablemoney.api.broking.BankAccountDBUpdateResponse
 */
export type BankAccountDBUpdateResponse = Message<"com.stablemoney.api.broking.BankAccountDBUpdateResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.BankAccountDB bank_account = 1;
   */
  bankAccount?: BankAccountDB;
};

/**
 * Describes the message com.stablemoney.api.broking.BankAccountDBUpdateResponse.
 * Use `create(BankAccountDBUpdateResponseSchema)` to create a new message.
 */
export const BankAccountDBUpdateResponseSchema: GenMessage<BankAccountDBUpdateResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 14);

/**
 * @generated from message com.stablemoney.api.broking.UserBankAccountsResponse
 */
export type UserBankAccountsResponse = Message<"com.stablemoney.api.broking.UserBankAccountsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.UserBankAccountInfo user_bank_accounts = 4;
   */
  userBankAccounts: UserBankAccountInfo[];
};

/**
 * Describes the message com.stablemoney.api.broking.UserBankAccountsResponse.
 * Use `create(UserBankAccountsResponseSchema)` to create a new message.
 */
export const UserBankAccountsResponseSchema: GenMessage<UserBankAccountsResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 15);

/**
 * @generated from message com.stablemoney.api.broking.UserBankAccountInfo
 */
export type UserBankAccountInfo = Message<"com.stablemoney.api.broking.UserBankAccountInfo"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string phone_no = 3;
   */
  phoneNo: string;

  /**
   * @generated from field: bool is_verified = 5;
   */
  isVerified: boolean;

  /**
   * @generated from field: bool is_primary = 6;
   */
  isPrimary: boolean;

  /**
   * @generated from field: string pd_status = 7;
   */
  pdStatus: string;

  /**
   * @generated from field: string rpd_status = 8;
   */
  rpdStatus: string;

  /**
   * @generated from field: bool fuzzy_match_result = 9;
   */
  fuzzyMatchResult: boolean;

  /**
   * @generated from field: double fuzzy_match_score = 10;
   */
  fuzzyMatchScore: number;

  /**
   * @generated from field: com.stablemoney.api.broking.BankAccountDB bank_account = 11;
   */
  bankAccount?: BankAccountDB;
};

/**
 * Describes the message com.stablemoney.api.broking.UserBankAccountInfo.
 * Use `create(UserBankAccountInfoSchema)` to create a new message.
 */
export const UserBankAccountInfoSchema: GenMessage<UserBankAccountInfo> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 16);

/**
 * @generated from message com.stablemoney.api.broking.IfscListResponse
 */
export type IfscListResponse = Message<"com.stablemoney.api.broking.IfscListResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.IfscList data = 2;
   */
  data: IfscList[];
};

/**
 * Describes the message com.stablemoney.api.broking.IfscListResponse.
 * Use `create(IfscListResponseSchema)` to create a new message.
 */
export const IfscListResponseSchema: GenMessage<IfscListResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 17);

/**
 * @generated from message com.stablemoney.api.broking.IfscList
 */
export type IfscList = Message<"com.stablemoney.api.broking.IfscList"> & {
  /**
   * @generated from field: string bank = 1;
   */
  bank: string;

  /**
   * @generated from field: string branch = 2;
   */
  branch: string;

  /**
   * @generated from field: string ifsc = 3;
   */
  ifsc: string;

  /**
   * @generated from field: string id = 4;
   */
  id: string;

  /**
   * @generated from field: string bank_logo_url = 5;
   */
  bankLogoUrl: string;
};

/**
 * Describes the message com.stablemoney.api.broking.IfscList.
 * Use `create(IfscListSchema)` to create a new message.
 */
export const IfscListSchema: GenMessage<IfscList> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 18);

/**
 * @generated from message com.stablemoney.api.broking.BankStatusResponse
 */
export type BankStatusResponse = Message<"com.stablemoney.api.broking.BankStatusResponse"> & {
  /**
   * @generated from field: bool is_verified = 1;
   */
  isVerified: boolean;

  /**
   * @generated from field: string rpd_status = 2;
   */
  rpdStatus: string;

  /**
   * @generated from field: string pd_status = 3;
   */
  pdStatus: string;

  /**
   * @generated from field: string cheque_status = 4;
   */
  chequeStatus: string;

  /**
   * @generated from field: string metadata = 5;
   */
  metadata: string;

  /**
   * @generated from field: bool fuzzy_match_result = 6;
   */
  fuzzyMatchResult: boolean;

  /**
   * @generated from field: string account_number = 7;
   */
  accountNumber: string;

  /**
   * @generated from field: string account_holder_name = 8;
   */
  accountHolderName: string;

  /**
   * @generated from field: string bank_name = 9;
   */
  bankName: string;

  /**
   * @generated from field: string branch_name = 10;
   */
  branchName: string;

  /**
   * @generated from field: string ifsc_code = 11;
   */
  ifscCode: string;

  /**
   * @generated from field: string logo = 12;
   */
  logo: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BankStatusResponse.
 * Use `create(BankStatusResponseSchema)` to create a new message.
 */
export const BankStatusResponseSchema: GenMessage<BankStatusResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 19);

/**
 * @generated from message com.stablemoney.api.broking.Cheque
 */
export type Cheque = Message<"com.stablemoney.api.broking.Cheque"> & {
  /**
   * @generated from field: string document_id = 1;
   */
  documentId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.Cheque.
 * Use `create(ChequeSchema)` to create a new message.
 */
export const ChequeSchema: GenMessage<Cheque> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 20);

/**
 * @generated from message com.stablemoney.api.broking.InitiateChequeResponse
 */
export type InitiateChequeResponse = Message<"com.stablemoney.api.broking.InitiateChequeResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateChequeResponse.
 * Use `create(InitiateChequeResponseSchema)` to create a new message.
 */
export const InitiateChequeResponseSchema: GenMessage<InitiateChequeResponse> = /*@__PURE__*/
  messageDesc(file_BankAccountVerification, 21);

/**
 * @generated from enum com.stablemoney.api.broking.BankVerificationType
 */
export enum BankVerificationType {
  /**
   * @generated from enum value: UNKNOWN_TYPE = 0;
   */
  UNKNOWN_TYPE = 0,

  /**
   * @generated from enum value: PENNY_DROP = 1;
   */
  PENNY_DROP = 1,

  /**
   * @generated from enum value: REVERSE_PENNY_DROP = 2;
   */
  REVERSE_PENNY_DROP = 2,

  /**
   * @generated from enum value: CANCEL_CHEQUE = 3;
   */
  CANCEL_CHEQUE = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.BankVerificationType.
 */
export const BankVerificationTypeSchema: GenEnum<BankVerificationType> = /*@__PURE__*/
  enumDesc(file_BankAccountVerification, 0);

/**
 * @generated from enum com.stablemoney.api.broking.BankVerificationProvider
 */
export enum BankVerificationProvider {
  /**
   * @generated from enum value: UNKNOWN_PROVIDER = 0;
   */
  UNKNOWN_PROVIDER = 0,

  /**
   * @generated from enum value: DIGIO_PD = 1;
   */
  DIGIO_PD = 1,

  /**
   * @generated from enum value: SETU_RPD = 2;
   */
  SETU_RPD = 2,

  /**
   * @generated from enum value: CHEQUE = 3;
   */
  CHEQUE = 3,

  /**
   * @generated from enum value: SIGNZY_OCR_READ = 4;
   */
  SIGNZY_OCR_READ = 4,

  /**
   * @generated from enum value: CASHFREE_RPD = 5;
   */
  CASHFREE_RPD = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.BankVerificationProvider.
 */
export const BankVerificationProviderSchema: GenEnum<BankVerificationProvider> = /*@__PURE__*/
  enumDesc(file_BankAccountVerification, 1);

