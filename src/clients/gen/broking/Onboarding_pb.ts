// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Onboarding.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { OnBoardingStatus, StepName } from "./Kyc_pb.js";
import { file_Kyc } from "./Kyc_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Onboarding.proto.
 */
export const file_Onboarding: GenFile = /*@__PURE__*/
  fileDesc("ChBPbmJvYXJkaW5nLnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmciVwobVXNlck9uYm9hcmRpbmdTdGVwc1Jlc3BvbnNlEjgKBGRhdGEYAiADKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuVXNlclN0YXRlRGF0YSKOAQoNVXNlclN0YXRlRGF0YRI+Cg9vbmJvYXJkaW5nX3N0ZXAYASABKA4yJS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuU3RlcE5hbWUSPQoGc3RhdHVzGAIgASgOMi0uY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLk9uQm9hcmRpbmdTdGF0dXMiYQodT25ib2FyZGluZ01vZHVsZVN0ZXBzUmVzcG9uc2USQAoEZGF0YRgBIAMoCzIyLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5PbmJvYXJkaW5nTW9kdWxlU3RlcHMikwEKFU9uYm9hcmRpbmdNb2R1bGVTdGVwcxI+Cg9vbmJvYXJkaW5nX3N0ZXAYASABKA4yJS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuU3RlcE5hbWUSFAoMa3ljX3NlcXVlbmNlGAIgASgFEg4KBm1vZHVsZRgDIAEoCRIUCgxpc19za2lwcGFibGUYBCABKAgiGAoWU2tpcE9uYm9hcmRpbmdSZXNwb25zZSJuChtDaGFuZ2VPbmJvYXJkaW5nU3RlcFJlcXVlc3QSDwoHdXNlcl9pZBgBIAEoCRI+Cg9vbmJvYXJkaW5nX3N0ZXAYAiABKA4yJS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuU3RlcE5hbWUiLwocQ2hhbmdlT25ib2FyZGluZ1N0ZXBSZXNwb25zZRIPCgdjaGFuZ2VkGAEgASgIKpgBChBPbmJvYXJkaW5nTW9kdWxlEh0KGU9OQk9BUkRJTkdfTU9EVUxFX1VOS05PV04QABISCg5BUFBfT05CT0FSRElORxABEhEKDUZJWEVEX0RFUE9TSVQQAhIPCgtNVVRVQUxfRlVORBADEhMKD0JPTkRfT05CT0FSRElORxAEEhgKFFBPU1RfQk9ORF9PTkJPQVJESU5HEAVCHwobY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nUAFiBnByb3RvMw", [file_Kyc]);

/**
 * @generated from message com.stablemoney.api.broking.UserOnboardingStepsResponse
 */
export type UserOnboardingStepsResponse = Message<"com.stablemoney.api.broking.UserOnboardingStepsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.UserStateData data = 2;
   */
  data: UserStateData[];
};

/**
 * Describes the message com.stablemoney.api.broking.UserOnboardingStepsResponse.
 * Use `create(UserOnboardingStepsResponseSchema)` to create a new message.
 */
export const UserOnboardingStepsResponseSchema: GenMessage<UserOnboardingStepsResponse> = /*@__PURE__*/
  messageDesc(file_Onboarding, 0);

/**
 * @generated from message com.stablemoney.api.broking.UserStateData
 */
export type UserStateData = Message<"com.stablemoney.api.broking.UserStateData"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.StepName onboarding_step = 1;
   */
  onboardingStep: StepName;

  /**
   * @generated from field: com.stablemoney.api.broking.OnBoardingStatus status = 2;
   */
  status: OnBoardingStatus;
};

/**
 * Describes the message com.stablemoney.api.broking.UserStateData.
 * Use `create(UserStateDataSchema)` to create a new message.
 */
export const UserStateDataSchema: GenMessage<UserStateData> = /*@__PURE__*/
  messageDesc(file_Onboarding, 1);

/**
 * @generated from message com.stablemoney.api.broking.OnboardingModuleStepsResponse
 */
export type OnboardingModuleStepsResponse = Message<"com.stablemoney.api.broking.OnboardingModuleStepsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.OnboardingModuleSteps data = 1;
   */
  data: OnboardingModuleSteps[];
};

/**
 * Describes the message com.stablemoney.api.broking.OnboardingModuleStepsResponse.
 * Use `create(OnboardingModuleStepsResponseSchema)` to create a new message.
 */
export const OnboardingModuleStepsResponseSchema: GenMessage<OnboardingModuleStepsResponse> = /*@__PURE__*/
  messageDesc(file_Onboarding, 2);

/**
 * @generated from message com.stablemoney.api.broking.OnboardingModuleSteps
 */
export type OnboardingModuleSteps = Message<"com.stablemoney.api.broking.OnboardingModuleSteps"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.StepName onboarding_step = 1;
   */
  onboardingStep: StepName;

  /**
   * @generated from field: int32 kyc_sequence = 2;
   */
  kycSequence: number;

  /**
   * @generated from field: string module = 3;
   */
  module: string;

  /**
   * @generated from field: bool is_skippable = 4;
   */
  isSkippable: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.OnboardingModuleSteps.
 * Use `create(OnboardingModuleStepsSchema)` to create a new message.
 */
export const OnboardingModuleStepsSchema: GenMessage<OnboardingModuleSteps> = /*@__PURE__*/
  messageDesc(file_Onboarding, 3);

/**
 * @generated from message com.stablemoney.api.broking.SkipOnboardingResponse
 */
export type SkipOnboardingResponse = Message<"com.stablemoney.api.broking.SkipOnboardingResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.SkipOnboardingResponse.
 * Use `create(SkipOnboardingResponseSchema)` to create a new message.
 */
export const SkipOnboardingResponseSchema: GenMessage<SkipOnboardingResponse> = /*@__PURE__*/
  messageDesc(file_Onboarding, 4);

/**
 * @generated from message com.stablemoney.api.broking.ChangeOnboardingStepRequest
 */
export type ChangeOnboardingStepRequest = Message<"com.stablemoney.api.broking.ChangeOnboardingStepRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.StepName onboarding_step = 2;
   */
  onboardingStep: StepName;
};

/**
 * Describes the message com.stablemoney.api.broking.ChangeOnboardingStepRequest.
 * Use `create(ChangeOnboardingStepRequestSchema)` to create a new message.
 */
export const ChangeOnboardingStepRequestSchema: GenMessage<ChangeOnboardingStepRequest> = /*@__PURE__*/
  messageDesc(file_Onboarding, 5);

/**
 * @generated from message com.stablemoney.api.broking.ChangeOnboardingStepResponse
 */
export type ChangeOnboardingStepResponse = Message<"com.stablemoney.api.broking.ChangeOnboardingStepResponse"> & {
  /**
   * @generated from field: bool changed = 1;
   */
  changed: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.ChangeOnboardingStepResponse.
 * Use `create(ChangeOnboardingStepResponseSchema)` to create a new message.
 */
export const ChangeOnboardingStepResponseSchema: GenMessage<ChangeOnboardingStepResponse> = /*@__PURE__*/
  messageDesc(file_Onboarding, 6);

/**
 * @generated from enum com.stablemoney.api.broking.OnboardingModule
 */
export enum OnboardingModule {
  /**
   * @generated from enum value: ONBOARDING_MODULE_UNKNOWN = 0;
   */
  ONBOARDING_MODULE_UNKNOWN = 0,

  /**
   * @generated from enum value: APP_ONBOARDING = 1;
   */
  APP_ONBOARDING = 1,

  /**
   * @generated from enum value: FIXED_DEPOSIT = 2;
   */
  FIXED_DEPOSIT = 2,

  /**
   * @generated from enum value: MUTUAL_FUND = 3;
   */
  MUTUAL_FUND = 3,

  /**
   * @generated from enum value: BOND_ONBOARDING = 4;
   */
  BOND_ONBOARDING = 4,

  /**
   * @generated from enum value: POST_BOND_ONBOARDING = 5;
   */
  POST_BOND_ONBOARDING = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.OnboardingModule.
 */
export const OnboardingModuleSchema: GenEnum<OnboardingModule> = /*@__PURE__*/
  enumDesc(file_Onboarding, 0);

