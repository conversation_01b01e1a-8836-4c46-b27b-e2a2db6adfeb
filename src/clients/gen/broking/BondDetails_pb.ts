// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file BondDetails.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { DataKey, RedirectDeeplink } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file BondDetails.proto.
 */
export const file_BondDetails: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Common]);

/**
 * @generated from message com.stablemoney.api.broking.InstitutionStat
 */
export type InstitutionStat = Message<"com.stablemoney.api.broking.InstitutionStat"> & {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: string value = 2;
   */
  value: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InstitutionStat.
 * Use `create(InstitutionStatSchema)` to create a new message.
 */
export const InstitutionStatSchema: GenMessage<InstitutionStat> = /*@__PURE__*/
  messageDesc(file_BondDetails, 0);

/**
 * @generated from message com.stablemoney.api.broking.InstitutionDetails
 */
export type InstitutionDetails = Message<"com.stablemoney.api.broking.InstitutionDetails"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: string sector = 3;
   */
  sector: string;

  /**
   * @generated from field: string bond_institution_webpage = 4;
   */
  bondInstitutionWebpage: string;

  /**
   * @generated from field: string bond_institution_name = 5;
   */
  bondInstitutionName: string;

  /**
   * @generated from field: string logo = 6;
   */
  logo: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.InstitutionStat stats = 7;
   */
  stats: InstitutionStat[];

  /**
   * @generated from field: string id = 8;
   */
  id: string;

  /**
   * @generated from field: string slug = 10;
   */
  slug: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InstitutionDetails.
 * Use `create(InstitutionDetailsSchema)` to create a new message.
 */
export const InstitutionDetailsSchema: GenMessage<InstitutionDetails> = /*@__PURE__*/
  messageDesc(file_BondDetails, 1);

/**
 * @generated from message com.stablemoney.api.broking.InterestPaymentSchedule
 */
export type InterestPaymentSchedule = Message<"com.stablemoney.api.broking.InterestPaymentSchedule"> & {
  /**
   * @generated from field: string type = 1;
   */
  type: string;

  /**
   * @generated from field: double interest_amount = 2;
   */
  interestAmount: number;

  /**
   * @generated from field: string date = 3;
   */
  date: string;

  /**
   * @generated from field: double principal_amount = 4;
   */
  principalAmount: number;

  /**
   * @generated from field: string date_time = 5;
   */
  dateTime: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InterestPaymentSchedule.
 * Use `create(InterestPaymentScheduleSchema)` to create a new message.
 */
export const InterestPaymentScheduleSchema: GenMessage<InterestPaymentSchedule> = /*@__PURE__*/
  messageDesc(file_BondDetails, 2);

/**
 * @generated from message com.stablemoney.api.broking.BondTagItem
 */
export type BondTagItem = Message<"com.stablemoney.api.broking.BondTagItem"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string tag_type = 2;
   */
  tagType: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BondTagItem.
 * Use `create(BondTagItemSchema)` to create a new message.
 */
export const BondTagItemSchema: GenMessage<BondTagItem> = /*@__PURE__*/
  messageDesc(file_BondDetails, 3);

/**
 * @generated from message com.stablemoney.api.broking.BondHighlight
 */
export type BondHighlight = Message<"com.stablemoney.api.broking.BondHighlight"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.DataKey data_key = 1;
   */
  dataKey?: DataKey;
};

/**
 * Describes the message com.stablemoney.api.broking.BondHighlight.
 * Use `create(BondHighlightSchema)` to create a new message.
 */
export const BondHighlightSchema: GenMessage<BondHighlight> = /*@__PURE__*/
  messageDesc(file_BondDetails, 4);

/**
 * @generated from message com.stablemoney.api.broking.BondFactSheet
 */
export type BondFactSheet = Message<"com.stablemoney.api.broking.BondFactSheet"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.DataKey label = 1;
   */
  label?: DataKey;

  /**
   * @generated from field: com.stablemoney.api.broking.DataKey value = 2;
   */
  value?: DataKey;
};

/**
 * Describes the message com.stablemoney.api.broking.BondFactSheet.
 * Use `create(BondFactSheetSchema)` to create a new message.
 */
export const BondFactSheetSchema: GenMessage<BondFactSheet> = /*@__PURE__*/
  messageDesc(file_BondDetails, 5);

/**
 * @generated from message com.stablemoney.api.broking.BondFaqItem
 */
export type BondFaqItem = Message<"com.stablemoney.api.broking.BondFaqItem"> & {
  /**
   * @generated from field: string question = 1;
   */
  question: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BondFaqItem.
 * Use `create(BondFaqItemSchema)` to create a new message.
 */
export const BondFaqItemSchema: GenMessage<BondFaqItem> = /*@__PURE__*/
  messageDesc(file_BondDetails, 6);

/**
 * @generated from message com.stablemoney.api.broking.BondQuantity
 */
export type BondQuantity = Message<"com.stablemoney.api.broking.BondQuantity"> & {
  /**
   * @generated from field: int32 total_count = 1;
   */
  totalCount: number;

  /**
   * @generated from field: int32 available_count = 2;
   */
  availableCount: number;

  /**
   * @generated from field: int32 min_count = 3;
   */
  minCount: number;

  /**
   * @generated from field: int32 max_count = 4;
   */
  maxCount: number;
};

/**
 * Describes the message com.stablemoney.api.broking.BondQuantity.
 * Use `create(BondQuantitySchema)` to create a new message.
 */
export const BondQuantitySchema: GenMessage<BondQuantity> = /*@__PURE__*/
  messageDesc(file_BondDetails, 7);

/**
 * @generated from message com.stablemoney.api.broking.MarketStatus
 */
export type MarketStatus = Message<"com.stablemoney.api.broking.MarketStatus"> & {
  /**
   * @generated from field: bool is_active = 1;
   */
  isActive: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message com.stablemoney.api.broking.MarketStatus.
 * Use `create(MarketStatusSchema)` to create a new message.
 */
export const MarketStatusSchema: GenMessage<MarketStatus> = /*@__PURE__*/
  messageDesc(file_BondDetails, 8);

/**
 * @generated from message com.stablemoney.api.broking.SupportedDocument
 */
export type SupportedDocument = Message<"com.stablemoney.api.broking.SupportedDocument"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string url = 2;
   */
  url: string;
};

/**
 * Describes the message com.stablemoney.api.broking.SupportedDocument.
 * Use `create(SupportedDocumentSchema)` to create a new message.
 */
export const SupportedDocumentSchema: GenMessage<SupportedDocument> = /*@__PURE__*/
  messageDesc(file_BondDetails, 9);

/**
 * @generated from message com.stablemoney.api.broking.MediaItem
 */
export type MediaItem = Message<"com.stablemoney.api.broking.MediaItem"> & {
  /**
   * @generated from field: string section = 1;
   */
  section: string;

  /**
   * @generated from field: com.stablemoney.api.broking.MediaItem.MediaType media_type = 2;
   */
  mediaType: MediaItem_MediaType;

  /**
   * @generated from field: string url = 3;
   */
  url: string;

  /**
   * @generated from field: com.stablemoney.api.broking.MediaItem.ScreenType screen_type = 4;
   */
  screenType: MediaItem_ScreenType;

  /**
   * @generated from field: com.stablemoney.api.broking.RedirectDeeplink redirect_deeplink = 5;
   */
  redirectDeeplink?: RedirectDeeplink;
};

/**
 * Describes the message com.stablemoney.api.broking.MediaItem.
 * Use `create(MediaItemSchema)` to create a new message.
 */
export const MediaItemSchema: GenMessage<MediaItem> = /*@__PURE__*/
  messageDesc(file_BondDetails, 10);

/**
 * @generated from enum com.stablemoney.api.broking.MediaItem.MediaType
 */
export enum MediaItem_MediaType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: IMAGE = 1;
   */
  IMAGE = 1,

  /**
   * @generated from enum value: VIDEO = 2;
   */
  VIDEO = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.MediaItem.MediaType.
 */
export const MediaItem_MediaTypeSchema: GenEnum<MediaItem_MediaType> = /*@__PURE__*/
  enumDesc(file_BondDetails, 10, 0);

/**
 * @generated from enum com.stablemoney.api.broking.MediaItem.ScreenType
 */
export enum MediaItem_ScreenType {
  /**
   * @generated from enum value: SCREEN_TYPE_UNKNOWN = 0;
   */
  SCREEN_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: MOBILE = 1;
   */
  MOBILE = 1,

  /**
   * @generated from enum value: DESKTOP = 2;
   */
  DESKTOP = 2,

  /**
   * @generated from enum value: TABLET = 3;
   */
  TABLET = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.MediaItem.ScreenType.
 */
export const MediaItem_ScreenTypeSchema: GenEnum<MediaItem_ScreenType> = /*@__PURE__*/
  enumDesc(file_BondDetails, 10, 1);

/**
 * @generated from message com.stablemoney.api.broking.BondDetailItem
 */
export type BondDetailItem = Message<"com.stablemoney.api.broking.BondDetailItem"> & {
  /**
   * @generated from field: string bond_name = 1;
   */
  bondName: string;

  /**
   * @generated from field: string bond_type = 2;
   */
  bondType: string;

  /**
   * @generated from field: double xirr = 3;
   */
  xirr: number;

  /**
   * @generated from field: string time_left_to_maturity = 4;
   */
  timeLeftToMaturity: string;

  /**
   * @generated from field: string maturity_date = 5;
   */
  maturityDate: string;

  /**
   * @generated from field: string isin_code = 6;
   */
  isinCode: string;

  /**
   * @generated from field: string interest_payment = 7;
   */
  interestPayment: string;

  /**
   * @generated from field: string principal_repayment = 8;
   */
  principalRepayment: string;

  /**
   * @generated from field: double minimum_investment = 9;
   */
  minimumInvestment: number;

  /**
   * @generated from field: string display_title = 10;
   */
  displayTitle: string;

  /**
   * @generated from field: string id = 11;
   */
  id: string;

  /**
   * @generated from field: string rating = 12;
   */
  rating: string;

  /**
   * @generated from field: double price_per_bond = 13;
   */
  pricePerBond: number;

  /**
   * @generated from field: double clean_price = 14;
   */
  cleanPrice: number;

  /**
   * @generated from field: double dirty_price = 15;
   */
  dirtyPrice: number;

  /**
   * @generated from field: double accrued_interest = 16;
   */
  accruedInterest: number;

  /**
   * @generated from field: int32 issue_size = 17;
   */
  issueSize: number;

  /**
   * @generated from field: string rating_agency = 18;
   */
  ratingAgency: string;

  /**
   * @generated from field: string rating_supporting_url = 19;
   */
  ratingSupportingUrl: string;

  /**
   * @generated from field: double coupon_rate = 20;
   */
  couponRate: number;

  /**
   * @generated from field: int32 count = 21;
   */
  count: number;

  /**
   * @generated from field: string settlement_date = 22;
   */
  settlementDate: string;

  /**
   * @generated from field: com.stablemoney.api.broking.InstitutionDetails about_the_institution = 23;
   */
  aboutTheInstitution?: InstitutionDetails;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.InterestPaymentSchedule interest_payment_and_principal_repayment_schedule = 24;
   */
  interestPaymentAndPrincipalRepaymentSchedule: InterestPaymentSchedule[];

  /**
   * @generated from field: repeated com.stablemoney.api.broking.BondTagItem bond_tags = 25;
   */
  bondTags: BondTagItem[];

  /**
   * @generated from field: repeated com.stablemoney.api.broking.BondFaqItem bond_faqs = 26;
   */
  bondFaqs: BondFaqItem[];

  /**
   * @generated from field: com.stablemoney.api.broking.BondQuantity bond_quantity = 27;
   */
  bondQuantity?: BondQuantity;

  /**
   * @generated from field: com.stablemoney.api.broking.MarketStatus market_status = 28;
   */
  marketStatus?: MarketStatus;

  /**
   * @generated from field: string bg_color = 29;
   */
  bgColor: string;

  /**
   * @generated from field: string face_value = 30;
   */
  faceValue: string;

  /**
   * @generated from field: string issue_date = 31;
   */
  issueDate: string;

  /**
   * @generated from field: string issue_mode = 32;
   */
  issueMode: string;

  /**
   * @generated from field: string information_memorandum = 33;
   */
  informationMemorandum: string;

  /**
   * @generated from field: string debenture_trustee = 34;
   */
  debentureTrustee: string;

  /**
   * @generated from field: string rating_date = 35;
   */
  ratingDate: string;

  /**
   * @generated from field: string financial_snapshot_url = 36;
   */
  financialSnapshotUrl: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.SupportedDocument supported_documents = 37;
   */
  supportedDocuments: SupportedDocument[];

  /**
   * @generated from field: bool is_active = 38;
   */
  isActive: boolean;

  /**
   * @generated from field: string cover_image_url = 39;
   */
  coverImageUrl: string;

  /**
   * @generated from field: string nature = 40;
   */
  nature: string;

  /**
   * @generated from field: string seniority = 41;
   */
  seniority: string;

  /**
   * @generated from field: string coupon_type = 42;
   */
  couponType: string;

  /**
   * @generated from field: string seller_entity_disclosure = 43;
   */
  sellerEntityDisclosure: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.BondHighlight highlights = 45;
   */
  highlights: BondHighlight[];

  /**
   * @generated from field: com.stablemoney.api.broking.InvestabilityStatus investability_status = 46;
   */
  investabilityStatus: InvestabilityStatus;

  /**
   * @generated from field: int32 default_quantity = 47;
   */
  defaultQuantity: number;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.BondFactSheet bond_fact_sheet = 48;
   */
  bondFactSheet: BondFactSheet[];

  /**
   * @generated from field: repeated com.stablemoney.api.broking.MediaItem media_items = 50;
   */
  mediaItems: MediaItem[];

  /**
   * @generated from field: int32 min_quantity = 51;
   */
  minQuantity: number;

  /**
   * @generated from field: optional com.stablemoney.api.broking.BondAmountCalculatorResponse default_calculation_response = 52;
   */
  defaultCalculationResponse?: BondAmountCalculatorResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.BondDetailItem.
 * Use `create(BondDetailItemSchema)` to create a new message.
 */
export const BondDetailItemSchema: GenMessage<BondDetailItem> = /*@__PURE__*/
  messageDesc(file_BondDetails, 11);

/**
 * @generated from message com.stablemoney.api.broking.HomePageBondItem
 */
export type HomePageBondItem = Message<"com.stablemoney.api.broking.HomePageBondItem"> & {
  /**
   * @generated from field: string display_title = 1;
   */
  displayTitle: string;

  /**
   * @generated from field: string rating_agency = 2;
   */
  ratingAgency: string;

  /**
   * @generated from field: string rating = 3;
   */
  rating: string;

  /**
   * @generated from field: double xirr = 4;
   */
  xirr: number;

  /**
   * @generated from field: string id = 5;
   */
  id: string;

  /**
   * @generated from field: string bg_color = 6;
   */
  bgColor: string;

  /**
   * @generated from field: com.stablemoney.api.broking.InstitutionDetails about_the_institution = 7;
   */
  aboutTheInstitution?: InstitutionDetails;

  /**
   * @generated from field: string interest_payment = 8;
   */
  interestPayment: string;

  /**
   * @generated from field: string face_value = 9;
   */
  faceValue: string;

  /**
   * @generated from field: string cover_image_url = 10;
   */
  coverImageUrl: string;
};

/**
 * Describes the message com.stablemoney.api.broking.HomePageBondItem.
 * Use `create(HomePageBondItemSchema)` to create a new message.
 */
export const HomePageBondItemSchema: GenMessage<HomePageBondItem> = /*@__PURE__*/
  messageDesc(file_BondDetails, 12);

/**
 * @generated from message com.stablemoney.api.broking.HomepageConfig
 */
export type HomepageConfig = Message<"com.stablemoney.api.broking.HomepageConfig"> & {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: string value = 2;
   */
  value: string;
};

/**
 * Describes the message com.stablemoney.api.broking.HomepageConfig.
 * Use `create(HomepageConfigSchema)` to create a new message.
 */
export const HomepageConfigSchema: GenMessage<HomepageConfig> = /*@__PURE__*/
  messageDesc(file_BondDetails, 13);

/**
 * @generated from message com.stablemoney.api.broking.HomaPageResponse
 */
export type HomaPageResponse = Message<"com.stablemoney.api.broking.HomaPageResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.HomePageBondItem featured_bonds = 2;
   */
  featuredBonds: HomePageBondItem[];
};

/**
 * Describes the message com.stablemoney.api.broking.HomaPageResponse.
 * Use `create(HomaPageResponseSchema)` to create a new message.
 */
export const HomaPageResponseSchema: GenMessage<HomaPageResponse> = /*@__PURE__*/
  messageDesc(file_BondDetails, 14);

/**
 * @generated from message com.stablemoney.api.broking.BondAmountCalculatorResponse
 */
export type BondAmountCalculatorResponse = Message<"com.stablemoney.api.broking.BondAmountCalculatorResponse"> & {
  /**
   * @generated from field: double total_consideration = 1;
   */
  totalConsideration: number;

  /**
   * @generated from field: double accrued_interest = 2;
   */
  accruedInterest: number;

  /**
   * @generated from field: double stamp_duty = 3;
   */
  stampDuty: number;

  /**
   * @generated from field: double purchase_amount = 4;
   */
  purchaseAmount: number;

  /**
   * @generated from field: double maturity_amount = 5;
   */
  maturityAmount: number;

  /**
   * @generated from field: double average_payout_amount = 6;
   */
  averagePayoutAmount: number;
};

/**
 * Describes the message com.stablemoney.api.broking.BondAmountCalculatorResponse.
 * Use `create(BondAmountCalculatorResponseSchema)` to create a new message.
 */
export const BondAmountCalculatorResponseSchema: GenMessage<BondAmountCalculatorResponse> = /*@__PURE__*/
  messageDesc(file_BondDetails, 15);

/**
 * @generated from message com.stablemoney.api.broking.BondDetailsRequest
 */
export type BondDetailsRequest = Message<"com.stablemoney.api.broking.BondDetailsRequest"> & {
  /**
   * @generated from field: string bond_detail_id = 1;
   */
  bondDetailId: string;

  /**
   * @generated from field: optional string user_id = 2;
   */
  userId?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BondDetailsRequest.
 * Use `create(BondDetailsRequestSchema)` to create a new message.
 */
export const BondDetailsRequestSchema: GenMessage<BondDetailsRequest> = /*@__PURE__*/
  messageDesc(file_BondDetails, 16);

/**
 * @generated from message com.stablemoney.api.broking.BondDetailsResponse
 */
export type BondDetailsResponse = Message<"com.stablemoney.api.broking.BondDetailsResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.BondDetailItem bond_details = 1;
   */
  bondDetails?: BondDetailItem;
};

/**
 * Describes the message com.stablemoney.api.broking.BondDetailsResponse.
 * Use `create(BondDetailsResponseSchema)` to create a new message.
 */
export const BondDetailsResponseSchema: GenMessage<BondDetailsResponse> = /*@__PURE__*/
  messageDesc(file_BondDetails, 17);

/**
 * @generated from message com.stablemoney.api.broking.BondOfferingUpdatesRequest
 */
export type BondOfferingUpdatesRequest = Message<"com.stablemoney.api.broking.BondOfferingUpdatesRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.BondOfferingUpdate bond_offering_updates = 1;
   */
  bondOfferingUpdates: BondOfferingUpdate[];
};

/**
 * Describes the message com.stablemoney.api.broking.BondOfferingUpdatesRequest.
 * Use `create(BondOfferingUpdatesRequestSchema)` to create a new message.
 */
export const BondOfferingUpdatesRequestSchema: GenMessage<BondOfferingUpdatesRequest> = /*@__PURE__*/
  messageDesc(file_BondDetails, 18);

/**
 * @generated from message com.stablemoney.api.broking.BondOfferingUpdate
 */
export type BondOfferingUpdate = Message<"com.stablemoney.api.broking.BondOfferingUpdate"> & {
  /**
   * @generated from field: string bond_offering_detail_id = 1;
   */
  bondOfferingDetailId: string;

  /**
   * @generated from field: int32 inventory_delta = 2;
   */
  inventoryDelta: number;

  /**
   * @generated from field: bool set_is_active_status = 3;
   */
  setIsActiveStatus: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.BondOfferingUpdate.
 * Use `create(BondOfferingUpdateSchema)` to create a new message.
 */
export const BondOfferingUpdateSchema: GenMessage<BondOfferingUpdate> = /*@__PURE__*/
  messageDesc(file_BondDetails, 19);

/**
 * @generated from message com.stablemoney.api.broking.GenerateBondCreationSheetRequest
 */
export type GenerateBondCreationSheetRequest = Message<"com.stablemoney.api.broking.GenerateBondCreationSheetRequest"> & {
  /**
   * @generated from field: string bond_detail_id = 1;
   */
  bondDetailId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.GenerateBondCreationSheetRequest.
 * Use `create(GenerateBondCreationSheetRequestSchema)` to create a new message.
 */
export const GenerateBondCreationSheetRequestSchema: GenMessage<GenerateBondCreationSheetRequest> = /*@__PURE__*/
  messageDesc(file_BondDetails, 20);

/**
 * @generated from message com.stablemoney.api.broking.ChangeBondStatusRequest
 */
export type ChangeBondStatusRequest = Message<"com.stablemoney.api.broking.ChangeBondStatusRequest"> & {
  /**
   * @generated from field: string bond_detail_id = 1;
   */
  bondDetailId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.InvestabilityStatus status = 2;
   */
  status: InvestabilityStatus;
};

/**
 * Describes the message com.stablemoney.api.broking.ChangeBondStatusRequest.
 * Use `create(ChangeBondStatusRequestSchema)` to create a new message.
 */
export const ChangeBondStatusRequestSchema: GenMessage<ChangeBondStatusRequest> = /*@__PURE__*/
  messageDesc(file_BondDetails, 21);

/**
 * @generated from message com.stablemoney.api.broking.BondDetailsByIssuerResponse
 */
export type BondDetailsByIssuerResponse = Message<"com.stablemoney.api.broking.BondDetailsByIssuerResponse"> & {
  /**
   * @generated from field: string display_title = 1;
   */
  displayTitle: string;

  /**
   * @generated from field: string id = 2;
   */
  id: string;

  /**
   * @generated from field: string information_memorandum = 3;
   */
  informationMemorandum: string;

  /**
   * @generated from field: bool is_active = 4;
   */
  isActive: boolean;

  /**
   * @generated from field: string isin_code = 5;
   */
  isinCode: string;

  /**
   * @generated from field: string issue_date = 6;
   */
  issueDate: string;

  /**
   * @generated from field: double issue_face_value = 7;
   */
  issueFaceValue: number;

  /**
   * @generated from field: string issue_mode = 8;
   */
  issueMode: string;

  /**
   * @generated from field: double issue_price = 9;
   */
  issuePrice: number;

  /**
   * @generated from field: int32 issue_size = 10;
   */
  issueSize: number;

  /**
   * @generated from field: string issuer = 11;
   */
  issuer: string;

  /**
   * @generated from field: string maturity_date = 12;
   */
  maturityDate: string;

  /**
   * @generated from field: string name = 13;
   */
  name: string;

  /**
   * @generated from field: string nature_of_bond = 14;
   */
  natureOfBond: string;

  /**
   * @generated from field: com.stablemoney.api.broking.RepaymentFrequency principal_repayment_frequency = 15;
   */
  principalRepaymentFrequency: RepaymentFrequency;

  /**
   * @generated from field: string principal_repayment_frequency_desc = 16;
   */
  principalRepaymentFrequencyDesc: string;

  /**
   * @generated from field: string put_call = 17;
   */
  putCall: string;

  /**
   * @generated from field: string rating = 18;
   */
  rating: string;

  /**
   * @generated from field: string rating_agency = 19;
   */
  ratingAgency: string;

  /**
   * @generated from field: string rating_date = 20;
   */
  ratingDate: string;

  /**
   * @generated from field: string rating_supporting_url = 21;
   */
  ratingSupportingUrl: string;

  /**
   * @generated from field: string type = 22;
   */
  type: string;

  /**
   * @generated from field: string type_of_yield = 23;
   */
  typeOfYield: string;

  /**
   * @generated from field: string updated_at = 24;
   */
  updatedAt: string;

  /**
   * @generated from field: string bond_issuing_institution_id = 25;
   */
  bondIssuingInstitutionId: string;

  /**
   * @generated from field: string coupon_type = 26;
   */
  couponType: string;

  /**
   * @generated from field: string nature = 27;
   */
  nature: string;

  /**
   * @generated from field: string seniority = 28;
   */
  seniority: string;

  /**
   * @generated from field: double yield = 29;
   */
  yield: number;

  /**
   * @generated from field: int32 per_user_purchase_limit = 30;
   */
  perUserPurchaseLimit: number;

  /**
   * @generated from field: int32 min_lot_size = 31;
   */
  minLotSize: number;

  /**
   * @generated from field: com.stablemoney.api.broking.InvestabilityStatus investability_status = 32;
   */
  investabilityStatus: InvestabilityStatus;
};

/**
 * Describes the message com.stablemoney.api.broking.BondDetailsByIssuerResponse.
 * Use `create(BondDetailsByIssuerResponseSchema)` to create a new message.
 */
export const BondDetailsByIssuerResponseSchema: GenMessage<BondDetailsByIssuerResponse> = /*@__PURE__*/
  messageDesc(file_BondDetails, 22);

/**
 * @generated from message com.stablemoney.api.broking.AllBondsRequest
 */
export type AllBondsRequest = Message<"com.stablemoney.api.broking.AllBondsRequest"> & {
  /**
   * @generated from field: int32 page_number = 1;
   */
  pageNumber: number;

  /**
   * @generated from field: int32 page_size = 2;
   */
  pageSize: number;

  /**
   * @generated from field: string secret_key = 3;
   */
  secretKey: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AllBondsRequest.
 * Use `create(AllBondsRequestSchema)` to create a new message.
 */
export const AllBondsRequestSchema: GenMessage<AllBondsRequest> = /*@__PURE__*/
  messageDesc(file_BondDetails, 23);

/**
 * @generated from message com.stablemoney.api.broking.AllBondsResponse
 */
export type AllBondsResponse = Message<"com.stablemoney.api.broking.AllBondsResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.AllBondsResponse.Bond bonds = 1;
   */
  bonds: AllBondsResponse_Bond[];

  /**
   * @generated from field: int32 total_count = 2;
   */
  totalCount: number;
};

/**
 * Describes the message com.stablemoney.api.broking.AllBondsResponse.
 * Use `create(AllBondsResponseSchema)` to create a new message.
 */
export const AllBondsResponseSchema: GenMessage<AllBondsResponse> = /*@__PURE__*/
  messageDesc(file_BondDetails, 24);

/**
 * @generated from message com.stablemoney.api.broking.AllBondsResponse.Bond
 */
export type AllBondsResponse_Bond = Message<"com.stablemoney.api.broking.AllBondsResponse.Bond"> & {
  /**
   * @generated from field: string issuer_slug = 1;
   */
  issuerSlug: string;

  /**
   * @generated from field: string isin_code = 2;
   */
  isinCode: string;

  /**
   * @generated from field: string name = 3;
   */
  name: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AllBondsResponse.Bond.
 * Use `create(AllBondsResponse_BondSchema)` to create a new message.
 */
export const AllBondsResponse_BondSchema: GenMessage<AllBondsResponse_Bond> = /*@__PURE__*/
  messageDesc(file_BondDetails, 24, 0);

/**
 * @generated from enum com.stablemoney.api.broking.RepaymentFrequency
 */
export enum RepaymentFrequency {
  /**
   * @generated from enum value: REPAYMENT_FREQUENCY_UNKNOWN = 0;
   */
  REPAYMENT_FREQUENCY_UNKNOWN = 0,

  /**
   * @generated from enum value: MONTHLY = 1;
   */
  MONTHLY = 1,

  /**
   * @generated from enum value: QUARTERLY = 2;
   */
  QUARTERLY = 2,

  /**
   * @generated from enum value: HALF_YEARLY = 3;
   */
  HALF_YEARLY = 3,

  /**
   * @generated from enum value: YEARLY = 4;
   */
  YEARLY = 4,

  /**
   * @generated from enum value: CUMULATIVE = 5;
   */
  CUMULATIVE = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.RepaymentFrequency.
 */
export const RepaymentFrequencySchema: GenEnum<RepaymentFrequency> = /*@__PURE__*/
  enumDesc(file_BondDetails, 0);

/**
 * @generated from enum com.stablemoney.api.broking.InvestabilityStatus
 */
export enum InvestabilityStatus {
  /**
   * @generated from enum value: INVESTABILITY_STATUS_UNKNOWN = 0;
   */
  INVESTABILITY_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: LIVE = 1;
   */
  LIVE = 1,

  /**
   * @generated from enum value: SOLD_OUT = 2;
   */
  SOLD_OUT = 2,

  /**
   * @generated from enum value: COMING_SOON = 3;
   */
  COMING_SOON = 3,

  /**
   * @generated from enum value: INACTIVE = 4;
   */
  INACTIVE = 4,
}

/**
 * Describes the enum com.stablemoney.api.broking.InvestabilityStatus.
 */
export const InvestabilityStatusSchema: GenEnum<InvestabilityStatus> = /*@__PURE__*/
  enumDesc(file_BondDetails, 1);

/**
 * @generated from enum com.stablemoney.api.broking.InventoryAlarmStatus
 */
export enum InventoryAlarmStatus {
  /**
   * @generated from enum value: ALARM_STATUS_UNKNOWN = 0;
   */
  ALARM_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: NORMAL = 1;
   */
  NORMAL = 1,

  /**
   * @generated from enum value: LEVEL_1 = 2;
   */
  LEVEL_1 = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.InventoryAlarmStatus.
 */
export const InventoryAlarmStatusSchema: GenEnum<InventoryAlarmStatus> = /*@__PURE__*/
  enumDesc(file_BondDetails, 2);

