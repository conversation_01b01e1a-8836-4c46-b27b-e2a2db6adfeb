// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file CollectionAdmin.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { CollectionType, TagConfig } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file CollectionAdmin.proto.
 */
export const file_CollectionAdmin: GenFile = /*@__PURE__*/
  fileDesc("ChVDb2xsZWN0aW9uQWRtaW4ucHJvdG8SG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZyKVAgoXQ3JlYXRlQ29sbGVjdGlvblJlcXVlc3QSDAoEbmFtZRgBIAEoCRINCgV0aXRsZRgCIAEoCRITCgtkZXNjcmlwdGlvbhgDIAEoCRJECg9jb2xsZWN0aW9uX3R5cGUYBCABKA4yKy5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQ29sbGVjdGlvblR5cGUSHwoXZXhwcmVzc2lvbl93aGVyZV9jbGF1c2UYBSABKAkSIgoaZXhwcmVzc2lvbl9vcmRlcl9ieV9jbGF1c2UYBiABKAkSEQoJaXNfYWN0aXZlGAcgASgIEhgKEGluY2x1ZGVfc29sZF9vdXQYCCABKAgSEAoIaWNvbl91cmwYCSABKAkiGgoYQ3JlYXRlQ29sbGVjdGlvblJlc3BvbnNlImkKHENyZWF0ZUJ1bGtDb2xsZWN0aW9uc1JlcXVlc3QSSQoLY29sbGVjdGlvbnMYASADKAsyNC5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuQ3JlYXRlQ29sbGVjdGlvblJlcXVlc3QiHwodQ3JlYXRlQnVsa0NvbGxlY3Rpb25zUmVzcG9uc2Ui9gEKG0NyZWF0ZUNvbGxlY3Rpb25JdGVtUmVxdWVzdBIRCglpc2luX2NvZGUYASABKAkSHgoWaXNzdWluZ19pbnN0aXR1dGlvbl9pZBgCIAEoCRIVCg1kaXNwbGF5X3RpdGxlGAMgASgJEhEKCWlzX2FjdGl2ZRgEIAEoCBIQCghwcmlvcml0eRgFIAEoBRIVCg1jb2xsZWN0aW9uX2lkGAYgASgJEjkKCXRhZ0NvbmZpZxgHIAEoCzImLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5UYWdDb25maWcSDwoCaWQYCCABKAlIAIgBAUIFCgNfaWQiHgocQ3JlYXRlQ29sbGVjdGlvbkl0ZW1SZXNwb25zZSJ2CiBDcmVhdGVCdWxrQ29sbGVjdGlvbkl0ZW1zUmVxdWVzdBJSChBjb2xsZWN0aW9uX2l0ZW1zGAEgAygLMjguY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkNyZWF0ZUNvbGxlY3Rpb25JdGVtUmVxdWVzdCIjCiFDcmVhdGVCdWxrQ29sbGVjdGlvbkl0ZW1zUmVzcG9uc2VCHwobY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nUAFiBnByb3RvMw", [file_Common]);

/**
 * @generated from message com.stablemoney.api.broking.CreateCollectionRequest
 */
export type CreateCollectionRequest = Message<"com.stablemoney.api.broking.CreateCollectionRequest"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string title = 2;
   */
  title: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: com.stablemoney.api.broking.CollectionType collection_type = 4;
   */
  collectionType: CollectionType;

  /**
   * @generated from field: string expression_where_clause = 5;
   */
  expressionWhereClause: string;

  /**
   * @generated from field: string expression_order_by_clause = 6;
   */
  expressionOrderByClause: string;

  /**
   * @generated from field: bool is_active = 7;
   */
  isActive: boolean;

  /**
   * @generated from field: bool include_sold_out = 8;
   */
  includeSoldOut: boolean;

  /**
   * @generated from field: string icon_url = 9;
   */
  iconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.broking.CreateCollectionRequest.
 * Use `create(CreateCollectionRequestSchema)` to create a new message.
 */
export const CreateCollectionRequestSchema: GenMessage<CreateCollectionRequest> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 0);

/**
 * @generated from message com.stablemoney.api.broking.CreateCollectionResponse
 */
export type CreateCollectionResponse = Message<"com.stablemoney.api.broking.CreateCollectionResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.CreateCollectionResponse.
 * Use `create(CreateCollectionResponseSchema)` to create a new message.
 */
export const CreateCollectionResponseSchema: GenMessage<CreateCollectionResponse> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 1);

/**
 * @generated from message com.stablemoney.api.broking.CreateBulkCollectionsRequest
 */
export type CreateBulkCollectionsRequest = Message<"com.stablemoney.api.broking.CreateBulkCollectionsRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.CreateCollectionRequest collections = 1;
   */
  collections: CreateCollectionRequest[];
};

/**
 * Describes the message com.stablemoney.api.broking.CreateBulkCollectionsRequest.
 * Use `create(CreateBulkCollectionsRequestSchema)` to create a new message.
 */
export const CreateBulkCollectionsRequestSchema: GenMessage<CreateBulkCollectionsRequest> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 2);

/**
 * @generated from message com.stablemoney.api.broking.CreateBulkCollectionsResponse
 */
export type CreateBulkCollectionsResponse = Message<"com.stablemoney.api.broking.CreateBulkCollectionsResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.CreateBulkCollectionsResponse.
 * Use `create(CreateBulkCollectionsResponseSchema)` to create a new message.
 */
export const CreateBulkCollectionsResponseSchema: GenMessage<CreateBulkCollectionsResponse> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 3);

/**
 * @generated from message com.stablemoney.api.broking.CreateCollectionItemRequest
 */
export type CreateCollectionItemRequest = Message<"com.stablemoney.api.broking.CreateCollectionItemRequest"> & {
  /**
   * @generated from field: string isin_code = 1;
   */
  isinCode: string;

  /**
   * @generated from field: string issuing_institution_id = 2;
   */
  issuingInstitutionId: string;

  /**
   * @generated from field: string display_title = 3;
   */
  displayTitle: string;

  /**
   * @generated from field: bool is_active = 4;
   */
  isActive: boolean;

  /**
   * @generated from field: int32 priority = 5;
   */
  priority: number;

  /**
   * @generated from field: string collection_id = 6;
   */
  collectionId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.TagConfig tagConfig = 7;
   */
  tagConfig?: TagConfig;

  /**
   * @generated from field: optional string id = 8;
   */
  id?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.CreateCollectionItemRequest.
 * Use `create(CreateCollectionItemRequestSchema)` to create a new message.
 */
export const CreateCollectionItemRequestSchema: GenMessage<CreateCollectionItemRequest> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 4);

/**
 * @generated from message com.stablemoney.api.broking.CreateCollectionItemResponse
 */
export type CreateCollectionItemResponse = Message<"com.stablemoney.api.broking.CreateCollectionItemResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.CreateCollectionItemResponse.
 * Use `create(CreateCollectionItemResponseSchema)` to create a new message.
 */
export const CreateCollectionItemResponseSchema: GenMessage<CreateCollectionItemResponse> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 5);

/**
 * @generated from message com.stablemoney.api.broking.CreateBulkCollectionItemsRequest
 */
export type CreateBulkCollectionItemsRequest = Message<"com.stablemoney.api.broking.CreateBulkCollectionItemsRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.CreateCollectionItemRequest collection_items = 1;
   */
  collectionItems: CreateCollectionItemRequest[];
};

/**
 * Describes the message com.stablemoney.api.broking.CreateBulkCollectionItemsRequest.
 * Use `create(CreateBulkCollectionItemsRequestSchema)` to create a new message.
 */
export const CreateBulkCollectionItemsRequestSchema: GenMessage<CreateBulkCollectionItemsRequest> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 6);

/**
 * @generated from message com.stablemoney.api.broking.CreateBulkCollectionItemsResponse
 */
export type CreateBulkCollectionItemsResponse = Message<"com.stablemoney.api.broking.CreateBulkCollectionItemsResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.CreateBulkCollectionItemsResponse.
 * Use `create(CreateBulkCollectionItemsResponseSchema)` to create a new message.
 */
export const CreateBulkCollectionItemsResponseSchema: GenMessage<CreateBulkCollectionItemsResponse> = /*@__PURE__*/
  messageDesc(file_CollectionAdmin, 7);

