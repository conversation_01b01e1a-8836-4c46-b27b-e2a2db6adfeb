// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Time.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Time.proto.
 */
export const file_Time: GenFile = /*@__PURE__*/
  fileDesc("CgpUaW1lLnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmciawoTQ3VycmVudFRpbWVSZXNwb25zZRIYChBjdXJyZW50X3RpbWV6b25lGAEgASgJEhgKEGN1cnJlbnRfdGltZV9pc28YAiABKAkSIAoYY3VycmVudF90aW1lX2Vwb2NoX21pbGxpGAMgASgDQh8KG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZ1ABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.broking.CurrentTimeResponse
 */
export type CurrentTimeResponse = Message<"com.stablemoney.api.broking.CurrentTimeResponse"> & {
  /**
   * @generated from field: string current_timezone = 1;
   */
  currentTimezone: string;

  /**
   * @generated from field: string current_time_iso = 2;
   */
  currentTimeIso: string;

  /**
   * @generated from field: int64 current_time_epoch_milli = 3;
   */
  currentTimeEpochMilli: bigint;
};

/**
 * Describes the message com.stablemoney.api.broking.CurrentTimeResponse.
 * Use `create(CurrentTimeResponseSchema)` to create a new message.
 */
export const CurrentTimeResponseSchema: GenMessage<CurrentTimeResponse> = /*@__PURE__*/
  messageDesc(file_Time, 0);

