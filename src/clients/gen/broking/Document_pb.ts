// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Document.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Document.proto.
 */
export const file_Document: GenFile = /*@__PURE__*/
  fileDesc("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");

/**
 * @generated from message com.stablemoney.api.broking.DocumentUploadResponse
 */
export type DocumentUploadResponse = Message<"com.stablemoney.api.broking.DocumentUploadResponse"> & {
  /**
   * @generated from field: string document_id = 1;
   */
  documentId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.DocumentUploadResponse.
 * Use `create(DocumentUploadResponseSchema)` to create a new message.
 */
export const DocumentUploadResponseSchema: GenMessage<DocumentUploadResponse> = /*@__PURE__*/
  messageDesc(file_Document, 0);

/**
 * @generated from message com.stablemoney.api.broking.BondDocumentUploadResponse
 */
export type BondDocumentUploadResponse = Message<"com.stablemoney.api.broking.BondDocumentUploadResponse"> & {
  /**
   * @generated from field: string s3_key = 1;
   */
  s3Key: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BondDocumentUploadResponse.
 * Use `create(BondDocumentUploadResponseSchema)` to create a new message.
 */
export const BondDocumentUploadResponseSchema: GenMessage<BondDocumentUploadResponse> = /*@__PURE__*/
  messageDesc(file_Document, 1);

/**
 * @generated from message com.stablemoney.api.broking.BondDocumentUploadRequest
 */
export type BondDocumentUploadRequest = Message<"com.stablemoney.api.broking.BondDocumentUploadRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.BondDocumentType bond_document_type = 1;
   */
  bondDocumentType: BondDocumentType;

  /**
   * @generated from field: optional com.stablemoney.api.broking.CreateOrderTicketRequest create_order_ticket_request = 2;
   */
  createOrderTicketRequest?: CreateOrderTicketRequest;

  /**
   * @generated from field: optional com.stablemoney.api.broking.DealSheetRequest deal_sheet_request = 3;
   */
  dealSheetRequest?: DealSheetRequest;

  /**
   * @generated from field: optional com.stablemoney.api.broking.QuoteReceiptRequest quote_receipt_request = 5;
   */
  quoteReceiptRequest?: QuoteReceiptRequest;

  /**
   * @generated from field: string user_id = 4;
   */
  userId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.BondDocumentUploadRequest.
 * Use `create(BondDocumentUploadRequestSchema)` to create a new message.
 */
export const BondDocumentUploadRequestSchema: GenMessage<BondDocumentUploadRequest> = /*@__PURE__*/
  messageDesc(file_Document, 2);

/**
 * @generated from message com.stablemoney.api.broking.CreateOrderTicketRequest
 */
export type CreateOrderTicketRequest = Message<"com.stablemoney.api.broking.CreateOrderTicketRequest"> & {
  /**
   * @generated from field: string trade_type = 1;
   */
  tradeType: string;

  /**
   * @generated from field: string security_name = 2;
   */
  securityName: string;

  /**
   * @generated from field: string isin = 3;
   */
  isin: string;

  /**
   * @generated from field: string security_issuer_name = 4;
   */
  securityIssuerName: string;

  /**
   * @generated from field: string seller_name = 5;
   */
  sellerName: string;

  /**
   * @generated from field: string buyer_name = 6;
   */
  buyerName: string;

  /**
   * @generated from field: string buyer_pan = 7;
   */
  buyerPan: string;

  /**
   * @generated from field: string buyer_bank_account_number = 8;
   */
  buyerBankAccountNumber: string;

  /**
   * @generated from field: string buyer_dp_id = 9;
   */
  buyerDpId: string;

  /**
   * @generated from field: string buyer_demat_account_number = 10;
   */
  buyerDematAccountNumber: string;

  /**
   * @generated from field: string debenture_trustee = 11;
   */
  debentureTrustee: string;

  /**
   * @generated from field: string rating_agency = 12;
   */
  ratingAgency: string;

  /**
   * @generated from field: string rating = 13;
   */
  rating: string;

  /**
   * @generated from field: string issuance = 14;
   */
  issuance: string;

  /**
   * @generated from field: string depository = 15;
   */
  depository: string;

  /**
   * @generated from field: string settlement_platform = 16;
   */
  settlementPlatform: string;

  /**
   * @generated from field: string trade_date_time = 17;
   */
  tradeDateTime: string;

  /**
   * @generated from field: string settlement_date = 18;
   */
  settlementDate: string;

  /**
   * @generated from field: string interest_type = 19;
   */
  interestType: string;

  /**
   * @generated from field: string offered_yield = 20;
   */
  offeredYield: string;

  /**
   * @generated from field: string interest_payment_frequency = 21;
   */
  interestPaymentFrequency: string;

  /**
   * @generated from field: int32 purchase_quantity = 22;
   */
  purchaseQuantity: number;

  /**
   * @generated from field: double face_value = 23;
   */
  faceValue: number;

  /**
   * @generated from field: double bond_price = 24;
   */
  bondPrice: number;

  /**
   * @generated from field: double clean_price = 25;
   */
  cleanPrice: number;

  /**
   * @generated from field: double dirty_price = 26;
   */
  dirtyPrice: number;

  /**
   * @generated from field: double accrued_interest = 27;
   */
  accruedInterest: number;

  /**
   * @generated from field: double total_consideration = 28;
   */
  totalConsideration: number;

  /**
   * @generated from field: string iccl_benficiary_name = 29;
   */
  icclBenficiaryName: string;

  /**
   * @generated from field: string iccl_bank_name = 30;
   */
  icclBankName: string;

  /**
   * @generated from field: string iccl_bank_account_number = 31;
   */
  icclBankAccountNumber: string;

  /**
   * @generated from field: string iccl_account_type = 32;
   */
  icclAccountType: string;

  /**
   * @generated from field: string iccl_ifsc_code = 33;
   */
  icclIfscCode: string;

  /**
   * @generated from field: string iccl_branch = 34;
   */
  icclBranch: string;

  /**
   * @generated from field: string iccl_mode = 35;
   */
  icclMode: string;

  /**
   * @generated from field: string order_id = 36;
   */
  orderId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.CreateOrderTicketRequest.
 * Use `create(CreateOrderTicketRequestSchema)` to create a new message.
 */
export const CreateOrderTicketRequestSchema: GenMessage<CreateOrderTicketRequest> = /*@__PURE__*/
  messageDesc(file_Document, 3);

/**
 * @generated from message com.stablemoney.api.broking.DealSheetRequest
 */
export type DealSheetRequest = Message<"com.stablemoney.api.broking.DealSheetRequest"> & {
  /**
   * @generated from field: string market_type = 1;
   */
  marketType: string;

  /**
   * @generated from field: string quote_type = 2;
   */
  quoteType: string;

  /**
   * @generated from field: string deal_type = 3;
   */
  dealType: string;

  /**
   * @generated from field: string deal_id = 4;
   */
  dealId: string;

  /**
   * @generated from field: string order_number = 5;
   */
  orderNumber: string;

  /**
   * @generated from field: string settlement_no = 6;
   */
  settlementNo: string;

  /**
   * @generated from field: string buyer = 7;
   */
  buyer: string;

  /**
   * @generated from field: string seller = 8;
   */
  seller: string;

  /**
   * @generated from field: string isin = 9;
   */
  isin: string;

  /**
   * @generated from field: string issuer_name = 10;
   */
  issuerName: string;

  /**
   * @generated from field: string maturity_date = 11;
   */
  maturityDate: string;

  /**
   * @generated from field: int32 quantity = 12;
   */
  quantity: number;

  /**
   * @generated from field: string trade_date = 13;
   */
  tradeDate: string;

  /**
   * @generated from field: string settlement_type = 14;
   */
  settlementType: string;

  /**
   * @generated from field: string settlement_date = 15;
   */
  settlementDate: string;

  /**
   * @generated from field: double bond_price = 16;
   */
  bondPrice: number;

  /**
   * @generated from field: double trade_value = 17;
   */
  tradeValue: number;

  /**
   * @generated from field: double accrued_interest = 18;
   */
  accruedInterest: number;

  /**
   * @generated from field: double buyer_consideration = 19;
   */
  buyerConsideration: number;

  /**
   * @generated from field: double stamp_duty = 20;
   */
  stampDuty: number;

  /**
   * @generated from field: double seller_consideration = 21;
   */
  sellerConsideration: number;

  /**
   * @generated from field: string deal_time = 22;
   */
  dealTime: string;

  /**
   * @generated from field: string reported_time = 23;
   */
  reportedTime: string;

  /**
   * @generated from field: string approved_time = 24;
   */
  approvedTime: string;

  /**
   * @generated from field: string yield_type = 25;
   */
  yieldType: string;

  /**
   * @generated from field: double yield = 26;
   */
  yield: number;

  /**
   * @generated from field: string status = 27;
   */
  status: string;

  /**
   * @generated from field: string order_id = 28;
   */
  orderId: string;

  /**
   * @generated from field: string trade_date_time = 29;
   */
  tradeDateTime: string;

  /**
   * @generated from field: string settlement_time = 30;
   */
  settlementTime: string;
};

/**
 * Describes the message com.stablemoney.api.broking.DealSheetRequest.
 * Use `create(DealSheetRequestSchema)` to create a new message.
 */
export const DealSheetRequestSchema: GenMessage<DealSheetRequest> = /*@__PURE__*/
  messageDesc(file_Document, 4);

/**
 * @generated from message com.stablemoney.api.broking.QuoteReceiptRequest
 */
export type QuoteReceiptRequest = Message<"com.stablemoney.api.broking.QuoteReceiptRequest"> & {
  /**
   * @generated from field: string order_number = 1;
   */
  orderNumber: string;

  /**
   * @generated from field: string name_of_seller = 2;
   */
  nameOfSeller: string;

  /**
   * @generated from field: string seller_cin = 3;
   */
  sellerCin: string;

  /**
   * @generated from field: string security_name = 4;
   */
  securityName: string;

  /**
   * @generated from field: string isin = 5;
   */
  isin: string;

  /**
   * @generated from field: string issuer_name = 6;
   */
  issuerName: string;

  /**
   * @generated from field: string name_of_buyer = 7;
   */
  nameOfBuyer: string;

  /**
   * @generated from field: string buyer_pan = 8;
   */
  buyerPan: string;

  /**
   * @generated from field: string buyer_demat_account_number = 9;
   */
  buyerDematAccountNumber: string;

  /**
   * @generated from field: string rating = 10;
   */
  rating: string;

  /**
   * @generated from field: string issuance = 11;
   */
  issuance: string;

  /**
   * @generated from field: string depository = 12;
   */
  depository: string;

  /**
   * @generated from field: string settlement_platform = 13;
   */
  settlementPlatform: string;

  /**
   * @generated from field: string date_and_time_of_quote = 14;
   */
  dateAndTimeOfQuote: string;

  /**
   * @generated from field: string date_of_quote = 15;
   */
  dateOfQuote: string;

  /**
   * @generated from field: int32 quantity_quoted = 16;
   */
  quantityQuoted: number;

  /**
   * @generated from field: int32 amount_quoted = 17;
   */
  amountQuoted: number;
};

/**
 * Describes the message com.stablemoney.api.broking.QuoteReceiptRequest.
 * Use `create(QuoteReceiptRequestSchema)` to create a new message.
 */
export const QuoteReceiptRequestSchema: GenMessage<QuoteReceiptRequest> = /*@__PURE__*/
  messageDesc(file_Document, 5);

/**
 * @generated from enum com.stablemoney.api.broking.DocumentType
 */
export enum DocumentType {
  /**
   * @generated from enum value: UNKNOWN_DOCUMENT_TYPE = 0;
   */
  UNKNOWN_DOCUMENT_TYPE = 0,

  /**
   * @generated from enum value: WET_SIGNATURE_DOCUMENT_TYPE = 1;
   */
  WET_SIGNATURE_DOCUMENT_TYPE = 1,

  /**
   * @generated from enum value: ESIGNATURE_DOCUMENT_TYPE = 2;
   */
  ESIGNATURE_DOCUMENT_TYPE = 2,

  /**
   * @generated from enum value: CANCEL_CHEQUE_DOCUMENT_TYPE = 3;
   */
  CANCEL_CHEQUE_DOCUMENT_TYPE = 3,

  /**
   * @generated from enum value: SELFIE_IMAGE_DOCUMENT_TYPE = 4;
   */
  SELFIE_IMAGE_DOCUMENT_TYPE = 4,

  /**
   * @generated from enum value: DEMAT_CML_REPORT_TYPE = 5;
   */
  DEMAT_CML_REPORT_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.DocumentType.
 */
export const DocumentTypeSchema: GenEnum<DocumentType> = /*@__PURE__*/
  enumDesc(file_Document, 0);

/**
 * @generated from enum com.stablemoney.api.broking.BondDocumentType
 */
export enum BondDocumentType {
  /**
   * @generated from enum value: UNKNOWN_BOND_DOCUMENT_TYPE = 0;
   */
  UNKNOWN_BOND_DOCUMENT_TYPE = 0,

  /**
   * @generated from enum value: ORDER_RECEIPT_BOND_DOCUMENT_TYPE = 1;
   */
  ORDER_RECEIPT_BOND_DOCUMENT_TYPE = 1,

  /**
   * @generated from enum value: DEAL_SHEET_BOND_DOCUMENT_TYPE = 2;
   */
  DEAL_SHEET_BOND_DOCUMENT_TYPE = 2,

  /**
   * @generated from enum value: QUOTE_RECEIPT_BOND_DOCUMENT_TYPE = 3;
   */
  QUOTE_RECEIPT_BOND_DOCUMENT_TYPE = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.BondDocumentType.
 */
export const BondDocumentTypeSchema: GenEnum<BondDocumentType> = /*@__PURE__*/
  enumDesc(file_Document, 1);

