// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Procurement.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Procurement.proto.
 */
export const file_Procurement: GenFile = /*@__PURE__*/
  fileDesc("ChFQcm9jdXJlbWVudC5wcm90bxIbY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nIt0BChJQcm9jdXJlbWVudERldGFpbHMSFgoOYm9uZF9kZXRhaWxfaWQYASABKAkSEQoJc2VsbGVyX2lkGAIgASgJEg0KBWNvdW50GAMgASgFEhMKC2NsZWFuX3ByaWNlGAQgASgBEhMKC2RpcnR5X3ByaWNlGAUgASgBEhIKCmZhY2VfdmFsdWUYBiABKAESDAoEeGlychgHIAEoARIYChBwcm9jdXJlbWVudF9kYXRlGAggASgJEgoKAmlkGAkgASgJEhsKE3Byb2N1cmVtZW50X3B1cnBvc2UYCiABKAkiZgoWUHJvY3VyZW1lbnREZXRhaWxzTGlzdBJMChNwcm9jdXJlbWVudF9kZXRhaWxzGAEgAygLMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLlByb2N1cmVtZW50RGV0YWlscyLsAQoRRXh0ZXJuYWxQbmxMZWRnZXISGwoTYm9uZF9wcm9jdXJlbWVudF9pZBgBIAEoCRIQCghxdWFudGl0eRgCIAEoBRIYChBzYWxlX2NsZWFuX3ByaWNlGAMgASgBEhgKEHNhbGVfZGlydHlfcHJpY2UYBCABKAESFwoPc2V0dGxlbWVudF9kYXRlGAUgASgJEhcKD3NhbGVfZmFjZV92YWx1ZRgGIAEoARIYChBib25kX29mZmVyaW5nX2lkGAcgASgJEhIKCmVudHJ5X3R5cGUYCCABKAkSFAoMcG5sX3Blcl9ib25kGAkgASgBQh8KG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZ1ABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.broking.ProcurementDetails
 */
export type ProcurementDetails = Message<"com.stablemoney.api.broking.ProcurementDetails"> & {
  /**
   * @generated from field: string bond_detail_id = 1;
   */
  bondDetailId: string;

  /**
   * @generated from field: string seller_id = 2;
   */
  sellerId: string;

  /**
   * @generated from field: int32 count = 3;
   */
  count: number;

  /**
   * @generated from field: double clean_price = 4;
   */
  cleanPrice: number;

  /**
   * @generated from field: double dirty_price = 5;
   */
  dirtyPrice: number;

  /**
   * @generated from field: double face_value = 6;
   */
  faceValue: number;

  /**
   * @generated from field: double xirr = 7;
   */
  xirr: number;

  /**
   * @generated from field: string procurement_date = 8;
   */
  procurementDate: string;

  /**
   * @generated from field: string id = 9;
   */
  id: string;

  /**
   * @generated from field: string procurement_purpose = 10;
   */
  procurementPurpose: string;
};

/**
 * Describes the message com.stablemoney.api.broking.ProcurementDetails.
 * Use `create(ProcurementDetailsSchema)` to create a new message.
 */
export const ProcurementDetailsSchema: GenMessage<ProcurementDetails> = /*@__PURE__*/
  messageDesc(file_Procurement, 0);

/**
 * @generated from message com.stablemoney.api.broking.ProcurementDetailsList
 */
export type ProcurementDetailsList = Message<"com.stablemoney.api.broking.ProcurementDetailsList"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.ProcurementDetails procurement_details = 1;
   */
  procurementDetails: ProcurementDetails[];
};

/**
 * Describes the message com.stablemoney.api.broking.ProcurementDetailsList.
 * Use `create(ProcurementDetailsListSchema)` to create a new message.
 */
export const ProcurementDetailsListSchema: GenMessage<ProcurementDetailsList> = /*@__PURE__*/
  messageDesc(file_Procurement, 1);

/**
 * @generated from message com.stablemoney.api.broking.ExternalPnlLedger
 */
export type ExternalPnlLedger = Message<"com.stablemoney.api.broking.ExternalPnlLedger"> & {
  /**
   * @generated from field: string bond_procurement_id = 1;
   */
  bondProcurementId: string;

  /**
   * @generated from field: int32 quantity = 2;
   */
  quantity: number;

  /**
   * @generated from field: double sale_clean_price = 3;
   */
  saleCleanPrice: number;

  /**
   * @generated from field: double sale_dirty_price = 4;
   */
  saleDirtyPrice: number;

  /**
   * @generated from field: string settlement_date = 5;
   */
  settlementDate: string;

  /**
   * @generated from field: double sale_face_value = 6;
   */
  saleFaceValue: number;

  /**
   * @generated from field: string bond_offering_id = 7;
   */
  bondOfferingId: string;

  /**
   * @generated from field: string entry_type = 8;
   */
  entryType: string;

  /**
   * @generated from field: double pnl_per_bond = 9;
   */
  pnlPerBond: number;
};

/**
 * Describes the message com.stablemoney.api.broking.ExternalPnlLedger.
 * Use `create(ExternalPnlLedgerSchema)` to create a new message.
 */
export const ExternalPnlLedgerSchema: GenMessage<ExternalPnlLedger> = /*@__PURE__*/
  messageDesc(file_Procurement, 2);

