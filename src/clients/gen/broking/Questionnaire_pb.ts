// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Questionnaire.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Questionnaire.proto.
 */
export const file_Questionnaire: GenFile = /*@__PURE__*/
  fileDesc("ChNRdWVzdGlvbm5haXJlLnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmciXAoUUXVlc3Rpb25MaXN0UmVzcG9uc2USRAoNcXVlc3Rpb25fZGF0YRgBIAMoCzItLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5RdWVzdGlvblJlc3BvbnNlIvwBChBRdWVzdGlvblJlc3BvbnNlEgoKAmlkGAEgASgJEhAKCHF1ZXN0aW9uGAIgASgJEhMKC2Rlc2NyaXB0aW9uGAMgASgJEhMKC2J1dHRvbl90ZXh0GAQgASgJEhQKDGlzX3NraXBwYWJsZRgFIAEoCBJACg1xdWVzdGlvbl90eXBlGAYgASgOMikuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLlF1ZXN0aW9uVHlwZRJICgthbnN3ZXJfZGF0YRgHIAMoCzIzLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5RdWVzdGlvbkFuc3dlclJlc3BvbnNlIjQKFlF1ZXN0aW9uQW5zd2VyUmVzcG9uc2USCgoCaWQYASABKAkSDgoGYW5zd2VyGAIgASgJImkKHFF1ZXN0aW9uc0Fuc3dlclN1Ym1pdFJlcXVlc3QSNQoEZGF0YRgBIAMoCzInLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5BbnN3ZXJEYXRhEhIKCmlzX3NraXBwZWQYAiABKAgiNgoKQW5zd2VyRGF0YRITCgtxdWVzdGlvbl9pZBgBIAEoCRITCgthbnN3ZXJfZGF0YRgCIAMoCSIfCh1RdWVzdGlvbnNBbnN3ZXJTdWJtaXRSZXNwb25zZSpRCgxRdWVzdGlvblR5cGUSGQoVUVVFU1RJT05fVFlQRV9VTktOT1dOEAASEQoNU0lOR0xFX0NIT0lDRRABEhMKD01VTFRJUExFX0NIT0lDRRACQh8KG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZ1ABYgZwcm90bzM");

/**
 * @generated from message com.stablemoney.api.broking.QuestionListResponse
 */
export type QuestionListResponse = Message<"com.stablemoney.api.broking.QuestionListResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.QuestionResponse question_data = 1;
   */
  questionData: QuestionResponse[];
};

/**
 * Describes the message com.stablemoney.api.broking.QuestionListResponse.
 * Use `create(QuestionListResponseSchema)` to create a new message.
 */
export const QuestionListResponseSchema: GenMessage<QuestionListResponse> = /*@__PURE__*/
  messageDesc(file_Questionnaire, 0);

/**
 * @generated from message com.stablemoney.api.broking.QuestionResponse
 */
export type QuestionResponse = Message<"com.stablemoney.api.broking.QuestionResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string question = 2;
   */
  question: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string button_text = 4;
   */
  buttonText: string;

  /**
   * @generated from field: bool is_skippable = 5;
   */
  isSkippable: boolean;

  /**
   * @generated from field: com.stablemoney.api.broking.QuestionType question_type = 6;
   */
  questionType: QuestionType;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.QuestionAnswerResponse answer_data = 7;
   */
  answerData: QuestionAnswerResponse[];
};

/**
 * Describes the message com.stablemoney.api.broking.QuestionResponse.
 * Use `create(QuestionResponseSchema)` to create a new message.
 */
export const QuestionResponseSchema: GenMessage<QuestionResponse> = /*@__PURE__*/
  messageDesc(file_Questionnaire, 1);

/**
 * @generated from message com.stablemoney.api.broking.QuestionAnswerResponse
 */
export type QuestionAnswerResponse = Message<"com.stablemoney.api.broking.QuestionAnswerResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.broking.QuestionAnswerResponse.
 * Use `create(QuestionAnswerResponseSchema)` to create a new message.
 */
export const QuestionAnswerResponseSchema: GenMessage<QuestionAnswerResponse> = /*@__PURE__*/
  messageDesc(file_Questionnaire, 2);

/**
 * @generated from message com.stablemoney.api.broking.QuestionsAnswerSubmitRequest
 */
export type QuestionsAnswerSubmitRequest = Message<"com.stablemoney.api.broking.QuestionsAnswerSubmitRequest"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.AnswerData data = 1;
   */
  data: AnswerData[];

  /**
   * @generated from field: bool is_skipped = 2;
   */
  isSkipped: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.QuestionsAnswerSubmitRequest.
 * Use `create(QuestionsAnswerSubmitRequestSchema)` to create a new message.
 */
export const QuestionsAnswerSubmitRequestSchema: GenMessage<QuestionsAnswerSubmitRequest> = /*@__PURE__*/
  messageDesc(file_Questionnaire, 3);

/**
 * @generated from message com.stablemoney.api.broking.AnswerData
 */
export type AnswerData = Message<"com.stablemoney.api.broking.AnswerData"> & {
  /**
   * @generated from field: string question_id = 1;
   */
  questionId: string;

  /**
   * @generated from field: repeated string answer_data = 2;
   */
  answerData: string[];
};

/**
 * Describes the message com.stablemoney.api.broking.AnswerData.
 * Use `create(AnswerDataSchema)` to create a new message.
 */
export const AnswerDataSchema: GenMessage<AnswerData> = /*@__PURE__*/
  messageDesc(file_Questionnaire, 4);

/**
 * @generated from message com.stablemoney.api.broking.QuestionsAnswerSubmitResponse
 */
export type QuestionsAnswerSubmitResponse = Message<"com.stablemoney.api.broking.QuestionsAnswerSubmitResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.QuestionsAnswerSubmitResponse.
 * Use `create(QuestionsAnswerSubmitResponseSchema)` to create a new message.
 */
export const QuestionsAnswerSubmitResponseSchema: GenMessage<QuestionsAnswerSubmitResponse> = /*@__PURE__*/
  messageDesc(file_Questionnaire, 5);

/**
 * @generated from enum com.stablemoney.api.broking.QuestionType
 */
export enum QuestionType {
  /**
   * @generated from enum value: QUESTION_TYPE_UNKNOWN = 0;
   */
  QUESTION_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: SINGLE_CHOICE = 1;
   */
  SINGLE_CHOICE = 1,

  /**
   * @generated from enum value: MULTIPLE_CHOICE = 2;
   */
  MULTIPLE_CHOICE = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.QuestionType.
 */
export const QuestionTypeSchema: GenEnum<QuestionType> = /*@__PURE__*/
  enumDesc(file_Questionnaire, 0);

