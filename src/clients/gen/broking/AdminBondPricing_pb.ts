// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file AdminBondPricing.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file AdminBondPricing.proto.
 */
export const file_AdminBondPricing: GenFile = /*@__PURE__*/
  fileDesc("ChZBZG1pbkJvbmRQcmljaW5nLnByb3RvEhtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmciXwoYQWRtaW5Cb25kUHJpY2luZ1Jlc3BvbnNlEkMKDGJvbmRfcHJpY2luZxgBIAMoCzItLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5BZG1pbkJvbmRQcmljaW5nIm8KEEFkbWluQm9uZFByaWNpbmcSEwoLY2xlYW5fcHJpY2UYASABKAESEwoLZGlydHlfcHJpY2UYAiABKAESFwoPc2V0dGxlbWVudF9kYXRlGAMgASgJEhgKEGFjY3J1ZWRfaW50ZXJlc3QYBCABKAFCHwobY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nUAFiBnByb3RvMw");

/**
 * @generated from message com.stablemoney.api.broking.AdminBondPricingResponse
 */
export type AdminBondPricingResponse = Message<"com.stablemoney.api.broking.AdminBondPricingResponse"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.broking.AdminBondPricing bond_pricing = 1;
   */
  bondPricing: AdminBondPricing[];
};

/**
 * Describes the message com.stablemoney.api.broking.AdminBondPricingResponse.
 * Use `create(AdminBondPricingResponseSchema)` to create a new message.
 */
export const AdminBondPricingResponseSchema: GenMessage<AdminBondPricingResponse> = /*@__PURE__*/
  messageDesc(file_AdminBondPricing, 0);

/**
 * @generated from message com.stablemoney.api.broking.AdminBondPricing
 */
export type AdminBondPricing = Message<"com.stablemoney.api.broking.AdminBondPricing"> & {
  /**
   * @generated from field: double clean_price = 1;
   */
  cleanPrice: number;

  /**
   * @generated from field: double dirty_price = 2;
   */
  dirtyPrice: number;

  /**
   * @generated from field: string settlement_date = 3;
   */
  settlementDate: string;

  /**
   * @generated from field: double accrued_interest = 4;
   */
  accruedInterest: number;
};

/**
 * Describes the message com.stablemoney.api.broking.AdminBondPricing.
 * Use `create(AdminBondPricingSchema)` to create a new message.
 */
export const AdminBondPricingSchema: GenMessage<AdminBondPricing> = /*@__PURE__*/
  messageDesc(file_AdminBondPricing, 1);

