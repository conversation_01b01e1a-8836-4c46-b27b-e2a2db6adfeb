// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Demat.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Demat.proto.
 */
export const file_Demat: GenFile = /*@__PURE__*/
  fileDesc("CgtEZW1hdC5wcm90bxIbY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nIkEKGUdldFByb3ZpZGVyRGV0YWlsc1JlcXVlc3QSJAoccGFydGlhbF9kZW1hdF9hY2NvdW50X251bWJlchgBIAEoCSJWCh9HZXREZW1hdFByb3ZpZGVyRGV0YWlsc1Jlc3BvbnNlEhMKC3Byb3ZpZGVyX2lkGAEgASgJEgwKBG5hbWUYAiABKAkSEAoIaWNvbl91cmwYAyABKAkinwEKFkFkZERlbWF0QWNjb3VudFJlcXVlc3QSHAoUZGVtYXRfYWNjb3VudF9udW1iZXIYAyABKAkSHQoVZGVtYXRfY21sX2RvY3VtZW50X2lkGAQgASgJEigKG2RlbWF0X2NtbF9kb2N1bWVudF9wYXNzd29yZBgFIAEoCUgAiAEBQh4KHF9kZW1hdF9jbWxfZG9jdW1lbnRfcGFzc3dvcmQiTQoXQWRkRGVtYXRBY2NvdW50UmVzcG9uc2USEwoLaXNfdmVyaWZpZWQYASABKAgSHQoVdmVyaWZpY2F0aW9uX3Jlc3BvbnNlGAIgASgJItYCCg5EZW1hdEFjY291bnREQhIPCgJpZBgBIAEoCUgAiAEBEhQKB3VzZXJfaWQYAiABKAlIAYgBARIbCg5hY2NvdW50X251bWJlchgDIAEoCUgCiAEBEhgKC3Byb3ZpZGVyX2lkGAQgASgJSAOIAQESIgoVZGVtYXRfY21sX2RvY3VtZW50X2lkGAUgASgJSASIAQESKAobZGVtYXRfY21sX2RvY3VtZW50X3Bhc3N3b3JkGAYgASgJSAWIAQESGAoLaXNfdmVyaWZpZWQYByABKAhIBogBAUIFCgNfaWRCCgoIX3VzZXJfaWRCEQoPX2FjY291bnRfbnVtYmVyQg4KDF9wcm92aWRlcl9pZEIYChZfZGVtYXRfY21sX2RvY3VtZW50X2lkQh4KHF9kZW1hdF9jbWxfZG9jdW1lbnRfcGFzc3dvcmRCDgoMX2lzX3ZlcmlmaWVkImEKG0RlbWF0QWNjb3VudERCVXBkYXRlUmVxdWVzdBJCCg1kZW1hdF9hY2NvdW50GAEgASgLMisuY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nLkRlbWF0QWNjb3VudERCImIKHERlbWF0QWNjb3VudERCVXBkYXRlUmVzcG9uc2USQgoNZGVtYXRfYWNjb3VudBgBIAEoCzIrLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5EZW1hdEFjY291bnREQiJPChlEZW1hdFZlcmlmaWNhdGlvblJlc3BvbnNlEhMKC2lzX3ZlcmlmaWVkGAEgASgIEh0KFXZlcmlmaWNhdGlvbl9yZXNwb25zZRgCIAEoCUIfChtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmdQAWIGcHJvdG8z");

/**
 * @generated from message com.stablemoney.api.broking.GetProviderDetailsRequest
 */
export type GetProviderDetailsRequest = Message<"com.stablemoney.api.broking.GetProviderDetailsRequest"> & {
  /**
   * @generated from field: string partial_demat_account_number = 1;
   */
  partialDematAccountNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.GetProviderDetailsRequest.
 * Use `create(GetProviderDetailsRequestSchema)` to create a new message.
 */
export const GetProviderDetailsRequestSchema: GenMessage<GetProviderDetailsRequest> = /*@__PURE__*/
  messageDesc(file_Demat, 0);

/**
 * @generated from message com.stablemoney.api.broking.GetDematProviderDetailsResponse
 */
export type GetDematProviderDetailsResponse = Message<"com.stablemoney.api.broking.GetDematProviderDetailsResponse"> & {
  /**
   * @generated from field: string provider_id = 1;
   */
  providerId: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string icon_url = 3;
   */
  iconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.broking.GetDematProviderDetailsResponse.
 * Use `create(GetDematProviderDetailsResponseSchema)` to create a new message.
 */
export const GetDematProviderDetailsResponseSchema: GenMessage<GetDematProviderDetailsResponse> = /*@__PURE__*/
  messageDesc(file_Demat, 1);

/**
 * @generated from message com.stablemoney.api.broking.AddDematAccountRequest
 */
export type AddDematAccountRequest = Message<"com.stablemoney.api.broking.AddDematAccountRequest"> & {
  /**
   * @generated from field: string demat_account_number = 3;
   */
  dematAccountNumber: string;

  /**
   * @generated from field: string demat_cml_document_id = 4;
   */
  dematCmlDocumentId: string;

  /**
   * @generated from field: optional string demat_cml_document_password = 5;
   */
  dematCmlDocumentPassword?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AddDematAccountRequest.
 * Use `create(AddDematAccountRequestSchema)` to create a new message.
 */
export const AddDematAccountRequestSchema: GenMessage<AddDematAccountRequest> = /*@__PURE__*/
  messageDesc(file_Demat, 2);

/**
 * @generated from message com.stablemoney.api.broking.AddDematAccountResponse
 */
export type AddDematAccountResponse = Message<"com.stablemoney.api.broking.AddDematAccountResponse"> & {
  /**
   * @generated from field: bool is_verified = 1;
   */
  isVerified: boolean;

  /**
   * @generated from field: string verification_response = 2;
   */
  verificationResponse: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AddDematAccountResponse.
 * Use `create(AddDematAccountResponseSchema)` to create a new message.
 */
export const AddDematAccountResponseSchema: GenMessage<AddDematAccountResponse> = /*@__PURE__*/
  messageDesc(file_Demat, 3);

/**
 * @generated from message com.stablemoney.api.broking.DematAccountDB
 */
export type DematAccountDB = Message<"com.stablemoney.api.broking.DematAccountDB"> & {
  /**
   * @generated from field: optional string id = 1;
   */
  id?: string;

  /**
   * @generated from field: optional string user_id = 2;
   */
  userId?: string;

  /**
   * @generated from field: optional string account_number = 3;
   */
  accountNumber?: string;

  /**
   * @generated from field: optional string provider_id = 4;
   */
  providerId?: string;

  /**
   * @generated from field: optional string demat_cml_document_id = 5;
   */
  dematCmlDocumentId?: string;

  /**
   * @generated from field: optional string demat_cml_document_password = 6;
   */
  dematCmlDocumentPassword?: string;

  /**
   * @generated from field: optional bool is_verified = 7;
   */
  isVerified?: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.DematAccountDB.
 * Use `create(DematAccountDBSchema)` to create a new message.
 */
export const DematAccountDBSchema: GenMessage<DematAccountDB> = /*@__PURE__*/
  messageDesc(file_Demat, 4);

/**
 * @generated from message com.stablemoney.api.broking.DematAccountDBUpdateRequest
 */
export type DematAccountDBUpdateRequest = Message<"com.stablemoney.api.broking.DematAccountDBUpdateRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.DematAccountDB demat_account = 1;
   */
  dematAccount?: DematAccountDB;
};

/**
 * Describes the message com.stablemoney.api.broking.DematAccountDBUpdateRequest.
 * Use `create(DematAccountDBUpdateRequestSchema)` to create a new message.
 */
export const DematAccountDBUpdateRequestSchema: GenMessage<DematAccountDBUpdateRequest> = /*@__PURE__*/
  messageDesc(file_Demat, 5);

/**
 * @generated from message com.stablemoney.api.broking.DematAccountDBUpdateResponse
 */
export type DematAccountDBUpdateResponse = Message<"com.stablemoney.api.broking.DematAccountDBUpdateResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.DematAccountDB demat_account = 1;
   */
  dematAccount?: DematAccountDB;
};

/**
 * Describes the message com.stablemoney.api.broking.DematAccountDBUpdateResponse.
 * Use `create(DematAccountDBUpdateResponseSchema)` to create a new message.
 */
export const DematAccountDBUpdateResponseSchema: GenMessage<DematAccountDBUpdateResponse> = /*@__PURE__*/
  messageDesc(file_Demat, 6);

/**
 * @generated from message com.stablemoney.api.broking.DematVerificationResponse
 */
export type DematVerificationResponse = Message<"com.stablemoney.api.broking.DematVerificationResponse"> & {
  /**
   * @generated from field: bool is_verified = 1;
   */
  isVerified: boolean;

  /**
   * @generated from field: string verification_response = 2;
   */
  verificationResponse: string;
};

/**
 * Describes the message com.stablemoney.api.broking.DematVerificationResponse.
 * Use `create(DematVerificationResponseSchema)` to create a new message.
 */
export const DematVerificationResponseSchema: GenMessage<DematVerificationResponse> = /*@__PURE__*/
  messageDesc(file_Demat, 7);

