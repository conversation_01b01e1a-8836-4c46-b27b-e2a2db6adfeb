// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file Auth.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { UserDevice } from "./Device_pb.js";
import { file_Device } from "./Device_pb.js";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file Auth.proto.
 */
export const file_Auth: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Device, file_google_protobuf_empty]);

/**
 * @generated from message com.stablemoney.api.broking.InitiateAuthRequest
 */
export type InitiateAuthRequest = Message<"com.stablemoney.api.broking.InitiateAuthRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.AuthType auth_type = 1;
   */
  authType: AuthType;

  /**
   * @generated from field: com.stablemoney.api.broking.UserDevice user_device = 2;
   */
  userDevice?: UserDevice;

  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateAuthRequest.result
   */
  result: {
    /**
     * @generated from field: string email = 3;
     */
    value: string;
    case: "email";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AppleLoginRequest apple_login_request = 4;
     */
    value: AppleLoginRequest;
    case: "appleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.GoogleLoginRequest google_login_request = 5;
     */
    value: GoogleLoginRequest;
    case: "googleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.MobileLoginRequest mobile_login_request = 6;
     */
    value: MobileLoginRequest;
    case: "mobileLoginRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateAuthRequest.
 * Use `create(InitiateAuthRequestSchema)` to create a new message.
 */
export const InitiateAuthRequestSchema: GenMessage<InitiateAuthRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 0);

/**
 * @generated from message com.stablemoney.api.broking.AppleLoginRequest
 */
export type AppleLoginRequest = Message<"com.stablemoney.api.broking.AppleLoginRequest"> & {
  /**
   * @generated from field: string authorisation_code = 1;
   */
  authorisationCode: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AppleLoginRequest.
 * Use `create(AppleLoginRequestSchema)` to create a new message.
 */
export const AppleLoginRequestSchema: GenMessage<AppleLoginRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 1);

/**
 * @generated from message com.stablemoney.api.broking.GoogleLoginRequest
 */
export type GoogleLoginRequest = Message<"com.stablemoney.api.broking.GoogleLoginRequest"> & {
  /**
   * @generated from field: string id_token = 1;
   */
  idToken: string;
};

/**
 * Describes the message com.stablemoney.api.broking.GoogleLoginRequest.
 * Use `create(GoogleLoginRequestSchema)` to create a new message.
 */
export const GoogleLoginRequestSchema: GenMessage<GoogleLoginRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 2);

/**
 * @generated from message com.stablemoney.api.broking.InitiateAuthResponse
 */
export type InitiateAuthResponse = Message<"com.stablemoney.api.broking.InitiateAuthResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateAuthResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.OTPChallenge otp_challenge = 2;
     */
    value: OTPChallenge;
    case: "otpChallenge";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AuthenticationResult authentication_result = 3;
     */
    value: AuthenticationResult;
    case: "authenticationResult";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateAuthResponse.
 * Use `create(InitiateAuthResponseSchema)` to create a new message.
 */
export const InitiateAuthResponseSchema: GenMessage<InitiateAuthResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 3);

/**
 * @generated from message com.stablemoney.api.broking.AuthenticationResult
 */
export type AuthenticationResult = Message<"com.stablemoney.api.broking.AuthenticationResult"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;

  /**
   * @generated from field: string refresh_token = 2;
   */
  refreshToken: string;

  /**
   * @generated from field: string token_type = 3;
   */
  tokenType: string;

  /**
   * @generated from field: string expires_in = 4;
   */
  expiresIn: string;
};

/**
 * Describes the message com.stablemoney.api.broking.AuthenticationResult.
 * Use `create(AuthenticationResultSchema)` to create a new message.
 */
export const AuthenticationResultSchema: GenMessage<AuthenticationResult> = /*@__PURE__*/
  messageDesc(file_Auth, 4);

/**
 * @generated from message com.stablemoney.api.broking.RespondToAuthChallengeResponse
 */
export type RespondToAuthChallengeResponse = Message<"com.stablemoney.api.broking.RespondToAuthChallengeResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.AuthenticationResult authentication_result = 2;
   */
  authenticationResult?: AuthenticationResult;
};

/**
 * Describes the message com.stablemoney.api.broking.RespondToAuthChallengeResponse.
 * Use `create(RespondToAuthChallengeResponseSchema)` to create a new message.
 */
export const RespondToAuthChallengeResponseSchema: GenMessage<RespondToAuthChallengeResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 5);

/**
 * @generated from message com.stablemoney.api.broking.OTPChallenge
 */
export type OTPChallenge = Message<"com.stablemoney.api.broking.OTPChallenge"> & {
  /**
   * @generated from field: string challenge_id = 1;
   */
  challengeId: string;

  /**
   * @generated from field: int64 expiry = 2;
   */
  expiry: bigint;
};

/**
 * Describes the message com.stablemoney.api.broking.OTPChallenge.
 * Use `create(OTPChallengeSchema)` to create a new message.
 */
export const OTPChallengeSchema: GenMessage<OTPChallenge> = /*@__PURE__*/
  messageDesc(file_Auth, 6);

/**
 * @generated from message com.stablemoney.api.broking.RespondToAuthChallengeRequest
 */
export type RespondToAuthChallengeRequest = Message<"com.stablemoney.api.broking.RespondToAuthChallengeRequest"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: string challenge_id = 2;
   */
  challengeId: string;

  /**
   * @generated from field: string answer = 3;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.broking.RespondToAuthChallengeRequest.
 * Use `create(RespondToAuthChallengeRequestSchema)` to create a new message.
 */
export const RespondToAuthChallengeRequestSchema: GenMessage<RespondToAuthChallengeRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 7);

/**
 * @generated from message com.stablemoney.api.broking.RefreshTokenRequest
 */
export type RefreshTokenRequest = Message<"com.stablemoney.api.broking.RefreshTokenRequest"> & {
  /**
   * @generated from field: string token = 1;
   */
  token: string;
};

/**
 * Describes the message com.stablemoney.api.broking.RefreshTokenRequest.
 * Use `create(RefreshTokenRequestSchema)` to create a new message.
 */
export const RefreshTokenRequestSchema: GenMessage<RefreshTokenRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 8);

/**
 * @generated from message com.stablemoney.api.broking.RefreshTokenResponse
 */
export type RefreshTokenResponse = Message<"com.stablemoney.api.broking.RefreshTokenResponse"> & {
  /**
   * @generated from field: string user_id = 1;
   */
  userId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.AuthenticationResult authentication_result = 2;
   */
  authenticationResult?: AuthenticationResult;
};

/**
 * Describes the message com.stablemoney.api.broking.RefreshTokenResponse.
 * Use `create(RefreshTokenResponseSchema)` to create a new message.
 */
export const RefreshTokenResponseSchema: GenMessage<RefreshTokenResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 9);

/**
 * @generated from message com.stablemoney.api.broking.MobileLoginRequest
 */
export type MobileLoginRequest = Message<"com.stablemoney.api.broking.MobileLoginRequest"> & {
  /**
   * @generated from field: string mobile = 1;
   */
  mobile: string;

  /**
   * @generated from field: string country_code = 2;
   */
  countryCode: string;
};

/**
 * Describes the message com.stablemoney.api.broking.MobileLoginRequest.
 * Use `create(MobileLoginRequestSchema)` to create a new message.
 */
export const MobileLoginRequestSchema: GenMessage<MobileLoginRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 10);

/**
 * @generated from message com.stablemoney.api.broking.InitiateVerifyRequest
 */
export type InitiateVerifyRequest = Message<"com.stablemoney.api.broking.InitiateVerifyRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.AuthType auth_type = 1;
   */
  authType: AuthType;

  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateVerifyRequest.result
   */
  result: {
    /**
     * @generated from field: string email = 2;
     */
    value: string;
    case: "email";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.AppleLoginRequest apple_login_request = 3;
     */
    value: AppleLoginRequest;
    case: "appleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.GoogleLoginRequest google_login_request = 4;
     */
    value: GoogleLoginRequest;
    case: "googleLoginRequest";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.MobileLoginRequest mobile_login_request = 5;
     */
    value: MobileLoginRequest;
    case: "mobileLoginRequest";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateVerifyRequest.
 * Use `create(InitiateVerifyRequestSchema)` to create a new message.
 */
export const InitiateVerifyRequestSchema: GenMessage<InitiateVerifyRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 11);

/**
 * @generated from message com.stablemoney.api.broking.InitiateVerifyResponse
 */
export type InitiateVerifyResponse = Message<"com.stablemoney.api.broking.InitiateVerifyResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.InitiateVerifyResponse.result
   */
  result: {
    /**
     * @generated from field: bool is_email_verified = 1;
     */
    value: boolean;
    case: "isEmailVerified";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.OTPChallenge otp_challenge = 2;
     */
    value: OTPChallenge;
    case: "otpChallenge";
  } | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateVerifyResponse.
 * Use `create(InitiateVerifyResponseSchema)` to create a new message.
 */
export const InitiateVerifyResponseSchema: GenMessage<InitiateVerifyResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 12);

/**
 * @generated from message com.stablemoney.api.broking.RespondToVerifyChallengeRequest
 */
export type RespondToVerifyChallengeRequest = Message<"com.stablemoney.api.broking.RespondToVerifyChallengeRequest"> & {
  /**
   * @generated from field: string challenge_id = 1;
   */
  challengeId: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.broking.RespondToVerifyChallengeRequest.
 * Use `create(RespondToVerifyChallengeRequestSchema)` to create a new message.
 */
export const RespondToVerifyChallengeRequestSchema: GenMessage<RespondToVerifyChallengeRequest> = /*@__PURE__*/
  messageDesc(file_Auth, 13);

/**
 * @generated from message com.stablemoney.api.broking.RespondToVerifyChallengeResponse
 */
export type RespondToVerifyChallengeResponse = Message<"com.stablemoney.api.broking.RespondToVerifyChallengeResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.RespondToVerifyChallengeResponse.
 * Use `create(RespondToVerifyChallengeResponseSchema)` to create a new message.
 */
export const RespondToVerifyChallengeResponseSchema: GenMessage<RespondToVerifyChallengeResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 14);

/**
 * @generated from message com.stablemoney.api.broking.EmptyProtoResponse
 */
export type EmptyProtoResponse = Message<"com.stablemoney.api.broking.EmptyProtoResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.EmptyProtoResponse.
 * Use `create(EmptyProtoResponseSchema)` to create a new message.
 */
export const EmptyProtoResponseSchema: GenMessage<EmptyProtoResponse> = /*@__PURE__*/
  messageDesc(file_Auth, 15);

/**
 * @generated from enum com.stablemoney.api.broking.AuthType
 */
export enum AuthType {
  /**
   * @generated from enum value: LOGIN_TYPE_UNKNOWN = 0;
   */
  LOGIN_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: EMAIL_OTP = 1;
   */
  EMAIL_OTP = 1,

  /**
   * @generated from enum value: GOOGLE = 2;
   */
  GOOGLE = 2,

  /**
   * @generated from enum value: APPLE = 3;
   */
  APPLE = 3,

  /**
   * @generated from enum value: MOBILE_OTP = 4;
   */
  MOBILE_OTP = 4,
}

/**
 * Describes the enum com.stablemoney.api.broking.AuthType.
 */
export const AuthTypeSchema: GenEnum<AuthType> = /*@__PURE__*/
  enumDesc(file_Auth, 0);

/**
 * @generated from enum com.stablemoney.api.broking.AuthProcess
 */
export enum AuthProcess {
  /**
   * @generated from enum value: AUTH_PROCESS_UNKNOWN = 0;
   */
  AUTH_PROCESS_UNKNOWN = 0,

  /**
   * @generated from enum value: LOGIN = 1;
   */
  LOGIN = 1,

  /**
   * @generated from enum value: VERIFY = 2;
   */
  VERIFY = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.AuthProcess.
 */
export const AuthProcessSchema: GenEnum<AuthProcess> = /*@__PURE__*/
  enumDesc(file_Auth, 1);

