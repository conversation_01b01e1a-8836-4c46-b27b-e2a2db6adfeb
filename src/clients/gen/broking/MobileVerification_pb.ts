// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file MobileVerification.proto (package com.stablemoney.api.broking, syntax proto3)
/* eslint-disable */

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { OTPChallenge } from "./Auth_pb.js";
import { file_Auth } from "./Auth_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file MobileVerification.proto.
 */
export const file_MobileVerification: GenFile = /*@__PURE__*/
  fileDesc("ChhNb2JpbGVWZXJpZmljYXRpb24ucHJvdG8SG2NvbS5zdGFibGVtb25leS5hcGkuYnJva2luZyJJCiFJbml0aWF0ZU1vYmlsZVZlcmlmaWNhdGlvblJlcXVlc3QSDgoGbW9iaWxlGAEgASgJEhQKDGNvdW50cnlfY29kZRgCIAEoCSJmCiJJbml0aWF0ZU1vYmlsZVZlcmlmaWNhdGlvblJlc3BvbnNlEkAKDW90cF9jaGFsbGVuZ2UYASABKAsyKS5jb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmcuT1RQQ2hhbGxlbmdlIkwKJFJlc3BvbmRUb01vYmlsZVZlcmlmaWNhdGlvbkNoYWxsZW5nZRIUCgxjaGFsbGVuZ2VfaWQYASABKAkSDgoGYW5zd2VyGAIgASgJIlAKLFJlc3BvbmRUb01vYmlsZVZlcmlmaWNhdGlvbkNoYWxsZW5nZVJlc3BvbnNlEg8KB2V4cGlyZWQYASABKAgSDwoHbWVzc2FnZRgCIAEoCUIfChtjb20uc3RhYmxlbW9uZXkuYXBpLmJyb2tpbmdQAWIGcHJvdG8z", [file_Auth]);

/**
 * @generated from message com.stablemoney.api.broking.InitiateMobileVerificationRequest
 */
export type InitiateMobileVerificationRequest = Message<"com.stablemoney.api.broking.InitiateMobileVerificationRequest"> & {
  /**
   * @generated from field: string mobile = 1;
   */
  mobile: string;

  /**
   * @generated from field: string country_code = 2;
   */
  countryCode: string;
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateMobileVerificationRequest.
 * Use `create(InitiateMobileVerificationRequestSchema)` to create a new message.
 */
export const InitiateMobileVerificationRequestSchema: GenMessage<InitiateMobileVerificationRequest> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 0);

/**
 * @generated from message com.stablemoney.api.broking.InitiateMobileVerificationResponse
 */
export type InitiateMobileVerificationResponse = Message<"com.stablemoney.api.broking.InitiateMobileVerificationResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.OTPChallenge otp_challenge = 1;
   */
  otpChallenge?: OTPChallenge;
};

/**
 * Describes the message com.stablemoney.api.broking.InitiateMobileVerificationResponse.
 * Use `create(InitiateMobileVerificationResponseSchema)` to create a new message.
 */
export const InitiateMobileVerificationResponseSchema: GenMessage<InitiateMobileVerificationResponse> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 1);

/**
 * @generated from message com.stablemoney.api.broking.RespondToMobileVerificationChallenge
 */
export type RespondToMobileVerificationChallenge = Message<"com.stablemoney.api.broking.RespondToMobileVerificationChallenge"> & {
  /**
   * @generated from field: string challenge_id = 1;
   */
  challengeId: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;
};

/**
 * Describes the message com.stablemoney.api.broking.RespondToMobileVerificationChallenge.
 * Use `create(RespondToMobileVerificationChallengeSchema)` to create a new message.
 */
export const RespondToMobileVerificationChallengeSchema: GenMessage<RespondToMobileVerificationChallenge> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 2);

/**
 * @generated from message com.stablemoney.api.broking.RespondToMobileVerificationChallengeResponse
 */
export type RespondToMobileVerificationChallengeResponse = Message<"com.stablemoney.api.broking.RespondToMobileVerificationChallengeResponse"> & {
  /**
   * @generated from field: bool expired = 1;
   */
  expired: boolean;

  /**
   * @generated from field: string message = 2;
   */
  message: string;
};

/**
 * Describes the message com.stablemoney.api.broking.RespondToMobileVerificationChallengeResponse.
 * Use `create(RespondToMobileVerificationChallengeResponseSchema)` to create a new message.
 */
export const RespondToMobileVerificationChallengeResponseSchema: GenMessage<RespondToMobileVerificationChallengeResponse> = /*@__PURE__*/
  messageDesc(file_MobileVerification, 3);

