// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file WorkflowStep.proto (package com.stablemoney.api.broking.identity, syntax proto3)
/* eslint-disable */

import type { GenEnum, GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { AddressProto, EmploymentType, Gender, IncomeRange, MaritalStatus, TradingExperience } from "./Common_pb.js";
import { file_Common } from "./Common_pb.js";
import type { RelationshipType } from "./Nominee_pb.js";
import { file_Nominee } from "./Nominee_pb.js";
import type { BankAccount, BankVerificationType, Cheque, InitiateChequeResponse, InitiatePdResponse, InitiateRpdResponse } from "./BankAccountVerification_pb.js";
import { file_BankAccountVerification } from "./BankAccountVerification_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file WorkflowStep.proto.
 */
export const file_WorkflowStep: GenFile = /*@__PURE__*/
  fileDesc("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", [file_Common, file_Nominee, file_BankAccountVerification]);

/**
 * @generated from message com.stablemoney.api.broking.identity.InitiateDigioKycRequest
 */
export type InitiateDigioKycRequest = Message<"com.stablemoney.api.broking.identity.InitiateDigioKycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.InitiateDigioKycRequest.
 * Use `create(InitiateDigioKycRequestSchema)` to create a new message.
 */
export const InitiateDigioKycRequestSchema: GenMessage<InitiateDigioKycRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 0);

/**
 * @generated from message com.stablemoney.api.broking.identity.InitiateDigioKycResponse
 */
export type InitiateDigioKycResponse = Message<"com.stablemoney.api.broking.identity.InitiateDigioKycResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string access_token = 2;
   */
  accessToken: string;

  /**
   * @generated from field: string customer_identifier = 3;
   */
  customerIdentifier: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 4;
   */
  nextStep?: ContinueWorkflowResponse;

  /**
   * @generated from field: bool is_complete = 5;
   */
  isComplete: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.InitiateDigioKycResponse.
 * Use `create(InitiateDigioKycResponseSchema)` to create a new message.
 */
export const InitiateDigioKycResponseSchema: GenMessage<InitiateDigioKycResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 1);

/**
 * @generated from message com.stablemoney.api.broking.identity.CheckDigioKycStatusRequest
 */
export type CheckDigioKycStatusRequest = Message<"com.stablemoney.api.broking.identity.CheckDigioKycStatusRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.CheckDigioKycStatusRequest.
 * Use `create(CheckDigioKycStatusRequestSchema)` to create a new message.
 */
export const CheckDigioKycStatusRequestSchema: GenMessage<CheckDigioKycStatusRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 2);

/**
 * @generated from message com.stablemoney.api.broking.identity.CheckDigioKycStatusResponse
 */
export type CheckDigioKycStatusResponse = Message<"com.stablemoney.api.broking.identity.CheckDigioKycStatusResponse"> & {
  /**
   * @generated from field: string kyc_status = 1;
   */
  kycStatus: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 3;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.CheckDigioKycStatusResponse.
 * Use `create(CheckDigioKycStatusResponseSchema)` to create a new message.
 */
export const CheckDigioKycStatusResponseSchema: GenMessage<CheckDigioKycStatusResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 3);

/**
 * @generated from message com.stablemoney.api.broking.identity.UserProfileUpdateRequest
 */
export type UserProfileUpdateRequest = Message<"com.stablemoney.api.broking.identity.UserProfileUpdateRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;

  /**
   * @generated from field: optional string dob = 2;
   */
  dob?: string;

  /**
   * @generated from field: optional com.stablemoney.api.broking.IncomeRange income_range = 3;
   */
  incomeRange?: IncomeRange;

  /**
   * @generated from field: optional com.stablemoney.api.broking.EmploymentType employment_type = 4;
   */
  employmentType?: EmploymentType;

  /**
   * @generated from field: optional com.stablemoney.api.broking.TradingExperience trading_experience = 5;
   */
  tradingExperience?: TradingExperience;

  /**
   * @generated from field: optional com.stablemoney.api.broking.MaritalStatus marital_status = 6;
   */
  maritalStatus?: MaritalStatus;

  /**
   * @generated from field: optional string father_name = 7;
   */
  fatherName?: string;

  /**
   * @generated from field: optional string mother_name = 8;
   */
  motherName?: string;

  /**
   * @generated from field: optional com.stablemoney.api.broking.Gender gender = 9;
   */
  gender?: Gender;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.UserProfileUpdateRequest.
 * Use `create(UserProfileUpdateRequestSchema)` to create a new message.
 */
export const UserProfileUpdateRequestSchema: GenMessage<UserProfileUpdateRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 4);

/**
 * @generated from message com.stablemoney.api.broking.identity.UserProfileUpdateResponse
 */
export type UserProfileUpdateResponse = Message<"com.stablemoney.api.broking.identity.UserProfileUpdateResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 1;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.UserProfileUpdateResponse.
 * Use `create(UserProfileUpdateResponseSchema)` to create a new message.
 */
export const UserProfileUpdateResponseSchema: GenMessage<UserProfileUpdateResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 5);

/**
 * @generated from message com.stablemoney.api.broking.identity.SelfieInitiationRequest
 */
export type SelfieInitiationRequest = Message<"com.stablemoney.api.broking.identity.SelfieInitiationRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.SelfieInitiationRequest.
 * Use `create(SelfieInitiationRequestSchema)` to create a new message.
 */
export const SelfieInitiationRequestSchema: GenMessage<SelfieInitiationRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 6);

/**
 * @generated from message com.stablemoney.api.broking.identity.SelfieInitiationResponse
 */
export type SelfieInitiationResponse = Message<"com.stablemoney.api.broking.identity.SelfieInitiationResponse"> & {
  /**
   * @generated from field: string selfie = 1;
   */
  selfie: string;

  /**
   * @generated from field: string workflow_id = 2;
   */
  workflowId: string;

  /**
   * @generated from field: string access_token = 3;
   */
  accessToken: string;

  /**
   * @generated from field: string transaction_id = 4;
   */
  transactionId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 5;
   */
  nextStep?: ContinueWorkflowResponse;

  /**
   * @generated from field: bool is_complete = 6;
   */
  isComplete: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.SelfieInitiationResponse.
 * Use `create(SelfieInitiationResponseSchema)` to create a new message.
 */
export const SelfieInitiationResponseSchema: GenMessage<SelfieInitiationResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 7);

/**
 * @generated from message com.stablemoney.api.broking.identity.SelfieStatusCheckRequest
 */
export type SelfieStatusCheckRequest = Message<"com.stablemoney.api.broking.identity.SelfieStatusCheckRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;

  /**
   * @generated from field: string transaction_id = 2;
   */
  transactionId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.SelfieStatusCheckRequest.
 * Use `create(SelfieStatusCheckRequestSchema)` to create a new message.
 */
export const SelfieStatusCheckRequestSchema: GenMessage<SelfieStatusCheckRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 8);

/**
 * @generated from message com.stablemoney.api.broking.identity.SelfieStatusCheckResponse
 */
export type SelfieStatusCheckResponse = Message<"com.stablemoney.api.broking.identity.SelfieStatusCheckResponse"> & {
  /**
   * @generated from field: string selfie_status = 1;
   */
  selfieStatus: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 3;
   */
  nextStep?: ContinueWorkflowResponse;

  /**
   * @generated from field: bool is_complete = 4;
   */
  isComplete: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.SelfieStatusCheckResponse.
 * Use `create(SelfieStatusCheckResponseSchema)` to create a new message.
 */
export const SelfieStatusCheckResponseSchema: GenMessage<SelfieStatusCheckResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 9);

/**
 * @generated from message com.stablemoney.api.broking.identity.WetSignatureRequest
 */
export type WetSignatureRequest = Message<"com.stablemoney.api.broking.identity.WetSignatureRequest"> & {
  /**
   * @generated from field: optional bool is_pep = 1;
   */
  isPep?: boolean;

  /**
   * @generated from field: optional bool is_indian_citizen = 2;
   */
  isIndianCitizen?: boolean;

  /**
   * @generated from field: string document_id = 4;
   */
  documentId: string;

  /**
   * @generated from field: optional bool credit_report_consent = 5;
   */
  creditReportConsent?: boolean;

  /**
   * @generated from field: optional bool is_edis = 6;
   */
  isEdis?: boolean;

  /**
   * @generated from field: optional bool is_sms_consent = 7;
   */
  isSmsConsent?: boolean;

  /**
   * @generated from field: optional bool is_unlawful = 8;
   */
  isUnlawful?: boolean;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 9;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.WetSignatureRequest.
 * Use `create(WetSignatureRequestSchema)` to create a new message.
 */
export const WetSignatureRequestSchema: GenMessage<WetSignatureRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 10);

/**
 * @generated from message com.stablemoney.api.broking.identity.WetSignatureResponse
 */
export type WetSignatureResponse = Message<"com.stablemoney.api.broking.identity.WetSignatureResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 1;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.WetSignatureResponse.
 * Use `create(WetSignatureResponseSchema)` to create a new message.
 */
export const WetSignatureResponseSchema: GenMessage<WetSignatureResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 11);

/**
 * @generated from message com.stablemoney.api.broking.identity.GenerateEsignTokenRequest
 */
export type GenerateEsignTokenRequest = Message<"com.stablemoney.api.broking.identity.GenerateEsignTokenRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.StepName step_name = 2;
   */
  stepName: StepName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.GenerateEsignTokenRequest.
 * Use `create(GenerateEsignTokenRequestSchema)` to create a new message.
 */
export const GenerateEsignTokenRequestSchema: GenMessage<GenerateEsignTokenRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 12);

/**
 * @generated from message com.stablemoney.api.broking.identity.GenerateEsignTokenResponse
 */
export type GenerateEsignTokenResponse = Message<"com.stablemoney.api.broking.identity.GenerateEsignTokenResponse"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string access_token = 2;
   */
  accessToken: string;

  /**
   * @generated from field: string customer_identifier = 3;
   */
  customerIdentifier: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 4;
   */
  nextStep?: ContinueWorkflowResponse;

  /**
   * @generated from field: bool is_complete = 5;
   */
  isComplete: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.GenerateEsignTokenResponse.
 * Use `create(GenerateEsignTokenResponseSchema)` to create a new message.
 */
export const GenerateEsignTokenResponseSchema: GenMessage<GenerateEsignTokenResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 13);

/**
 * @generated from message com.stablemoney.api.broking.identity.EsignStatusRequest
 */
export type EsignStatusRequest = Message<"com.stablemoney.api.broking.identity.EsignStatusRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;

  /**
   * @generated from field: string id = 2;
   */
  id: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.StepName step_name = 3;
   */
  stepName: StepName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.EsignStatusRequest.
 * Use `create(EsignStatusRequestSchema)` to create a new message.
 */
export const EsignStatusRequestSchema: GenMessage<EsignStatusRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 14);

/**
 * @generated from message com.stablemoney.api.broking.identity.EsignStatusResponse
 */
export type EsignStatusResponse = Message<"com.stablemoney.api.broking.identity.EsignStatusResponse"> & {
  /**
   * @generated from field: string esign_status = 1;
   */
  esignStatus: string;

  /**
   * @generated from field: string description = 2;
   */
  description: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 3;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.EsignStatusResponse.
 * Use `create(EsignStatusResponseSchema)` to create a new message.
 */
export const EsignStatusResponseSchema: GenMessage<EsignStatusResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 15);

/**
 * @generated from message com.stablemoney.api.broking.identity.SelfieStartRequest
 */
export type SelfieStartRequest = Message<"com.stablemoney.api.broking.identity.SelfieStartRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;

  /**
   * @generated from field: string transaction_id = 2;
   */
  transactionId: string;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.SelfieStartRequest.
 * Use `create(SelfieStartRequestSchema)` to create a new message.
 */
export const SelfieStartRequestSchema: GenMessage<SelfieStartRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 16);

/**
 * @generated from message com.stablemoney.api.broking.identity.SelfieStartResponse
 */
export type SelfieStartResponse = Message<"com.stablemoney.api.broking.identity.SelfieStartResponse"> & {
  /**
   * @generated from field: string selfie = 1;
   */
  selfie: string;

  /**
   * @generated from field: string workflow_id = 2;
   */
  workflowId: string;

  /**
   * @generated from field: string access_token = 3;
   */
  accessToken: string;

  /**
   * @generated from field: string transaction_id = 4;
   */
  transactionId: string;

  /**
   * @generated from field: bool completed = 5;
   */
  completed: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.SelfieStartResponse.
 * Use `create(SelfieStartResponseSchema)` to create a new message.
 */
export const SelfieStartResponseSchema: GenMessage<SelfieStartResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 17);

/**
 * @generated from message com.stablemoney.api.broking.identity.ContinueWorkflowResponse
 */
export type ContinueWorkflowResponse = Message<"com.stablemoney.api.broking.identity.ContinueWorkflowResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowStatus workflow_status = 1;
   */
  workflowStatus: WorkflowStatus;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.StepName next_step = 2;
   */
  nextStep: StepName;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow_name = 3;
   */
  workflowName: WorkflowName;

  /**
   * @generated from field: bool is_background_step = 4;
   */
  isBackgroundStep: boolean;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.ContinueWorkflowResponse.
 * Use `create(ContinueWorkflowResponseSchema)` to create a new message.
 */
export const ContinueWorkflowResponseSchema: GenMessage<ContinueWorkflowResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 18);

/**
 * @generated from message com.stablemoney.api.broking.identity.InitiateWorkflowResponse
 */
export type InitiateWorkflowResponse = Message<"com.stablemoney.api.broking.identity.InitiateWorkflowResponse"> & {
};

/**
 * Describes the message com.stablemoney.api.broking.identity.InitiateWorkflowResponse.
 * Use `create(InitiateWorkflowResponseSchema)` to create a new message.
 */
export const InitiateWorkflowResponseSchema: GenMessage<InitiateWorkflowResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 19);

/**
 * @generated from message com.stablemoney.api.broking.identity.GuardianDetails
 */
export type GuardianDetails = Message<"com.stablemoney.api.broking.identity.GuardianDetails"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.broking.RelationshipType relationship_type = 2;
   */
  relationshipType: RelationshipType;

  /**
   * @generated from field: string relationship = 3;
   */
  relationship: string;

  /**
   * @generated from field: com.stablemoney.api.broking.AddressProto address = 4;
   */
  address?: AddressProto;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.GuardianDetails.
 * Use `create(GuardianDetailsSchema)` to create a new message.
 */
export const GuardianDetailsSchema: GenMessage<GuardianDetails> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 20);

/**
 * @generated from message com.stablemoney.api.broking.identity.NomineeDetails
 */
export type NomineeDetails = Message<"com.stablemoney.api.broking.identity.NomineeDetails"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: com.stablemoney.api.broking.RelationshipType relationship_type = 2;
   */
  relationshipType: RelationshipType;

  /**
   * @generated from field: string dob = 3;
   */
  dob: string;

  /**
   * @generated from field: string relationship = 4;
   */
  relationship: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.GuardianDetails guardian_details = 5;
   */
  guardianDetails?: GuardianDetails;

  /**
   * @generated from field: com.stablemoney.api.broking.AddressProto address = 6;
   */
  address?: AddressProto;

  /**
   * @generated from field: optional double allocation_percentage = 7;
   */
  allocationPercentage?: number;

  /**
   * @generated from field: optional com.stablemoney.api.broking.identity.NomineeIdentifierType identifier_type = 8;
   */
  identifierType?: NomineeIdentifierType;

  /**
   * @generated from field: optional string identifier = 9;
   */
  identifier?: string;

  /**
   * @generated from field: optional string email = 10;
   */
  email?: string;

  /**
   * @generated from field: optional string phone_number = 11;
   */
  phoneNumber?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.NomineeDetails.
 * Use `create(NomineeDetailsSchema)` to create a new message.
 */
export const NomineeDetailsSchema: GenMessage<NomineeDetails> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 21);

/**
 * @generated from message com.stablemoney.api.broking.identity.NomineeRequest
 */
export type NomineeRequest = Message<"com.stablemoney.api.broking.identity.NomineeRequest"> & {
  /**
   * @generated from field: bool opt_out = 1;
   */
  optOut: boolean;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.identity.NomineeDetails nominee_details = 2;
   */
  nomineeDetails: NomineeDetails[];

  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 3;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.NomineeRequest.
 * Use `create(NomineeRequestSchema)` to create a new message.
 */
export const NomineeRequestSchema: GenMessage<NomineeRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 22);

/**
 * @generated from message com.stablemoney.api.broking.identity.DematRequest
 */
export type DematRequest = Message<"com.stablemoney.api.broking.identity.DematRequest"> & {
  /**
   * @generated from field: bool create_account = 1;
   */
  createAccount: boolean;

  /**
   * @generated from field: optional string demat_account_number = 2;
   */
  dematAccountNumber?: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 3;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.DematRequest.
 * Use `create(DematRequestSchema)` to create a new message.
 */
export const DematRequestSchema: GenMessage<DematRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 23);

/**
 * @generated from message com.stablemoney.api.broking.identity.DematResponse
 */
export type DematResponse = Message<"com.stablemoney.api.broking.identity.DematResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 1;
   */
  nextStep?: ContinueWorkflowResponse;

  /**
   * @generated from field: optional bool is_verified = 2;
   */
  isVerified?: boolean;

  /**
   * @generated from field: optional string verification_response = 3;
   */
  verificationResponse?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.DematResponse.
 * Use `create(DematResponseSchema)` to create a new message.
 */
export const DematResponseSchema: GenMessage<DematResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 24);

/**
 * @generated from message com.stablemoney.api.broking.identity.NomineeResponse
 */
export type NomineeResponse = Message<"com.stablemoney.api.broking.identity.NomineeResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 1;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.NomineeResponse.
 * Use `create(NomineeResponseSchema)` to create a new message.
 */
export const NomineeResponseSchema: GenMessage<NomineeResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 25);

/**
 * @generated from message com.stablemoney.api.broking.identity.PanVerificationRequest
 */
export type PanVerificationRequest = Message<"com.stablemoney.api.broking.identity.PanVerificationRequest"> & {
  /**
   * @generated from field: string full_name = 1;
   */
  fullName: string;

  /**
   * @generated from field: string dob = 2;
   */
  dob: string;

  /**
   * @generated from field: string pan_number = 3;
   */
  panNumber: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow_name = 4;
   */
  workflowName: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.PanVerificationRequest.
 * Use `create(PanVerificationRequestSchema)` to create a new message.
 */
export const PanVerificationRequestSchema: GenMessage<PanVerificationRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 26);

/**
 * @generated from message com.stablemoney.api.broking.identity.PanVerificationResponse
 */
export type PanVerificationResponse = Message<"com.stablemoney.api.broking.identity.PanVerificationResponse"> & {
  /**
   * @generated from field: bool pan_kyc_status = 1;
   */
  panKycStatus: boolean;

  /**
   * @generated from field: bool name_match_status = 2;
   */
  nameMatchStatus: boolean;

  /**
   * @generated from field: bool dob_match_status = 3;
   */
  dobMatchStatus: boolean;

  /**
   * @generated from field: bool is_pan_valid = 4;
   */
  isPanValid: boolean;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 5;
   */
  nextStep?: ContinueWorkflowResponse;

  /**
   * @generated from field: optional string error_message = 6;
   */
  errorMessage?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.PanVerificationResponse.
 * Use `create(PanVerificationResponseSchema)` to create a new message.
 */
export const PanVerificationResponseSchema: GenMessage<PanVerificationResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 27);

/**
 * @generated from message com.stablemoney.api.broking.identity.AadhaarPanMatchRequest
 */
export type AadhaarPanMatchRequest = Message<"com.stablemoney.api.broking.identity.AadhaarPanMatchRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.AadhaarPanMatchRequest.
 * Use `create(AadhaarPanMatchRequestSchema)` to create a new message.
 */
export const AadhaarPanMatchRequestSchema: GenMessage<AadhaarPanMatchRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 28);

/**
 * @generated from message com.stablemoney.api.broking.identity.AadhaarPanMatchResponse
 */
export type AadhaarPanMatchResponse = Message<"com.stablemoney.api.broking.identity.AadhaarPanMatchResponse"> & {
  /**
   * @generated from field: bool aadhaar_pan_match_status = 1;
   */
  aadhaarPanMatchStatus: boolean;

  /**
   * @generated from field: bool aadhaar_pan_dob_match = 2;
   */
  aadhaarPanDobMatch: boolean;

  /**
   * @generated from field: bool aadhaar_pan_name_match = 3;
   */
  aadhaarPanNameMatch: boolean;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.AadhaarDetails aadhaar_details = 4;
   */
  aadhaarDetails?: AadhaarPanMatchResponse_AadhaarDetails;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.PanDetails pan_details = 5;
   */
  panDetails?: AadhaarPanMatchResponse_PanDetails;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 6;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.
 * Use `create(AadhaarPanMatchResponseSchema)` to create a new message.
 */
export const AadhaarPanMatchResponseSchema: GenMessage<AadhaarPanMatchResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 29);

/**
 * @generated from message com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.AadhaarDetails
 */
export type AadhaarPanMatchResponse_AadhaarDetails = Message<"com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.AadhaarDetails"> & {
  /**
   * @generated from field: string aadhaar_name = 1;
   */
  aadhaarName: string;

  /**
   * @generated from field: string aadhaar_number = 2;
   */
  aadhaarNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.AadhaarDetails.
 * Use `create(AadhaarPanMatchResponse_AadhaarDetailsSchema)` to create a new message.
 */
export const AadhaarPanMatchResponse_AadhaarDetailsSchema: GenMessage<AadhaarPanMatchResponse_AadhaarDetails> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 29, 0);

/**
 * @generated from message com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.PanDetails
 */
export type AadhaarPanMatchResponse_PanDetails = Message<"com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.PanDetails"> & {
  /**
   * @generated from field: string pan_name = 1;
   */
  panName: string;

  /**
   * @generated from field: string pan_number = 2;
   */
  panNumber: string;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.AadhaarPanMatchResponse.PanDetails.
 * Use `create(AadhaarPanMatchResponse_PanDetailsSchema)` to create a new message.
 */
export const AadhaarPanMatchResponse_PanDetailsSchema: GenMessage<AadhaarPanMatchResponse_PanDetails> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 29, 1);

/**
 * @generated from message com.stablemoney.api.broking.identity.RetryStepRequest
 */
export type RetryStepRequest = Message<"com.stablemoney.api.broking.identity.RetryStepRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 1;
   */
  workflow: WorkflowName;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.StepName step_name = 2;
   */
  stepName: StepName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.RetryStepRequest.
 * Use `create(RetryStepRequestSchema)` to create a new message.
 */
export const RetryStepRequestSchema: GenMessage<RetryStepRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 30);

/**
 * @generated from message com.stablemoney.api.broking.identity.RetryStepResponse
 */
export type RetryStepResponse = Message<"com.stablemoney.api.broking.identity.RetryStepResponse"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 1;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.RetryStepResponse.
 * Use `create(RetryStepResponseSchema)` to create a new message.
 */
export const RetryStepResponseSchema: GenMessage<RetryStepResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 31);

/**
 * @generated from message com.stablemoney.api.broking.identity.InitiateBankVerificationRequest
 */
export type InitiateBankVerificationRequest = Message<"com.stablemoney.api.broking.identity.InitiateBankVerificationRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.BankVerificationType type = 1;
   */
  type: BankVerificationType;

  /**
   * @generated from oneof com.stablemoney.api.broking.identity.InitiateBankVerificationRequest.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.BankAccount bank_account = 2;
     */
    value: BankAccount;
    case: "bankAccount";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.Cheque cheque = 3;
     */
    value: Cheque;
    case: "cheque";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: string transaction_id = 4;
   */
  transactionId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 5;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.InitiateBankVerificationRequest.
 * Use `create(InitiateBankVerificationRequestSchema)` to create a new message.
 */
export const InitiateBankVerificationRequestSchema: GenMessage<InitiateBankVerificationRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 32);

/**
 * @generated from message com.stablemoney.api.broking.identity.InitiateBankVerificationResponse
 */
export type InitiateBankVerificationResponse = Message<"com.stablemoney.api.broking.identity.InitiateBankVerificationResponse"> & {
  /**
   * @generated from oneof com.stablemoney.api.broking.identity.InitiateBankVerificationResponse.result
   */
  result: {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiatePdResponse initiate_pd_response = 2;
     */
    value: InitiatePdResponse;
    case: "initiatePdResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiateRpdResponse initiate_rpd_response = 3;
     */
    value: InitiateRpdResponse;
    case: "initiateRpdResponse";
  } | {
    /**
     * @generated from field: com.stablemoney.api.broking.InitiateChequeResponse initiate_cheque_response = 4;
     */
    value: InitiateChequeResponse;
    case: "initiateChequeResponse";
  } | { case: undefined; value?: undefined };

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 5;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.InitiateBankVerificationResponse.
 * Use `create(InitiateBankVerificationResponseSchema)` to create a new message.
 */
export const InitiateBankVerificationResponseSchema: GenMessage<InitiateBankVerificationResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 33);

/**
 * @generated from message com.stablemoney.api.broking.identity.BankVerificationStatusRequest
 */
export type BankVerificationStatusRequest = Message<"com.stablemoney.api.broking.identity.BankVerificationStatusRequest"> & {
  /**
   * @generated from field: string ref_id = 1;
   */
  refId: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.WorkflowName workflow = 2;
   */
  workflow: WorkflowName;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.BankVerificationStatusRequest.
 * Use `create(BankVerificationStatusRequestSchema)` to create a new message.
 */
export const BankVerificationStatusRequestSchema: GenMessage<BankVerificationStatusRequest> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 34);

/**
 * @generated from message com.stablemoney.api.broking.identity.BankVerificationStatusResponse
 */
export type BankVerificationStatusResponse = Message<"com.stablemoney.api.broking.identity.BankVerificationStatusResponse"> & {
  /**
   * @generated from field: string ref_id = 1;
   */
  refId: string;

  /**
   * @generated from field: string status = 3;
   */
  status: string;

  /**
   * @generated from field: bool verified = 4;
   */
  verified: boolean;

  /**
   * @generated from field: string message = 5;
   */
  message: string;

  /**
   * @generated from field: com.stablemoney.api.broking.identity.ContinueWorkflowResponse next_step = 6;
   */
  nextStep?: ContinueWorkflowResponse;
};

/**
 * Describes the message com.stablemoney.api.broking.identity.BankVerificationStatusResponse.
 * Use `create(BankVerificationStatusResponseSchema)` to create a new message.
 */
export const BankVerificationStatusResponseSchema: GenMessage<BankVerificationStatusResponse> = /*@__PURE__*/
  messageDesc(file_WorkflowStep, 35);

/**
 * @generated from enum com.stablemoney.api.broking.identity.WorkflowName
 */
export enum WorkflowName {
  /**
   * @generated from enum value: UNKNOWN_WORKFLOW = 0;
   */
  UNKNOWN_WORKFLOW = 0,

  /**
   * @generated from enum value: DEMAT_ACCOUNT_OPENING = 1;
   */
  DEMAT_ACCOUNT_OPENING = 1,

  /**
   * @generated from enum value: TRADING_ACCOUNT_OPENING = 2;
   */
  TRADING_ACCOUNT_OPENING = 2,

  /**
   * @generated from enum value: TRADING_DEMAT_ACCOUNT_OPENING = 3;
   */
  TRADING_DEMAT_ACCOUNT_OPENING = 3,

  /**
   * @generated from enum value: ONBOARDING_INITIATION = 4;
   */
  ONBOARDING_INITIATION = 4,
}

/**
 * Describes the enum com.stablemoney.api.broking.identity.WorkflowName.
 */
export const WorkflowNameSchema: GenEnum<WorkflowName> = /*@__PURE__*/
  enumDesc(file_WorkflowStep, 0);

/**
 * @generated from enum com.stablemoney.api.broking.identity.StepName
 */
export enum StepName {
  /**
   * @generated from enum value: UNKNOWN_STEP = 0;
   */
  UNKNOWN_STEP = 0,

  /**
   * @generated from enum value: DIGIO_KYC = 1;
   */
  DIGIO_KYC = 1,

  /**
   * @generated from enum value: USER_PROFILE = 2;
   */
  USER_PROFILE = 2,

  /**
   * @generated from enum value: SELFIE = 3;
   */
  SELFIE = 3,

  /**
   * @generated from enum value: WET_SIGNATURE = 4;
   */
  WET_SIGNATURE = 4,

  /**
   * @generated from enum value: CVL_PULL = 5;
   */
  CVL_PULL = 5,

  /**
   * @generated from enum value: ESIGN = 6;
   */
  ESIGN = 6,

  /**
   * @generated from enum value: CVL_PUSH = 7;
   */
  CVL_PUSH = 7,

  /**
   * @generated from enum value: CDSL_PUSH_INITIATE = 8;
   */
  CDSL_PUSH_INITIATE = 8,

  /**
   * @generated from enum value: CDSL_PUSH = 9;
   */
  CDSL_PUSH = 9,

  /**
   * @generated from enum value: CDSL_STATUS_CHECK = 10;
   */
  CDSL_STATUS_CHECK = 10,

  /**
   * @generated from enum value: NSE_ONBOARDING = 11;
   */
  NSE_ONBOARDING = 11,

  /**
   * @generated from enum value: CDSL_PUSH_WAITING = 12;
   */
  CDSL_PUSH_WAITING = 12,

  /**
   * @generated from enum value: CVL_PUSH_WAITING = 13;
   */
  CVL_PUSH_WAITING = 13,

  /**
   * @generated from enum value: NSE_PUSH_WAITING = 14;
   */
  NSE_PUSH_WAITING = 14,

  /**
   * @generated from enum value: NOMINEE = 15;
   */
  NOMINEE = 15,

  /**
   * @generated from enum value: NOMINEE_SYNC = 16;
   */
  NOMINEE_SYNC = 16,

  /**
   * @generated from enum value: DEMAT_ACCOUNT = 17;
   */
  DEMAT_ACCOUNT = 17,

  /**
   * @generated from enum value: PAN_KYC = 18;
   */
  PAN_KYC = 18,

  /**
   * @generated from enum value: AADHAAR_PAN_MATCH = 19;
   */
  AADHAAR_PAN_MATCH = 19,

  /**
   * @generated from enum value: BANK_ACCOUNT = 20;
   */
  BANK_ACCOUNT = 20,

  /**
   * @generated from enum value: ADDRESS_SYNC_CVL_PUSH = 22;
   */
  ADDRESS_SYNC_CVL_PUSH = 22,

  /**
   * @generated from enum value: ESIGN_WITH_DEMAT = 23;
   */
  ESIGN_WITH_DEMAT = 23,

  /**
   * @generated from enum value: ADD_DEMAT_TO_NSE = 24;
   */
  ADD_DEMAT_TO_NSE = 24,

  /**
   * @generated from enum value: CVL_PULL_WAITING = 25;
   */
  CVL_PULL_WAITING = 25,
}

/**
 * Describes the enum com.stablemoney.api.broking.identity.StepName.
 */
export const StepNameSchema: GenEnum<StepName> = /*@__PURE__*/
  enumDesc(file_WorkflowStep, 1);

/**
 * @generated from enum com.stablemoney.api.broking.identity.NomineeIdentifierType
 */
export enum NomineeIdentifierType {
  /**
   * @generated from enum value: UNKNOWN_IDENTITY = 0;
   */
  UNKNOWN_IDENTITY = 0,

  /**
   * @generated from enum value: AADHAAR = 1;
   */
  AADHAAR = 1,

  /**
   * @generated from enum value: PAN = 2;
   */
  PAN = 2,

  /**
   * @generated from enum value: DRIVING_LICENSE = 3;
   */
  DRIVING_LICENSE = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.identity.NomineeIdentifierType.
 */
export const NomineeIdentifierTypeSchema: GenEnum<NomineeIdentifierType> = /*@__PURE__*/
  enumDesc(file_WorkflowStep, 2);

/**
 * @generated from enum com.stablemoney.api.broking.identity.WorkflowStatus
 */
export enum WorkflowStatus {
  /**
   * @generated from enum value: UNKNOWN_STATUS = 0;
   */
  UNKNOWN_STATUS = 0,

  /**
   * @generated from enum value: INITIATED = 1;
   */
  INITIATED = 1,

  /**
   * @generated from enum value: COMPLETED = 2;
   */
  COMPLETED = 2,

  /**
   * @generated from enum value: EXPIRED = 3;
   */
  EXPIRED = 3,

  /**
   * @generated from enum value: NOT_INITIATED = 4;
   */
  NOT_INITIATED = 4,
}

/**
 * Describes the enum com.stablemoney.api.broking.identity.WorkflowStatus.
 */
export const WorkflowStatusSchema: GenEnum<WorkflowStatus> = /*@__PURE__*/
  enumDesc(file_WorkflowStep, 3);

