// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/personalization/PersonalizationAdminService.proto (package com.stablemoney.api.personalization, syntax proto3)

import type { GenFile, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import type {
  CreatePageRequestSchema,
  DeletePageResponseSchema,
  IdRequestSchema,
  PageResponseSchema,
  PagesRequestSchema,
  PagesResponseSchema,
  UpdatePageRequestSchema,
} from "../../../public/models/personalization/PersonalizationAdmin_pb.js";
import { file_public_models_personalization_PersonalizationAdmin } from "../../../public/models/personalization/PersonalizationAdmin_pb.js";

/**
 * Describes the file private/services/personalization/PersonalizationAdminService.proto.
 */
export const file_private_services_personalization_PersonalizationAdminService: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CkJwcml2YXRlL3NlcnZpY2VzL3BlcnNvbmFsaXphdGlvbi9QZXJzb25hbGl6YXRpb25BZG1pblNlcnZpY2UucHJvdG8SI2NvbS5zdGFibGVtb25leS5hcGkucGVyc29uYWxpemF0aW9uMucEChtQZXJzb25hbGl6YXRpb25BZG1pblNlcnZpY2USdwoKY3JlYXRlUGFnZRI2LmNvbS5zdGFibGVtb25leS5hcGkucGVyc29uYWxpemF0aW9uLkNyZWF0ZVBhZ2VSZXF1ZXN0GjEuY29tLnN0YWJsZW1vbmV5LmFwaS5wZXJzb25hbGl6YXRpb24uUGFnZVJlc3BvbnNlEncKCnVwZGF0ZVBhZ2USNi5jb20uc3RhYmxlbW9uZXkuYXBpLnBlcnNvbmFsaXphdGlvbi5VcGRhdGVQYWdlUmVxdWVzdBoxLmNvbS5zdGFibGVtb25leS5hcGkucGVyc29uYWxpemF0aW9uLlBhZ2VSZXNwb25zZRJsCgdnZXRQYWdlEi4uY29tLnN0YWJsZW1vbmV5LmFwaS5wZXJzb25hbGl6YXRpb24uSWRSZXF1ZXN0GjEuY29tLnN0YWJsZW1vbmV5LmFwaS5wZXJzb25hbGl6YXRpb24uUGFnZVJlc3BvbnNlEnUKCmRlbGV0ZVBhZ2USLi5jb20uc3RhYmxlbW9uZXkuYXBpLnBlcnNvbmFsaXphdGlvbi5JZFJlcXVlc3QaNy5jb20uc3RhYmxlbW9uZXkuYXBpLnBlcnNvbmFsaXphdGlvbi5EZWxldGVQYWdlUmVzcG9uc2UScQoIZ2V0UGFnZXMSMS5jb20uc3RhYmxlbW9uZXkuYXBpLnBlcnNvbmFsaXphdGlvbi5QYWdlc1JlcXVlc3QaMi5jb20uc3RhYmxlbW9uZXkuYXBpLnBlcnNvbmFsaXphdGlvbi5QYWdlc1Jlc3BvbnNlQicKI2NvbS5zdGFibGVtb25leS5hcGkucGVyc29uYWxpemF0aW9uUAFiBnByb3RvMw",
    [file_public_models_personalization_PersonalizationAdmin],
  );

/**
 * @generated from service com.stablemoney.api.personalization.PersonalizationAdminService
 */
export const PersonalizationAdminService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.personalization.PersonalizationAdminService.createPage
   */
  createPage: {
    methodKind: "unary";
    input: typeof CreatePageRequestSchema;
    output: typeof PageResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.personalization.PersonalizationAdminService.updatePage
   */
  updatePage: {
    methodKind: "unary";
    input: typeof UpdatePageRequestSchema;
    output: typeof PageResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.personalization.PersonalizationAdminService.getPage
   */
  getPage: {
    methodKind: "unary";
    input: typeof IdRequestSchema;
    output: typeof PageResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.personalization.PersonalizationAdminService.deletePage
   */
  deletePage: {
    methodKind: "unary";
    input: typeof IdRequestSchema;
    output: typeof DeletePageResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.personalization.PersonalizationAdminService.getPages
   */
  getPages: {
    methodKind: "unary";
    input: typeof PagesRequestSchema;
    output: typeof PagesResponseSchema;
  };
}> =
  /*@__PURE__*/
  serviceDesc(
    file_private_services_personalization_PersonalizationAdminService,
    0,
  );
