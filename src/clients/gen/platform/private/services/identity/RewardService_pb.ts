// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/RewardService.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type {
  RewardTypesRequestSchema,
  RewardTypesResponseSchema,
} from "../../../public/models/identity/Reward_pb.js";
import { file_public_models_identity_Reward } from "../../../public/models/identity/Reward_pb.js";

/**
 * Describes the file private/services/identity/RewardService.proto.
 */
export const file_private_services_identity_RewardService: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci1wcml2YXRlL3NlcnZpY2VzL2lkZW50aXR5L1Jld2FyZFNlcnZpY2UucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkyhgEKDVJld2FyZFNlcnZpY2USdQoOZ2V0UmV3YXJkVHlwZXMSMC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlJld2FyZFR5cGVzUmVxdWVzdBoxLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUmV3YXJkVHlwZXNSZXNwb25zZWIGcHJvdG8z",
    [file_google_protobuf_empty, file_public_models_identity_Reward],
  );

/**
 * @generated from service com.stablemoney.api.identity.RewardService
 */
export const RewardService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.RewardService.getRewardTypes
   */
  getRewardTypes: {
    methodKind: "unary";
    input: typeof RewardTypesRequestSchema;
    output: typeof RewardTypesResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_identity_RewardService, 0);
