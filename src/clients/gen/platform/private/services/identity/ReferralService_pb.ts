// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/ReferralService.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenService } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, serviceDesc } from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type {
  GetRefereesRequestSchema,
  GetRefereesResponseSchema,
  RefererSchema,
  ReferralLinkRequestSchema,
} from "../../../public/models/identity/Referral_pb.js";
import { file_public_models_identity_Referral } from "../../../public/models/identity/Referral_pb.js";
import type { ReferralLinkResponseSchema } from "../../../public/models/identity/Document_pb.js";
import { file_public_models_identity_Document } from "../../../public/models/identity/Document_pb.js";
import type { UserRequestSchema } from "./User_pb.js";
import { file_private_services_identity_User } from "./User_pb.js";

/**
 * Describes the file private/services/identity/ReferralService.proto.
 */
export const file_private_services_identity_ReferralService: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci9wcml2YXRlL3NlcnZpY2VzL2lkZW50aXR5L1JlZmVycmFsU2VydmljZS5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eTL0AgoPUmVmZXJyYWxTZXJ2aWNlEnkKEmdldFJlZmVyZWVzRm9yVXNlchIwLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuR2V0UmVmZXJlZXNSZXF1ZXN0GjEuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5HZXRSZWZlcmVlc1Jlc3BvbnNlEn8KFmdldFJlZmVycmFsTGlua0ZvclVzZXISMS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlJlZmVycmFsTGlua1JlcXVlc3QaMi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlJlZmVycmFsTGlua1Jlc3BvbnNlEmUKEWdldFJlZmVyZXJGb3JVc2VyEikuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Vc2VyUmVxdWVzdBolLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUmVmZXJlcmIGcHJvdG8z",
    [
      file_google_protobuf_empty,
      file_public_models_identity_Referral,
      file_public_models_identity_Document,
      file_private_services_identity_User,
    ],
  );

/**
 * @generated from service com.stablemoney.api.identity.ReferralService
 */
export const ReferralService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.ReferralService.getRefereesForUser
   */
  getRefereesForUser: {
    methodKind: "unary";
    input: typeof GetRefereesRequestSchema;
    output: typeof GetRefereesResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.ReferralService.getReferralLinkForUser
   */
  getReferralLinkForUser: {
    methodKind: "unary";
    input: typeof ReferralLinkRequestSchema;
    output: typeof ReferralLinkResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.ReferralService.getRefererForUser
   */
  getRefererForUser: {
    methodKind: "unary";
    input: typeof UserRequestSchema;
    output: typeof RefererSchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_identity_ReferralService,
  0,
);
