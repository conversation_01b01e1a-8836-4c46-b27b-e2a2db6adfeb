// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/UserReward.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { PushNotificationType } from "../../../public/models/identity/Common_pb.js";
import { file_public_models_identity_Common } from "../../../public/models/identity/Common_pb.js";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/identity/UserReward.proto.
 */
export const file_private_services_identity_UserReward: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Cipwcml2YXRlL3NlcnZpY2VzL2lkZW50aXR5L1VzZXJSZXdhcmQucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiQgoTUGF5bWVudFN1Y2Nlc3NFdmVudBIbChNwYXJ0bmVyX2N1c3RvbWVyX2lkGAEgASgJEg4KBmFtb3VudBgCIAEoASJCChNCb29raW5nU3VjY2Vzc0V2ZW50EhsKE3BhcnRuZXJfY3VzdG9tZXJfaWQYASABKAkSDgoGYW1vdW50GAIgASgBIrwBChVQdXNoTm90aWZpY2F0aW9uRXZlbnQSDwoHdXNlcl9pZBgBIAEoCRIOCgZhbW91bnQYAiABKAESQQoFZXZlbnQYAyABKA4yMi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlB1c2hOb3RpZmljYXRpb25UeXBlEhEKCWJhbmtfbmFtZRgEIAEoCRIaCg1ib29raW5nX2NvdW50GAUgASgFSACIAQFCEAoOX2Jvb2tpbmdfY291bnQyyQIKEVVzZXJSZXdhcmRTZXJ2aWNlEmQKF3Bvc3RQYXltZW50U3VjY2Vzc0V2ZW50EjEuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5QYXltZW50U3VjY2Vzc0V2ZW50GhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5EmQKF3Bvc3RCb29raW5nU3VjY2Vzc0V2ZW50EjEuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Cb29raW5nU3VjY2Vzc0V2ZW50GhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5EmgKGXBvc3RQdXNoTm90aWZpY2F0aW9uRXZlbnQSMy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlB1c2hOb3RpZmljYXRpb25FdmVudBoWLmdvb2dsZS5wcm90b2J1Zi5FbXB0eUIuCipjb20uc3RhYmxlbW9uZXkuaW50ZXJuYWwuYXBpLm5vdGlmaWNhdGlvbnNQAWIGcHJvdG8z",
    [file_public_models_identity_Common, file_google_protobuf_empty],
  );

/**
 * @generated from message com.stablemoney.api.identity.PaymentSuccessEvent
 */
export type PaymentSuccessEvent =
  Message<"com.stablemoney.api.identity.PaymentSuccessEvent"> & {
    /**
     * @generated from field: string partner_customer_id = 1;
     */
    partnerCustomerId: string;

    /**
     * @generated from field: double amount = 2;
     */
    amount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.PaymentSuccessEvent.
 * Use `create(PaymentSuccessEventSchema)` to create a new message.
 */
export const PaymentSuccessEventSchema: GenMessage<PaymentSuccessEvent> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_UserReward, 0);

/**
 * @generated from message com.stablemoney.api.identity.BookingSuccessEvent
 */
export type BookingSuccessEvent =
  Message<"com.stablemoney.api.identity.BookingSuccessEvent"> & {
    /**
     * @generated from field: string partner_customer_id = 1;
     */
    partnerCustomerId: string;

    /**
     * @generated from field: double amount = 2;
     */
    amount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.BookingSuccessEvent.
 * Use `create(BookingSuccessEventSchema)` to create a new message.
 */
export const BookingSuccessEventSchema: GenMessage<BookingSuccessEvent> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_UserReward, 1);

/**
 * @generated from message com.stablemoney.api.identity.PushNotificationEvent
 */
export type PushNotificationEvent =
  Message<"com.stablemoney.api.identity.PushNotificationEvent"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: double amount = 2;
     */
    amount: number;

    /**
     * @generated from field: com.stablemoney.api.identity.PushNotificationType event = 3;
     */
    event: PushNotificationType;

    /**
     * @generated from field: string bank_name = 4;
     */
    bankName: string;

    /**
     * @generated from field: optional int32 booking_count = 5;
     */
    bookingCount?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.PushNotificationEvent.
 * Use `create(PushNotificationEventSchema)` to create a new message.
 */
export const PushNotificationEventSchema: GenMessage<PushNotificationEvent> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_UserReward, 2);

/**
 * @generated from service com.stablemoney.api.identity.UserRewardService
 */
export const UserRewardService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.UserRewardService.postPaymentSuccessEvent
   */
  postPaymentSuccessEvent: {
    methodKind: "unary";
    input: typeof PaymentSuccessEventSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserRewardService.postBookingSuccessEvent
   */
  postBookingSuccessEvent: {
    methodKind: "unary";
    input: typeof BookingSuccessEventSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserRewardService.postPushNotificationEvent
   */
  postPushNotificationEvent: {
    methodKind: "unary";
    input: typeof PushNotificationEventSchema;
    output: typeof EmptySchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_identity_UserReward, 0);
