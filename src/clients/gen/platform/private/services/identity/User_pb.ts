// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/User.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type {
  EmploymentType,
  Gender,
  IncomeRange,
  MaritalStatus,
  TradingExperience,
} from "../../../public/models/identity/Profile_pb.js";
import { file_public_models_identity_Profile } from "../../../public/models/identity/Profile_pb.js";
import type { DeviceType } from "../../../public/models/identity/Device_pb.js";
import { file_public_models_identity_Device } from "../../../public/models/identity/Device_pb.js";
import type { Empty } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type {
  ProfileCompletionEmploymentType,
  UserBank,
} from "../../../public/models/identity/ProfileCompletion_pb.js";
import { file_public_models_identity_ProfileCompletion } from "../../../public/models/identity/ProfileCompletion_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/identity/User.proto.
 */
export const file_private_services_identity_User: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_identity_Profile,
      file_public_models_identity_Device,
      file_google_protobuf_empty,
      file_public_models_identity_ProfileCompletion,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.UserProfileRequest
 */
export type UserProfileRequest =
  Message<"com.stablemoney.api.identity.UserProfileRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileRequest.
 * Use `create(UserProfileRequestSchema)` to create a new message.
 */
export const UserProfileRequestSchema: GenMessage<UserProfileRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 0);

/**
 * @generated from message com.stablemoney.api.identity.UserRequest
 */
export type UserRequest =
  Message<"com.stablemoney.api.identity.UserRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserRequest.
 * Use `create(UserRequestSchema)` to create a new message.
 */
export const UserRequestSchema: GenMessage<UserRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 1);

/**
 * @generated from message com.stablemoney.api.identity.UsersRequest
 */
export type UsersRequest =
  Message<"com.stablemoney.api.identity.UsersRequest"> & {
    /**
     * @generated from field: repeated string user_ids = 1;
     */
    userIds: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UsersRequest.
 * Use `create(UsersRequestSchema)` to create a new message.
 */
export const UsersRequestSchema: GenMessage<UsersRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 2);

/**
 * @generated from message com.stablemoney.api.identity.UsersResponse
 */
export type UsersResponse =
  Message<"com.stablemoney.api.identity.UsersResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.UserResponse users = 1;
     */
    users: UserResponse[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UsersResponse.
 * Use `create(UsersResponseSchema)` to create a new message.
 */
export const UsersResponseSchema: GenMessage<UsersResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 3);

/**
 * @generated from message com.stablemoney.api.identity.UserDeviceRequest
 */
export type UserDeviceRequest =
  Message<"com.stablemoney.api.identity.UserDeviceRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserDeviceRequest.
 * Use `create(UserDeviceRequestSchema)` to create a new message.
 */
export const UserDeviceRequestSchema: GenMessage<UserDeviceRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 4);

/**
 * @generated from message com.stablemoney.api.identity.UserProfileGrpcResponse
 */
export type UserProfileGrpcResponse =
  Message<"com.stablemoney.api.identity.UserProfileGrpcResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string pan_number = 2 [deprecated = true];
     * @deprecated
     */
    panNumber: string;

    /**
     * @generated from field: string aadhar_number = 3 [deprecated = true];
     * @deprecated
     */
    aadharNumber: string;

    /**
     * @generated from field: string dob = 4;
     */
    dob: string;

    /**
     * @generated from field: com.stablemoney.api.identity.Gender gender = 5;
     */
    gender: Gender;

    /**
     * @generated from field: com.stablemoney.api.identity.IncomeRange income_range = 6;
     */
    incomeRange: IncomeRange;

    /**
     * @generated from field: com.stablemoney.api.identity.EmploymentType employment_type_old = 7 [deprecated = true];
     * @deprecated
     */
    employmentTypeOld: EmploymentType;

    /**
     * @generated from field: com.stablemoney.api.identity.TradingExperience trading_experience = 8 [deprecated = true];
     * @deprecated
     */
    tradingExperience: TradingExperience;

    /**
     * @generated from field: com.stablemoney.api.identity.MaritalStatus marital_status = 9;
     */
    maritalStatus: MaritalStatus;

    /**
     * @generated from field: string father_name = 10 [deprecated = true];
     * @deprecated
     */
    fatherName: string;

    /**
     * @generated from field: string mother_name = 11 [deprecated = true];
     * @deprecated
     */
    motherName: string;

    /**
     * @generated from field: string esign_url = 12 [deprecated = true];
     * @deprecated
     */
    esignUrl: string;

    /**
     * @generated from field: string kra_name = 13 [deprecated = true];
     * @deprecated
     */
    kraName: string;

    /**
     * @generated from field: string income_tax_department_name = 14 [deprecated = true];
     * @deprecated
     */
    incomeTaxDepartmentName: string;

    /**
     * @generated from field: bool is_pan_verified = 15 [deprecated = true];
     * @deprecated
     */
    isPanVerified: boolean;

    /**
     * @generated from field: string referral_url_suffix = 16;
     */
    referralUrlSuffix: string;

    /**
     * @generated from field: com.stablemoney.api.identity.ProfileCompletionEmploymentType employment_type = 20;
     */
    employmentType: ProfileCompletionEmploymentType;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileGrpcResponse.
 * Use `create(UserProfileGrpcResponseSchema)` to create a new message.
 */
export const UserProfileGrpcResponseSchema: GenMessage<UserProfileGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 5);

/**
 * @generated from message com.stablemoney.api.identity.UserResponse
 */
export type UserResponse =
  Message<"com.stablemoney.api.identity.UserResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string phone_number = 3;
     */
    phoneNumber: string;

    /**
     * @generated from field: bool phone_verified = 4;
     */
    phoneVerified: boolean;

    /**
     * @generated from field: string email = 5;
     */
    email: string;

    /**
     * @generated from field: bool email_verified = 6;
     */
    emailVerified: boolean;

    /**
     * @generated from field: string profile_image_url = 8;
     */
    profileImageUrl: string;

    /**
     * @generated from field: string masked_email = 12;
     */
    maskedEmail: string;

    /**
     * @generated from field: string social_name = 13;
     */
    socialName: string;

    /**
     * @generated from field: string first_name = 14;
     */
    firstName: string;

    /**
     * @generated from field: string last_name = 15;
     */
    lastName: string;

    /**
     * @generated from field: string unverified_email = 16;
     */
    unverifiedEmail: string;

    /**
     * @generated from field: string created_at = 17;
     */
    createdAt: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserResponse.
 * Use `create(UserResponseSchema)` to create a new message.
 */
export const UserResponseSchema: GenMessage<UserResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 6);

/**
 * @generated from message com.stablemoney.api.identity.UserDeviceResponse
 */
export type UserDeviceResponse =
  Message<"com.stablemoney.api.identity.UserDeviceResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.UserDeviceData user_devices = 1;
     */
    userDevices: UserDeviceData[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UserDeviceResponse.
 * Use `create(UserDeviceResponseSchema)` to create a new message.
 */
export const UserDeviceResponseSchema: GenMessage<UserDeviceResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 7);

/**
 * @generated from message com.stablemoney.api.identity.UserDeviceData
 */
export type UserDeviceData =
  Message<"com.stablemoney.api.identity.UserDeviceData"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string user_id = 2;
     */
    userId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.DeviceType device_type = 3;
     */
    deviceType: DeviceType;

    /**
     * @generated from field: string os_version = 4;
     */
    osVersion: string;

    /**
     * @generated from field: string model = 5;
     */
    model: string;

    /**
     * @generated from field: string app_version = 6;
     */
    appVersion: string;

    /**
     * @generated from field: string last_active_time = 7;
     */
    lastActiveTime: string;

    /**
     * @generated from field: string notification_token = 8;
     */
    notificationToken: string;

    /**
     * @generated from field: string device_id = 9;
     */
    deviceId: string;

    /**
     * @generated from field: string aa_id = 10;
     */
    aaId: string;

    /**
     * @generated from field: optional string idfv = 11;
     */
    idfv?: string;

    /**
     * @generated from field: optional string appsflyer_id = 12;
     */
    appsflyerId?: string;

    /**
     * @generated from field: optional string app_version_name = 13;
     */
    appVersionName?: string;

    /**
     * @generated from field: optional bool aie = 14;
     */
    aie?: boolean;

    /**
     * @generated from field: optional int64 att = 15;
     */
    att?: bigint;

    /**
     * @generated from field: optional string app_store = 16;
     */
    appStore?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserDeviceData.
 * Use `create(UserDeviceDataSchema)` to create a new message.
 */
export const UserDeviceDataSchema: GenMessage<UserDeviceData> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 8);

/**
 * @generated from message com.stablemoney.api.identity.UserByPhoneNumberRequest
 */
export type UserByPhoneNumberRequest =
  Message<"com.stablemoney.api.identity.UserByPhoneNumberRequest"> & {
    /**
     * @generated from field: string phone_number = 1;
     */
    phoneNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserByPhoneNumberRequest.
 * Use `create(UserByPhoneNumberRequestSchema)` to create a new message.
 */
export const UserByPhoneNumberRequestSchema: GenMessage<UserByPhoneNumberRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 9);

/**
 * @generated from message com.stablemoney.api.identity.DeleteUserGrpcRequest
 */
export type DeleteUserGrpcRequest =
  Message<"com.stablemoney.api.identity.DeleteUserGrpcRequest"> & {
    /**
     * @generated from field: string phone_number = 1;
     */
    phoneNumber: string;

    /**
     * @generated from field: string reason = 2;
     */
    reason: string;

    /**
     * @generated from field: bool force_delete = 3;
     */
    forceDelete: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.DeleteUserGrpcRequest.
 * Use `create(DeleteUserGrpcRequestSchema)` to create a new message.
 */
export const DeleteUserGrpcRequestSchema: GenMessage<DeleteUserGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 10);

/**
 * @generated from message com.stablemoney.api.identity.UserNotificationTokenRequest
 */
export type UserNotificationTokenRequest =
  Message<"com.stablemoney.api.identity.UserNotificationTokenRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserNotificationTokenRequest.
 * Use `create(UserNotificationTokenRequestSchema)` to create a new message.
 */
export const UserNotificationTokenRequestSchema: GenMessage<UserNotificationTokenRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 11);

/**
 * @generated from message com.stablemoney.api.identity.IsUpswingTicketRaised
 */
export type IsUpswingTicketRaised =
  Message<"com.stablemoney.api.identity.IsUpswingTicketRaised"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: bool is_upswing_ticket_raised = 2;
     */
    isUpswingTicketRaised: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.IsUpswingTicketRaised.
 * Use `create(IsUpswingTicketRaisedSchema)` to create a new message.
 */
export const IsUpswingTicketRaisedSchema: GenMessage<IsUpswingTicketRaised> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 12);

/**
 * @generated from message com.stablemoney.api.identity.UserNotificationTokenResponse
 */
export type UserNotificationTokenResponse =
  Message<"com.stablemoney.api.identity.UserNotificationTokenResponse"> & {
    /**
     * @generated from field: string notification_token = 1;
     */
    notificationToken: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserNotificationTokenResponse.
 * Use `create(UserNotificationTokenResponseSchema)` to create a new message.
 */
export const UserNotificationTokenResponseSchema: GenMessage<UserNotificationTokenResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 13);

/**
 * @generated from message com.stablemoney.api.identity.DeleteUserGrpcResponse
 */
export type DeleteUserGrpcResponse =
  Message<"com.stablemoney.api.identity.DeleteUserGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.DeleteUserGrpcResponse.
 * Use `create(DeleteUserGrpcResponseSchema)` to create a new message.
 */
export const DeleteUserGrpcResponseSchema: GenMessage<DeleteUserGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 14);

/**
 * @generated from message com.stablemoney.api.identity.EmptyGrpcResponse
 */
export type EmptyGrpcResponse =
  Message<"com.stablemoney.api.identity.EmptyGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmptyGrpcResponse.
 * Use `create(EmptyGrpcResponseSchema)` to create a new message.
 */
export const EmptyGrpcResponseSchema: GenMessage<EmptyGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 15);

/**
 * @generated from message com.stablemoney.api.identity.UpdateInAppCohortRequest
 */
export type UpdateInAppCohortRequest =
  Message<"com.stablemoney.api.identity.UpdateInAppCohortRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string cohort = 2;
     */
    cohort: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateInAppCohortRequest.
 * Use `create(UpdateInAppCohortRequestSchema)` to create a new message.
 */
export const UpdateInAppCohortRequestSchema: GenMessage<UpdateInAppCohortRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 16);

/**
 * @generated from message com.stablemoney.api.identity.LinkReferralRequest
 */
export type LinkReferralRequest =
  Message<"com.stablemoney.api.identity.LinkReferralRequest"> & {
    /**
     * @generated from field: string referer_phone_number = 1;
     */
    refererPhoneNumber: string;

    /**
     * @generated from field: string referee_phone_number = 2;
     */
    refereePhoneNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.LinkReferralRequest.
 * Use `create(LinkReferralRequestSchema)` to create a new message.
 */
export const LinkReferralRequestSchema: GenMessage<LinkReferralRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 17);

/**
 * @generated from message com.stablemoney.api.identity.GetUserBanksRequest
 */
export type GetUserBanksRequest =
  Message<"com.stablemoney.api.identity.GetUserBanksRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetUserBanksRequest.
 * Use `create(GetUserBanksRequestSchema)` to create a new message.
 */
export const GetUserBanksRequestSchema: GenMessage<GetUserBanksRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 18);

/**
 * @generated from message com.stablemoney.api.identity.GetUserBanksResponse
 */
export type GetUserBanksResponse =
  Message<"com.stablemoney.api.identity.GetUserBanksResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.UserBank userBanks = 1;
     */
    userBanks: UserBank[];
  };

/**
 * Describes the message com.stablemoney.api.identity.GetUserBanksResponse.
 * Use `create(GetUserBanksResponseSchema)` to create a new message.
 */
export const GetUserBanksResponseSchema: GenMessage<GetUserBanksResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 19);

/**
 * @generated from message com.stablemoney.api.identity.GetGTConfigRequest
 */
export type GetGTConfigRequest =
  Message<"com.stablemoney.api.identity.GetGTConfigRequest"> & {};

/**
 * Describes the message com.stablemoney.api.identity.GetGTConfigRequest.
 * Use `create(GetGTConfigRequestSchema)` to create a new message.
 */
export const GetGTConfigRequestSchema: GenMessage<GetGTConfigRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 20);

/**
 * @generated from message com.stablemoney.api.identity.GetGTConfigResponse
 */
export type GetGTConfigResponse =
  Message<"com.stablemoney.api.identity.GetGTConfigResponse"> & {
    /**
     * @generated from field: double amount = 1;
     */
    amount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetGTConfigResponse.
 * Use `create(GetGTConfigResponseSchema)` to create a new message.
 */
export const GetGTConfigResponseSchema: GenMessage<GetGTConfigResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 21);

/**
 * @generated from message com.stablemoney.api.identity.UserProfileRequestV2
 */
export type UserProfileRequestV2 =
  Message<"com.stablemoney.api.identity.UserProfileRequestV2"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileRequestV2.
 * Use `create(UserProfileRequestV2Schema)` to create a new message.
 */
export const UserProfileRequestV2Schema: GenMessage<UserProfileRequestV2> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 22);

/**
 * @generated from message com.stablemoney.api.identity.UserProfileResponseV2
 */
export type UserProfileResponseV2 =
  Message<"com.stablemoney.api.identity.UserProfileResponseV2"> & {
    /**
     * @generated from field: double profile_completion_percentage = 1;
     */
    profileCompletionPercentage: number;

    /**
     * @generated from field: string dob = 2;
     */
    dob: string;

    /**
     * @generated from field: com.stablemoney.api.identity.Gender gender = 3;
     */
    gender: Gender;

    /**
     * @generated from field: com.stablemoney.api.identity.MaritalStatus marital_status = 4;
     */
    maritalStatus: MaritalStatus;

    /**
     * @generated from field: optional com.stablemoney.api.identity.ProfileCompletionEmploymentType employment_type = 5;
     */
    employmentType?: ProfileCompletionEmploymentType;

    /**
     * @generated from field: com.stablemoney.api.identity.UserProfileResponseV2.City city = 6;
     */
    city?: UserProfileResponseV2_City;

    /**
     * @generated from field: bool is_gold_member = 7;
     */
    isGoldMember: boolean;

    /**
     * @generated from field: optional string acquisition_source = 8;
     */
    acquisitionSource?: string;

    /**
     * @generated from field: optional bool is_senior_citizen = 9;
     */
    isSeniorCitizen?: boolean;

    /**
     * @generated from field: bool is_special_gold_member = 10;
     */
    isSpecialGoldMember: boolean;

    /**
     * @generated from field: int64 member_since = 12;
     */
    memberSince: bigint;

    /**
     * @generated from field: com.stablemoney.api.identity.UserProfileResponseV2.ReferralInfo referral_info = 14;
     */
    referralInfo?: UserProfileResponseV2_ReferralInfo;

    /**
     * @generated from field: bool first_fd_reward_enabled = 16;
     */
    firstFdRewardEnabled: boolean;

    /**
     * @generated from field: optional string acquisition_agency = 18;
     */
    acquisitionAgency?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileResponseV2.
 * Use `create(UserProfileResponseV2Schema)` to create a new message.
 */
export const UserProfileResponseV2Schema: GenMessage<UserProfileResponseV2> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 23);

/**
 * @generated from message com.stablemoney.api.identity.UserProfileResponseV2.City
 */
export type UserProfileResponseV2_City =
  Message<"com.stablemoney.api.identity.UserProfileResponseV2.City"> & {
    /**
     * @generated from field: string city_id = 1;
     */
    cityId: string;

    /**
     * @generated from field: string city_name = 2;
     */
    cityName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileResponseV2.City.
 * Use `create(UserProfileResponseV2_CitySchema)` to create a new message.
 */
export const UserProfileResponseV2_CitySchema: GenMessage<UserProfileResponseV2_City> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 23, 0);

/**
 * @generated from message com.stablemoney.api.identity.UserProfileResponseV2.ReferralInfo
 */
export type UserProfileResponseV2_ReferralInfo =
  Message<"com.stablemoney.api.identity.UserProfileResponseV2.ReferralInfo"> & {
    /**
     * @generated from field: bool referred_user = 1;
     */
    referredUser: boolean;

    /**
     * @generated from field: string campaign = 2;
     */
    campaign: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileResponseV2.ReferralInfo.
 * Use `create(UserProfileResponseV2_ReferralInfoSchema)` to create a new message.
 */
export const UserProfileResponseV2_ReferralInfoSchema: GenMessage<UserProfileResponseV2_ReferralInfo> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 23, 1);

/**
 * @generated from message com.stablemoney.api.identity.GetCityByCityIdRequest
 */
export type GetCityByCityIdRequest =
  Message<"com.stablemoney.api.identity.GetCityByCityIdRequest"> & {
    /**
     * @generated from field: string city_id = 1;
     */
    cityId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetCityByCityIdRequest.
 * Use `create(GetCityByCityIdRequestSchema)` to create a new message.
 */
export const GetCityByCityIdRequestSchema: GenMessage<GetCityByCityIdRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 24);

/**
 * @generated from message com.stablemoney.api.identity.GetCityByCityIdResponse
 */
export type GetCityByCityIdResponse =
  Message<"com.stablemoney.api.identity.GetCityByCityIdResponse"> & {
    /**
     * @generated from field: string city_name = 1;
     */
    cityName: string;

    /**
     * @generated from field: string city_id = 2;
     */
    cityId: string;

    /**
     * @generated from field: string state_name = 3;
     */
    stateName: string;

    /**
     * @generated from field: string country_name = 4;
     */
    countryName: string;

    /**
     * @generated from field: bool is_tier_1 = 5;
     */
    isTier1: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetCityByCityIdResponse.
 * Use `create(GetCityByCityIdResponseSchema)` to create a new message.
 */
export const GetCityByCityIdResponseSchema: GenMessage<GetCityByCityIdResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 25);

/**
 * @generated from message com.stablemoney.api.identity.SaveUserPanDetailsRequest
 */
export type SaveUserPanDetailsRequest =
  Message<"com.stablemoney.api.identity.SaveUserPanDetailsRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string pan_number = 2;
     */
    panNumber: string;

    /**
     * @generated from field: string dob = 3;
     */
    dob: string;

    /**
     * @generated from field: bool pan_consent = 4;
     */
    panConsent: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.SaveUserPanDetailsRequest.
 * Use `create(SaveUserPanDetailsRequestSchema)` to create a new message.
 */
export const SaveUserPanDetailsRequestSchema: GenMessage<SaveUserPanDetailsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 26);

/**
 * @generated from message com.stablemoney.api.identity.SaveUserPanDetailsResponse
 */
export type SaveUserPanDetailsResponse =
  Message<"com.stablemoney.api.identity.SaveUserPanDetailsResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SaveUserPanDetailsResponse.
 * Use `create(SaveUserPanDetailsResponseSchema)` to create a new message.
 */
export const SaveUserPanDetailsResponseSchema: GenMessage<SaveUserPanDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 27);

/**
 * @generated from message com.stablemoney.api.identity.UserPanDetailsRequest
 */
export type UserPanDetailsRequest =
  Message<"com.stablemoney.api.identity.UserPanDetailsRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserPanDetailsRequest.
 * Use `create(UserPanDetailsRequestSchema)` to create a new message.
 */
export const UserPanDetailsRequestSchema: GenMessage<UserPanDetailsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 28);

/**
 * @generated from message com.stablemoney.api.identity.UserPanDetailsResponse
 */
export type UserPanDetailsResponse =
  Message<"com.stablemoney.api.identity.UserPanDetailsResponse"> & {
    /**
     * @generated from field: string pan_number = 2;
     */
    panNumber: string;

    /**
     * @generated from field: string dob = 3;
     */
    dob: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserPanDetailsResponse.
 * Use `create(UserPanDetailsResponseSchema)` to create a new message.
 */
export const UserPanDetailsResponseSchema: GenMessage<UserPanDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 29);

/**
 * @generated from message com.stablemoney.api.identity.UserCityRequest
 */
export type UserCityRequest =
  Message<"com.stablemoney.api.identity.UserCityRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserCityRequest.
 * Use `create(UserCityRequestSchema)` to create a new message.
 */
export const UserCityRequestSchema: GenMessage<UserCityRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 30);

/**
 * @generated from message com.stablemoney.api.identity.UserCityResponse
 */
export type UserCityResponse =
  Message<"com.stablemoney.api.identity.UserCityResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.UserCityResponse.City city = 2;
     */
    city?: UserCityResponse_City;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserCityResponse.
 * Use `create(UserCityResponseSchema)` to create a new message.
 */
export const UserCityResponseSchema: GenMessage<UserCityResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 31);

/**
 * @generated from message com.stablemoney.api.identity.UserCityResponse.City
 */
export type UserCityResponse_City =
  Message<"com.stablemoney.api.identity.UserCityResponse.City"> & {
    /**
     * @generated from field: string city_id = 1;
     */
    cityId: string;

    /**
     * @generated from field: string city_name = 2;
     */
    cityName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserCityResponse.City.
 * Use `create(UserCityResponse_CitySchema)` to create a new message.
 */
export const UserCityResponse_CitySchema: GenMessage<UserCityResponse_City> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_User, 31, 0);

/**
 * @generated from service com.stablemoney.api.identity.UserService
 */
export const UserService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserProfileData
   */
  getUserProfileData: {
    methodKind: "unary";
    input: typeof UserProfileRequestSchema;
    output: typeof UserProfileGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserData
   */
  getUserData: {
    methodKind: "unary";
    input: typeof UserRequestSchema;
    output: typeof UserResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUsersData
   */
  getUsersData: {
    methodKind: "unary";
    input: typeof UsersRequestSchema;
    output: typeof UsersResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserDeviceData
   */
  getUserDeviceData: {
    methodKind: "unary";
    input: typeof UserDeviceRequestSchema;
    output: typeof UserDeviceResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserLastActiveDevice
   */
  getUserLastActiveDevice: {
    methodKind: "unary";
    input: typeof UserDeviceRequestSchema;
    output: typeof UserDeviceDataSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserDataByPhoneNumber
   */
  getUserDataByPhoneNumber: {
    methodKind: "unary";
    input: typeof UserByPhoneNumberRequestSchema;
    output: typeof UserResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.deleteUser
   */
  deleteUser: {
    methodKind: "unary";
    input: typeof DeleteUserGrpcRequestSchema;
    output: typeof DeleteUserGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserNotificationToken
   */
  getUserNotificationToken: {
    methodKind: "unary";
    input: typeof UserNotificationTokenRequestSchema;
    output: typeof UserNotificationTokenResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.postIsUpswingTicketRaised
   */
  postIsUpswingTicketRaised: {
    methodKind: "unary";
    input: typeof IsUpswingTicketRaisedSchema;
    output: typeof EmptyGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.linkReferral
   */
  linkReferral: {
    methodKind: "unary";
    input: typeof LinkReferralRequestSchema;
    output: typeof EmptyGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserBanks
   */
  getUserBanks: {
    methodKind: "unary";
    input: typeof GetUserBanksRequestSchema;
    output: typeof GetUserBanksResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getGTConfig
   */
  getGTConfig: {
    methodKind: "unary";
    input: typeof GetGTConfigRequestSchema;
    output: typeof GetGTConfigResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserProfileV2
   * @deprecated
   */
  getUserProfileV2: {
    methodKind: "unary";
    input: typeof UserProfileRequestV2Schema;
    output: typeof UserProfileResponseV2Schema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getCityByCityId
   */
  getCityByCityId: {
    methodKind: "unary";
    input: typeof GetCityByCityIdRequestSchema;
    output: typeof GetCityByCityIdResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserCity
   */
  getUserCity: {
    methodKind: "unary";
    input: typeof UserCityRequestSchema;
    output: typeof UserCityResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.saveUserPanDetails
   */
  saveUserPanDetails: {
    methodKind: "unary";
    input: typeof SaveUserPanDetailsRequestSchema;
    output: typeof SaveUserPanDetailsResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.UserService.getUserPanDetails
   */
  getUserPanDetails: {
    methodKind: "unary";
    input: typeof UserPanDetailsRequestSchema;
    output: typeof UserPanDetailsResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_identity_User, 0);
