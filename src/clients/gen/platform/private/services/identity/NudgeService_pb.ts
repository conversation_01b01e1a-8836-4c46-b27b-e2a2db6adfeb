// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/NudgeService.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  enumDesc,
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/identity/NudgeService.proto.
 */
export const file_private_services_identity_NudgeService: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Cixwcml2YXRlL3NlcnZpY2VzL2lkZW50aXR5L051ZGdlU2VydmljZS5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSJOCg9HZXROdWRnZVJlcXVlc3QSDwoHdXNlcl9pZBgBIAEoCRIYChBhcHBfdmVyc2lvbl9jb2RlGAIgASgFEhAKCHBhZ2VfdXJpGAMgASgJIlMKEEdldE51ZGdlUmVzcG9uc2USEAoIbnVkZ2VfaWQYASABKAkSDQoFbnVkZ2UYAiABKAkSEAoIcHJpb3JpdHkYAyABKAUSDAoEbmFtZRgEIAEoCSLKAQoSTnVkZ2VBY3Rpb25SZXF1ZXN0Eg8KB3VzZXJfaWQYASABKAkSEAoIbnVkZ2VfaWQYAiABKAkSUAoLYWN0aW9uX3R5cGUYAyABKA4yOy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk51ZGdlQWN0aW9uUmVxdWVzdC5BY3Rpb25UeXBlIj8KCkFjdGlvblR5cGUSGAoUVU5LTk9XTl9OVURHRV9BQ1RJT04QABIKCgZBQ0NFUFQQARILCgdESVNNSVNTEAIiFQoTTnVkZ2VBY3Rpb25SZXNwb25zZTLzAQoMTnVkZ2VTZXJ2aWNlEmkKCGdldE51ZGdlEi0uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5HZXROdWRnZVJlcXVlc3QaLi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkdldE51ZGdlUmVzcG9uc2USeAoRdXBkYXRlTnVkZ2VBY3Rpb24SMC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk51ZGdlQWN0aW9uUmVxdWVzdBoxLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuTnVkZ2VBY3Rpb25SZXNwb25zZUIdChljb20uc3RhYmxlbW9uZXkuYXBpLm51ZGdlUAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.GetNudgeRequest
 */
export type GetNudgeRequest =
  Message<"com.stablemoney.api.identity.GetNudgeRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: int32 app_version_code = 2;
     */
    appVersionCode: number;

    /**
     * @generated from field: string page_uri = 3;
     */
    pageUri: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetNudgeRequest.
 * Use `create(GetNudgeRequestSchema)` to create a new message.
 */
export const GetNudgeRequestSchema: GenMessage<GetNudgeRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_NudgeService, 0);

/**
 * @generated from message com.stablemoney.api.identity.GetNudgeResponse
 */
export type GetNudgeResponse =
  Message<"com.stablemoney.api.identity.GetNudgeResponse"> & {
    /**
     * @generated from field: string nudge_id = 1;
     */
    nudgeId: string;

    /**
     * @generated from field: string nudge = 2;
     */
    nudge: string;

    /**
     * @generated from field: int32 priority = 3;
     */
    priority: number;

    /**
     * @generated from field: string name = 4;
     */
    name: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetNudgeResponse.
 * Use `create(GetNudgeResponseSchema)` to create a new message.
 */
export const GetNudgeResponseSchema: GenMessage<GetNudgeResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_NudgeService, 1);

/**
 * @generated from message com.stablemoney.api.identity.NudgeActionRequest
 */
export type NudgeActionRequest =
  Message<"com.stablemoney.api.identity.NudgeActionRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string nudge_id = 2;
     */
    nudgeId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.NudgeActionRequest.ActionType action_type = 3;
     */
    actionType: NudgeActionRequest_ActionType;
  };

/**
 * Describes the message com.stablemoney.api.identity.NudgeActionRequest.
 * Use `create(NudgeActionRequestSchema)` to create a new message.
 */
export const NudgeActionRequestSchema: GenMessage<NudgeActionRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_NudgeService, 2);

/**
 * @generated from enum com.stablemoney.api.identity.NudgeActionRequest.ActionType
 */
export enum NudgeActionRequest_ActionType {
  /**
   * @generated from enum value: UNKNOWN_NUDGE_ACTION = 0;
   */
  UNKNOWN_NUDGE_ACTION = 0,

  /**
   * @generated from enum value: ACCEPT = 1;
   */
  ACCEPT = 1,

  /**
   * @generated from enum value: DISMISS = 2;
   */
  DISMISS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.NudgeActionRequest.ActionType.
 */
export const NudgeActionRequest_ActionTypeSchema: GenEnum<NudgeActionRequest_ActionType> =
  /*@__PURE__*/
  enumDesc(file_private_services_identity_NudgeService, 2, 0);

/**
 * @generated from message com.stablemoney.api.identity.NudgeActionResponse
 */
export type NudgeActionResponse =
  Message<"com.stablemoney.api.identity.NudgeActionResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.NudgeActionResponse.
 * Use `create(NudgeActionResponseSchema)` to create a new message.
 */
export const NudgeActionResponseSchema: GenMessage<NudgeActionResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_NudgeService, 3);

/**
 * @generated from service com.stablemoney.api.identity.NudgeService
 */
export const NudgeService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.NudgeService.getNudge
   */
  getNudge: {
    methodKind: "unary";
    input: typeof GetNudgeRequestSchema;
    output: typeof GetNudgeResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.NudgeService.updateNudgeAction
   */
  updateNudgeAction: {
    methodKind: "unary";
    input: typeof NudgeActionRequestSchema;
    output: typeof NudgeActionResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_identity_NudgeService, 0);
