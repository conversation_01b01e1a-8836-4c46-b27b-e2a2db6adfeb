// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/UserQuestionnaire.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import { file_public_models_identity_Profile } from "../../../public/models/identity/Profile_pb.js";
import { file_public_models_business_BankListing } from "../../../public/models/business/BankListing_pb.js";
import type { QuestionType } from "../../../public/models/identity/Questionnaire_pb.js";
import { file_public_models_identity_Questionnaire } from "../../../public/models/identity/Questionnaire_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/identity/UserQuestionnaire.proto.
 */
export const file_private_services_identity_UserQuestionnaire: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CjFwcml2YXRlL3NlcnZpY2VzL2lkZW50aXR5L1VzZXJRdWVzdGlvbm5haXJlLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5ItgBChJBZGRRdWVzdGlvblJlcXVlc3QSEAoIcXVlc3Rpb24YASABKAkSEwoLZGVzY3JpcHRpb24YAiABKAkSEwoLYnV0dG9uX3RleHQYAyABKAkSFAoMaXNfc2tpcHBhYmxlGAQgASgIEkEKDXF1ZXN0aW9uX3R5cGUYBSABKA4yKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlF1ZXN0aW9uVHlwZRIZChFxdWVzdGlvbl9zZXF1ZW5jZRgGIAEoBRISCgpjcmVhdGVkX2J5GAcgASgJImwKGEFkZFF1ZXN0aW9uQW5zd2VyUmVxdWVzdBITCgtxdWVzdGlvbl9pZBgBIAEoCRIOCgZhbnN3ZXIYAiABKAkSFwoPYW5zd2VyX3NlcXVlbmNlGAMgASgFEhIKCmNyZWF0ZWRfYnkYByABKAkiSQoVVXBkYXRlUXVlc3Rpb25SZXF1ZXN0EgoKAmlkGAEgASgJEhAKCGlzQWN0aXZlGAIgASgIEhIKCnVwZGF0ZWRfYnkYAyABKAkiTwobVXBkYXRlUXVlc3Rpb25BbnN3ZXJSZXF1ZXN0EgoKAmlkGAEgASgJEhAKCGlzQWN0aXZlGAIgASgIEhIKCnVwZGF0ZWRfYnkYAyABKAkyngMKFFF1ZXN0aW9ubmFpcmVTZXJ2aWNlElsKD2FkZFF1ZXN0aW9uRGF0YRIwLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQWRkUXVlc3Rpb25SZXF1ZXN0GhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5EmEKEnVwZGF0ZVF1ZXN0aW9uRGF0YRIzLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuVXBkYXRlUXVlc3Rpb25SZXF1ZXN0GhYuZ29vZ2xlLnByb3RvYnVmLkVtcHR5El8KDWFkZEFuc3dlckRhdGESNi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkFkZFF1ZXN0aW9uQW5zd2VyUmVxdWVzdBoWLmdvb2dsZS5wcm90b2J1Zi5FbXB0eRJlChB1cGRhdGVBbnN3ZXJEYXRhEjkuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5VcGRhdGVRdWVzdGlvbkFuc3dlclJlcXVlc3QaFi5nb29nbGUucHJvdG9idWYuRW1wdHlCHgocY29tLnN0YWJsZW1vbmV5LmludGVybmFsLmFwaWIGcHJvdG8z",
    [
      file_google_protobuf_empty,
      file_public_models_identity_Profile,
      file_public_models_business_BankListing,
      file_public_models_identity_Questionnaire,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.AddQuestionRequest
 */
export type AddQuestionRequest =
  Message<"com.stablemoney.api.identity.AddQuestionRequest"> & {
    /**
     * @generated from field: string question = 1;
     */
    question: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: string button_text = 3;
     */
    buttonText: string;

    /**
     * @generated from field: bool is_skippable = 4;
     */
    isSkippable: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.QuestionType question_type = 5;
     */
    questionType: QuestionType;

    /**
     * @generated from field: int32 question_sequence = 6;
     */
    questionSequence: number;

    /**
     * @generated from field: string created_by = 7;
     */
    createdBy: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddQuestionRequest.
 * Use `create(AddQuestionRequestSchema)` to create a new message.
 */
export const AddQuestionRequestSchema: GenMessage<AddQuestionRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_UserQuestionnaire, 0);

/**
 * @generated from message com.stablemoney.api.identity.AddQuestionAnswerRequest
 */
export type AddQuestionAnswerRequest =
  Message<"com.stablemoney.api.identity.AddQuestionAnswerRequest"> & {
    /**
     * @generated from field: string question_id = 1;
     */
    questionId: string;

    /**
     * @generated from field: string answer = 2;
     */
    answer: string;

    /**
     * @generated from field: int32 answer_sequence = 3;
     */
    answerSequence: number;

    /**
     * @generated from field: string created_by = 7;
     */
    createdBy: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddQuestionAnswerRequest.
 * Use `create(AddQuestionAnswerRequestSchema)` to create a new message.
 */
export const AddQuestionAnswerRequestSchema: GenMessage<AddQuestionAnswerRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_UserQuestionnaire, 1);

/**
 * @generated from message com.stablemoney.api.identity.UpdateQuestionRequest
 */
export type UpdateQuestionRequest =
  Message<"com.stablemoney.api.identity.UpdateQuestionRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: bool isActive = 2;
     */
    isActive: boolean;

    /**
     * @generated from field: string updated_by = 3;
     */
    updatedBy: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateQuestionRequest.
 * Use `create(UpdateQuestionRequestSchema)` to create a new message.
 */
export const UpdateQuestionRequestSchema: GenMessage<UpdateQuestionRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_UserQuestionnaire, 2);

/**
 * @generated from message com.stablemoney.api.identity.UpdateQuestionAnswerRequest
 */
export type UpdateQuestionAnswerRequest =
  Message<"com.stablemoney.api.identity.UpdateQuestionAnswerRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: bool isActive = 2;
     */
    isActive: boolean;

    /**
     * @generated from field: string updated_by = 3;
     */
    updatedBy: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateQuestionAnswerRequest.
 * Use `create(UpdateQuestionAnswerRequestSchema)` to create a new message.
 */
export const UpdateQuestionAnswerRequestSchema: GenMessage<UpdateQuestionAnswerRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_UserQuestionnaire, 3);

/**
 * @generated from service com.stablemoney.api.identity.QuestionnaireService
 */
export const QuestionnaireService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.QuestionnaireService.addQuestionData
   */
  addQuestionData: {
    methodKind: "unary";
    input: typeof AddQuestionRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.QuestionnaireService.updateQuestionData
   */
  updateQuestionData: {
    methodKind: "unary";
    input: typeof UpdateQuestionRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.QuestionnaireService.addAnswerData
   */
  addAnswerData: {
    methodKind: "unary";
    input: typeof AddQuestionAnswerRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.QuestionnaireService.updateAnswerData
   */
  updateAnswerData: {
    methodKind: "unary";
    input: typeof UpdateQuestionAnswerRequestSchema;
    output: typeof EmptySchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_identity_UserQuestionnaire,
  0,
);
