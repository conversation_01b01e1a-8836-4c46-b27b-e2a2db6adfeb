// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/Rating.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  enumDesc,
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type {
  PostRatingRequestSchema,
  PostRatingResponseSchema,
} from "../../../public/models/identity/UserRating_pb.js";
import { file_public_models_identity_UserRating } from "../../../public/models/identity/UserRating_pb.js";
import { file_public_models_identity_Common } from "../../../public/models/identity/Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/identity/Rating.proto.
 */
export const file_private_services_identity_Rating: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiZwcml2YXRlL3NlcnZpY2VzL2lkZW50aXR5L1JhdGluZy5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSJQChVHZXRSYXRpbmdOdWRnZVJlcXVlc3QSDwoHdXNlcl9pZBgBIAEoCRImCh5pc19wYXJ0X29mX3JhdGluZ19udWRnZV9jb2hvcnQYAiABKAgiqAIKFkdldFJhdGluZ051ZGdlUmVzcG9uc2USGQoRc2hvd19yYXRpbmdfbnVkZ2UYASABKAgSVgoGc291cmNlGAIgASgOMkYuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5HZXRSYXRpbmdOdWRnZVJlc3BvbnNlLlJhdGluZ051ZGdlU291cmNlEgsKA3RhZxgDIAEoCSKNAQoRUmF0aW5nTnVkZ2VTb3VyY2USCwoHVU5LTk9XThAAEhMKD0ZJUlNUX0ZEX0JPT0tFRBABEhQKEFJFUEVBVF9GRF9CT09LRUQQAhISCg5GSVJTVF9SRUZFUlJBTBADEhMKD1JFUEVBVF9SRUZFUlJBTBAEEhcKE1JBVElOR19OVURHRV9DT0hPUlQQBSIsChlEaXNtaXNzUmF0aW5nTnVkZ2VSZXF1ZXN0Eg8KB3VzZXJfaWQYASABKAkiLQoaRGlzbWlzc1JhdGluZ051ZGdlUmVzcG9uc2USDwoHdXNlcl9pZBgBIAEoCTKLAwoNUmF0aW5nU2VydmljZRJ7Cg5nZXRSYXRpbmdOdWRnZRIzLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuR2V0UmF0aW5nTnVkZ2VSZXF1ZXN0GjQuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5HZXRSYXRpbmdOdWRnZVJlc3BvbnNlEnMKDnBvc3RVc2VyUmF0aW5nEi8uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Qb3N0UmF0aW5nUmVxdWVzdBowLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUG9zdFJhdGluZ1Jlc3BvbnNlEocBChJkaXNtaXNzUmF0aW5nTnVkZ2USNy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkRpc21pc3NSYXRpbmdOdWRnZVJlcXVlc3QaOC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkRpc21pc3NSYXRpbmdOdWRnZVJlc3BvbnNlQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z",
    [
      file_public_models_identity_UserRating,
      file_public_models_identity_Common,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.GetRatingNudgeRequest
 */
export type GetRatingNudgeRequest =
  Message<"com.stablemoney.api.identity.GetRatingNudgeRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: bool is_part_of_rating_nudge_cohort = 2;
     */
    isPartOfRatingNudgeCohort: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetRatingNudgeRequest.
 * Use `create(GetRatingNudgeRequestSchema)` to create a new message.
 */
export const GetRatingNudgeRequestSchema: GenMessage<GetRatingNudgeRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_Rating, 0);

/**
 * @generated from message com.stablemoney.api.identity.GetRatingNudgeResponse
 */
export type GetRatingNudgeResponse =
  Message<"com.stablemoney.api.identity.GetRatingNudgeResponse"> & {
    /**
     * @generated from field: bool show_rating_nudge = 1;
     */
    showRatingNudge: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.GetRatingNudgeResponse.RatingNudgeSource source = 2;
     */
    source: GetRatingNudgeResponse_RatingNudgeSource;

    /**
     * @generated from field: string tag = 3;
     */
    tag: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetRatingNudgeResponse.
 * Use `create(GetRatingNudgeResponseSchema)` to create a new message.
 */
export const GetRatingNudgeResponseSchema: GenMessage<GetRatingNudgeResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_Rating, 1);

/**
 * @generated from enum com.stablemoney.api.identity.GetRatingNudgeResponse.RatingNudgeSource
 */
export enum GetRatingNudgeResponse_RatingNudgeSource {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: FIRST_FD_BOOKED = 1;
   */
  FIRST_FD_BOOKED = 1,

  /**
   * @generated from enum value: REPEAT_FD_BOOKED = 2;
   */
  REPEAT_FD_BOOKED = 2,

  /**
   * @generated from enum value: FIRST_REFERRAL = 3;
   */
  FIRST_REFERRAL = 3,

  /**
   * @generated from enum value: REPEAT_REFERRAL = 4;
   */
  REPEAT_REFERRAL = 4,

  /**
   * @generated from enum value: RATING_NUDGE_COHORT = 5;
   */
  RATING_NUDGE_COHORT = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.GetRatingNudgeResponse.RatingNudgeSource.
 */
export const GetRatingNudgeResponse_RatingNudgeSourceSchema: GenEnum<GetRatingNudgeResponse_RatingNudgeSource> =
  /*@__PURE__*/
  enumDesc(file_private_services_identity_Rating, 1, 0);

/**
 * @generated from message com.stablemoney.api.identity.DismissRatingNudgeRequest
 */
export type DismissRatingNudgeRequest =
  Message<"com.stablemoney.api.identity.DismissRatingNudgeRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.DismissRatingNudgeRequest.
 * Use `create(DismissRatingNudgeRequestSchema)` to create a new message.
 */
export const DismissRatingNudgeRequestSchema: GenMessage<DismissRatingNudgeRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_Rating, 2);

/**
 * @generated from message com.stablemoney.api.identity.DismissRatingNudgeResponse
 */
export type DismissRatingNudgeResponse =
  Message<"com.stablemoney.api.identity.DismissRatingNudgeResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.DismissRatingNudgeResponse.
 * Use `create(DismissRatingNudgeResponseSchema)` to create a new message.
 */
export const DismissRatingNudgeResponseSchema: GenMessage<DismissRatingNudgeResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_Rating, 3);

/**
 * @generated from service com.stablemoney.api.identity.RatingService
 */
export const RatingService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.RatingService.getRatingNudge
   */
  getRatingNudge: {
    methodKind: "unary";
    input: typeof GetRatingNudgeRequestSchema;
    output: typeof GetRatingNudgeResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.RatingService.postUserRating
   */
  postUserRating: {
    methodKind: "unary";
    input: typeof PostRatingRequestSchema;
    output: typeof PostRatingResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.RatingService.dismissRatingNudge
   */
  dismissRatingNudge: {
    methodKind: "unary";
    input: typeof DismissRatingNudgeRequestSchema;
    output: typeof DismissRatingNudgeResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_identity_Rating, 0);
