// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/identity/CacheEviction.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/identity/CacheEviction.proto.
 */
export const file_private_services_identity_CacheEviction: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci1wcml2YXRlL3NlcnZpY2VzL2lkZW50aXR5L0NhY2hlRXZpY3Rpb24ucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiIQoRRXZpY3RDYWNoZVJlcXVlc3QSDAoEbmFtZRgBIAEoCSIUChJFdmljdENhY2hlUmVzcG9uc2UyjwEKHEJ1c2luZXNzQ2FjaGVFdmljdGlvblNlcnZpY2USbwoKZXZpY3RDYWNoZRIvLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuRXZpY3RDYWNoZVJlcXVlc3QaMC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkV2aWN0Q2FjaGVSZXNwb25zZTKPAQocSWRlbnRpdHlDYWNoZUV2aWN0aW9uU2VydmljZRJvCgpldmljdENhY2hlEi8uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5FdmljdENhY2hlUmVxdWVzdBowLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuRXZpY3RDYWNoZVJlc3BvbnNlQiAKHGNvbS5zdGFibGVtb25leS5pbnRlcm5hbC5hcGlQAWIGcHJvdG8z",
    [file_google_protobuf_empty],
  );

/**
 * @generated from message com.stablemoney.api.identity.EvictCacheRequest
 */
export type EvictCacheRequest =
  Message<"com.stablemoney.api.identity.EvictCacheRequest"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.EvictCacheRequest.
 * Use `create(EvictCacheRequestSchema)` to create a new message.
 */
export const EvictCacheRequestSchema: GenMessage<EvictCacheRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_CacheEviction, 0);

/**
 * @generated from message com.stablemoney.api.identity.EvictCacheResponse
 */
export type EvictCacheResponse =
  Message<"com.stablemoney.api.identity.EvictCacheResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.EvictCacheResponse.
 * Use `create(EvictCacheResponseSchema)` to create a new message.
 */
export const EvictCacheResponseSchema: GenMessage<EvictCacheResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_identity_CacheEviction, 1);

/**
 * @generated from service com.stablemoney.api.identity.BusinessCacheEvictionService
 */
export const BusinessCacheEvictionService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.BusinessCacheEvictionService.evictCache
   */
  evictCache: {
    methodKind: "unary";
    input: typeof EvictCacheRequestSchema;
    output: typeof EvictCacheResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_identity_CacheEviction, 0);

/**
 * @generated from service com.stablemoney.api.identity.IdentityCacheEvictionService
 */
export const IdentityCacheEvictionService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.IdentityCacheEvictionService.evictCache
   */
  evictCache: {
    methodKind: "unary";
    input: typeof EvictCacheRequestSchema;
    output: typeof EvictCacheResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_identity_CacheEviction, 1);
