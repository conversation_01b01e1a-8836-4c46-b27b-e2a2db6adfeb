// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/broking/Common.proto (package com.stablemoney.api.broking, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/broking/Common.proto.
 */
export const file_private_services_broking_Common: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.broking.ErrorResponse
 */
export type ErrorResponse =
  Message<"com.stablemoney.api.broking.ErrorResponse"> & {
    /**
     * @generated from field: string error_code = 1;
     */
    errorCode: string;

    /**
     * @generated from field: string error_message = 2;
     */
    errorMessage: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.ErrorResponse.
 * Use `create(ErrorResponseSchema)` to create a new message.
 */
export const ErrorResponseSchema: GenMessage<ErrorResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Common, 0);

/**
 * @generated from message com.stablemoney.api.broking.ConfigDataResponse
 */
export type ConfigDataResponse =
  Message<"com.stablemoney.api.broking.ConfigDataResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.broking.ConfigData data = 1;
     */
    data: ConfigData[];
  };

/**
 * Describes the message com.stablemoney.api.broking.ConfigDataResponse.
 * Use `create(ConfigDataResponseSchema)` to create a new message.
 */
export const ConfigDataResponseSchema: GenMessage<ConfigDataResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Common, 1);

/**
 * @generated from message com.stablemoney.api.broking.ConfigData
 */
export type ConfigData = Message<"com.stablemoney.api.broking.ConfigData"> & {
  /**
   * @generated from field: com.stablemoney.api.broking.AppConfigType config_name = 1;
   */
  configName: AppConfigType;

  /**
   * @generated from field: string config_value = 2;
   */
  configValue: string;

  /**
   * @generated from field: com.stablemoney.api.broking.AppConfigValueType config_type = 3;
   */
  configType: AppConfigValueType;

  /**
   * @generated from field: int32 min_version = 4;
   */
  minVersion: number;

  /**
   * @generated from field: int32 max_version = 5;
   */
  maxVersion: number;

  /**
   * @generated from field: string description = 6;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.broking.ConfigData.
 * Use `create(ConfigDataSchema)` to create a new message.
 */
export const ConfigDataSchema: GenMessage<ConfigData> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Common, 2);

/**
 * @generated from message com.stablemoney.api.broking.UserConfigResponse
 */
export type UserConfigResponse =
  Message<"com.stablemoney.api.broking.UserConfigResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.broking.UserConfigData data = 1;
     */
    data: UserConfigData[];
  };

/**
 * Describes the message com.stablemoney.api.broking.UserConfigResponse.
 * Use `create(UserConfigResponseSchema)` to create a new message.
 */
export const UserConfigResponseSchema: GenMessage<UserConfigResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Common, 3);

/**
 * @generated from message com.stablemoney.api.broking.UserConfigData
 */
export type UserConfigData =
  Message<"com.stablemoney.api.broking.UserConfigData"> & {
    /**
     * @generated from field: com.stablemoney.api.broking.UserConfigType config_name = 1;
     */
    configName: UserConfigType;

    /**
     * @generated from field: com.stablemoney.api.broking.AppConfigValueType config_type = 2;
     */
    configType: AppConfigValueType;

    /**
     * @generated from field: string config_value = 3;
     */
    configValue: string;

    /**
     * @generated from field: string description = 4;
     */
    description: string;

    /**
     * @generated from field: bool show_cta = 5;
     */
    showCta: boolean;
  };

/**
 * Describes the message com.stablemoney.api.broking.UserConfigData.
 * Use `create(UserConfigDataSchema)` to create a new message.
 */
export const UserConfigDataSchema: GenMessage<UserConfigData> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Common, 4);

/**
 * @generated from message com.stablemoney.api.broking.DataKey
 */
export type DataKey = Message<"com.stablemoney.api.broking.DataKey"> & {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.DataKey.Variable context_variables = 3;
   */
  contextVariables: DataKey_Variable[];
};

/**
 * Describes the message com.stablemoney.api.broking.DataKey.
 * Use `create(DataKeySchema)` to create a new message.
 */
export const DataKeySchema: GenMessage<DataKey> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Common, 5);

/**
 * @generated from message com.stablemoney.api.broking.DataKey.Variable
 */
export type DataKey_Variable =
  Message<"com.stablemoney.api.broking.DataKey.Variable"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @generated from field: com.stablemoney.api.broking.DataKey.VariableType type = 2;
     */
    type: DataKey_VariableType;

    /**
     * @generated from field: string value = 3;
     */
    value: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.DataKey.Variable.
 * Use `create(DataKey_VariableSchema)` to create a new message.
 */
export const DataKey_VariableSchema: GenMessage<DataKey_Variable> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Common, 5, 0);

/**
 * @generated from enum com.stablemoney.api.broking.DataKey.VariableType
 */
export enum DataKey_VariableType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STRING = 1;
   */
  STRING = 1,

  /**
   * @generated from enum value: CURRENCY = 2;
   */
  CURRENCY = 2,

  /**
   * @generated from enum value: DATE = 3;
   */
  DATE = 3,

  /**
   * @generated from enum value: DATE_TIME = 4;
   */
  DATE_TIME = 4,

  /**
   * @generated from enum value: USER_FIRST_NAME = 5;
   */
  USER_FIRST_NAME = 5,

  /**
   * @generated from enum value: SHORT_CURRENCY = 6;
   */
  SHORT_CURRENCY = 6,

  /**
   * @generated from enum value: PERCENT = 7;
   */
  PERCENT = 7,

  /**
   * @generated from enum value: PERCENT_2F = 8;
   */
  PERCENT_2F = 8,
}

/**
 * Describes the enum com.stablemoney.api.broking.DataKey.VariableType.
 */
export const DataKey_VariableTypeSchema: GenEnum<DataKey_VariableType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 5, 0);

/**
 * @generated from enum com.stablemoney.api.broking.MessageType
 */
export enum MessageType {
  /**
   * @generated from enum value: UNKNOWN_MESSAGE_TYPE = 0;
   */
  UNKNOWN_MESSAGE_TYPE = 0,

  /**
   * @generated from enum value: PROMOTIONAL_MESSAGE = 1;
   */
  PROMOTIONAL_MESSAGE = 1,

  /**
   * @generated from enum value: TRANSACTIONAL_MESSAGE = 2;
   */
  TRANSACTIONAL_MESSAGE = 2,

  /**
   * @generated from enum value: OTP_MESSAGE = 3;
   */
  OTP_MESSAGE = 3,
}

/**
 * Describes the enum com.stablemoney.api.broking.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 0);

/**
 * @generated from enum com.stablemoney.api.broking.ContentType
 */
export enum ContentType {
  /**
   * @generated from enum value: UNKNOWN_CONTENT_TYPE = 0;
   */
  UNKNOWN_CONTENT_TYPE = 0,

  /**
   * @generated from enum value: TEXT = 1;
   */
  TEXT = 1,

  /**
   * @generated from enum value: UNICODE = 2;
   */
  UNICODE = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.ContentType.
 */
export const ContentTypeSchema: GenEnum<ContentType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 1);

/**
 * @generated from enum com.stablemoney.api.broking.OptinStatus
 */
export enum OptinStatus {
  /**
   * @generated from enum value: UNKNOWN_OPTIN_STATUS = 0;
   */
  UNKNOWN_OPTIN_STATUS = 0,

  /**
   * @generated from enum value: OPTED_IN = 1;
   */
  OPTED_IN = 1,

  /**
   * @generated from enum value: OPTED_OUT = 2;
   */
  OPTED_OUT = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.OptinStatus.
 */
export const OptinStatusSchema: GenEnum<OptinStatus> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 2);

/**
 * @generated from enum com.stablemoney.api.broking.WhatsappProviderType
 */
export enum WhatsappProviderType {
  /**
   * @generated from enum value: UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0;
   */
  UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0,

  /**
   * @generated from enum value: GUPSHUP = 1;
   */
  GUPSHUP = 1,
}

/**
 * Describes the enum com.stablemoney.api.broking.WhatsappProviderType.
 */
export const WhatsappProviderTypeSchema: GenEnum<WhatsappProviderType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 3);

/**
 * @generated from enum com.stablemoney.api.broking.AppConfigType
 */
export enum AppConfigType {
  /**
   * @generated from enum value: APP_CONFIG_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: BANK_VERIFICATION_RPD = 1;
   */
  BANK_VERIFICATION_RPD = 1,

  /**
   * @generated from enum value: BANK_VERIFICATION_PD = 2;
   */
  BANK_VERIFICATION_PD = 2,

  /**
   * @generated from enum value: BASIC_DETAILS_QUESTIONS = 3;
   */
  BASIC_DETAILS_QUESTIONS = 3,

  /**
   * @generated from enum value: PAN_CVL_CHECK = 4;
   */
  PAN_CVL_CHECK = 4,

  /**
   * @generated from enum value: RPD_SUPPORTED_UPI_APPS = 5;
   */
  RPD_SUPPORTED_UPI_APPS = 5,

  /**
   * @generated from enum value: CREDIT_REPORT = 6;
   */
  CREDIT_REPORT = 6,

  /**
   * @generated from enum value: MOBILE_RESEND = 7;
   */
  MOBILE_RESEND = 7,

  /**
   * @generated from enum value: MOBILE_ATTEMPT = 8;
   */
  MOBILE_ATTEMPT = 8,

  /**
   * @generated from enum value: MOBILE_OTP_LENGTH = 9;
   */
  MOBILE_OTP_LENGTH = 9,

  /**
   * @generated from enum value: EMAIL_OTP_LENGTH = 10;
   */
  EMAIL_OTP_LENGTH = 10,

  /**
   * @generated from enum value: EMAIL_RESEND = 11;
   */
  EMAIL_RESEND = 11,

  /**
   * @generated from enum value: EMAIL_ATTEMPT = 12;
   */
  EMAIL_ATTEMPT = 12,

  /**
   * @generated from enum value: ANDROID_APP_VERSION = 13;
   */
  ANDROID_APP_VERSION = 13,

  /**
   * @generated from enum value: IOS_APP_VERSION = 14;
   */
  IOS_APP_VERSION = 14,

  /**
   * @generated from enum value: WEB_APP_VERSION = 15;
   */
  WEB_APP_VERSION = 15,

  /**
   * @generated from enum value: MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16;
   */
  MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16,

  /**
   * @generated from enum value: EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17;
   */
  EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17,

  /**
   * @generated from enum value: MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18;
   */
  MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18,

  /**
   * @generated from enum value: EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19;
   */
  EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19,

  /**
   * @generated from enum value: EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20;
   */
  EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20,

  /**
   * @generated from enum value: EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21;
   */
  EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21,

  /**
   * @generated from enum value: MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22;
   */
  MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22,

  /**
   * @generated from enum value: MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23;
   */
  MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23,

  /**
   * @generated from enum value: MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24;
   */
  MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24,

  /**
   * @generated from enum value: EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25;
   */
  EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25,

  /**
   * @generated from enum value: MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26;
   */
  MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26,

  /**
   * @generated from enum value: TOKEN_VALIDITY_IN_HOURS = 27;
   */
  TOKEN_VALIDITY_IN_HOURS = 27,

  /**
   * @generated from enum value: REFRESH_TOKEN_VALIDITY_IN_HOURS = 28;
   */
  REFRESH_TOKEN_VALIDITY_IN_HOURS = 28,
}

/**
 * Describes the enum com.stablemoney.api.broking.AppConfigType.
 */
export const AppConfigTypeSchema: GenEnum<AppConfigType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 4);

/**
 * @generated from enum com.stablemoney.api.broking.AppConfigValueType
 */
export enum AppConfigValueType {
  /**
   * @generated from enum value: APP_CONFIG_VALUE_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_VALUE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: TEXT_TYPE = 1;
   */
  TEXT_TYPE = 1,

  /**
   * @generated from enum value: BOOLEAN_TYPE = 2;
   */
  BOOLEAN_TYPE = 2,

  /**
   * @generated from enum value: STRING_TYPE = 3;
   */
  STRING_TYPE = 3,

  /**
   * @generated from enum value: JSON_TYPE = 4;
   */
  JSON_TYPE = 4,

  /**
   * @generated from enum value: INTEGER_TYPE = 5;
   */
  INTEGER_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.broking.AppConfigValueType.
 */
export const AppConfigValueTypeSchema: GenEnum<AppConfigValueType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 5);

/**
 * @generated from enum com.stablemoney.api.broking.UserConfigType
 */
export enum UserConfigType {
  /**
   * @generated from enum value: UNKNOWN_USER_CONFIG_TYPE = 0;
   */
  UNKNOWN_USER_CONFIG_TYPE = 0,

  /**
   * @generated from enum value: BANNER_CONFIG = 1;
   */
  BANNER_CONFIG = 1,
}

/**
 * Describes the enum com.stablemoney.api.broking.UserConfigType.
 */
export const UserConfigTypeSchema: GenEnum<UserConfigType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Common, 6);
