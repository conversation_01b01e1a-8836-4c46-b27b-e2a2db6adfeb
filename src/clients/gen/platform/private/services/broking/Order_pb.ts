// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/broking/Order.proto (package com.stablemoney.api.broking, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import { file_private_services_broking_Common } from "./Common_pb.js";
import type { InterestPaymentSchedule } from "./BondDetails_pb.js";
import { file_private_services_broking_BondDetails } from "./BondDetails_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/broking/Order.proto.
 */
export const file_private_services_broking_Order: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_private_services_broking_Common,
      file_private_services_broking_BondDetails,
    ],
  );

/**
 * @generated from message com.stablemoney.api.broking.PlaceOrderRequest
 */
export type PlaceOrderRequest =
  Message<"com.stablemoney.api.broking.PlaceOrderRequest"> & {
    /**
     * @generated from field: string bond_offering_details_id = 1;
     */
    bondOfferingDetailsId: string;

    /**
     * @generated from field: int32 quantity = 2;
     */
    quantity: number;
  };

/**
 * Describes the message com.stablemoney.api.broking.PlaceOrderRequest.
 * Use `create(PlaceOrderRequestSchema)` to create a new message.
 */
export const PlaceOrderRequestSchema: GenMessage<PlaceOrderRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 0);

/**
 * @generated from message com.stablemoney.api.broking.StatusNode
 */
export type StatusNode = Message<"com.stablemoney.api.broking.StatusNode"> & {
  /**
   * @generated from field: bool status = 1;
   */
  status: boolean;

  /**
   * @generated from field: string date_time = 2;
   */
  dateTime: string;

  /**
   * @generated from field: optional string description = 3;
   */
  description?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.StatusNode.
 * Use `create(StatusNodeSchema)` to create a new message.
 */
export const StatusNodeSchema: GenMessage<StatusNode> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 1);

/**
 * @generated from message com.stablemoney.api.broking.OrderData
 */
export type OrderData = Message<"com.stablemoney.api.broking.OrderData"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string bond_name = 2;
   */
  bondName: string;

  /**
   * @generated from field: string bond_issuer_logo = 3;
   */
  bondIssuerLogo: string;

  /**
   * @generated from field: int32 quantity = 4;
   */
  quantity: number;

  /**
   * @generated from field: double total_consideration = 5;
   */
  totalConsideration: number;

  /**
   * @generated from field: string status = 6;
   */
  status: string;

  /**
   * @generated from field: string order_type = 7;
   */
  orderType: string;

  /**
   * @generated from field: string bond_offering_id = 8;
   */
  bondOfferingId: string;

  /**
   * @generated from field: double ytm = 9;
   */
  ytm: number;

  /**
   * @generated from field: string bond_issuer_name = 10;
   */
  bondIssuerName: string;

  /**
   * @generated from field: string tenure = 11;
   */
  tenure: string;

  /**
   * @generated from field: string expected_returns = 12;
   */
  expectedReturns: string;

  /**
   * @generated from field: bool is_order_settled = 13;
   */
  isOrderSettled: boolean;

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode order_placed = 14;
   */
  orderPlaced?: StatusNode;

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode payment = 15;
   */
  payment?: StatusNode;

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode settlement = 16;
   */
  settlement?: StatusNode;

  /**
   * @generated from field: string order_receipt_url = 17;
   */
  orderReceiptUrl: string;

  /**
   * @generated from field: string deal_sheet_url = 18;
   */
  dealSheetUrl: string;

  /**
   * @generated from field: string payment_url = 19;
   */
  paymentUrl: string;

  /**
   * @generated from field: string order_number = 20;
   */
  orderNumber: string;

  /**
   * @generated from field: string created_at = 21;
   */
  createdAt: string;

  /**
   * @generated from field: repeated com.stablemoney.api.broking.InterestPaymentSchedule repayment_schedule = 22;
   */
  repaymentSchedule: InterestPaymentSchedule[];

  /**
   * @generated from field: string payment_gateway = 23;
   */
  paymentGateway: string;

  /**
   * @generated from field: com.stablemoney.api.broking.StatusNode trade_placed = 24;
   */
  tradePlaced?: StatusNode;

  /**
   * dd-mm-yyyy
   *
   * @generated from field: string maturity_date = 25;
   */
  maturityDate: string;

  /**
   * @generated from field: string heading = 26;
   */
  heading: string;

  /**
   * @generated from field: bool is_bond_investable = 27;
   */
  isBondInvestable: boolean;

  /**
   * dd-mm-yyyy
   *
   * @generated from field: string settlement_date = 28;
   */
  settlementDate: string;

  /**
   * @generated from field: optional string bond_issuer_id = 29;
   */
  bondIssuerId?: string;

  /**
   * @generated from field: optional string bond_issuer_color = 30;
   */
  bondIssuerColor?: string;
};

/**
 * Describes the message com.stablemoney.api.broking.OrderData.
 * Use `create(OrderDataSchema)` to create a new message.
 */
export const OrderDataSchema: GenMessage<OrderData> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 2);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentSummary
 */
export type InvestmentSummary =
  Message<"com.stablemoney.api.broking.InvestmentSummary"> & {
    /**
     * @generated from field: com.stablemoney.api.broking.OrderData order_data = 1;
     */
    orderData?: OrderData;

    /**
     * @generated from field: com.stablemoney.api.broking.InterestPayoutDetails interest_payout_details = 2;
     */
    interestPayoutDetails?: InterestPayoutDetails;
  };

/**
 * Describes the message com.stablemoney.api.broking.InvestmentSummary.
 * Use `create(InvestmentSummarySchema)` to create a new message.
 */
export const InvestmentSummarySchema: GenMessage<InvestmentSummary> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 3);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentSummariesResponse
 */
export type InvestmentSummariesResponse =
  Message<"com.stablemoney.api.broking.InvestmentSummariesResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.broking.InvestmentSummary investment_summaries = 1;
     */
    investmentSummaries: InvestmentSummary[];
  };

/**
 * Describes the message com.stablemoney.api.broking.InvestmentSummariesResponse.
 * Use `create(InvestmentSummariesResponseSchema)` to create a new message.
 */
export const InvestmentSummariesResponseSchema: GenMessage<InvestmentSummariesResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 4);

/**
 * @generated from message com.stablemoney.api.broking.PlaceOrderResponse
 */
export type PlaceOrderResponse =
  Message<"com.stablemoney.api.broking.PlaceOrderResponse"> & {
    /**
     * @generated from field: string order_id = 1;
     */
    orderId: string;

    /**
     * @generated from field: com.stablemoney.api.broking.OrderData order = 2;
     */
    order?: OrderData;

    /**
     * @generated from field: string payment_gateway = 4;
     */
    paymentGateway: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.PlaceOrderResponse.
 * Use `create(PlaceOrderResponseSchema)` to create a new message.
 */
export const PlaceOrderResponseSchema: GenMessage<PlaceOrderResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 5);

/**
 * @generated from message com.stablemoney.api.broking.PendingOrderItem
 */
export type PendingOrderItem =
  Message<"com.stablemoney.api.broking.PendingOrderItem"> & {
    /**
     * @generated from field: string order_id = 1;
     */
    orderId: string;

    /**
     * @generated from field: double amount = 2;
     */
    amount: number;

    /**
     * @generated from field: int32 quantity = 3;
     */
    quantity: number;

    /**
     * @generated from field: string created_at = 4;
     */
    createdAt: string;

    /**
     * @generated from field: string expire_by = 5;
     */
    expireBy: string;

    /**
     * @generated from field: string bond_id = 6;
     */
    bondId: string;

    /**
     * @generated from field: string bond_logo = 7;
     */
    bondLogo: string;

    /**
     * @generated from field: string bond_name = 8;
     */
    bondName: string;

    /**
     * @generated from field: string payment_gateway = 9;
     */
    paymentGateway: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.PendingOrderItem.
 * Use `create(PendingOrderItemSchema)` to create a new message.
 */
export const PendingOrderItemSchema: GenMessage<PendingOrderItem> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 6);

/**
 * @generated from message com.stablemoney.api.broking.PendingOrdersResponse
 */
export type PendingOrdersResponse =
  Message<"com.stablemoney.api.broking.PendingOrdersResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.broking.PendingOrderItem items = 1;
     */
    items: PendingOrderItem[];
  };

/**
 * Describes the message com.stablemoney.api.broking.PendingOrdersResponse.
 * Use `create(PendingOrdersResponseSchema)` to create a new message.
 */
export const PendingOrdersResponseSchema: GenMessage<PendingOrdersResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 7);

/**
 * @generated from message com.stablemoney.api.broking.ProcessPaymentRequest
 */
export type ProcessPaymentRequest =
  Message<"com.stablemoney.api.broking.ProcessPaymentRequest"> & {
    /**
     * @generated from field: string order_id = 1;
     */
    orderId: string;

    /**
     * @generated from field: optional com.stablemoney.api.broking.PaymentType payment_type = 2;
     */
    paymentType?: PaymentType;

    /**
     * @generated from field: optional string upi_id = 3;
     */
    upiId?: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.ProcessPaymentRequest.
 * Use `create(ProcessPaymentRequestSchema)` to create a new message.
 */
export const ProcessPaymentRequestSchema: GenMessage<ProcessPaymentRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 8);

/**
 * @generated from message com.stablemoney.api.broking.ProcessPaymentResponse
 */
export type ProcessPaymentResponse =
  Message<"com.stablemoney.api.broking.ProcessPaymentResponse"> & {
    /**
     * @generated from field: string status = 1;
     */
    status: string;

    /**
     * @generated from field: string payment_url = 2;
     */
    paymentUrl: string;

    /**
     * @generated from field: string upi_id = 3;
     */
    upiId: string;

    /**
     * @generated from field: string order_id = 4;
     */
    orderId: string;

    /**
     * @generated from field: double payable_amount = 5;
     */
    payableAmount: number;

    /**
     * @generated from field: string payment_session_id = 6;
     */
    paymentSessionId: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.ProcessPaymentResponse.
 * Use `create(ProcessPaymentResponseSchema)` to create a new message.
 */
export const ProcessPaymentResponseSchema: GenMessage<ProcessPaymentResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 9);

/**
 * @generated from message com.stablemoney.api.broking.PaymentStatusResponse
 */
export type PaymentStatusResponse =
  Message<"com.stablemoney.api.broking.PaymentStatusResponse"> & {
    /**
     * @generated from field: string order_id = 1;
     */
    orderId: string;

    /**
     * @generated from field: string status = 2;
     */
    status: string;

    /**
     * @generated from field: string bond_name = 3;
     */
    bondName: string;

    /**
     * @generated from field: int32 quantity = 4;
     */
    quantity: number;

    /**
     * @generated from field: double payable_amount = 5;
     */
    payableAmount: number;

    /**
     * @generated from field: string payment_time = 6;
     */
    paymentTime: string;

    /**
     * @generated from field: string settlement_date = 7;
     */
    settlementDate: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.PaymentStatusResponse.
 * Use `create(PaymentStatusResponseSchema)` to create a new message.
 */
export const PaymentStatusResponseSchema: GenMessage<PaymentStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 10);

/**
 * @generated from message com.stablemoney.api.broking.OrdersResponse
 */
export type OrdersResponse =
  Message<"com.stablemoney.api.broking.OrdersResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.broking.OrderData orders = 1;
     */
    orders: OrderData[];
  };

/**
 * Describes the message com.stablemoney.api.broking.OrdersResponse.
 * Use `create(OrdersResponseSchema)` to create a new message.
 */
export const OrdersResponseSchema: GenMessage<OrdersResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 11);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentDetailsResponse
 */
export type InvestmentDetailsResponse =
  Message<"com.stablemoney.api.broking.InvestmentDetailsResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.broking.OrderData orderData = 1;
     */
    orderData?: OrderData;

    /**
     * @generated from field: optional com.stablemoney.api.broking.InvestmentNomineeDetails nominee_details = 2;
     */
    nomineeDetails?: InvestmentNomineeDetails;

    /**
     * @generated from field: optional com.stablemoney.api.broking.WithdrawalBankAccountDetails withdrawal_bank_account_details = 3;
     */
    withdrawalBankAccountDetails?: WithdrawalBankAccountDetails;

    /**
     * @generated from field: optional com.stablemoney.api.broking.InterestPayoutDetails interest_payout_details = 4;
     */
    interestPayoutDetails?: InterestPayoutDetails;

    /**
     * @generated from field: optional com.stablemoney.api.broking.DematAccountDetails demat_account_details = 5;
     */
    dematAccountDetails?: DematAccountDetails;
  };

/**
 * Describes the message com.stablemoney.api.broking.InvestmentDetailsResponse.
 * Use `create(InvestmentDetailsResponseSchema)` to create a new message.
 */
export const InvestmentDetailsResponseSchema: GenMessage<InvestmentDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 12);

/**
 * @generated from message com.stablemoney.api.broking.BondsNetworthSummaryResponse
 */
export type BondsNetworthSummaryResponse =
  Message<"com.stablemoney.api.broking.BondsNetworthSummaryResponse"> & {
    /**
     * @generated from field: double total_investment = 1;
     */
    totalInvestment: number;

    /**
     * @generated from field: double current_gains = 2;
     */
    currentGains: number;
  };

/**
 * Describes the message com.stablemoney.api.broking.BondsNetworthSummaryResponse.
 * Use `create(BondsNetworthSummaryResponseSchema)` to create a new message.
 */
export const BondsNetworthSummaryResponseSchema: GenMessage<BondsNetworthSummaryResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 13);

/**
 * @generated from message com.stablemoney.api.broking.InvestmentNomineeDetails
 */
export type InvestmentNomineeDetails =
  Message<"com.stablemoney.api.broking.InvestmentNomineeDetails"> & {
    /**
     * @generated from field: string nominee_name = 1;
     */
    nomineeName: string;

    /**
     * @generated from field: string nominee_relation = 2;
     */
    nomineeRelation: string;

    /**
     * @generated from field: string heading = 3;
     */
    heading: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.InvestmentNomineeDetails.
 * Use `create(InvestmentNomineeDetailsSchema)` to create a new message.
 */
export const InvestmentNomineeDetailsSchema: GenMessage<InvestmentNomineeDetails> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 14);

/**
 * @generated from message com.stablemoney.api.broking.WithdrawalBankAccountDetails
 */
export type WithdrawalBankAccountDetails =
  Message<"com.stablemoney.api.broking.WithdrawalBankAccountDetails"> & {
    /**
     * @generated from field: string bank_name = 1;
     */
    bankName: string;

    /**
     * @generated from field: string account_number = 2;
     */
    accountNumber: string;

    /**
     * @generated from field: string ifsc_code = 3;
     */
    ifscCode: string;

    /**
     * @generated from field: string heading = 4;
     */
    heading: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.WithdrawalBankAccountDetails.
 * Use `create(WithdrawalBankAccountDetailsSchema)` to create a new message.
 */
export const WithdrawalBankAccountDetailsSchema: GenMessage<WithdrawalBankAccountDetails> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 15);

/**
 * @generated from message com.stablemoney.api.broking.InterestPayoutDetails
 */
export type InterestPayoutDetails =
  Message<"com.stablemoney.api.broking.InterestPayoutDetails"> & {
    /**
     * @generated from field: double realised_gains = 1;
     */
    realisedGains: number;

    /**
     * @generated from field: double unrealised_gains = 2;
     */
    unrealisedGains: number;

    /**
     * dd-mm-yyyy
     *
     * @generated from field: string next_payout_date = 3;
     */
    nextPayoutDate: string;

    /**
     * @generated from field: string heading = 4;
     */
    heading: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.InterestPayoutDetails.
 * Use `create(InterestPayoutDetailsSchema)` to create a new message.
 */
export const InterestPayoutDetailsSchema: GenMessage<InterestPayoutDetails> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 16);

/**
 * @generated from message com.stablemoney.api.broking.DematAccountDetails
 */
export type DematAccountDetails =
  Message<"com.stablemoney.api.broking.DematAccountDetails"> & {
    /**
     * @generated from field: string demat_number = 1;
     */
    dematNumber: string;

    /**
     * @generated from field: string demat_provider = 2;
     */
    dematProvider: string;

    /**
     * @generated from field: string heading = 3;
     */
    heading: string;
  };

/**
 * Describes the message com.stablemoney.api.broking.DematAccountDetails.
 * Use `create(DematAccountDetailsSchema)` to create a new message.
 */
export const DematAccountDetailsSchema: GenMessage<DematAccountDetails> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Order, 17);

/**
 * @generated from enum com.stablemoney.api.broking.PaymentType
 */
export enum PaymentType {
  /**
   * @generated from enum value: UNKNOWN_PAYMENT_TYPE = 0;
   */
  UNKNOWN_PAYMENT_TYPE = 0,

  /**
   * @generated from enum value: UPI = 1;
   */
  UPI = 1,

  /**
   * @generated from enum value: NET_BANKING = 2;
   */
  NET_BANKING = 2,
}

/**
 * Describes the enum com.stablemoney.api.broking.PaymentType.
 */
export const PaymentTypeSchema: GenEnum<PaymentType> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Order, 0);
