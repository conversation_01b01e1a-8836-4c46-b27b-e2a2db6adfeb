// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/broking/Profile.proto (package com.stablemoney.api.broking, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/broking/Profile.proto.
 */
export const file_private_services_broking_Profile: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiZwcml2YXRlL3NlcnZpY2VzL2Jyb2tpbmcvUHJvZmlsZS5wcm90bxIbY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nImYKGlVzZXJMaWZldGltZVN0YXR1c1Jlc3BvbnNlEkgKD2xpZmV0aW1lX3N0YXR1cxgBIAEoDjIvLmNvbS5zdGFibGVtb25leS5hcGkuYnJva2luZy5Vc2VyTGlmZXRpbWVTdGF0dXMqxgEKElVzZXJMaWZldGltZVN0YXR1cxIbChdMSUZFVElNRV9TVEFUVVNfVU5LTk9XThAAEhIKDk5PVF9SRUdJU1RFUkVEEAISDAoITkVXX1VTRVIQBBIRCg1LWUNfSU5JVElBVEVEEAYSEAoMTUlOX0tZQ19ET05FEAcSEQoNS1lDX0NPTVBMRVRFRBAIEhMKD09SREVSX0lOSVRJQVRFRBAKEhAKDFBBWU1FTlRfRE9ORRAMEhIKDkJPTkRfUFVSQ0hBU0VEEBBCHwobY29tLnN0YWJsZW1vbmV5LmFwaS5icm9raW5nUAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.broking.UserLifetimeStatusResponse
 */
export type UserLifetimeStatusResponse =
  Message<"com.stablemoney.api.broking.UserLifetimeStatusResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.broking.UserLifetimeStatus lifetime_status = 1;
     */
    lifetimeStatus: UserLifetimeStatus;
  };

/**
 * Describes the message com.stablemoney.api.broking.UserLifetimeStatusResponse.
 * Use `create(UserLifetimeStatusResponseSchema)` to create a new message.
 */
export const UserLifetimeStatusResponseSchema: GenMessage<UserLifetimeStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_broking_Profile, 0);

/**
 * @generated from enum com.stablemoney.api.broking.UserLifetimeStatus
 */
export enum UserLifetimeStatus {
  /**
   * @generated from enum value: LIFETIME_STATUS_UNKNOWN = 0;
   */
  LIFETIME_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: NOT_REGISTERED = 2;
   */
  NOT_REGISTERED = 2,

  /**
   * @generated from enum value: NEW_USER = 4;
   */
  NEW_USER = 4,

  /**
   * @generated from enum value: KYC_INITIATED = 6;
   */
  KYC_INITIATED = 6,

  /**
   * @generated from enum value: MIN_KYC_DONE = 7;
   */
  MIN_KYC_DONE = 7,

  /**
   * @generated from enum value: KYC_COMPLETED = 8;
   */
  KYC_COMPLETED = 8,

  /**
   * @generated from enum value: ORDER_INITIATED = 10;
   */
  ORDER_INITIATED = 10,

  /**
   * @generated from enum value: PAYMENT_DONE = 12;
   */
  PAYMENT_DONE = 12,

  /**
   * @generated from enum value: BOND_PURCHASED = 16;
   */
  BOND_PURCHASED = 16,
}

/**
 * Describes the enum com.stablemoney.api.broking.UserLifetimeStatus.
 */
export const UserLifetimeStatusSchema: GenEnum<UserLifetimeStatus> =
  /*@__PURE__*/
  enumDesc(file_private_services_broking_Profile, 0);
