// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/business/CollectionItem.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  enumDesc,
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { RedirectDeeplink } from "../../../public/models/business/BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "../../../public/models/business/BusinessCommon_pb.js";
import type { Empty } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import { file_public_models_business_Collection } from "../../../public/models/business/Collection_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/business/CollectionItem.proto.
 */
export const file_private_services_business_CollectionItem: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_BusinessCommon,
      file_google_protobuf_empty,
      file_public_models_business_Collection,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.AddCollectionGrpcRequest
 */
export type AddCollectionGrpcRequest =
  Message<"com.stablemoney.api.identity.AddCollectionGrpcRequest"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: optional string short_title = 2;
     */
    shortTitle?: string;

    /**
     * @generated from field: bool is_active = 3;
     */
    isActive: boolean;

    /**
     * @generated from field: optional string description = 4;
     */
    description?: string;

    /**
     * @generated from field: optional string short_description = 5;
     */
    shortDescription?: string;

    /**
     * @generated from field: int32 priority = 6;
     */
    priority: number;

    /**
     * @generated from field: optional string icon_url = 7;
     */
    iconUrl?: string;

    /**
     * @generated from field: string collection_data_strategy_name = 8;
     */
    collectionDataStrategyName: string;

    /**
     * @generated from field: optional string created_by = 9;
     */
    createdBy?: string;

    /**
     * @generated from field: optional bool show_on_home_page = 10;
     */
    showOnHomePage?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddCollectionGrpcRequest.
 * Use `create(AddCollectionGrpcRequestSchema)` to create a new message.
 */
export const AddCollectionGrpcRequestSchema: GenMessage<AddCollectionGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 0);

/**
 * @generated from message com.stablemoney.api.identity.AddCollectionGrpcResponse
 */
export type AddCollectionGrpcResponse =
  Message<"com.stablemoney.api.identity.AddCollectionGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddCollectionGrpcResponse.
 * Use `create(AddCollectionGrpcResponseSchema)` to create a new message.
 */
export const AddCollectionGrpcResponseSchema: GenMessage<AddCollectionGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 1);

/**
 * @generated from message com.stablemoney.api.identity.UpdateCollectionGrpcRequest
 */
export type UpdateCollectionGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateCollectionGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: optional string title = 2;
     */
    title?: string;

    /**
     * @generated from field: optional string description = 3;
     */
    description?: string;

    /**
     * @generated from field: optional string short_title = 4;
     */
    shortTitle?: string;

    /**
     * @generated from field: optional string short_description = 5;
     */
    shortDescription?: string;

    /**
     * @generated from field: optional int32 priority = 6;
     */
    priority?: number;

    /**
     * @generated from field: optional string icon_url = 7;
     */
    iconUrl?: string;

    /**
     * @generated from field: optional string collection_data_strategy_name = 8;
     */
    collectionDataStrategyName?: string;

    /**
     * @generated from field: optional bool is_active = 9;
     */
    isActive?: boolean;

    /**
     * @generated from field: optional string updated_by = 10;
     */
    updatedBy?: string;

    /**
     * @generated from field: optional bool show_on_home_page = 11;
     */
    showOnHomePage?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateCollectionGrpcRequest.
 * Use `create(UpdateCollectionGrpcRequestSchema)` to create a new message.
 */
export const UpdateCollectionGrpcRequestSchema: GenMessage<UpdateCollectionGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 2);

/**
 * @generated from message com.stablemoney.api.identity.UpdateCollectionGrpcResponse
 */
export type UpdateCollectionGrpcResponse =
  Message<"com.stablemoney.api.identity.UpdateCollectionGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateCollectionGrpcResponse.
 * Use `create(UpdateCollectionGrpcResponseSchema)` to create a new message.
 */
export const UpdateCollectionGrpcResponseSchema: GenMessage<UpdateCollectionGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 3);

/**
 * @generated from message com.stablemoney.api.identity.AddCollectionItemsGrpcRequest
 */
export type AddCollectionItemsGrpcRequest =
  Message<"com.stablemoney.api.identity.AddCollectionItemsGrpcRequest"> & {
    /**
     * @generated from field: string collection_id = 1;
     */
    collectionId: string;

    /**
     * @generated from field: string bank_id = 2;
     */
    bankId: string;

    /**
     * @generated from field: optional string description = 3;
     */
    description?: string;

    /**
     * @generated from field: int32 priority = 4;
     */
    priority: number;

    /**
     * @generated from field: bool is_active = 5;
     */
    isActive: boolean;

    /**
     * @generated from field: optional string created_by = 6;
     */
    createdBy?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.RedirectDeeplink deeplink = 7;
     */
    deeplink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddCollectionItemsGrpcRequest.
 * Use `create(AddCollectionItemsGrpcRequestSchema)` to create a new message.
 */
export const AddCollectionItemsGrpcRequestSchema: GenMessage<AddCollectionItemsGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 4);

/**
 * @generated from message com.stablemoney.api.identity.AddMultipleCollectionItemsGrpcRequest
 */
export type AddMultipleCollectionItemsGrpcRequest =
  Message<"com.stablemoney.api.identity.AddMultipleCollectionItemsGrpcRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.AddCollectionItemsGrpcRequest collection_item = 1;
     */
    collectionItem: AddCollectionItemsGrpcRequest[];
  };

/**
 * Describes the message com.stablemoney.api.identity.AddMultipleCollectionItemsGrpcRequest.
 * Use `create(AddMultipleCollectionItemsGrpcRequestSchema)` to create a new message.
 */
export const AddMultipleCollectionItemsGrpcRequestSchema: GenMessage<AddMultipleCollectionItemsGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 5);

/**
 * @generated from message com.stablemoney.api.identity.AddMultipleCollectionItemsGrpcResponse
 */
export type AddMultipleCollectionItemsGrpcResponse =
  Message<"com.stablemoney.api.identity.AddMultipleCollectionItemsGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddMultipleCollectionItemsGrpcResponse.
 * Use `create(AddMultipleCollectionItemsGrpcResponseSchema)` to create a new message.
 */
export const AddMultipleCollectionItemsGrpcResponseSchema: GenMessage<AddMultipleCollectionItemsGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 6);

/**
 * @generated from message com.stablemoney.api.identity.AddCollectionItemsGrpcResponse
 */
export type AddCollectionItemsGrpcResponse =
  Message<"com.stablemoney.api.identity.AddCollectionItemsGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddCollectionItemsGrpcResponse.
 * Use `create(AddCollectionItemsGrpcResponseSchema)` to create a new message.
 */
export const AddCollectionItemsGrpcResponseSchema: GenMessage<AddCollectionItemsGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 7);

/**
 * @generated from message com.stablemoney.api.identity.UpdateCollectionItemsGrpcRequest
 */
export type UpdateCollectionItemsGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateCollectionItemsGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: optional string description = 2;
     */
    description?: string;

    /**
     * @generated from field: optional int32 priority = 3;
     */
    priority?: number;

    /**
     * @generated from field: optional bool is_active = 4;
     */
    isActive?: boolean;

    /**
     * @generated from field: optional string updated_by = 5;
     */
    updatedBy?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.RedirectDeeplink deeplink = 6;
     */
    deeplink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateCollectionItemsGrpcRequest.
 * Use `create(UpdateCollectionItemsGrpcRequestSchema)` to create a new message.
 */
export const UpdateCollectionItemsGrpcRequestSchema: GenMessage<UpdateCollectionItemsGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 8);

/**
 * @generated from message com.stablemoney.api.identity.UpdateCollectionItemsGrpcResponse
 */
export type UpdateCollectionItemsGrpcResponse =
  Message<"com.stablemoney.api.identity.UpdateCollectionItemsGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateCollectionItemsGrpcResponse.
 * Use `create(UpdateCollectionItemsGrpcResponseSchema)` to create a new message.
 */
export const UpdateCollectionItemsGrpcResponseSchema: GenMessage<UpdateCollectionItemsGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 9);

/**
 * @generated from message com.stablemoney.api.identity.SyncCollectionGrpcRequest
 */
export type SyncCollectionGrpcRequest =
  Message<"com.stablemoney.api.identity.SyncCollectionGrpcRequest"> & {
    /**
     * @generated from field: string collection_id = 1;
     */
    collectionId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SyncCollectionGrpcRequest.
 * Use `create(SyncCollectionGrpcRequestSchema)` to create a new message.
 */
export const SyncCollectionGrpcRequestSchema: GenMessage<SyncCollectionGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 10);

/**
 * @generated from message com.stablemoney.api.identity.SyncAllCollectionGrpcRequest
 */
export type SyncAllCollectionGrpcRequest =
  Message<"com.stablemoney.api.identity.SyncAllCollectionGrpcRequest"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.SyncAllCollectionGrpcRequest.
 * Use `create(SyncAllCollectionGrpcRequestSchema)` to create a new message.
 */
export const SyncAllCollectionGrpcRequestSchema: GenMessage<SyncAllCollectionGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 11);

/**
 * @generated from message com.stablemoney.api.identity.SyncCollectionGrpcResponse
 */
export type SyncCollectionGrpcResponse =
  Message<"com.stablemoney.api.identity.SyncCollectionGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.SyncCollectionGrpcResponse.
 * Use `create(SyncCollectionGrpcResponseSchema)` to create a new message.
 */
export const SyncCollectionGrpcResponseSchema: GenMessage<SyncCollectionGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 12);

/**
 * @generated from message com.stablemoney.api.identity.SyncAllCollectionGrpcResponse
 */
export type SyncAllCollectionGrpcResponse =
  Message<"com.stablemoney.api.identity.SyncAllCollectionGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.SyncAllCollectionGrpcResponse.
 * Use `create(SyncAllCollectionGrpcResponseSchema)` to create a new message.
 */
export const SyncAllCollectionGrpcResponseSchema: GenMessage<SyncAllCollectionGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 13);

/**
 * @generated from message com.stablemoney.api.identity.AggregatedCollectionConfig
 */
export type AggregatedCollectionConfig =
  Message<"com.stablemoney.api.identity.AggregatedCollectionConfig"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.AggregatedCollectionType aggregated_collection_type = 1;
     */
    aggregatedCollectionType: AggregatedCollectionType;

    /**
     * @generated from oneof com.stablemoney.api.identity.AggregatedCollectionConfig.collection_config
     */
    collectionConfig:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.BankCardConfig bank_card_config = 2;
           */
          value: BankCardConfig;
          case: "bankCardConfig";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.AggregatedCollectionConfig.
 * Use `create(AggregatedCollectionConfigSchema)` to create a new message.
 */
export const AggregatedCollectionConfigSchema: GenMessage<AggregatedCollectionConfig> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 14);

/**
 * @generated from message com.stablemoney.api.identity.BankCardConfig
 */
export type BankCardConfig =
  Message<"com.stablemoney.api.identity.BankCardConfig"> & {
    /**
     * @generated from field: string primary_rate_simple_strategy_name = 1;
     */
    primaryRateSimpleStrategyName: string;

    /**
     * @generated from field: repeated string secondary_rates_simple_strategy_names = 2;
     */
    secondaryRatesSimpleStrategyNames: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankCardConfig.
 * Use `create(BankCardConfigSchema)` to create a new message.
 */
export const BankCardConfigSchema: GenMessage<BankCardConfig> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_CollectionItem, 15);

/**
 * @generated from enum com.stablemoney.api.identity.AggregatedCollectionType
 */
export enum AggregatedCollectionType {
  /**
   * @generated from enum value: UNKNOWN_AGGREGATED_COLLECTION_TYPE = 0;
   */
  UNKNOWN_AGGREGATED_COLLECTION_TYPE = 0,

  /**
   * @generated from enum value: BANK_CARD_AGGREGATED_COLLECTION_TYPE = 1;
   */
  BANK_CARD_AGGREGATED_COLLECTION_TYPE = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.AggregatedCollectionType.
 */
export const AggregatedCollectionTypeSchema: GenEnum<AggregatedCollectionType> =
  /*@__PURE__*/
  enumDesc(file_private_services_business_CollectionItem, 0);

/**
 * @generated from service com.stablemoney.api.identity.CollectionService
 */
export const CollectionService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.CollectionService.addCollectionData
   */
  addCollectionData: {
    methodKind: "unary";
    input: typeof AddCollectionGrpcRequestSchema;
    output: typeof AddCollectionGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.CollectionService.updateCollectionData
   */
  updateCollectionData: {
    methodKind: "unary";
    input: typeof UpdateCollectionGrpcRequestSchema;
    output: typeof UpdateCollectionGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.CollectionService.addCollectionItemsData
   */
  addCollectionItemsData: {
    methodKind: "unary";
    input: typeof AddCollectionItemsGrpcRequestSchema;
    output: typeof AddCollectionItemsGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.CollectionService.addMultipleCollectionItemsData
   */
  addMultipleCollectionItemsData: {
    methodKind: "unary";
    input: typeof AddMultipleCollectionItemsGrpcRequestSchema;
    output: typeof AddMultipleCollectionItemsGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.CollectionService.updateCollectionItemsData
   */
  updateCollectionItemsData: {
    methodKind: "unary";
    input: typeof UpdateCollectionItemsGrpcRequestSchema;
    output: typeof UpdateCollectionItemsGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.CollectionService.syncCollectionToEs
   */
  syncCollectionToEs: {
    methodKind: "unary";
    input: typeof SyncCollectionGrpcRequestSchema;
    output: typeof SyncCollectionGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.CollectionService.syncAllCollectionToEs
   */
  syncAllCollectionToEs: {
    methodKind: "unary";
    input: typeof SyncAllCollectionGrpcRequestSchema;
    output: typeof SyncAllCollectionGrpcResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_business_CollectionItem,
  0,
);
