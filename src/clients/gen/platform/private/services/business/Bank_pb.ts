// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/business/Bank.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type {
  BankType,
  InvestabilityRolloutStatus,
  InvestabilityStatus,
} from "../../../public/models/business/BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "../../../public/models/business/BusinessCommon_pb.js";
import type { Empty } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { RewardCode } from "../../../public/models/identity/Reward_pb.js";
import { file_public_models_identity_Reward } from "../../../public/models/identity/Reward_pb.js";
import type { BankResponseSchema } from "../../../public/models/business/Collection_pb.js";
import { file_public_models_business_Collection } from "../../../public/models/business/Collection_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/business/Bank.proto.
 */
export const file_private_services_business_Bank: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_BusinessCommon,
      file_google_protobuf_empty,
      file_public_models_identity_Reward,
      file_public_models_business_Collection,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankGrpcRequest
 */
export type UpdateBankGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateBankGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: optional string name = 2;
     */
    name?: string;

    /**
     * @generated from field: optional string logo_url = 3;
     */
    logoUrl?: string;

    /**
     * @generated from field: optional string website_url = 4;
     */
    websiteUrl?: string;

    /**
     * @generated from field: optional bool is_insured = 5;
     */
    isInsured?: boolean;

    /**
     * @generated from field: optional com.stablemoney.api.identity.BankType bank_type = 6;
     */
    bankType?: BankType;

    /**
     * @generated from field: optional com.stablemoney.api.identity.InvestabilityStatus investability_status = 7;
     */
    investabilityStatus?: InvestabilityStatus;

    /**
     * @generated from field: optional bool is_popular = 8;
     */
    isPopular?: boolean;

    /**
     * @generated from field: optional string updated_by = 9;
     */
    updatedBy?: string;

    /**
     * @generated from field: optional string operating_bank_id = 10;
     */
    operatingBankId?: string;

    /**
     * @generated from field: optional int32 establishment_year = 11;
     */
    establishmentYear?: number;

    /**
     * @generated from field: optional string customer_care_number = 12;
     */
    customerCareNumber?: string;

    /**
     * @generated from field: optional string rbi_license_url = 13;
     */
    rbiLicenseUrl?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.InvestabilityRolloutStatus investability_rollout_status = 14;
     */
    investabilityRolloutStatus?: InvestabilityRolloutStatus;

    /**
     * @generated from field: optional int32 min_version_number_for_investability = 15;
     */
    minVersionNumberForInvestability?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankGrpcRequest.
 * Use `create(UpdateBankGrpcRequestSchema)` to create a new message.
 */
export const UpdateBankGrpcRequestSchema: GenMessage<UpdateBankGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 0);

/**
 * @generated from message com.stablemoney.api.identity.AddBankGrpcRequest
 */
export type AddBankGrpcRequest =
  Message<"com.stablemoney.api.identity.AddBankGrpcRequest"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @generated from field: optional string logo_url = 2;
     */
    logoUrl?: string;

    /**
     * @generated from field: optional string website_url = 3;
     */
    websiteUrl?: string;

    /**
     * @generated from field: optional bool is_insured = 4;
     */
    isInsured?: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.BankType bank_type = 5;
     */
    bankType: BankType;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestabilityStatus investability_status = 6;
     */
    investabilityStatus: InvestabilityStatus;

    /**
     * @generated from field: optional bool is_popular = 7;
     */
    isPopular?: boolean;

    /**
     * @generated from field: optional string created_by = 8;
     */
    createdBy?: string;

    /**
     * @generated from field: optional string operating_bank_id = 9;
     */
    operatingBankId?: string;

    /**
     * @generated from field: optional int32 establishment_year = 10;
     */
    establishmentYear?: number;

    /**
     * @generated from field: optional string customer_care_number = 12;
     */
    customerCareNumber?: string;

    /**
     * @generated from field: optional string rbi_license_url = 13;
     */
    rbiLicenseUrl?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.InvestabilityRolloutStatus investability_rollout_status = 14;
     */
    investabilityRolloutStatus?: InvestabilityRolloutStatus;

    /**
     * @generated from field: optional int32 min_version_number_for_investability = 15;
     */
    minVersionNumberForInvestability?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankGrpcRequest.
 * Use `create(AddBankGrpcRequestSchema)` to create a new message.
 */
export const AddBankGrpcRequestSchema: GenMessage<AddBankGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 1);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankGrpcResponse
 */
export type UpdateBankGrpcResponse =
  Message<"com.stablemoney.api.identity.UpdateBankGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankGrpcResponse.
 * Use `create(UpdateBankGrpcResponseSchema)` to create a new message.
 */
export const UpdateBankGrpcResponseSchema: GenMessage<UpdateBankGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 2);

/**
 * @generated from message com.stablemoney.api.identity.AddBankGrpcResponse
 */
export type AddBankGrpcResponse =
  Message<"com.stablemoney.api.identity.AddBankGrpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankGrpcResponse.
 * Use `create(AddBankGrpcResponseSchema)` to create a new message.
 */
export const AddBankGrpcResponseSchema: GenMessage<AddBankGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 3);

/**
 * @generated from message com.stablemoney.api.identity.ReIndexAllBankAliasResponse
 */
export type ReIndexAllBankAliasResponse =
  Message<"com.stablemoney.api.identity.ReIndexAllBankAliasResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.ReIndexAllBankAliasResponse.
 * Use `create(ReIndexAllBankAliasResponseSchema)` to create a new message.
 */
export const ReIndexAllBankAliasResponseSchema: GenMessage<ReIndexAllBankAliasResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 4);

/**
 * @generated from message com.stablemoney.api.identity.ReIndexAllBankAliasRequest
 */
export type ReIndexAllBankAliasRequest =
  Message<"com.stablemoney.api.identity.ReIndexAllBankAliasRequest"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.ReIndexAllBankAliasRequest.
 * Use `create(ReIndexAllBankAliasRequestSchema)` to create a new message.
 */
export const ReIndexAllBankAliasRequestSchema: GenMessage<ReIndexAllBankAliasRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 5);

/**
 * @generated from message com.stablemoney.api.identity.BankRewardStatusRequest
 * @deprecated
 */
export type BankRewardStatusRequest =
  Message<"com.stablemoney.api.identity.BankRewardStatusRequest"> & {
    /**
     * @generated from field: string bank_name = 1;
     */
    bankName: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 2;
     */
    rewardCode: RewardCode;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankRewardStatusRequest.
 * Use `create(BankRewardStatusRequestSchema)` to create a new message.
 * @deprecated
 */
export const BankRewardStatusRequestSchema: GenMessage<BankRewardStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 6);

/**
 * @generated from message com.stablemoney.api.identity.BankRewardStatusResponse
 * @deprecated
 */
export type BankRewardStatusResponse =
  Message<"com.stablemoney.api.identity.BankRewardStatusResponse"> & {
    /**
     * @generated from field: bool is_eligible = 1;
     */
    isEligible: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankRewardStatusResponse.
 * Use `create(BankRewardStatusResponseSchema)` to create a new message.
 * @deprecated
 */
export const BankRewardStatusResponseSchema: GenMessage<BankRewardStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 7);

/**
 * @generated from message com.stablemoney.api.identity.BankRewardEligibilityRequest
 */
export type BankRewardEligibilityRequest =
  Message<"com.stablemoney.api.identity.BankRewardEligibilityRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 2;
     */
    rewardCode: RewardCode;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankRewardEligibilityRequest.
 * Use `create(BankRewardEligibilityRequestSchema)` to create a new message.
 */
export const BankRewardEligibilityRequestSchema: GenMessage<BankRewardEligibilityRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 8);

/**
 * @generated from message com.stablemoney.api.identity.BankRewardEligibilityResponse
 */
export type BankRewardEligibilityResponse =
  Message<"com.stablemoney.api.identity.BankRewardEligibilityResponse"> & {
    /**
     * @generated from field: bool is_eligible = 1;
     */
    isEligible: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankRewardEligibilityResponse.
 * Use `create(BankRewardEligibilityResponseSchema)` to create a new message.
 */
export const BankRewardEligibilityResponseSchema: GenMessage<BankRewardEligibilityResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 9);

/**
 * @generated from message com.stablemoney.api.identity.BankLogoRequest
 */
export type BankLogoRequest =
  Message<"com.stablemoney.api.identity.BankLogoRequest"> & {
    /**
     * @generated from field: string bank_name = 1;
     */
    bankName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankLogoRequest.
 * Use `create(BankLogoRequestSchema)` to create a new message.
 */
export const BankLogoRequestSchema: GenMessage<BankLogoRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 10);

/**
 * @generated from message com.stablemoney.api.identity.BankLogoResponse
 */
export type BankLogoResponse =
  Message<"com.stablemoney.api.identity.BankLogoResponse"> & {
    /**
     * @generated from field: string bank_logo_url = 1;
     */
    bankLogoUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankLogoResponse.
 * Use `create(BankLogoResponseSchema)` to create a new message.
 */
export const BankLogoResponseSchema: GenMessage<BankLogoResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 11);

/**
 * @generated from message com.stablemoney.api.identity.BankRequest
 */
export type BankRequest =
  Message<"com.stablemoney.api.identity.BankRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankRequest.
 * Use `create(BankRequestSchema)` to create a new message.
 */
export const BankRequestSchema: GenMessage<BankRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 12);

/**
 * @generated from message com.stablemoney.api.identity.BankHolidaysRequest
 */
export type BankHolidaysRequest =
  Message<"com.stablemoney.api.identity.BankHolidaysRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankHolidayRequest bank_holiday_request = 1;
     */
    bankHolidayRequest: BankHolidayRequest[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankHolidaysRequest.
 * Use `create(BankHolidaysRequestSchema)` to create a new message.
 */
export const BankHolidaysRequestSchema: GenMessage<BankHolidaysRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 13);

/**
 * @generated from message com.stablemoney.api.identity.BankHolidayRequest
 */
export type BankHolidayRequest =
  Message<"com.stablemoney.api.identity.BankHolidayRequest"> & {
    /**
     * @generated from field: string holiday_date = 1;
     */
    holidayDate: string;

    /**
     * @generated from field: string holiday_name = 2;
     */
    holidayName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankHolidayRequest.
 * Use `create(BankHolidayRequestSchema)` to create a new message.
 */
export const BankHolidayRequestSchema: GenMessage<BankHolidayRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 14);

/**
 * @generated from message com.stablemoney.api.identity.BankHolidayMappingsRequest
 */
export type BankHolidayMappingsRequest =
  Message<"com.stablemoney.api.identity.BankHolidayMappingsRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankHolidayMappingRequest bank_holiday_mapping_request = 1;
     */
    bankHolidayMappingRequest: BankHolidayMappingRequest[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankHolidayMappingsRequest.
 * Use `create(BankHolidayMappingsRequestSchema)` to create a new message.
 */
export const BankHolidayMappingsRequestSchema: GenMessage<BankHolidayMappingsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 15);

/**
 * @generated from message com.stablemoney.api.identity.BankHolidayMappingRequest
 */
export type BankHolidayMappingRequest =
  Message<"com.stablemoney.api.identity.BankHolidayMappingRequest"> & {
    /**
     * @generated from field: string bank_holiday_calender_id = 1;
     */
    bankHolidayCalenderId: string;

    /**
     * @generated from field: string bank_id = 2;
     */
    bankId: string;

    /**
     * @generated from field: string created_by = 3;
     */
    createdBy: string;

    /**
     * @generated from field: bool is_active = 4;
     */
    isActive: boolean;

    /**
     * @generated from field: bool is_agent_available = 5;
     */
    isAgentAvailable: boolean;

    /**
     * @generated from field: string updated_by = 6;
     */
    updatedBy: string;

    /**
     * @generated from field: bool is_withdrawal_processing_working_day = 7;
     */
    isWithdrawalProcessingWorkingDay: boolean;

    /**
     * @generated from field: bool is_refund_processing_working_day = 8;
     */
    isRefundProcessingWorkingDay: boolean;

    /**
     * @generated from field: bool is_vkyc_expiry_working_day = 9;
     */
    isVkycExpiryWorkingDay: boolean;

    /**
     * @generated from field: bool is_national_holiday = 10;
     */
    isNationalHoliday: boolean;

    /**
     * @generated from field: string vkyc_start_time = 11;
     */
    vkycStartTime: string;

    /**
     * @generated from field: string vkyc_end_time = 12;
     */
    vkycEndTime: string;

    /**
     * @generated from field: string description = 13;
     */
    description: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankHolidayMappingRequest.
 * Use `create(BankHolidayMappingRequestSchema)` to create a new message.
 */
export const BankHolidayMappingRequestSchema: GenMessage<BankHolidayMappingRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 16);

/**
 * @generated from message com.stablemoney.api.identity.BankHolidaysResponse
 */
export type BankHolidaysResponse =
  Message<"com.stablemoney.api.identity.BankHolidaysResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankHolidaysResponse.
 * Use `create(BankHolidaysResponseSchema)` to create a new message.
 */
export const BankHolidaysResponseSchema: GenMessage<BankHolidaysResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 17);

/**
 * @generated from message com.stablemoney.api.identity.BankHolidayMappingsResponse
 */
export type BankHolidayMappingsResponse =
  Message<"com.stablemoney.api.identity.BankHolidayMappingsResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankHolidayMappingsResponse.
 * Use `create(BankHolidayMappingsResponseSchema)` to create a new message.
 */
export const BankHolidayMappingsResponseSchema: GenMessage<BankHolidayMappingsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_Bank, 18);

/**
 * @generated from service com.stablemoney.api.identity.BankService
 */
export const BankService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.updateBankData
   */
  updateBankData: {
    methodKind: "unary";
    input: typeof UpdateBankGrpcRequestSchema;
    output: typeof UpdateBankGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.addBankData
   */
  addBankData: {
    methodKind: "unary";
    input: typeof AddBankGrpcRequestSchema;
    output: typeof AddBankGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.reIndexAllBankAlias
   */
  reIndexAllBankAlias: {
    methodKind: "unary";
    input: typeof ReIndexAllBankAliasRequestSchema;
    output: typeof ReIndexAllBankAliasResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.checkBankRewardStatus
   * @deprecated
   */
  checkBankRewardStatus: {
    methodKind: "unary";
    input: typeof BankRewardStatusRequestSchema;
    output: typeof BankRewardStatusResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.checkBankRewardEligibility
   */
  checkBankRewardEligibility: {
    methodKind: "unary";
    input: typeof BankRewardEligibilityRequestSchema;
    output: typeof BankRewardEligibilityResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.getBankLogoUrl
   */
  getBankLogoUrl: {
    methodKind: "unary";
    input: typeof BankLogoRequestSchema;
    output: typeof BankLogoResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.getBankById
   */
  getBankById: {
    methodKind: "unary";
    input: typeof BankRequestSchema;
    output: typeof BankResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.addBankHolidays
   */
  addBankHolidays: {
    methodKind: "unary";
    input: typeof BankHolidaysRequestSchema;
    output: typeof BankHolidaysResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankService.addBankHolidayMappings
   */
  addBankHolidayMappings: {
    methodKind: "unary";
    input: typeof BankHolidayMappingsRequestSchema;
    output: typeof BankHolidayMappingsResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_business_Bank, 0);
