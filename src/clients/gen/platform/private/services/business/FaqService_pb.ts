// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/business/FaqService.proto (package com.stablemoney.api.business.faq, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Faq } from "../../../public/models/business/Faq_pb.js";
import { file_public_models_business_Faq } from "../../../public/models/business/Faq_pb.js";
import { file_public_models_identity_Profile } from "../../../public/models/identity/Profile_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/business/FaqService.proto.
 */
export const file_private_services_business_FaqService: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Cipwcml2YXRlL3NlcnZpY2VzL2J1c2luZXNzL0ZhcVNlcnZpY2UucHJvdG8SIGNvbS5zdGFibGVtb25leS5hcGkuYnVzaW5lc3MuZmFxIiMKCkZhcVJlcXVlc3QSFQoNaW50ZXJuYWxfdGFncxgBIAMoCSLeAQoKRmFxRGV0YWlscxIyCgNmYXEYASABKAsyJS5jb20uc3RhYmxlbW9uZXkuYXBpLmJ1c2luZXNzLmZhcS5GYXESEQoJdXNlcl9uYW1lGAIgASgJEhUKDXVzZXJfbG9jYXRpb24YAyABKAkSHgoWdXNlcl9wcm9maWxlX2ltYWdlX3VybBgEIAEoCRIVCg1pbnRlcm5hbF90YWdzGAUgAygJEhUKDWV4dGVybmFsX3RhZ3MYBiADKAkSEAoIdXBfdm90ZXMYByABKAMSEgoKZG93bl92b3RlcxgIIAEoAyJJCgtGYXFSZXNwb25zZRI6CgRmYXFzGAEgAygLMiwuY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5mYXEuRmFxRGV0YWlsczJ0CgpGYXFTZXJ2aWNlEmYKB2dldEZhcXMSLC5jb20uc3RhYmxlbW9uZXkuYXBpLmJ1c2luZXNzLmZhcS5GYXFSZXF1ZXN0Gi0uY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5mYXEuRmFxUmVzcG9uc2VCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzc1ABYgZwcm90bzM",
    [file_public_models_business_Faq, file_public_models_identity_Profile],
  );

/**
 * @generated from message com.stablemoney.api.business.faq.FaqRequest
 */
export type FaqRequest =
  Message<"com.stablemoney.api.business.faq.FaqRequest"> & {
    /**
     * @generated from field: repeated string internal_tags = 1;
     */
    internalTags: string[];
  };

/**
 * Describes the message com.stablemoney.api.business.faq.FaqRequest.
 * Use `create(FaqRequestSchema)` to create a new message.
 */
export const FaqRequestSchema: GenMessage<FaqRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_FaqService, 0);

/**
 * @generated from message com.stablemoney.api.business.faq.FaqDetails
 */
export type FaqDetails =
  Message<"com.stablemoney.api.business.faq.FaqDetails"> & {
    /**
     * @generated from field: com.stablemoney.api.business.faq.Faq faq = 1;
     */
    faq?: Faq;

    /**
     * @generated from field: string user_name = 2;
     */
    userName: string;

    /**
     * @generated from field: string user_location = 3;
     */
    userLocation: string;

    /**
     * @generated from field: string user_profile_image_url = 4;
     */
    userProfileImageUrl: string;

    /**
     * @generated from field: repeated string internal_tags = 5;
     */
    internalTags: string[];

    /**
     * @generated from field: repeated string external_tags = 6;
     */
    externalTags: string[];

    /**
     * @generated from field: int64 up_votes = 7;
     */
    upVotes: bigint;

    /**
     * @generated from field: int64 down_votes = 8;
     */
    downVotes: bigint;
  };

/**
 * Describes the message com.stablemoney.api.business.faq.FaqDetails.
 * Use `create(FaqDetailsSchema)` to create a new message.
 */
export const FaqDetailsSchema: GenMessage<FaqDetails> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_FaqService, 1);

/**
 * @generated from message com.stablemoney.api.business.faq.FaqResponse
 */
export type FaqResponse =
  Message<"com.stablemoney.api.business.faq.FaqResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.FaqDetails faqs = 1;
     */
    faqs: FaqDetails[];
  };

/**
 * Describes the message com.stablemoney.api.business.faq.FaqResponse.
 * Use `create(FaqResponseSchema)` to create a new message.
 */
export const FaqResponseSchema: GenMessage<FaqResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_FaqService, 2);

/**
 * @generated from service com.stablemoney.api.business.faq.FaqService
 */
export const FaqService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.business.faq.FaqService.getFaqs
   */
  getFaqs: {
    methodKind: "unary";
    input: typeof FaqRequestSchema;
    output: typeof FaqResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_private_services_business_FaqService, 0);
