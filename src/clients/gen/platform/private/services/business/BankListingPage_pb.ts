// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/business/BankListingPage.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type {
  CompoundingFrequencyType,
  InterestPayoutType,
  InvestabilityStatus,
  InvestorType,
  TenureFormatType,
} from "../../../public/models/business/BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "../../../public/models/business/BusinessCommon_pb.js";
import type { Empty, Timestamp } from "@bufbuild/protobuf/wkt";
import {
  file_google_protobuf_empty,
  file_google_protobuf_timestamp,
} from "@bufbuild/protobuf/wkt";
import type {
  BankAward,
  BankCustomerTestimonial,
  BankProminentPersonnel,
  InvestNowMessage,
  StableMoneyAnalysis,
} from "../../../public/models/business/BankListing_pb.js";
import { file_public_models_business_BankListing } from "../../../public/models/business/BankListing_pb.js";
import type { Faq } from "../../../public/models/business/Faq_pb.js";
import { file_public_models_business_Faq } from "../../../public/models/business/Faq_pb.js";
import { file_public_models_business_Bank } from "../../../public/models/business/Bank_pb.js";
import type { MaturityInstruction } from "../../../public/models/business/Collection_pb.js";
import { file_public_models_business_Collection } from "../../../public/models/business/Collection_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/business/BankListingPage.proto.
 */
export const file_private_services_business_BankListingPage: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_BusinessCommon,
      file_google_protobuf_empty,
      file_public_models_business_BankListing,
      file_public_models_business_Faq,
      file_public_models_business_Bank,
      file_public_models_business_Collection,
      file_google_protobuf_timestamp,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.AddBankListingGrpcRequest
 */
export type AddBankListingGrpcRequest =
  Message<"com.stablemoney.api.identity.AddBankListingGrpcRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: optional string fd_booking_stats = 2;
     */
    fdBookingStats?: string;

    /**
     * @generated from field: optional string assurance_line = 3;
     */
    assuranceLine?: string;

    /**
     * @generated from field: optional string assurance_logo_url = 4;
     */
    assuranceLogoUrl?: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.StableMoneyAnalysis stable_money_analysis = 5;
     */
    stableMoneyAnalysis: StableMoneyAnalysis[];

    /**
     * @generated from field: optional string rates_pdf_url = 6;
     */
    ratesPdfUrl?: string;

    /**
     * @generated from field: optional string about_bank_info = 7;
     */
    aboutBankInfo?: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankProminentPersonnel bank_prominent_personnel = 8;
     */
    bankProminentPersonnel: BankProminentPersonnel[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankAward bank_award = 9;
     */
    bankAward: BankAward[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankCustomerTestimonial bank_customer_testimonial = 10;
     */
    bankCustomerTestimonial: BankCustomerTestimonial[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InvestNowMessage invest_now_message = 11;
     */
    investNowMessage: InvestNowMessage[];

    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 12;
     */
    bankFaq: Faq[];

    /**
     * @generated from field: string created_by = 13;
     */
    createdBy: string;

    /**
     * @generated from field: optional string safety_rating = 14;
     */
    safetyRating?: string;

    /**
     * @generated from field: optional bool is_bank_account_required = 15;
     */
    isBankAccountRequired?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankListingGrpcRequest.
 * Use `create(AddBankListingGrpcRequestSchema)` to create a new message.
 */
export const AddBankListingGrpcRequestSchema: GenMessage<AddBankListingGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 0);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankListingGrpcRequest
 */
export type UpdateBankListingGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateBankListingGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: optional string fd_booking_stats = 2;
     */
    fdBookingStats?: string;

    /**
     * @generated from field: optional string assurance_line = 3;
     */
    assuranceLine?: string;

    /**
     * @generated from field: optional string assurance_logo_url = 4;
     */
    assuranceLogoUrl?: string;

    /**
     * @generated from field: optional string rates_pdf_url = 5;
     */
    ratesPdfUrl?: string;

    /**
     * @generated from field: optional string about_bank_info = 6;
     */
    aboutBankInfo?: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InvestNowMessage invest_now_message = 7;
     */
    investNowMessage: InvestNowMessage[];

    /**
     * @generated from field: string updated_by = 8;
     */
    updatedBy: string;

    /**
     * @generated from field: optional string safety_rating = 9;
     */
    safetyRating?: string;

    /**
     * @generated from field: optional bool is_bank_account_required = 10;
     */
    isBankAccountRequired?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankListingGrpcRequest.
 * Use `create(UpdateBankListingGrpcRequestSchema)` to create a new message.
 */
export const UpdateBankListingGrpcRequestSchema: GenMessage<UpdateBankListingGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 1);

/**
 * @generated from message com.stablemoney.api.identity.AddBankAwardGrpcRequest
 */
export type AddBankAwardGrpcRequest =
  Message<"com.stablemoney.api.identity.AddBankAwardGrpcRequest"> & {
    /**
     * @generated from field: string bank_listing_page_id = 1;
     */
    bankListingPageId: string;

    /**
     * @generated from field: string created_by = 2;
     */
    createdBy: string;

    /**
     * @generated from field: optional string title = 3;
     */
    title?: string;

    /**
     * @generated from field: optional string icon_url = 4;
     */
    iconUrl?: string;

    /**
     * @generated from field: optional string received_by = 5;
     */
    receivedBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankAwardGrpcRequest.
 * Use `create(AddBankAwardGrpcRequestSchema)` to create a new message.
 */
export const AddBankAwardGrpcRequestSchema: GenMessage<AddBankAwardGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 2);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankAwardGrpcRequest
 */
export type UpdateBankAwardGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateBankAwardGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string updated_by = 2;
     */
    updatedBy: string;

    /**
     * @generated from field: optional string title = 3;
     */
    title?: string;

    /**
     * @generated from field: optional string icon_url = 4;
     */
    iconUrl?: string;

    /**
     * @generated from field: optional string received_by = 5;
     */
    receivedBy?: string;

    /**
     * @generated from field: optional bool is_active = 6;
     */
    isActive?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankAwardGrpcRequest.
 * Use `create(UpdateBankAwardGrpcRequestSchema)` to create a new message.
 */
export const UpdateBankAwardGrpcRequestSchema: GenMessage<UpdateBankAwardGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 3);

/**
 * @generated from message com.stablemoney.api.identity.AddBankStableMoneyAnalysisGrpcRequest
 */
export type AddBankStableMoneyAnalysisGrpcRequest =
  Message<"com.stablemoney.api.identity.AddBankStableMoneyAnalysisGrpcRequest"> & {
    /**
     * @generated from field: string bank_listing_page_id = 1;
     */
    bankListingPageId: string;

    /**
     * @generated from field: string created_by = 2;
     */
    createdBy: string;

    /**
     * @generated from field: optional string description = 3;
     */
    description?: string;

    /**
     * @generated from field: optional int32 priority = 4;
     */
    priority?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankStableMoneyAnalysisGrpcRequest.
 * Use `create(AddBankStableMoneyAnalysisGrpcRequestSchema)` to create a new message.
 */
export const AddBankStableMoneyAnalysisGrpcRequestSchema: GenMessage<AddBankStableMoneyAnalysisGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 4);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankStableMoneyAnalysisGrpcRequest
 */
export type UpdateBankStableMoneyAnalysisGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateBankStableMoneyAnalysisGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string updated_by = 2;
     */
    updatedBy: string;

    /**
     * @generated from field: optional string description = 3;
     */
    description?: string;

    /**
     * @generated from field: optional bool is_active = 4;
     */
    isActive?: boolean;

    /**
     * @generated from field: optional int32 priority = 5;
     */
    priority?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankStableMoneyAnalysisGrpcRequest.
 * Use `create(UpdateBankStableMoneyAnalysisGrpcRequestSchema)` to create a new message.
 */
export const UpdateBankStableMoneyAnalysisGrpcRequestSchema: GenMessage<UpdateBankStableMoneyAnalysisGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 5);

/**
 * @generated from message com.stablemoney.api.identity.AddBankCustomerTestimonialGrpcRequest
 */
export type AddBankCustomerTestimonialGrpcRequest =
  Message<"com.stablemoney.api.identity.AddBankCustomerTestimonialGrpcRequest"> & {
    /**
     * @generated from field: string bank_listing_page_id = 1;
     */
    bankListingPageId: string;

    /**
     * @generated from field: string created_by = 2;
     */
    createdBy: string;

    /**
     * @generated from field: optional string comment = 3;
     */
    comment?: string;

    /**
     * @generated from field: optional string name = 4;
     */
    name?: string;

    /**
     * @generated from field: optional string designation = 5;
     */
    designation?: string;

    /**
     * @generated from field: optional string picture_url = 6;
     */
    pictureUrl?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankCustomerTestimonialGrpcRequest.
 * Use `create(AddBankCustomerTestimonialGrpcRequestSchema)` to create a new message.
 */
export const AddBankCustomerTestimonialGrpcRequestSchema: GenMessage<AddBankCustomerTestimonialGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 6);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankCustomerTestimonialGrpcRequest
 */
export type UpdateBankCustomerTestimonialGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateBankCustomerTestimonialGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string updated_by = 2;
     */
    updatedBy: string;

    /**
     * @generated from field: optional string comment = 3;
     */
    comment?: string;

    /**
     * @generated from field: optional string name = 4;
     */
    name?: string;

    /**
     * @generated from field: optional string designation = 5;
     */
    designation?: string;

    /**
     * @generated from field: optional string picture_url = 6;
     */
    pictureUrl?: string;

    /**
     * @generated from field: optional bool is_active = 7;
     */
    isActive?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankCustomerTestimonialGrpcRequest.
 * Use `create(UpdateBankCustomerTestimonialGrpcRequestSchema)` to create a new message.
 */
export const UpdateBankCustomerTestimonialGrpcRequestSchema: GenMessage<UpdateBankCustomerTestimonialGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 7);

/**
 * @generated from message com.stablemoney.api.identity.AddBankProminentPersonnelGrpcRequest
 */
export type AddBankProminentPersonnelGrpcRequest =
  Message<"com.stablemoney.api.identity.AddBankProminentPersonnelGrpcRequest"> & {
    /**
     * @generated from field: string bank_listing_page_id = 1;
     */
    bankListingPageId: string;

    /**
     * @generated from field: string created_by = 2;
     */
    createdBy: string;

    /**
     * @generated from field: optional string heading = 3;
     */
    heading?: string;

    /**
     * @generated from field: optional string name = 4;
     */
    name?: string;

    /**
     * @generated from field: optional string description = 5;
     */
    description?: string;

    /**
     * @generated from field: optional string picture_url = 6;
     */
    pictureUrl?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankProminentPersonnelGrpcRequest.
 * Use `create(AddBankProminentPersonnelGrpcRequestSchema)` to create a new message.
 */
export const AddBankProminentPersonnelGrpcRequestSchema: GenMessage<AddBankProminentPersonnelGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 8);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankProminentPersonnelGrpcRequest
 */
export type UpdateBankProminentPersonnelGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateBankProminentPersonnelGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string updated_by = 2;
     */
    updatedBy: string;

    /**
     * @generated from field: optional string heading = 3;
     */
    heading?: string;

    /**
     * @generated from field: optional string name = 4;
     */
    name?: string;

    /**
     * @generated from field: optional string description = 5;
     */
    description?: string;

    /**
     * @generated from field: optional string picture_url = 6;
     */
    pictureUrl?: string;

    /**
     * @generated from field: optional bool is_active = 7;
     */
    isActive?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankProminentPersonnelGrpcRequest.
 * Use `create(UpdateBankProminentPersonnelGrpcRequestSchema)` to create a new message.
 */
export const UpdateBankProminentPersonnelGrpcRequestSchema: GenMessage<UpdateBankProminentPersonnelGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 9);

/**
 * @generated from message com.stablemoney.api.identity.AddBankFaqGrpcRequest
 */
export type AddBankFaqGrpcRequest =
  Message<"com.stablemoney.api.identity.AddBankFaqGrpcRequest"> & {
    /**
     * @generated from field: string bank_listing_page_id = 1;
     */
    bankListingPageId: string;

    /**
     * @generated from field: string question = 2;
     */
    question: string;

    /**
     * @generated from field: string answer = 3;
     */
    answer: string;

    /**
     * @generated from field: int32 faq_sequence = 4;
     */
    faqSequence: number;

    /**
     * @generated from field: string created_by = 5;
     */
    createdBy: string;

    /**
     * @generated from field: repeated string bank_faq_category_list = 6;
     */
    bankFaqCategoryList: string[];

    /**
     * @generated from field: optional string question_tag = 7;
     */
    questionTag?: string;

    /**
     * @generated from field: optional bool html_answer = 8;
     */
    htmlAnswer?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankFaqGrpcRequest.
 * Use `create(AddBankFaqGrpcRequestSchema)` to create a new message.
 */
export const AddBankFaqGrpcRequestSchema: GenMessage<AddBankFaqGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 10);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankFaqGrpcRequest
 */
export type UpdateBankFaqGrpcRequest =
  Message<"com.stablemoney.api.identity.UpdateBankFaqGrpcRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: optional string question = 2;
     */
    question?: string;

    /**
     * @generated from field: optional string answer = 3;
     */
    answer?: string;

    /**
     * @generated from field: optional string updated_by = 4;
     */
    updatedBy?: string;

    /**
     * @generated from field: optional int32 faq_sequence = 5;
     */
    faqSequence?: number;

    /**
     * @generated from field: optional bool is_active = 6;
     */
    isActive?: boolean;

    /**
     * @generated from field: repeated string bank_faq_category_list = 7;
     */
    bankFaqCategoryList: string[];

    /**
     * @generated from field: optional string question_tag = 8;
     */
    questionTag?: string;

    /**
     * @generated from field: optional bool html_answer = 9;
     */
    htmlAnswer?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankFaqGrpcRequest.
 * Use `create(UpdateBankFaqGrpcRequestSchema)` to create a new message.
 */
export const UpdateBankFaqGrpcRequestSchema: GenMessage<UpdateBankFaqGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 11);

/**
 * @generated from message com.stablemoney.api.identity.EmptyProtoRpcResponse
 */
export type EmptyProtoRpcResponse =
  Message<"com.stablemoney.api.identity.EmptyProtoRpcResponse"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmptyProtoRpcResponse.
 * Use `create(EmptyProtoRpcResponseSchema)` to create a new message.
 */
export const EmptyProtoRpcResponseSchema: GenMessage<EmptyProtoRpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 12);

/**
 * @generated from message com.stablemoney.api.identity.SyncBankListingGrpcRequest
 */
export type SyncBankListingGrpcRequest =
  Message<"com.stablemoney.api.identity.SyncBankListingGrpcRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SyncBankListingGrpcRequest.
 * Use `create(SyncBankListingGrpcRequestSchema)` to create a new message.
 */
export const SyncBankListingGrpcRequestSchema: GenMessage<SyncBankListingGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 13);

/**
 * @generated from message com.stablemoney.api.identity.SyncAllBankListingGrpcRequest
 */
export type SyncAllBankListingGrpcRequest =
  Message<"com.stablemoney.api.identity.SyncAllBankListingGrpcRequest"> & {
    /**
     * @generated from field: google.protobuf.Empty empty = 1;
     */
    empty?: Empty;
  };

/**
 * Describes the message com.stablemoney.api.identity.SyncAllBankListingGrpcRequest.
 * Use `create(SyncAllBankListingGrpcRequestSchema)` to create a new message.
 */
export const SyncAllBankListingGrpcRequestSchema: GenMessage<SyncAllBankListingGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 14);

/**
 * @generated from message com.stablemoney.api.identity.AddBankFixedDepositSteps
 */
export type AddBankFixedDepositSteps =
  Message<"com.stablemoney.api.identity.AddBankFixedDepositSteps"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.AddBankFixedDepositItem add_bank_fixed_deposit_items_list = 2;
     */
    addBankFixedDepositItemsList: AddBankFixedDepositItem[];

    /**
     * @generated from field: string created_by = 3;
     */
    createdBy: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankFixedDepositSteps.
 * Use `create(AddBankFixedDepositStepsSchema)` to create a new message.
 */
export const AddBankFixedDepositStepsSchema: GenMessage<AddBankFixedDepositSteps> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 15);

/**
 * @generated from message com.stablemoney.api.identity.AddBankFixedDepositItem
 */
export type AddBankFixedDepositItem =
  Message<"com.stablemoney.api.identity.AddBankFixedDepositItem"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: string image_url = 3;
     */
    imageUrl: string;

    /**
     * @generated from field: int32 priority = 4;
     */
    priority: number;

    /**
     * @generated from field: string created_by = 5;
     */
    createdBy: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddBankFixedDepositItem.
 * Use `create(AddBankFixedDepositItemSchema)` to create a new message.
 */
export const AddBankFixedDepositItemSchema: GenMessage<AddBankFixedDepositItem> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 16);

/**
 * @generated from message com.stablemoney.api.identity.UpdateBankFixedDepositSteps
 */
export type UpdateBankFixedDepositSteps =
  Message<"com.stablemoney.api.identity.UpdateBankFixedDepositSteps"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: optional string title = 2;
     */
    title?: string;

    /**
     * @generated from field: optional string description = 3;
     */
    description?: string;

    /**
     * @generated from field: optional int32 priority = 4;
     */
    priority?: number;

    /**
     * @generated from field: optional bool is_active = 5;
     */
    isActive?: boolean;

    /**
     * @generated from field: optional string image_url = 6;
     */
    imageUrl?: string;

    /**
     * @generated from field: optional string updated_by = 7;
     */
    updatedBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateBankFixedDepositSteps.
 * Use `create(UpdateBankFixedDepositStepsSchema)` to create a new message.
 */
export const UpdateBankFixedDepositStepsSchema: GenMessage<UpdateBankFixedDepositSteps> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 17);

/**
 * @generated from message com.stablemoney.api.identity.FixedDepositRates
 */
export type FixedDepositRates =
  Message<"com.stablemoney.api.identity.FixedDepositRates"> & {
    /**
     * @generated from field: optional string id = 1;
     */
    id?: string;

    /**
     * @generated from field: string bank_id = 2;
     */
    bankId: string;

    /**
     * @generated from field: optional bool is_active = 3;
     */
    isActive?: boolean;

    /**
     * @generated from field: double rate = 4;
     */
    rate: number;

    /**
     * @generated from field: optional double annualized_rate = 5;
     */
    annualizedRate?: number;

    /**
     * @generated from field: optional double min_deposit = 6;
     */
    minDeposit?: number;

    /**
     * @generated from field: optional double max_deposit = 7;
     */
    maxDeposit?: number;

    /**
     * @generated from field: int32 min_tenure_in_days = 8;
     */
    minTenureInDays: number;

    /**
     * @generated from field: int32 max_tenure_in_days = 9;
     */
    maxTenureInDays: number;

    /**
     * @generated from field: optional string raw_tenure = 10;
     */
    rawTenure?: string;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestorType investor_type = 11;
     */
    investorType: InvestorType;

    /**
     * @generated from field: optional int32 in_denomination_of = 12;
     */
    inDenominationOf?: number;

    /**
     * @generated from field: optional int32 lock_in_period_in_days = 13;
     */
    lockInPeriodInDays?: number;

    /**
     * @generated from field: bool is_tax_saving = 14;
     */
    isTaxSaving: boolean;

    /**
     * @generated from field: bool is_pre_mature_withdrawal_allowed = 15;
     */
    isPreMatureWithdrawalAllowed: boolean;

    /**
     * @generated from field: optional com.stablemoney.api.identity.InvestabilityStatus investability_status = 16;
     */
    investabilityStatus?: InvestabilityStatus;

    /**
     * @generated from field: com.stablemoney.api.identity.InterestPayoutType interest_payout_type = 17;
     */
    interestPayoutType: InterestPayoutType;

    /**
     * @generated from field: com.stablemoney.api.identity.CompoundingFrequencyType compounding_frequency = 18;
     */
    compoundingFrequency: CompoundingFrequencyType;

    /**
     * @generated from field: bool is_loan_against_fd_allowed = 19;
     */
    isLoanAgainstFdAllowed: boolean;

    /**
     * @generated from field: bool is_partial_withdrawal_allowed = 20;
     */
    isPartialWithdrawalAllowed: boolean;

    /**
     * @generated from field: optional string breakage_charges_and_description = 21;
     */
    breakageChargesAndDescription?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.TenureFormatType tenure_format_type = 22;
     */
    tenureFormatType?: TenureFormatType;

    /**
     * @generated from field: optional google.protobuf.Timestamp valid_from = 23;
     */
    validFrom?: Timestamp;

    /**
     * @generated from field: optional google.protobuf.Timestamp valid_till = 24;
     */
    validTill?: Timestamp;

    /**
     * @generated from field: int32 tenure_in_days = 25;
     */
    tenureInDays: number;

    /**
     * @generated from field: int32 tenure_in_months = 26;
     */
    tenureInMonths: number;

    /**
     * @generated from field: int32 tenure_in_years = 27;
     */
    tenureInYears: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.MaturityInstruction maturity_instructions = 28;
     */
    maturityInstructions: MaturityInstruction[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InterestPayoutType interest_payout_types = 29;
     */
    interestPayoutTypes: InterestPayoutType[];
  };

/**
 * Describes the message com.stablemoney.api.identity.FixedDepositRates.
 * Use `create(FixedDepositRatesSchema)` to create a new message.
 */
export const FixedDepositRatesSchema: GenMessage<FixedDepositRates> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 18);

/**
 * @generated from message com.stablemoney.api.identity.AddFixedDepositRatesGrpcRequest
 */
export type AddFixedDepositRatesGrpcRequest =
  Message<"com.stablemoney.api.identity.AddFixedDepositRatesGrpcRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FixedDepositRates fixed_deposit_rates = 2;
     */
    fixedDepositRates: FixedDepositRates[];
  };

/**
 * Describes the message com.stablemoney.api.identity.AddFixedDepositRatesGrpcRequest.
 * Use `create(AddFixedDepositRatesGrpcRequestSchema)` to create a new message.
 */
export const AddFixedDepositRatesGrpcRequestSchema: GenMessage<AddFixedDepositRatesGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 19);

/**
 * @generated from message com.stablemoney.api.identity.SetInactiveFdRateIdsRequest
 */
export type SetInactiveFdRateIdsRequest =
  Message<"com.stablemoney.api.identity.SetInactiveFdRateIdsRequest"> & {
    /**
     * @generated from field: google.protobuf.Timestamp valid_till = 1;
     */
    validTill?: Timestamp;

    /**
     * @generated from field: repeated string ids = 2;
     */
    ids: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.SetInactiveFdRateIdsRequest.
 * Use `create(SetInactiveFdRateIdsRequestSchema)` to create a new message.
 */
export const SetInactiveFdRateIdsRequestSchema: GenMessage<SetInactiveFdRateIdsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 20);

/**
 * @generated from message com.stablemoney.api.identity.BankSellingPointItem
 */
export type BankSellingPointItem =
  Message<"com.stablemoney.api.identity.BankSellingPointItem"> & {
    /**
     * @generated from field: optional string title = 2;
     */
    title?: string;

    /**
     * @generated from field: optional string description = 3;
     */
    description?: string;

    /**
     * @generated from field: optional string icon_url = 4;
     */
    iconUrl?: string;

    /**
     * @generated from field: bool is_active = 5;
     */
    isActive: boolean;

    /**
     * @generated from field: optional int32 priority = 6;
     */
    priority?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankSellingPointItem.
 * Use `create(BankSellingPointItemSchema)` to create a new message.
 */
export const BankSellingPointItemSchema: GenMessage<BankSellingPointItem> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 21);

/**
 * @generated from message com.stablemoney.api.identity.BankSellingPointsRequest
 */
export type BankSellingPointsRequest =
  Message<"com.stablemoney.api.identity.BankSellingPointsRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankSellingPointItem bank_selling_point_item = 2;
     */
    bankSellingPointItem: BankSellingPointItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankSellingPointsRequest.
 * Use `create(BankSellingPointsRequestSchema)` to create a new message.
 */
export const BankSellingPointsRequestSchema: GenMessage<BankSellingPointsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 22);

/**
 * @generated from message com.stablemoney.api.identity.SetInactiveBankSellingPointsRequest
 */
export type SetInactiveBankSellingPointsRequest =
  Message<"com.stablemoney.api.identity.SetInactiveBankSellingPointsRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: repeated string bank_selling_point_ids = 2;
     */
    bankSellingPointIds: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.SetInactiveBankSellingPointsRequest.
 * Use `create(SetInactiveBankSellingPointsRequestSchema)` to create a new message.
 */
export const SetInactiveBankSellingPointsRequestSchema: GenMessage<SetInactiveBankSellingPointsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 23);

/**
 * @generated from message com.stablemoney.api.identity.BankTopInfoCardItem
 */
export type BankTopInfoCardItem =
  Message<"com.stablemoney.api.identity.BankTopInfoCardItem"> & {
    /**
     * @generated from field: optional string title = 1;
     */
    title?: string;

    /**
     * @generated from field: optional string description = 2;
     */
    description?: string;

    /**
     * @generated from field: bool is_active = 3;
     */
    isActive: boolean;

    /**
     * @generated from field: optional int32 priority = 4;
     */
    priority?: number;

    /**
     * @generated from field: optional string icon_url = 5;
     */
    iconUrl?: string;

    /**
     * @generated from field: optional string info_bottom_sheet_title = 6;
     */
    infoBottomSheetTitle?: string;

    /**
     * @generated from field: optional string info_bottom_sheet_description = 7;
     */
    infoBottomSheetDescription?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankTopInfoCardItem.
 * Use `create(BankTopInfoCardItemSchema)` to create a new message.
 */
export const BankTopInfoCardItemSchema: GenMessage<BankTopInfoCardItem> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 24);

/**
 * @generated from message com.stablemoney.api.identity.BankTopInfoCardsRequest
 */
export type BankTopInfoCardsRequest =
  Message<"com.stablemoney.api.identity.BankTopInfoCardsRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankTopInfoCardItem bank_top_info_cards = 2;
     */
    bankTopInfoCards: BankTopInfoCardItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankTopInfoCardsRequest.
 * Use `create(BankTopInfoCardsRequestSchema)` to create a new message.
 */
export const BankTopInfoCardsRequestSchema: GenMessage<BankTopInfoCardsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 25);

/**
 * @generated from message com.stablemoney.api.identity.SetInactiveBankInfoCardsRequest
 */
export type SetInactiveBankInfoCardsRequest =
  Message<"com.stablemoney.api.identity.SetInactiveBankInfoCardsRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: repeated string bank_top_info_card_ids = 2;
     */
    bankTopInfoCardIds: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.SetInactiveBankInfoCardsRequest.
 * Use `create(SetInactiveBankInfoCardsRequestSchema)` to create a new message.
 */
export const SetInactiveBankInfoCardsRequestSchema: GenMessage<SetInactiveBankInfoCardsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 26);

/**
 * @generated from message com.stablemoney.api.identity.ClearCacheRequest
 */
export type ClearCacheRequest =
  Message<"com.stablemoney.api.identity.ClearCacheRequest"> & {};

/**
 * Describes the message com.stablemoney.api.identity.ClearCacheRequest.
 * Use `create(ClearCacheRequestSchema)` to create a new message.
 */
export const ClearCacheRequestSchema: GenMessage<ClearCacheRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 27);

/**
 * @generated from message com.stablemoney.api.identity.ReorderBankFaqCategoryListGrpcRequest
 */
export type ReorderBankFaqCategoryListGrpcRequest =
  Message<"com.stablemoney.api.identity.ReorderBankFaqCategoryListGrpcRequest"> & {
    /**
     * @generated from field: string bank_listing_page_id = 1;
     */
    bankListingPageId: string;

    /**
     * @generated from field: repeated string bank_faq_category_priority_list = 2;
     */
    bankFaqCategoryPriorityList: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.ReorderBankFaqCategoryListGrpcRequest.
 * Use `create(ReorderBankFaqCategoryListGrpcRequestSchema)` to create a new message.
 */
export const ReorderBankFaqCategoryListGrpcRequestSchema: GenMessage<ReorderBankFaqCategoryListGrpcRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_BankListingPage, 28);

/**
 * @generated from service com.stablemoney.api.identity.BankListingService
 */
export const BankListingService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.addBankListingPageData
   */
  addBankListingPageData: {
    methodKind: "unary";
    input: typeof AddBankListingGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.updateBankListingPageData
   */
  updateBankListingPageData: {
    methodKind: "unary";
    input: typeof UpdateBankListingGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.syncBankListingToEs
   */
  syncBankListingToEs: {
    methodKind: "unary";
    input: typeof SyncBankListingGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.addBankFaqData
   */
  addBankFaqData: {
    methodKind: "unary";
    input: typeof AddBankFaqGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.updateBankFaqData
   */
  updateBankFaqData: {
    methodKind: "unary";
    input: typeof UpdateBankFaqGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.syncAllBankListingPagesToEs
   */
  syncAllBankListingPagesToEs: {
    methodKind: "unary";
    input: typeof SyncAllBankListingGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.addBankFixedDepositSteps
   */
  addBankFixedDepositSteps: {
    methodKind: "unary";
    input: typeof AddBankFixedDepositStepsSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.updateBankFixedDepositSteps
   */
  updateBankFixedDepositSteps: {
    methodKind: "unary";
    input: typeof UpdateBankFixedDepositStepsSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.addFixedDepositRates
   */
  addFixedDepositRates: {
    methodKind: "unary";
    input: typeof AddFixedDepositRatesGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.setInactiveFdRateIds
   */
  setInactiveFdRateIds: {
    methodKind: "unary";
    input: typeof SetInactiveFdRateIdsRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.addBankSellingPoints
   */
  addBankSellingPoints: {
    methodKind: "unary";
    input: typeof BankSellingPointsRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.setInactiveBankSellingPoints
   */
  setInactiveBankSellingPoints: {
    methodKind: "unary";
    input: typeof SetInactiveBankSellingPointsRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.addBankTopCards
   */
  addBankTopCards: {
    methodKind: "unary";
    input: typeof BankTopInfoCardsRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.setInactiveBankTopCards
   */
  setInactiveBankTopCards: {
    methodKind: "unary";
    input: typeof SetInactiveBankInfoCardsRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.clearCache
   */
  clearCache: {
    methodKind: "unary";
    input: typeof ClearCacheRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.identity.BankListingService.reorderBankFaqCategoryList
   */
  reorderBankFaqCategoryList: {
    methodKind: "unary";
    input: typeof ReorderBankFaqCategoryListGrpcRequestSchema;
    output: typeof EmptyProtoRpcResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_business_BankListingPage,
  0,
);
