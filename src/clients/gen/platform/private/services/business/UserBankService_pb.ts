// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/business/UserBankService.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import { file_public_models_identity_Profile } from "../../../public/models/identity/Profile_pb.js";
import { file_public_models_identity_Device } from "../../../public/models/identity/Device_pb.js";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import { file_public_models_identity_ProfileCompletion } from "../../../public/models/identity/ProfileCompletion_pb.js";
import type { InvestabilityStatus } from "../../../public/models/business/BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "../../../public/models/business/BusinessCommon_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/business/UserBankService.proto.
 */
export const file_private_services_business_UserBankService: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci9wcml2YXRlL3NlcnZpY2VzL2J1c2luZXNzL1VzZXJCYW5rU2VydmljZS5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSJtCi5HZXRVc2VyQmFua1JlbGF0aW9uc2hpcEFuZEludmVzdGFiaWxpdHlSZXF1ZXN0Eg8KB3VzZXJfaWQYASABKAkSFAoMdmVyc2lvbl9jb2RlGAIgASgFEhQKDHBob25lX251bWJlchgDIAEoCSKLBAovR2V0VXNlckJhbmtSZWxhdGlvbnNoaXBBbmRJbnZlc3RhYmlsaXR5UmVzcG9uc2USlgEKHnVzZXJfYmFua19pbnZlc3RhYmlsaXR5X3N0YXR1cxgBIAMoCzJuLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuR2V0VXNlckJhbmtSZWxhdGlvbnNoaXBBbmRJbnZlc3RhYmlsaXR5UmVzcG9uc2UuVXNlckJhbmtJbnZlc3RhYmlsaXR5U3RhdHVzRW50cnkSiQEKF3VzZXJfYmFua19yZWxhdGlvbnNoaXBzGAIgAygLMmguY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5HZXRVc2VyQmFua1JlbGF0aW9uc2hpcEFuZEludmVzdGFiaWxpdHlSZXNwb25zZS5Vc2VyQmFua1JlbGF0aW9uc2hpcHNFbnRyeRp1CiBVc2VyQmFua0ludmVzdGFiaWxpdHlTdGF0dXNFbnRyeRILCgNrZXkYASABKAkSQAoFdmFsdWUYAiABKA4yMS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkludmVzdGFiaWxpdHlTdGF0dXM6AjgBGjwKGlVzZXJCYW5rUmVsYXRpb25zaGlwc0VudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEy2gEKD1VzZXJCYW5rU2VydmljZRLGAQonZ2V0VXNlckJhbmtSZWxhdGlvbnNoaXBBbmRJbnZlc3RhYmlsaXR5EkwuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5HZXRVc2VyQmFua1JlbGF0aW9uc2hpcEFuZEludmVzdGFiaWxpdHlSZXF1ZXN0Gk0uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5HZXRVc2VyQmFua1JlbGF0aW9uc2hpcEFuZEludmVzdGFiaWxpdHlSZXNwb25zZUIpCiVjb20uc3RhYmxlbW9uZXkuaW50ZXJuYWwuYXBpLmJ1c2luZXNzUAFiBnByb3RvMw",
    [
      file_public_models_identity_Profile,
      file_public_models_identity_Device,
      file_google_protobuf_empty,
      file_public_models_identity_ProfileCompletion,
      file_public_models_business_BusinessCommon,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.GetUserBankRelationshipAndInvestabilityRequest
 */
export type GetUserBankRelationshipAndInvestabilityRequest =
  Message<"com.stablemoney.api.identity.GetUserBankRelationshipAndInvestabilityRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: int32 version_code = 2;
     */
    versionCode: number;

    /**
     * @generated from field: string phone_number = 3;
     */
    phoneNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetUserBankRelationshipAndInvestabilityRequest.
 * Use `create(GetUserBankRelationshipAndInvestabilityRequestSchema)` to create a new message.
 */
export const GetUserBankRelationshipAndInvestabilityRequestSchema: GenMessage<GetUserBankRelationshipAndInvestabilityRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserBankService, 0);

/**
 * @generated from message com.stablemoney.api.identity.GetUserBankRelationshipAndInvestabilityResponse
 */
export type GetUserBankRelationshipAndInvestabilityResponse =
  Message<"com.stablemoney.api.identity.GetUserBankRelationshipAndInvestabilityResponse"> & {
    /**
     * @generated from field: map<string, com.stablemoney.api.identity.InvestabilityStatus> user_bank_investability_status = 1;
     */
    userBankInvestabilityStatus: { [key: string]: InvestabilityStatus };

    /**
     * @generated from field: map<string, string> user_bank_relationships = 2;
     */
    userBankRelationships: { [key: string]: string };
  };

/**
 * Describes the message com.stablemoney.api.identity.GetUserBankRelationshipAndInvestabilityResponse.
 * Use `create(GetUserBankRelationshipAndInvestabilityResponseSchema)` to create a new message.
 */
export const GetUserBankRelationshipAndInvestabilityResponseSchema: GenMessage<GetUserBankRelationshipAndInvestabilityResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserBankService, 1);

/**
 * @generated from service com.stablemoney.api.identity.UserBankService
 */
export const UserBankService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.identity.UserBankService.getUserBankRelationshipAndInvestability
   */
  getUserBankRelationshipAndInvestability: {
    methodKind: "unary";
    input: typeof GetUserBankRelationshipAndInvestabilityRequestSchema;
    output: typeof GetUserBankRelationshipAndInvestabilityResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_business_UserBankService,
  0,
);
