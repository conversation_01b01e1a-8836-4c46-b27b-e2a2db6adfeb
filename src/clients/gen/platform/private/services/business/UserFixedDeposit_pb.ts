// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/business/UserFixedDeposit.proto (package com.stablemoney.api.business, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type {
  FdProvider,
  InvestmentStatus,
  NetWorthSummaryResponseSchema,
  UserFixedDepositSummaryRequestSchema,
  UserFixedDepositSummaryResponseSchema,
} from "../../../public/models/business/FixedDeposit_pb.js";
import { file_public_models_business_FixedDeposit } from "../../../public/models/business/FixedDeposit_pb.js";
import type {
  BankResponse,
  MaturityInstruction,
} from "../../../public/models/business/Collection_pb.js";
import { file_public_models_business_Collection } from "../../../public/models/business/Collection_pb.js";
import type { BookingStatus } from "../../../public/models/business/Investment_pb.js";
import { file_public_models_business_Investment } from "../../../public/models/business/Investment_pb.js";
import type {
  InterestPayoutType,
  RedirectDeeplink,
} from "../../../public/models/business/BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "../../../public/models/business/BusinessCommon_pb.js";
import type { FdWithdrawalCalculation } from "../../../public/models/business/BankListing_pb.js";
import { file_public_models_business_BankListing } from "../../../public/models/business/BankListing_pb.js";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/business/UserFixedDeposit.proto.
 */
export const file_private_services_business_UserFixedDeposit: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_FixedDeposit,
      file_public_models_business_Collection,
      file_public_models_business_Investment,
      file_public_models_business_BusinessCommon,
      file_public_models_business_BankListing,
      file_google_protobuf_timestamp,
    ],
  );

/**
 * @generated from message com.stablemoney.api.business.UserNetWorthByPhoneNumberRequest
 */
export type UserNetWorthByPhoneNumberRequest =
  Message<"com.stablemoney.api.business.UserNetWorthByPhoneNumberRequest"> & {
    /**
     * @generated from field: string phone_number = 1;
     */
    phoneNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.business.UserNetWorthByPhoneNumberRequest.
 * Use `create(UserNetWorthByPhoneNumberRequestSchema)` to create a new message.
 */
export const UserNetWorthByPhoneNumberRequestSchema: GenMessage<UserNetWorthByPhoneNumberRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 0);

/**
 * @generated from message com.stablemoney.api.business.UserNetWorthByUserIdRequest
 */
export type UserNetWorthByUserIdRequest =
  Message<"com.stablemoney.api.business.UserNetWorthByUserIdRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.UserNetWorthByUserIdRequest.
 * Use `create(UserNetWorthByUserIdRequestSchema)` to create a new message.
 */
export const UserNetWorthByUserIdRequestSchema: GenMessage<UserNetWorthByUserIdRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 1);

/**
 * @generated from message com.stablemoney.api.business.MyInvestmentByUserIdRequest
 */
export type MyInvestmentByUserIdRequest =
  Message<"com.stablemoney.api.business.MyInvestmentByUserIdRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.MyInvestmentByUserIdRequest.
 * Use `create(MyInvestmentByUserIdRequestSchema)` to create a new message.
 */
export const MyInvestmentByUserIdRequestSchema: GenMessage<MyInvestmentByUserIdRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 2);

/**
 * @generated from message com.stablemoney.api.business.MyInvestmentByUserIdResponse
 */
export type MyInvestmentByUserIdResponse =
  Message<"com.stablemoney.api.business.MyInvestmentByUserIdResponse"> & {
    /**
     * @generated from field: bool has_my_investments = 1;
     */
    hasMyInvestments: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.MyInvestmentByUserIdResponse.
 * Use `create(MyInvestmentByUserIdResponseSchema)` to create a new message.
 */
export const MyInvestmentByUserIdResponseSchema: GenMessage<MyInvestmentByUserIdResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 3);

/**
 * @generated from message com.stablemoney.api.business.BankAccessByUserIdRequest
 */
export type BankAccessByUserIdRequest =
  Message<"com.stablemoney.api.business.BankAccessByUserIdRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string bank_id = 2;
     */
    bankId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.BankAccessByUserIdRequest.
 * Use `create(BankAccessByUserIdRequestSchema)` to create a new message.
 */
export const BankAccessByUserIdRequestSchema: GenMessage<BankAccessByUserIdRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 4);

/**
 * @generated from message com.stablemoney.api.business.BankAccessByUserIdResponse
 */
export type BankAccessByUserIdResponse =
  Message<"com.stablemoney.api.business.BankAccessByUserIdResponse"> & {
    /**
     * @generated from field: bool has_bank_access = 1;
     */
    hasBankAccess: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.BankAccessByUserIdResponse.
 * Use `create(BankAccessByUserIdResponseSchema)` to create a new message.
 */
export const BankAccessByUserIdResponseSchema: GenMessage<BankAccessByUserIdResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 5);

/**
 * @generated from message com.stablemoney.api.business.UjjivanMISRequest
 */
export type UjjivanMISRequest =
  Message<"com.stablemoney.api.business.UjjivanMISRequest"> & {
    /**
     * @generated from field: string file_name = 1;
     */
    fileName: string;

    /**
     * @generated from field: bytes file_data = 2;
     */
    fileData: Uint8Array;

    /**
     * @generated from field: int32 sheet_index = 3;
     */
    sheetIndex: number;
  };

/**
 * Describes the message com.stablemoney.api.business.UjjivanMISRequest.
 * Use `create(UjjivanMISRequestSchema)` to create a new message.
 */
export const UjjivanMISRequestSchema: GenMessage<UjjivanMISRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 6);

/**
 * @generated from message com.stablemoney.api.business.UjjivanMISResponse
 */
export type UjjivanMISResponse =
  Message<"com.stablemoney.api.business.UjjivanMISResponse"> & {};

/**
 * Describes the message com.stablemoney.api.business.UjjivanMISResponse.
 * Use `create(UjjivanMISResponseSchema)` to create a new message.
 */
export const UjjivanMISResponseSchema: GenMessage<UjjivanMISResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 7);

/**
 * @generated from message com.stablemoney.api.business.GetInvestmentStatsRequest
 */
export type GetInvestmentStatsRequest =
  Message<"com.stablemoney.api.business.GetInvestmentStatsRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.GetInvestmentStatsRequest.
 * Use `create(GetInvestmentStatsRequestSchema)` to create a new message.
 */
export const GetInvestmentStatsRequestSchema: GenMessage<GetInvestmentStatsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 8);

/**
 * @generated from message com.stablemoney.api.business.GetInvestmentStatsResponse
 */
export type GetInvestmentStatsResponse =
  Message<"com.stablemoney.api.business.GetInvestmentStatsResponse"> & {
    /**
     * @generated from field: double total_investment_amount = 1;
     */
    totalInvestmentAmount: number;

    /**
     * @generated from field: int32 in_progress_payment_success_count = 3;
     */
    inProgressPaymentSuccessCount: number;

    /**
     * @generated from field: int32 active_booking_count = 5;
     */
    activeBookingCount: number;

    /**
     * @generated from field: int32 matured_booking_count = 7;
     */
    maturedBookingCount: number;

    /**
     * @generated from field: int32 withdrawn_booking_count = 9;
     */
    withdrawnBookingCount: number;

    /**
     * @generated from field: double days_since_first_investment = 10;
     */
    daysSinceFirstInvestment: number;

    /**
     * @generated from field: double number_of_banks_invested_in = 11;
     */
    numberOfBanksInvestedIn: number;

    /**
     * @generated from field: double net_worth = 12;
     */
    netWorth: number;
  };

/**
 * Describes the message com.stablemoney.api.business.GetInvestmentStatsResponse.
 * Use `create(GetInvestmentStatsResponseSchema)` to create a new message.
 */
export const GetInvestmentStatsResponseSchema: GenMessage<GetInvestmentStatsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 9);

/**
 * @generated from message com.stablemoney.api.business.NonCancelledBookingsRequest
 */
export type NonCancelledBookingsRequest =
  Message<"com.stablemoney.api.business.NonCancelledBookingsRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.NonCancelledBookingsRequest.
 * Use `create(NonCancelledBookingsRequestSchema)` to create a new message.
 */
export const NonCancelledBookingsRequestSchema: GenMessage<NonCancelledBookingsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 10);

/**
 * @generated from message com.stablemoney.api.business.UserFixedDeposit
 */
export type UserFixedDeposit =
  Message<"com.stablemoney.api.business.UserFixedDeposit"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string journey_id = 2;
     */
    journeyId: string;

    /**
     * @generated from field: string status = 3;
     */
    status: string;

    /**
     * @generated from field: bool booking_done = 4;
     */
    bookingDone: boolean;

    /**
     * @generated from field: double investment_amount = 5;
     */
    investmentAmount: number;

    /**
     * @generated from field: int32 tenure_in_days = 6;
     */
    tenureInDays: number;

    /**
     * @generated from field: int32 tenure_in_months = 7;
     */
    tenureInMonths: number;

    /**
     * @generated from field: string booking_date = 8;
     */
    bookingDate: string;

    /**
     * @generated from field: string maturity_date = 9;
     */
    maturityDate: string;

    /**
     * @generated from field: bool verified = 10;
     */
    verified: boolean;

    /**
     * @generated from field: string maturity_instruction = 11;
     */
    maturityInstruction: string;

    /**
     * @generated from field: string bank_name = 12;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo = 13;
     */
    bankLogo: string;

    /**
     * @generated from field: optional string bank_color = 14;
     */
    bankColor?: string;

    /**
     * @generated from field: optional string payment_status = 15;
     */
    paymentStatus?: string;

    /**
     * @generated from field: optional double interest_rate = 16;
     */
    interestRate?: number;

    /**
     * @generated from field: optional string provider = 17;
     */
    provider?: string;

    /**
     * @generated from field: optional double current_amount = 18;
     */
    currentAmount?: number;

    /**
     * @generated from field: optional string bank_id = 19;
     */
    bankId?: string;

    /**
     * @generated from field: optional string bank_icon_bg_color = 20;
     */
    bankIconBgColor?: string;

    /**
     * @generated from field: optional string bank_short_name = 21;
     */
    bankShortName?: string;

    /**
     * @generated from field: double xirr = 22;
     */
    xirr: number;
  };

/**
 * Describes the message com.stablemoney.api.business.UserFixedDeposit.
 * Use `create(UserFixedDepositSchema)` to create a new message.
 */
export const UserFixedDepositSchema: GenMessage<UserFixedDeposit> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 11);

/**
 * @generated from message com.stablemoney.api.business.NonCancelledBookingsResponse
 */
export type NonCancelledBookingsResponse =
  Message<"com.stablemoney.api.business.NonCancelledBookingsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.UserFixedDeposit user_fixed_deposits = 1;
     */
    userFixedDeposits: UserFixedDeposit[];
  };

/**
 * Describes the message com.stablemoney.api.business.NonCancelledBookingsResponse.
 * Use `create(NonCancelledBookingsResponseSchema)` to create a new message.
 */
export const NonCancelledBookingsResponseSchema: GenMessage<NonCancelledBookingsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 12);

/**
 * @generated from message com.stablemoney.api.business.FdInvestmentSummaryRequest
 */
export type FdInvestmentSummaryRequest =
  Message<"com.stablemoney.api.business.FdInvestmentSummaryRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: optional string bank_id = 2;
     */
    bankId?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdInvestmentSummaryRequest.
 * Use `create(FdInvestmentSummaryRequestSchema)` to create a new message.
 */
export const FdInvestmentSummaryRequestSchema: GenMessage<FdInvestmentSummaryRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 13);

/**
 * @generated from message com.stablemoney.api.business.FdInvestmentSummaryResponse
 */
export type FdInvestmentSummaryResponse =
  Message<"com.stablemoney.api.business.FdInvestmentSummaryResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.FdProviderInvestmentSummary fd_provider_investment_summary = 1;
     */
    fdProviderInvestmentSummary: FdProviderInvestmentSummary[];
  };

/**
 * Describes the message com.stablemoney.api.business.FdInvestmentSummaryResponse.
 * Use `create(FdInvestmentSummaryResponseSchema)` to create a new message.
 */
export const FdInvestmentSummaryResponseSchema: GenMessage<FdInvestmentSummaryResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 14);

/**
 * @generated from message com.stablemoney.api.business.FdProviderInvestmentSummary
 */
export type FdProviderInvestmentSummary =
  Message<"com.stablemoney.api.business.FdProviderInvestmentSummary"> & {
    /**
     * @generated from field: com.stablemoney.api.business.FdProvider provider = 1;
     */
    provider: FdProvider;

    /**
     * @generated from field: repeated com.stablemoney.api.business.UserFixedDeposit user_fixed_deposits = 2;
     */
    userFixedDeposits: UserFixedDeposit[];

    /**
     * @generated from field: com.stablemoney.api.business.FdProviderInvestmentStats fd_provider_investment_stats = 3;
     */
    fdProviderInvestmentStats?: FdProviderInvestmentStats;

    /**
     * @generated from field: optional string last_updated = 4;
     */
    lastUpdated?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdProviderInvestmentSummary.
 * Use `create(FdProviderInvestmentSummarySchema)` to create a new message.
 */
export const FdProviderInvestmentSummarySchema: GenMessage<FdProviderInvestmentSummary> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 15);

/**
 * @generated from message com.stablemoney.api.business.FdInvestmentDetailsRequest
 */
export type FdInvestmentDetailsRequest =
  Message<"com.stablemoney.api.business.FdInvestmentDetailsRequest"> & {
    /**
     * @generated from field: string investmentId = 1;
     */
    investmentId: string;

    /**
     * @generated from field: string userId = 2;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdInvestmentDetailsRequest.
 * Use `create(FdInvestmentDetailsRequestSchema)` to create a new message.
 */
export const FdInvestmentDetailsRequestSchema: GenMessage<FdInvestmentDetailsRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 16);

/**
 * @generated from message com.stablemoney.api.business.FdInvestmentDetailsResponse
 */
export type FdInvestmentDetailsResponse =
  Message<"com.stablemoney.api.business.FdInvestmentDetailsResponse"> & {
    /**
     * @generated from field: string investmentId = 1;
     */
    investmentId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.BookingStatus booking_status = 2;
     */
    bookingStatus: BookingStatus;

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank_response = 3;
     */
    bankResponse?: BankResponse;

    /**
     * @generated from field: optional string booking_date = 4;
     */
    bookingDate?: string;

    /**
     * @generated from field: optional string maturity_date = 5;
     */
    maturityDate?: string;

    /**
     * @generated from field: double invested_amount = 6;
     */
    investedAmount: number;

    /**
     * @generated from field: optional double interest_earned_amount = 7;
     */
    interestEarnedAmount?: number;

    /**
     * @generated from field: optional double current_amount = 8;
     */
    currentAmount?: number;

    /**
     * @generated from field: double interest_rate = 9;
     */
    interestRate: number;

    /**
     * @generated from field: string raw_tenure = 10;
     */
    rawTenure: string;

    /**
     * @generated from field: optional string fd_account_number = 11;
     */
    fdAccountNumber?: string;

    /**
     * @generated from field: com.stablemoney.api.identity.InterestPayoutType interest_payout_type = 12;
     */
    interestPayoutType: InterestPayoutType;

    /**
     * @generated from field: com.stablemoney.api.identity.MaturityInstruction maturity_instruction = 13;
     */
    maturityInstruction: MaturityInstruction;

    /**
     * @generated from field: optional string nominee_name = 14;
     */
    nomineeName?: string;

    /**
     * @generated from field: optional string nominee_relation = 15;
     */
    nomineeRelation?: string;

    /**
     * @generated from field: optional string withdrawal_bank_name = 16;
     */
    withdrawalBankName?: string;

    /**
     * @generated from field: optional string withdrawal_bank_account_number = 17;
     */
    withdrawalBankAccountNumber?: string;

    /**
     * @generated from field: optional string withdrawal_bank_ifsc = 18;
     */
    withdrawalBankIfsc?: string;

    /**
     * @generated from field: optional string savings_account_customer_id = 19;
     */
    savingsAccountCustomerId?: string;

    /**
     * @generated from field: optional string savings_account_bank_name = 20;
     */
    savingsAccountBankName?: string;

    /**
     * @generated from field: optional string savings_account_number = 21;
     */
    savingsAccountNumber?: string;

    /**
     * @generated from field: optional string savings_account_ifsc_code = 22;
     */
    savingsAccountIfscCode?: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FdWithdrawalCalculation fd_withdrawal_calculations = 23;
     */
    fdWithdrawalCalculations: FdWithdrawalCalculation[];

    /**
     * @generated from field: int32 tenure_days = 24;
     */
    tenureDays: number;

    /**
     * @generated from field: int32 tenure_month = 25;
     */
    tenureMonth: number;
  };

/**
 * Describes the message com.stablemoney.api.business.FdInvestmentDetailsResponse.
 * Use `create(FdInvestmentDetailsResponseSchema)` to create a new message.
 */
export const FdInvestmentDetailsResponseSchema: GenMessage<FdInvestmentDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 17);

/**
 * @generated from message com.stablemoney.api.business.FdProviderInvestmentStats
 */
export type FdProviderInvestmentStats =
  Message<"com.stablemoney.api.business.FdProviderInvestmentStats"> & {
    /**
     * @generated from field: int32 investment_count = 1;
     */
    investmentCount: number;

    /**
     * @generated from field: double total_invested_amount = 2;
     */
    totalInvestedAmount: number;

    /**
     * @generated from field: double total_interest_earned = 3;
     */
    totalInterestEarned: number;

    /**
     * @generated from field: double total_current_amount = 4;
     */
    totalCurrentAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.business.FdProviderInvestmentStats.
 * Use `create(FdProviderInvestmentStatsSchema)` to create a new message.
 */
export const FdProviderInvestmentStatsSchema: GenMessage<FdProviderInvestmentStats> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 18);

/**
 * @generated from message com.stablemoney.api.business.UpdateUserInvestmentStatusRequest
 */
export type UpdateUserInvestmentStatusRequest =
  Message<"com.stablemoney.api.business.UpdateUserInvestmentStatusRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string bank_id = 2;
     */
    bankId: string;

    /**
     * @generated from field: string investment_id = 3;
     */
    investmentId: string;

    /**
     * @generated from field: google.protobuf.Timestamp investment_date_time = 4;
     */
    investmentDateTime?: Timestamp;

    /**
     * @generated from field: com.stablemoney.api.business.InvestmentStatus investment_status = 5;
     */
    investmentStatus: InvestmentStatus;

    /**
     * @generated from field: bool mark_user_etb = 6;
     */
    markUserEtb: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.UpdateUserInvestmentStatusRequest.
 * Use `create(UpdateUserInvestmentStatusRequestSchema)` to create a new message.
 */
export const UpdateUserInvestmentStatusRequestSchema: GenMessage<UpdateUserInvestmentStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 19);

/**
 * @generated from message com.stablemoney.api.business.UpdateUserInvestmentResponse
 */
export type UpdateUserInvestmentResponse =
  Message<"com.stablemoney.api.business.UpdateUserInvestmentResponse"> & {};

/**
 * Describes the message com.stablemoney.api.business.UpdateUserInvestmentResponse.
 * Use `create(UpdateUserInvestmentResponseSchema)` to create a new message.
 */
export const UpdateUserInvestmentResponseSchema: GenMessage<UpdateUserInvestmentResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 20);

/**
 * @generated from message com.stablemoney.api.business.CanUserBeDeletedRequest
 */
export type CanUserBeDeletedRequest =
  Message<"com.stablemoney.api.business.CanUserBeDeletedRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.CanUserBeDeletedRequest.
 * Use `create(CanUserBeDeletedRequestSchema)` to create a new message.
 */
export const CanUserBeDeletedRequestSchema: GenMessage<CanUserBeDeletedRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 21);

/**
 * @generated from message com.stablemoney.api.business.CanUserBeDeletedResponse
 */
export type CanUserBeDeletedResponse =
  Message<"com.stablemoney.api.business.CanUserBeDeletedResponse"> & {
    /**
     * @generated from field: bool can_user_be_deleted = 1;
     */
    canUserBeDeleted: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.CanUserBeDeletedResponse.
 * Use `create(CanUserBeDeletedResponseSchema)` to create a new message.
 */
export const CanUserBeDeletedResponseSchema: GenMessage<CanUserBeDeletedResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 22);

/**
 * @generated from message com.stablemoney.api.business.GetBankBannerRecommendationRequest
 */
export type GetBankBannerRecommendationRequest =
  Message<"com.stablemoney.api.business.GetBankBannerRecommendationRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: int32 app_version = 2;
     */
    appVersion: number;

    /**
     * @generated from field: int32 count = 3;
     */
    count: number;
  };

/**
 * Describes the message com.stablemoney.api.business.GetBankBannerRecommendationRequest.
 * Use `create(GetBankBannerRecommendationRequestSchema)` to create a new message.
 */
export const GetBankBannerRecommendationRequestSchema: GenMessage<GetBankBannerRecommendationRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 23);

/**
 * @generated from message com.stablemoney.api.business.GetBankBannerRecommendationsResponse
 */
export type GetBankBannerRecommendationsResponse =
  Message<"com.stablemoney.api.business.GetBankBannerRecommendationsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.GetBankBannerRecommendationsResponse.RecommendedBankBanner recommended_bank_banners = 1;
     */
    recommendedBankBanners: GetBankBannerRecommendationsResponse_RecommendedBankBanner[];
  };

/**
 * Describes the message com.stablemoney.api.business.GetBankBannerRecommendationsResponse.
 * Use `create(GetBankBannerRecommendationsResponseSchema)` to create a new message.
 */
export const GetBankBannerRecommendationsResponseSchema: GenMessage<GetBankBannerRecommendationsResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 24);

/**
 * @generated from message com.stablemoney.api.business.GetBankBannerRecommendationsResponse.RecommendedBankBanner
 */
export type GetBankBannerRecommendationsResponse_RecommendedBankBanner =
  Message<"com.stablemoney.api.business.GetBankBannerRecommendationsResponse.RecommendedBankBanner"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 4;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: string banner_url = 6;
     */
    bannerUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.business.GetBankBannerRecommendationsResponse.RecommendedBankBanner.
 * Use `create(GetBankBannerRecommendationsResponse_RecommendedBankBannerSchema)` to create a new message.
 */
export const GetBankBannerRecommendationsResponse_RecommendedBankBannerSchema: GenMessage<GetBankBannerRecommendationsResponse_RecommendedBankBanner> =
  /*@__PURE__*/
  messageDesc(file_private_services_business_UserFixedDeposit, 24, 0);

/**
 * @generated from service com.stablemoney.api.business.UserFixedDepositService
 */
export const UserFixedDepositService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getUserNetWorthData
   * @deprecated
   */
  getUserNetWorthData: {
    methodKind: "unary";
    input: typeof UserNetWorthByPhoneNumberRequestSchema;
    output: typeof NetWorthSummaryResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getLastUserNetWorthData
   * @deprecated
   */
  getLastUserNetWorthData: {
    methodKind: "unary";
    input: typeof UserNetWorthByUserIdRequestSchema;
    output: typeof NetWorthSummaryResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getMyInvestment
   */
  getMyInvestment: {
    methodKind: "unary";
    input: typeof MyInvestmentByUserIdRequestSchema;
    output: typeof MyInvestmentByUserIdResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getBankAccess
   */
  getBankAccess: {
    methodKind: "unary";
    input: typeof BankAccessByUserIdRequestSchema;
    output: typeof BankAccessByUserIdResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.postUpdateUserInvestment
   */
  postUpdateUserInvestment: {
    methodKind: "unary";
    input: typeof UpdateUserInvestmentStatusRequestSchema;
    output: typeof UpdateUserInvestmentResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getNonCancelledUserBookings
   */
  getNonCancelledUserBookings: {
    methodKind: "unary";
    input: typeof NonCancelledBookingsRequestSchema;
    output: typeof NonCancelledBookingsResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getFdInvestmentSummary
   */
  getFdInvestmentSummary: {
    methodKind: "unary";
    input: typeof FdInvestmentSummaryRequestSchema;
    output: typeof FdInvestmentSummaryResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getFdInvestmentDetails
   */
  getFdInvestmentDetails: {
    methodKind: "unary";
    input: typeof FdInvestmentDetailsRequestSchema;
    output: typeof FdInvestmentDetailsResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.canUserBeDeleted
   */
  canUserBeDeleted: {
    methodKind: "unary";
    input: typeof CanUserBeDeletedRequestSchema;
    output: typeof CanUserBeDeletedResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getUserNetWorth
   */
  getUserNetWorth: {
    methodKind: "unary";
    input: typeof UserFixedDepositSummaryRequestSchema;
    output: typeof UserFixedDepositSummaryResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getInvestmentStats
   */
  getInvestmentStats: {
    methodKind: "unary";
    input: typeof GetInvestmentStatsRequestSchema;
    output: typeof GetInvestmentStatsResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.business.UserFixedDepositService.getBankBannerRecommendation
   */
  getBankBannerRecommendation: {
    methodKind: "unary";
    input: typeof GetBankBannerRecommendationRequestSchema;
    output: typeof GetBankBannerRecommendationsResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_business_UserFixedDeposit,
  0,
);
