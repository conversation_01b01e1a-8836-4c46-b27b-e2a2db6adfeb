// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/notifications/NotificationCommon.proto (package com.stablemoney.api.notifications, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  enumDesc,
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/notifications/NotificationCommon.proto.
 */
export const file_private_services_notifications_NotificationCommon: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.notifications.SendNotificationRequest
 */
export type SendNotificationRequest =
  Message<"com.stablemoney.api.notifications.SendNotificationRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string notification_name = 2;
     */
    notificationName: string;

    /**
     * @generated from field: optional com.stablemoney.api.notifications.OtpRequest otp_request = 3;
     */
    otpRequest?: OtpRequest;

    /**
     * @generated from field: optional int32 retry_count = 4;
     */
    retryCount?: number;

    /**
     * @generated from field: optional string reward_link = 5;
     */
    rewardLink?: string;

    /**
     * @generated from field: optional string referral_link = 6;
     */
    referralLink?: string;

    /**
     * @generated from field: optional string failed_provider = 7;
     */
    failedProvider?: string;

    /**
     * @generated from field: optional string referee_name = 8;
     */
    refereeName?: string;

    /**
     * @generated from field: optional double amount = 9;
     */
    amount?: number;

    /**
     * @generated from field: optional string bank_name = 10;
     */
    bankName?: string;

    /**
     * @generated from field: optional string referral_percent = 11;
     */
    referralPercent?: string;

    /**
     * @generated from field: optional double minimum_reward_amount = 12;
     */
    minimumRewardAmount?: number;

    /**
     * @generated from field: optional double investment_amount = 13;
     */
    investmentAmount?: number;

    /**
     * @generated from field: optional string payout_link = 14;
     */
    payoutLink?: string;

    /**
     * @generated from field: optional string l2_referee_name = 15;
     */
    l2RefereeName?: string;

    /**
     * @generated from field: optional string l3_referee_name = 16;
     */
    l3RefereeName?: string;

    /**
     * @generated from field: optional int32 total_referrals = 17;
     */
    totalReferrals?: number;

    /**
     * @generated from field: optional int32 total_bookings = 18;
     */
    totalBookings?: number;

    /**
     * @generated from field: optional double amount_won = 19;
     */
    amountWon?: number;

    /**
     * @generated from field: optional com.stablemoney.api.notifications.ProfileCompletionEmailRequest profile_completion_request = 20;
     */
    profileCompletionRequest?: ProfileCompletionEmailRequest;

    /**
     * @generated from field: optional string bank_image_link = 21;
     */
    bankImageLink?: string;

    /**
     * @generated from field: optional string date_of_booking = 22;
     */
    dateOfBooking?: string;

    /**
     * @generated from field: optional string submit_link = 23;
     */
    submitLink?: string;

    /**
     * @generated from field: optional string investment_amount_string = 24;
     */
    investmentAmountString?: string;

    /**
     * @generated from field: optional com.stablemoney.api.notifications.VkycInviteRequest vkyc_invite_request = 25;
     */
    vkycInviteRequest?: VkycInviteRequest;

    /**
     * @generated from field: optional com.stablemoney.api.notifications.RedirectDeeplink redirect_deeplink = 26;
     */
    redirectDeeplink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.notifications.SendNotificationRequest.
 * Use `create(SendNotificationRequestSchema)` to create a new message.
 */
export const SendNotificationRequestSchema: GenMessage<SendNotificationRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 0);

/**
 * @generated from message com.stablemoney.api.notifications.RedirectDeeplink
 */
export type RedirectDeeplink =
  Message<"com.stablemoney.api.notifications.RedirectDeeplink"> & {
    /**
     * @generated from field: string path = 1;
     */
    path: string;

    /**
     * @generated from field: string path_type = 2;
     */
    pathType: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.RedirectDeeplink.
 * Use `create(RedirectDeeplinkSchema)` to create a new message.
 */
export const RedirectDeeplinkSchema: GenMessage<RedirectDeeplink> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 1);

/**
 * @generated from message com.stablemoney.api.notifications.VkycInviteRequest
 */
export type VkycInviteRequest =
  Message<"com.stablemoney.api.notifications.VkycInviteRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string dtstamp = 2;
     */
    dtstamp: string;

    /**
     * @generated from field: string dtstart = 3;
     */
    dtstart: string;

    /**
     * @generated from field: string dtend = 4;
     */
    dtend: string;

    /**
     * @generated from field: string bank_name = 5;
     */
    bankName: string;

    /**
     * @generated from field: string sequence = 6;
     */
    sequence: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.VkycInviteRequest.
 * Use `create(VkycInviteRequestSchema)` to create a new message.
 */
export const VkycInviteRequestSchema: GenMessage<VkycInviteRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 2);

/**
 * @generated from message com.stablemoney.api.notifications.ProfileCompletionEmailRequest
 */
export type ProfileCompletionEmailRequest =
  Message<"com.stablemoney.api.notifications.ProfileCompletionEmailRequest"> & {
    /**
     * @generated from field: bool is_large_city = 1;
     */
    isLargeCity: boolean;

    /**
     * @generated from field: int32 user_city_count = 2;
     */
    userCityCount: number;

    /**
     * @generated from field: string ordinal_suffix = 3;
     */
    ordinalSuffix: string;

    /**
     * @generated from field: string city_name = 4;
     */
    cityName: string;

    /**
     * @generated from field: string first_name = 5;
     */
    firstName: string;

    /**
     * @generated from field: string last_name = 6;
     */
    lastName: string;

    /**
     * @generated from field: int32 national_average_cost_of_living = 7;
     */
    nationalAverageCostOfLiving: number;

    /**
     * @generated from field: int32 user_city_cost_of_living = 8;
     */
    userCityCostOfLiving: number;

    /**
     * @generated from field: bool has_fd = 9;
     */
    hasFd: boolean;

    /**
     * @generated from field: string formatted_city_count = 10;
     */
    formattedCityCount: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.ProfileCompletionEmailRequest.
 * Use `create(ProfileCompletionEmailRequestSchema)` to create a new message.
 */
export const ProfileCompletionEmailRequestSchema: GenMessage<ProfileCompletionEmailRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 3);

/**
 * @generated from message com.stablemoney.api.notifications.OtpRequest
 */
export type OtpRequest =
  Message<"com.stablemoney.api.notifications.OtpRequest"> & {
    /**
     * @generated from field: string otp = 1;
     */
    otp: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.OtpRequest.
 * Use `create(OtpRequestSchema)` to create a new message.
 */
export const OtpRequestSchema: GenMessage<OtpRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 4);

/**
 * @generated from message com.stablemoney.api.notifications.SendNotificationResponse
 */
export type SendNotificationResponse =
  Message<"com.stablemoney.api.notifications.SendNotificationResponse"> & {};

/**
 * Describes the message com.stablemoney.api.notifications.SendNotificationResponse.
 * Use `create(SendNotificationResponseSchema)` to create a new message.
 */
export const SendNotificationResponseSchema: GenMessage<SendNotificationResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 5);

/**
 * @generated from message com.stablemoney.api.notifications.GetInvestedUsersResponse
 */
export type GetInvestedUsersResponse =
  Message<"com.stablemoney.api.notifications.GetInvestedUsersResponse"> & {
    /**
     * @generated from field: repeated string user_ids = 1;
     */
    userIds: string[];
  };

/**
 * Describes the message com.stablemoney.api.notifications.GetInvestedUsersResponse.
 * Use `create(GetInvestedUsersResponseSchema)` to create a new message.
 */
export const GetInvestedUsersResponseSchema: GenMessage<GetInvestedUsersResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 6);

/**
 * @generated from message com.stablemoney.api.notifications.GetInvestedUsersRequest
 */
export type GetInvestedUsersRequest =
  Message<"com.stablemoney.api.notifications.GetInvestedUsersRequest"> & {
    /**
     * @generated from field: int32 page = 1;
     */
    page: number;

    /**
     * @generated from field: int32 size = 2;
     */
    size: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.GetInvestedUsersRequest.
 * Use `create(GetInvestedUsersRequestSchema)` to create a new message.
 */
export const GetInvestedUsersRequestSchema: GenMessage<GetInvestedUsersRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 7);

/**
 * @generated from message com.stablemoney.api.notifications.WhatsappOptinRequest
 */
export type WhatsappOptinRequest =
  Message<"com.stablemoney.api.notifications.WhatsappOptinRequest"> & {
    /**
     * @generated from field: string mobile_number = 1;
     */
    mobileNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.WhatsappOptinRequest.
 * Use `create(WhatsappOptinRequestSchema)` to create a new message.
 */
export const WhatsappOptinRequestSchema: GenMessage<WhatsappOptinRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 8);

/**
 * @generated from message com.stablemoney.api.notifications.WhatsappOptinResponse
 */
export type WhatsappOptinResponse =
  Message<"com.stablemoney.api.notifications.WhatsappOptinResponse"> & {};

/**
 * Describes the message com.stablemoney.api.notifications.WhatsappOptinResponse.
 * Use `create(WhatsappOptinResponseSchema)` to create a new message.
 */
export const WhatsappOptinResponseSchema: GenMessage<WhatsappOptinResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 9);

/**
 * @generated from message com.stablemoney.api.notifications.NotificationStatus
 */
export type NotificationStatus =
  Message<"com.stablemoney.api.notifications.NotificationStatus"> & {
    /**
     * @generated from field: string notification_id = 1;
     */
    notificationId: string;

    /**
     * @generated from field: string user_id = 2;
     */
    userId: string;

    /**
     * @generated from field: string notification_name = 3;
     */
    notificationName: string;

    /**
     * @generated from field: com.stablemoney.api.notifications.NotificationChannelType channel_type = 4;
     */
    channelType: NotificationChannelType;

    /**
     * @generated from field: com.stablemoney.api.notifications.NotificationStatusEnum notificationStatus = 5;
     */
    notificationStatus: NotificationStatusEnum;
  };

/**
 * Describes the message com.stablemoney.api.notifications.NotificationStatus.
 * Use `create(NotificationStatusSchema)` to create a new message.
 */
export const NotificationStatusSchema: GenMessage<NotificationStatus> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_NotificationCommon, 10);

/**
 * @generated from enum com.stablemoney.api.notifications.NotificationChannelType
 */
export enum NotificationChannelType {
  /**
   * @generated from enum value: UNKNOWN_CHANNEL = 0;
   */
  UNKNOWN_CHANNEL = 0,

  /**
   * @generated from enum value: EMAIL = 1;
   */
  EMAIL = 1,

  /**
   * @generated from enum value: SMS = 2;
   */
  SMS = 2,

  /**
   * @generated from enum value: PUSH = 3;
   */
  PUSH = 3,

  /**
   * @generated from enum value: WHATSAPP = 4;
   */
  WHATSAPP = 4,
}

/**
 * Describes the enum com.stablemoney.api.notifications.NotificationChannelType.
 */
export const NotificationChannelTypeSchema: GenEnum<NotificationChannelType> =
  /*@__PURE__*/
  enumDesc(file_private_services_notifications_NotificationCommon, 0);

/**
 * @generated from enum com.stablemoney.api.notifications.TemplateType
 */
export enum TemplateType {
  /**
   * @generated from enum value: UNKNOWN_TEMPLATE = 0;
   */
  UNKNOWN_TEMPLATE = 0,

  /**
   * @generated from enum value: OTP = 1;
   */
  OTP = 1,

  /**
   * @generated from enum value: TRANSACTIONAL = 2;
   */
  TRANSACTIONAL = 2,

  /**
   * @generated from enum value: PROMOTIONAL = 3;
   */
  PROMOTIONAL = 3,
}

/**
 * Describes the enum com.stablemoney.api.notifications.TemplateType.
 */
export const TemplateTypeSchema: GenEnum<TemplateType> =
  /*@__PURE__*/
  enumDesc(file_private_services_notifications_NotificationCommon, 1);

/**
 * @generated from enum com.stablemoney.api.notifications.WhatsappMessageType
 */
export enum WhatsappMessageType {
  /**
   * @generated from enum value: UNKNOWN_TYPE = 0;
   */
  UNKNOWN_TYPE = 0,

  /**
   * @generated from enum value: TEXT = 1;
   */
  TEXT = 1,

  /**
   * @generated from enum value: IMAGE = 2;
   */
  IMAGE = 2,

  /**
   * @generated from enum value: TEXT_WITHOUT_CTA = 3;
   */
  TEXT_WITHOUT_CTA = 3,

  /**
   * @generated from enum value: IMAGE_WITHOUT_CTA = 4;
   */
  IMAGE_WITHOUT_CTA = 4,
}

/**
 * Describes the enum com.stablemoney.api.notifications.WhatsappMessageType.
 */
export const WhatsappMessageTypeSchema: GenEnum<WhatsappMessageType> =
  /*@__PURE__*/
  enumDesc(file_private_services_notifications_NotificationCommon, 2);

/**
 * @generated from enum com.stablemoney.api.notifications.WhatsappMethodType
 */
export enum WhatsappMethodType {
  /**
   * @generated from enum value: UNKNOWN_METHOD_TYPE = 0;
   */
  UNKNOWN_METHOD_TYPE = 0,

  /**
   * @generated from enum value: SendMessage = 1;
   */
  SendMessage = 1,

  /**
   * @generated from enum value: SENDMEDIAMESSAGE = 2;
   */
  SENDMEDIAMESSAGE = 2,
}

/**
 * Describes the enum com.stablemoney.api.notifications.WhatsappMethodType.
 */
export const WhatsappMethodTypeSchema: GenEnum<WhatsappMethodType> =
  /*@__PURE__*/
  enumDesc(file_private_services_notifications_NotificationCommon, 3);

/**
 * @generated from enum com.stablemoney.api.notifications.NotificationStatusEnum
 */
export enum NotificationStatusEnum {
  /**
   * @generated from enum value: UNKNOWN_STATUS = 0;
   */
  UNKNOWN_STATUS = 0,

  /**
   * @generated from enum value: ADDED_TO_QUEUE = 1;
   */
  ADDED_TO_QUEUE = 1,

  /**
   * @generated from enum value: SENT = 2;
   */
  SENT = 2,

  /**
   * @generated from enum value: FAILED = 3;
   */
  FAILED = 3,
}

/**
 * Describes the enum com.stablemoney.api.notifications.NotificationStatusEnum.
 */
export const NotificationStatusEnumSchema: GenEnum<NotificationStatusEnum> =
  /*@__PURE__*/
  enumDesc(file_private_services_notifications_NotificationCommon, 4);

/**
 * @generated from enum com.stablemoney.api.notifications.NotificationHandlerType
 */
export enum NotificationHandlerType {
  /**
   * @generated from enum value: UNKNOWN_HANDLER = 0;
   */
  UNKNOWN_HANDLER = 0,

  /**
   * @generated from enum value: STATIC_CONTENT_HANDLER = 1;
   */
  STATIC_CONTENT_HANDLER = 1,

  /**
   * @generated from enum value: USER_DATA_HANDLER = 2;
   */
  USER_DATA_HANDLER = 2,

  /**
   * @generated from enum value: OTP_HANDLER = 3;
   */
  OTP_HANDLER = 3,

  /**
   * @generated from enum value: OTP_AND_USER_DATA_HANDLER = 4;
   */
  OTP_AND_USER_DATA_HANDLER = 4,

  /**
   * @generated from enum value: REWARD_HANDLER = 5;
   */
  REWARD_HANDLER = 5,

  /**
   * @generated from enum value: PROFILE_COMPLETION_HANDLER = 6;
   */
  PROFILE_COMPLETION_HANDLER = 6,

  /**
   * @generated from enum value: VKYC_INVITE_HANDLER = 7;
   */
  VKYC_INVITE_HANDLER = 7,
}

/**
 * Describes the enum com.stablemoney.api.notifications.NotificationHandlerType.
 */
export const NotificationHandlerTypeSchema: GenEnum<NotificationHandlerType> =
  /*@__PURE__*/
  enumDesc(file_private_services_notifications_NotificationCommon, 5);

/**
 * @generated from enum com.stablemoney.api.notifications.AttachmentType
 */
export enum AttachmentType {
  /**
   * @generated from enum value: UNKNOWN_ATTACHMENT_TYPE = 0;
   */
  UNKNOWN_ATTACHMENT_TYPE = 0,

  /**
   * @generated from enum value: ICS_ATTACHMENT_TYPE = 1;
   */
  ICS_ATTACHMENT_TYPE = 1,
}

/**
 * Describes the enum com.stablemoney.api.notifications.AttachmentType.
 */
export const AttachmentTypeSchema: GenEnum<AttachmentType> =
  /*@__PURE__*/
  enumDesc(file_private_services_notifications_NotificationCommon, 6);

/**
 * @generated from service com.stablemoney.api.notifications.NotificationService
 */
export const NotificationService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.notifications.NotificationService.sendNotification
   */
  sendNotification: {
    methodKind: "unary";
    input: typeof SendNotificationRequestSchema;
    output: typeof SendNotificationResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.NotificationService.whatsappOptin
   */
  whatsappOptin: {
    methodKind: "unary";
    input: typeof WhatsappOptinRequestSchema;
    output: typeof WhatsappOptinResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.NotificationService.getInvestedUsers
   */
  getInvestedUsers: {
    methodKind: "unary";
    input: typeof GetInvestedUsersRequestSchema;
    output: typeof GetInvestedUsersResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_notifications_NotificationCommon,
  0,
);
