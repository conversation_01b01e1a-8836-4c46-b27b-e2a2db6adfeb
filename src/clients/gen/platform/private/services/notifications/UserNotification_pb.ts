// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file private/services/notifications/UserNotification.proto (package com.stablemoney.api.notifications, syntax proto3)

import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv2";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv2";
import type { EmptySchema } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_empty } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file private/services/notifications/UserNotification.proto.
 */
export const file_private_services_notifications_UserNotification: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [file_google_protobuf_empty],
  );

/**
 * @generated from message com.stablemoney.api.notifications.UserNotificationRequest
 */
export type UserNotificationRequest =
  Message<"com.stablemoney.api.notifications.UserNotificationRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserNotificationRequest.
 * Use `create(UserNotificationRequestSchema)` to create a new message.
 */
export const UserNotificationRequestSchema: GenMessage<UserNotificationRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 0);

/**
 * @generated from message com.stablemoney.api.notifications.UserNotificationEventRequest
 */
export type UserNotificationEventRequest =
  Message<"com.stablemoney.api.notifications.UserNotificationEventRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string event_type = 2;
     */
    eventType: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserNotificationEventRequest.
 * Use `create(UserNotificationEventRequestSchema)` to create a new message.
 */
export const UserNotificationEventRequestSchema: GenMessage<UserNotificationEventRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 1);

/**
 * @generated from message com.stablemoney.api.notifications.UserNotificationEventRequestPerBank
 */
export type UserNotificationEventRequestPerBank =
  Message<"com.stablemoney.api.notifications.UserNotificationEventRequestPerBank"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string event_type = 2;
     */
    eventType: string;

    /**
     * @generated from field: string bank_name = 3;
     */
    bankName: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserNotificationEventRequestPerBank.
 * Use `create(UserNotificationEventRequestPerBankSchema)` to create a new message.
 */
export const UserNotificationEventRequestPerBankSchema: GenMessage<UserNotificationEventRequestPerBank> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 2);

/**
 * @generated from message com.stablemoney.api.notifications.UserEventCountResponse
 */
export type UserEventCountResponse =
  Message<"com.stablemoney.api.notifications.UserEventCountResponse"> & {
    /**
     * @generated from field: int32 count = 1;
     */
    count: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserEventCountResponse.
 * Use `create(UserEventCountResponseSchema)` to create a new message.
 */
export const UserEventCountResponseSchema: GenMessage<UserEventCountResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 3);

/**
 * @generated from message com.stablemoney.api.notifications.UserNotificationGrpcResponse
 */
export type UserNotificationGrpcResponse =
  Message<"com.stablemoney.api.notifications.UserNotificationGrpcResponse"> & {
    /**
     * @generated from field: int32 count = 1;
     */
    count: number;

    /**
     * @generated from field: string latest_event_type = 2;
     */
    latestEventType: string;

    /**
     * @generated from field: string latest_event_time = 3;
     */
    latestEventTime: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserNotificationGrpcResponse.
 * Use `create(UserNotificationGrpcResponseSchema)` to create a new message.
 */
export const UserNotificationGrpcResponseSchema: GenMessage<UserNotificationGrpcResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 4);

/**
 * @generated from message com.stablemoney.api.notifications.UserBookingCountResponse
 */
export type UserBookingCountResponse =
  Message<"com.stablemoney.api.notifications.UserBookingCountResponse"> & {
    /**
     * @generated from field: int32 count = 1;
     */
    count: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserBookingCountResponse.
 * Use `create(UserBookingCountResponseSchema)` to create a new message.
 */
export const UserBookingCountResponseSchema: GenMessage<UserBookingCountResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 5);

/**
 * @generated from message com.stablemoney.api.notifications.UserTDResumeCountResponse
 */
export type UserTDResumeCountResponse =
  Message<"com.stablemoney.api.notifications.UserTDResumeCountResponse"> & {
    /**
     * @generated from field: int32 count = 1;
     */
    count: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserTDResumeCountResponse.
 * Use `create(UserTDResumeCountResponseSchema)` to create a new message.
 */
export const UserTDResumeCountResponseSchema: GenMessage<UserTDResumeCountResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 6);

/**
 * @generated from message com.stablemoney.api.notifications.UserTDPaymentSuccessCountResponse
 */
export type UserTDPaymentSuccessCountResponse =
  Message<"com.stablemoney.api.notifications.UserTDPaymentSuccessCountResponse"> & {
    /**
     * @generated from field: int32 count = 1;
     */
    count: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserTDPaymentSuccessCountResponse.
 * Use `create(UserTDPaymentSuccessCountResponseSchema)` to create a new message.
 */
export const UserTDPaymentSuccessCountResponseSchema: GenMessage<UserTDPaymentSuccessCountResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 7);

/**
 * @generated from message com.stablemoney.api.notifications.UserAllFdStatusRequest
 */
export type UserAllFdStatusRequest =
  Message<"com.stablemoney.api.notifications.UserAllFdStatusRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserAllFdStatusRequest.
 * Use `create(UserAllFdStatusRequestSchema)` to create a new message.
 */
export const UserAllFdStatusRequestSchema: GenMessage<UserAllFdStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 8);

/**
 * @generated from message com.stablemoney.api.notifications.FdEventData
 */
export type FdEventData =
  Message<"com.stablemoney.api.notifications.FdEventData"> & {
    /**
     * @generated from field: string transaction_id = 1;
     */
    transactionId: string;

    /**
     * @generated from field: string device_token = 3;
     */
    deviceToken: string;

    /**
     * @generated from field: string title = 4;
     */
    title: string;

    /**
     * @generated from field: string body = 5;
     */
    body: string;

    /**
     * @generated from field: string link = 6;
     */
    link: string;

    /**
     * @generated from field: string page_html = 7;
     */
    pageHtml: string;

    /**
     * @generated from field: string bank_name = 8;
     */
    bankName: string;

    /**
     * @generated from field: string page_url = 9;
     */
    pageUrl: string;

    /**
     * @generated from field: double maturity_amount = 10;
     */
    maturityAmount: number;

    /**
     * @generated from field: string salutation = 11;
     */
    salutation: string;

    /**
     * @generated from field: string user_name = 12;
     */
    userName: string;

    /**
     * @generated from field: string gender = 13;
     */
    gender: string;

    /**
     * @generated from field: string date_of_birth = 14;
     */
    dateOfBirth: string;

    /**
     * @generated from field: string email_address = 15;
     */
    emailAddress: string;

    /**
     * @generated from field: string address = 16;
     */
    address: string;

    /**
     * @generated from field: double interest_amount = 17;
     */
    interestAmount: number;

    /**
     * @generated from field: double interest_rate = 18;
     */
    interestRate: number;

    /**
     * @generated from field: string maturity_date = 19;
     */
    maturityDate: string;

    /**
     * @generated from field: double investment_amount = 20;
     */
    investmentAmount: number;

    /**
     * @generated from field: string account_type = 21;
     */
    accountType: string;

    /**
     * @generated from field: string interest_payout_type = 22;
     */
    interestPayoutType: string;

    /**
     * @generated from field: string tenure = 23;
     */
    tenure: string;

    /**
     * @generated from field: string account_holder_name = 24;
     */
    accountHolderName: string;

    /**
     * @generated from field: string bank_account_number = 25;
     */
    bankAccountNumber: string;

    /**
     * @generated from field: string ifsc_code = 26;
     */
    ifscCode: string;

    /**
     * @generated from field: string nominee_name = 27;
     */
    nomineeName: string;

    /**
     * @generated from field: string nominee_relationship = 28;
     */
    nomineeRelationship: string;

    /**
     * @generated from field: string nominee_date_of_birth = 29;
     */
    nomineeDateOfBirth: string;

    /**
     * @generated from field: bool is_tax_saver = 30;
     */
    isTaxSaver: boolean;
  };

/**
 * Describes the message com.stablemoney.api.notifications.FdEventData.
 * Use `create(FdEventDataSchema)` to create a new message.
 */
export const FdEventDataSchema: GenMessage<FdEventData> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 9);

/**
 * @generated from message com.stablemoney.api.notifications.FdNotificationRequest
 */
export type FdNotificationRequest =
  Message<"com.stablemoney.api.notifications.FdNotificationRequest"> & {
    /**
     * @generated from field: string event_type = 1;
     */
    eventType: string;

    /**
     * @generated from field: optional string user_id = 2;
     */
    userId?: string;

    /**
     * @generated from field: repeated com.stablemoney.api.notifications.FdEventData channel_data = 3;
     */
    channelData: FdEventData[];

    /**
     * @generated from field: optional string bank_id = 4;
     */
    bankId?: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.FdNotificationRequest.
 * Use `create(FdNotificationRequestSchema)` to create a new message.
 */
export const FdNotificationRequestSchema: GenMessage<FdNotificationRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 10);

/**
 * @generated from message com.stablemoney.api.notifications.UserAllFdStatusResponse
 */
export type UserAllFdStatusResponse =
  Message<"com.stablemoney.api.notifications.UserAllFdStatusResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.notifications.FdStatus fd_statuses = 2;
     */
    fdStatuses: FdStatus[];

    /**
     * @generated from field: optional bool hasPaymentSuccessInLast24h = 3;
     */
    hasPaymentSuccessInLast24h?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserAllFdStatusResponse.
 * Use `create(UserAllFdStatusResponseSchema)` to create a new message.
 */
export const UserAllFdStatusResponseSchema: GenMessage<UserAllFdStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 11);

/**
 * @generated from message com.stablemoney.api.notifications.UserFdEventByIdRequest
 */
export type UserFdEventByIdRequest =
  Message<"com.stablemoney.api.notifications.UserFdEventByIdRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string pnl_id = 2;
     */
    pnlId: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserFdEventByIdRequest.
 * Use `create(UserFdEventByIdRequestSchema)` to create a new message.
 */
export const UserFdEventByIdRequestSchema: GenMessage<UserFdEventByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 12);

/**
 * @generated from message com.stablemoney.api.notifications.UserFdEventByIdResponse
 */
export type UserFdEventByIdResponse =
  Message<"com.stablemoney.api.notifications.UserFdEventByIdResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string pnl_id = 2;
     */
    pnlId: string;

    /**
     * @generated from field: com.stablemoney.api.notifications.FdStatus fd_statuses = 3;
     */
    fdStatuses?: FdStatus;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserFdEventByIdResponse.
 * Use `create(UserFdEventByIdResponseSchema)` to create a new message.
 */
export const UserFdEventByIdResponseSchema: GenMessage<UserFdEventByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 13);

/**
 * @generated from message com.stablemoney.api.notifications.FdStatus
 */
export type FdStatus = Message<"com.stablemoney.api.notifications.FdStatus"> & {
  /**
   * @generated from field: string fd_id = 1;
   */
  fdId: string;

  /**
   * @generated from field: string fd_status = 2;
   */
  fdStatus: string;

  /**
   * @generated from field: string payment_success_date = 3;
   */
  paymentSuccessDate: string;

  /**
   * @generated from field: string latest_event_date = 4;
   */
  latestEventDate: string;

  /**
   * @generated from field: double fd_amount = 5;
   */
  fdAmount: number;

  /**
   * @generated from field: string latest_status = 6;
   */
  latestStatus: string;

  /**
   * @generated from field: string bank_name = 7;
   */
  bankName: string;

  /**
   * @generated from field: string id = 8;
   */
  id: string;
};

/**
 * Describes the message com.stablemoney.api.notifications.FdStatus.
 * Use `create(FdStatusSchema)` to create a new message.
 */
export const FdStatusSchema: GenMessage<FdStatus> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 14);

/**
 * @generated from message com.stablemoney.api.notifications.MixPanelNetworthRequest
 */
export type MixPanelNetworthRequest =
  Message<"com.stablemoney.api.notifications.MixPanelNetworthRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: double networth = 2;
     */
    networth: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.MixPanelNetworthRequest.
 * Use `create(MixPanelNetworthRequestSchema)` to create a new message.
 */
export const MixPanelNetworthRequestSchema: GenMessage<MixPanelNetworthRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 15);

/**
 * @generated from message com.stablemoney.api.notifications.MixpanelCreatedAtRequest
 */
export type MixpanelCreatedAtRequest =
  Message<"com.stablemoney.api.notifications.MixpanelCreatedAtRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string created_at = 2;
     */
    createdAt: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.MixpanelCreatedAtRequest.
 * Use `create(MixpanelCreatedAtRequestSchema)` to create a new message.
 */
export const MixpanelCreatedAtRequestSchema: GenMessage<MixpanelCreatedAtRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 16);

/**
 * @generated from message com.stablemoney.api.notifications.MixPanelEventRequest
 */
export type MixPanelEventRequest =
  Message<"com.stablemoney.api.notifications.MixPanelEventRequest"> & {
    /**
     * @generated from field: string event = 1;
     */
    event: string;

    /**
     * @generated from field: string userId = 2;
     */
    userId: string;

    /**
     * @generated from field: string key = 3;
     */
    key: string;

    /**
     * @generated from field: string value = 4;
     */
    value: string;

    /**
     * @generated from field: optional string referee_name = 5;
     */
    refereeName?: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.MixPanelEventRequest.
 * Use `create(MixPanelEventRequestSchema)` to create a new message.
 */
export const MixPanelEventRequestSchema: GenMessage<MixPanelEventRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 17);

/**
 * @generated from message com.stablemoney.api.notifications.MixPanelMultiplePropertiesEventRequest
 */
export type MixPanelMultiplePropertiesEventRequest =
  Message<"com.stablemoney.api.notifications.MixPanelMultiplePropertiesEventRequest"> & {
    /**
     * @generated from field: string event = 1;
     */
    event: string;

    /**
     * @generated from field: string userId = 2;
     */
    userId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.notifications.MixPanelProperty properties = 3;
     */
    properties: MixPanelProperty[];
  };

/**
 * Describes the message com.stablemoney.api.notifications.MixPanelMultiplePropertiesEventRequest.
 * Use `create(MixPanelMultiplePropertiesEventRequestSchema)` to create a new message.
 */
export const MixPanelMultiplePropertiesEventRequestSchema: GenMessage<MixPanelMultiplePropertiesEventRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 18);

/**
 * @generated from message com.stablemoney.api.notifications.MixPanelProperty
 */
export type MixPanelProperty =
  Message<"com.stablemoney.api.notifications.MixPanelProperty"> & {
    /**
     * @generated from field: string key = 1;
     */
    key: string;

    /**
     * @generated from field: string value = 2;
     */
    value: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.MixPanelProperty.
 * Use `create(MixPanelPropertySchema)` to create a new message.
 */
export const MixPanelPropertySchema: GenMessage<MixPanelProperty> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 19);

/**
 * @generated from message com.stablemoney.api.notifications.UserNotificationQueryRequest
 */
export type UserNotificationQueryRequest =
  Message<"com.stablemoney.api.notifications.UserNotificationQueryRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string notification_name = 2;
     */
    notificationName: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserNotificationQueryRequest.
 * Use `create(UserNotificationQueryRequestSchema)` to create a new message.
 */
export const UserNotificationQueryRequestSchema: GenMessage<UserNotificationQueryRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 20);

/**
 * @generated from message com.stablemoney.api.notifications.UserNotificationQueryResponse
 */
export type UserNotificationQueryResponse =
  Message<"com.stablemoney.api.notifications.UserNotificationQueryResponse"> & {
    /**
     * @generated from field: bool is_present = 1;
     */
    isPresent: boolean;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserNotificationQueryResponse.
 * Use `create(UserNotificationQueryResponseSchema)` to create a new message.
 */
export const UserNotificationQueryResponseSchema: GenMessage<UserNotificationQueryResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 21);

/**
 * @generated from message com.stablemoney.api.notifications.GetUserInvestmentsAndVkycStatusPerBankResponse
 */
export type GetUserInvestmentsAndVkycStatusPerBankResponse =
  Message<"com.stablemoney.api.notifications.GetUserInvestmentsAndVkycStatusPerBankResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.notifications.UserInvestmentsAndVkycStatusPerBank user_investments_and_vkyc_status_per_bank = 1;
     */
    userInvestmentsAndVkycStatusPerBank: UserInvestmentsAndVkycStatusPerBank[];
  };

/**
 * Describes the message com.stablemoney.api.notifications.GetUserInvestmentsAndVkycStatusPerBankResponse.
 * Use `create(GetUserInvestmentsAndVkycStatusPerBankResponseSchema)` to create a new message.
 */
export const GetUserInvestmentsAndVkycStatusPerBankResponseSchema: GenMessage<GetUserInvestmentsAndVkycStatusPerBankResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 22);

/**
 * @generated from message com.stablemoney.api.notifications.UserInvestmentsAndVkycStatusPerBank
 */
export type UserInvestmentsAndVkycStatusPerBank =
  Message<"com.stablemoney.api.notifications.UserInvestmentsAndVkycStatusPerBank"> & {
    /**
     * @generated from field: string bank_name = 1;
     */
    bankName: string;

    /**
     * @generated from field: bool vkyc_status = 2;
     */
    vkycStatus: boolean;

    /**
     * @generated from field: double current_invested_amount = 3;
     */
    currentInvestedAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserInvestmentsAndVkycStatusPerBank.
 * Use `create(UserInvestmentsAndVkycStatusPerBankSchema)` to create a new message.
 */
export const UserInvestmentsAndVkycStatusPerBankSchema: GenMessage<UserInvestmentsAndVkycStatusPerBank> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 23);

/**
 * @generated from message com.stablemoney.api.notifications.UserFirstEventTimeRequest
 */
export type UserFirstEventTimeRequest =
  Message<"com.stablemoney.api.notifications.UserFirstEventTimeRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string event_type = 2;
     */
    eventType: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserFirstEventTimeRequest.
 * Use `create(UserFirstEventTimeRequestSchema)` to create a new message.
 */
export const UserFirstEventTimeRequestSchema: GenMessage<UserFirstEventTimeRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 24);

/**
 * @generated from message com.stablemoney.api.notifications.UserFirstEventTimeResponse
 */
export type UserFirstEventTimeResponse =
  Message<"com.stablemoney.api.notifications.UserFirstEventTimeResponse"> & {
    /**
     * @generated from field: optional string first_event_time = 1;
     */
    firstEventTime?: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserFirstEventTimeResponse.
 * Use `create(UserFirstEventTimeResponseSchema)` to create a new message.
 */
export const UserFirstEventTimeResponseSchema: GenMessage<UserFirstEventTimeResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 25);

/**
 * @generated from message com.stablemoney.api.notifications.BookingsSumRequest
 */
export type BookingsSumRequest =
  Message<"com.stablemoney.api.notifications.BookingsSumRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string threshold_time = 2;
     */
    thresholdTime: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.BookingsSumRequest.
 * Use `create(BookingsSumRequestSchema)` to create a new message.
 */
export const BookingsSumRequestSchema: GenMessage<BookingsSumRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 26);

/**
 * @generated from message com.stablemoney.api.notifications.BookingsSumResponse
 */
export type BookingsSumResponse =
  Message<"com.stablemoney.api.notifications.BookingsSumResponse"> & {
    /**
     * @generated from field: double amount = 1;
     */
    amount: number;
  };

/**
 * Describes the message com.stablemoney.api.notifications.BookingsSumResponse.
 * Use `create(BookingsSumResponseSchema)` to create a new message.
 */
export const BookingsSumResponseSchema: GenMessage<BookingsSumResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 27);

/**
 * @generated from message com.stablemoney.api.notifications.UserOptinRequest
 */
export type UserOptinRequest =
  Message<"com.stablemoney.api.notifications.UserOptinRequest"> & {
    /**
     * @generated from field: string phone_number = 1;
     */
    phoneNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.UserOptinRequest.
 * Use `create(UserOptinRequestSchema)` to create a new message.
 */
export const UserOptinRequestSchema: GenMessage<UserOptinRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 28);

/**
 * @generated from message com.stablemoney.api.notifications.ForceUserLogoutRequest
 */
export type ForceUserLogoutRequest =
  Message<"com.stablemoney.api.notifications.ForceUserLogoutRequest"> & {
    /**
     * @generated from field: string notification_token = 1;
     */
    notificationToken: string;
  };

/**
 * Describes the message com.stablemoney.api.notifications.ForceUserLogoutRequest.
 * Use `create(ForceUserLogoutRequestSchema)` to create a new message.
 */
export const ForceUserLogoutRequestSchema: GenMessage<ForceUserLogoutRequest> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 29);

/**
 * @generated from message com.stablemoney.api.notifications.ForceUserLogoutResponse
 */
export type ForceUserLogoutResponse =
  Message<"com.stablemoney.api.notifications.ForceUserLogoutResponse"> & {
    /**
     * @generated from field: bool is_success = 1;
     */
    isSuccess: boolean;
  };

/**
 * Describes the message com.stablemoney.api.notifications.ForceUserLogoutResponse.
 * Use `create(ForceUserLogoutResponseSchema)` to create a new message.
 */
export const ForceUserLogoutResponseSchema: GenMessage<ForceUserLogoutResponse> =
  /*@__PURE__*/
  messageDesc(file_private_services_notifications_UserNotification, 30);

/**
 * @generated from service com.stablemoney.api.notifications.UserNotificationService
 */
export const UserNotificationService: GenService<{
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserNotificationData
   */
  getUserNotificationData: {
    methodKind: "unary";
    input: typeof UserNotificationRequestSchema;
    output: typeof UserNotificationGrpcResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserBookingCount
   */
  getUserBookingCount: {
    methodKind: "unary";
    input: typeof UserNotificationRequestSchema;
    output: typeof UserBookingCountResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserTDResumeCount
   */
  getUserTDResumeCount: {
    methodKind: "unary";
    input: typeof UserNotificationRequestSchema;
    output: typeof UserTDResumeCountResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserEventCount
   */
  getUserEventCount: {
    methodKind: "unary";
    input: typeof UserNotificationEventRequestSchema;
    output: typeof UserEventCountResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserEventCountPerBank
   */
  getUserEventCountPerBank: {
    methodKind: "unary";
    input: typeof UserNotificationEventRequestPerBankSchema;
    output: typeof UserEventCountResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserAllFdStatus
   */
  getUserAllFdStatus: {
    methodKind: "unary";
    input: typeof UserAllFdStatusRequestSchema;
    output: typeof UserAllFdStatusResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserInvestmentsAndVkycStatusPerBank
   */
  getUserInvestmentsAndVkycStatusPerBank: {
    methodKind: "unary";
    input: typeof UserNotificationRequestSchema;
    output: typeof GetUserInvestmentsAndVkycStatusPerBankResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserPaymentSuccessCount
   */
  getUserPaymentSuccessCount: {
    methodKind: "unary";
    input: typeof UserNotificationRequestSchema;
    output: typeof UserTDPaymentSuccessCountResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.postFixedDepositEvent
   */
  postFixedDepositEvent: {
    methodKind: "unary";
    input: typeof FdNotificationRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.postMixPanelNetworth
   */
  postMixPanelNetworth: {
    methodKind: "unary";
    input: typeof MixPanelNetworthRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.postMixpanelCreatedAt
   */
  postMixpanelCreatedAt: {
    methodKind: "unary";
    input: typeof MixpanelCreatedAtRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.postMixPanelEvent
   */
  postMixPanelEvent: {
    methodKind: "unary";
    input: typeof MixPanelEventRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getPaymentFailureStatuses
   */
  getPaymentFailureStatuses: {
    methodKind: "unary";
    input: typeof UserAllFdStatusRequestSchema;
    output: typeof UserAllFdStatusResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getFirstEventTimeResponse
   */
  getFirstEventTimeResponse: {
    methodKind: "unary";
    input: typeof UserFirstEventTimeRequestSchema;
    output: typeof UserFirstEventTimeResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getSumOfBookingsPostDate
   */
  getSumOfBookingsPostDate: {
    methodKind: "unary";
    input: typeof BookingsSumRequestSchema;
    output: typeof BookingsSumResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.getUserFdEventById
   */
  getUserFdEventById: {
    methodKind: "unary";
    input: typeof UserFdEventByIdRequestSchema;
    output: typeof UserFdEventByIdResponseSchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.postUserOptin
   */
  postUserOptin: {
    methodKind: "unary";
    input: typeof UserOptinRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.postMultiplePropertiesMixpanelEvent
   */
  postMultiplePropertiesMixpanelEvent: {
    methodKind: "unary";
    input: typeof MixPanelMultiplePropertiesEventRequestSchema;
    output: typeof EmptySchema;
  };
  /**
   * @generated from rpc com.stablemoney.api.notifications.UserNotificationService.forceUserLogout
   */
  forceUserLogout: {
    methodKind: "unary";
    input: typeof ForceUserLogoutRequestSchema;
    output: typeof ForceUserLogoutResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(
  file_private_services_notifications_UserNotification,
  0,
);
