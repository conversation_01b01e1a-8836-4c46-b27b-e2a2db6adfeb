// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/personalization/PersonalizationAdmin.proto (package com.stablemoney.api.personalization, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type {
  PaginationRequest,
  PaginationResponse,
} from "../identity/Common_pb.js";
import { file_public_models_identity_Common } from "../identity/Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/personalization/PersonalizationAdmin.proto.
 */
export const file_public_models_personalization_PersonalizationAdmin: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CjhwdWJsaWMvbW9kZWxzL3BlcnNvbmFsaXphdGlvbi9QZXJzb25hbGl6YXRpb25BZG1pbi5wcm90bxIjY29tLnN0YWJsZW1vbmV5LmFwaS5wZXJzb25hbGl6YXRpb24ifwoMUGFnZVJlc3BvbnNlEgwKBHBhdGgYASABKAkSDAoEbmFtZRgCIAEoCRIKCgJpZBgDIAEoCRISCgpjcmVhdGVkX2F0GAQgASgBEhIKCnVwZGF0ZWRfYXQYBSABKAESDwoHY29udGVudBgGIAEoCRIOCgZjb25maWcYByABKAkihQEKC1BhZ2VTdW1tYXJ5EgwKBHBhdGgYASABKAkSDAoEbmFtZRgCIAEoCRIKCgJpZBgDIAEoCRISCgpjcmVhdGVkX2F0GAQgASgBEhIKCnVwZGF0ZWRfYXQYBSABKAESFwoKZGVsZXRlZF9hdBgGIAEoAUgAiAEBQg0KC19kZWxldGVkX2F0IpUBCg1QYWdlc1Jlc3BvbnNlEj4KBGRhdGEYASADKAsyMC5jb20uc3RhYmxlbW9uZXkuYXBpLnBlcnNvbmFsaXphdGlvbi5QYWdlU3VtbWFyeRJECgpwYWdpbmF0aW9uGAIgASgLMjAuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5QYWdpbmF0aW9uUmVzcG9uc2UiggEKDFBhZ2VzUmVxdWVzdBJDCgpwYWdpbmF0aW9uGAEgASgLMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5QYWdpbmF0aW9uUmVxdWVzdBIXCg9pbmNsdWRlX2RlbGV0ZWQYAiABKAgSDgoBcRgDIAEoCUgAiAEBQgQKAl9xIlAKEUNyZWF0ZVBhZ2VSZXF1ZXN0EgwKBHBhdGgYASABKAkSDAoEbmFtZRgCIAEoCRIPCgdjb250ZW50GAMgASgJEg4KBmNvbmZpZxgEIAEoCSIXCglJZFJlcXVlc3QSCgoCaWQYASABKAkiXAoRVXBkYXRlUGFnZVJlcXVlc3QSCgoCaWQYASABKAkSDAoEcGF0aBgCIAEoCRIMCgRuYW1lGAMgASgJEg8KB2NvbnRlbnQYBCABKAkSDgoGY29uZmlnGAUgASgJIiUKEkRlbGV0ZVBhZ2VSZXNwb25zZRIPCgdzdWNjZXNzGAEgASgIQicKI2NvbS5zdGFibGVtb25leS5hcGkucGVyc29uYWxpemF0aW9uUAFiBnByb3RvMw",
    [file_public_models_identity_Common],
  );

/**
 * @generated from message com.stablemoney.api.personalization.PageResponse
 */
export type PageResponse =
  Message<"com.stablemoney.api.personalization.PageResponse"> & {
    /**
     * @generated from field: string path = 1;
     */
    path: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string id = 3;
     */
    id: string;

    /**
     * @generated from field: double created_at = 4;
     */
    createdAt: number;

    /**
     * @generated from field: double updated_at = 5;
     */
    updatedAt: number;

    /**
     * @generated from field: string content = 6;
     */
    content: string;

    /**
     * @generated from field: string config = 7;
     */
    config: string;
  };

/**
 * Describes the message com.stablemoney.api.personalization.PageResponse.
 * Use `create(PageResponseSchema)` to create a new message.
 */
export const PageResponseSchema: GenMessage<PageResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 0);

/**
 * @generated from message com.stablemoney.api.personalization.PageSummary
 */
export type PageSummary =
  Message<"com.stablemoney.api.personalization.PageSummary"> & {
    /**
     * @generated from field: string path = 1;
     */
    path: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string id = 3;
     */
    id: string;

    /**
     * @generated from field: double created_at = 4;
     */
    createdAt: number;

    /**
     * @generated from field: double updated_at = 5;
     */
    updatedAt: number;

    /**
     * @generated from field: optional double deleted_at = 6;
     */
    deletedAt?: number;
  };

/**
 * Describes the message com.stablemoney.api.personalization.PageSummary.
 * Use `create(PageSummarySchema)` to create a new message.
 */
export const PageSummarySchema: GenMessage<PageSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 1);

/**
 * @generated from message com.stablemoney.api.personalization.PagesResponse
 */
export type PagesResponse =
  Message<"com.stablemoney.api.personalization.PagesResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.personalization.PageSummary data = 1;
     */
    data: PageSummary[];

    /**
     * @generated from field: com.stablemoney.api.identity.PaginationResponse pagination = 2;
     */
    pagination?: PaginationResponse;
  };

/**
 * Describes the message com.stablemoney.api.personalization.PagesResponse.
 * Use `create(PagesResponseSchema)` to create a new message.
 */
export const PagesResponseSchema: GenMessage<PagesResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 2);

/**
 * @generated from message com.stablemoney.api.personalization.PagesRequest
 */
export type PagesRequest =
  Message<"com.stablemoney.api.personalization.PagesRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;

    /**
     * @generated from field: bool include_deleted = 2;
     */
    includeDeleted: boolean;

    /**
     * @generated from field: optional string q = 3;
     */
    q?: string;
  };

/**
 * Describes the message com.stablemoney.api.personalization.PagesRequest.
 * Use `create(PagesRequestSchema)` to create a new message.
 */
export const PagesRequestSchema: GenMessage<PagesRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 3);

/**
 * @generated from message com.stablemoney.api.personalization.CreatePageRequest
 */
export type CreatePageRequest =
  Message<"com.stablemoney.api.personalization.CreatePageRequest"> & {
    /**
     * @generated from field: string path = 1;
     */
    path: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string content = 3;
     */
    content: string;

    /**
     * @generated from field: string config = 4;
     */
    config: string;
  };

/**
 * Describes the message com.stablemoney.api.personalization.CreatePageRequest.
 * Use `create(CreatePageRequestSchema)` to create a new message.
 */
export const CreatePageRequestSchema: GenMessage<CreatePageRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 4);

/**
 * @generated from message com.stablemoney.api.personalization.IdRequest
 */
export type IdRequest =
  Message<"com.stablemoney.api.personalization.IdRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message com.stablemoney.api.personalization.IdRequest.
 * Use `create(IdRequestSchema)` to create a new message.
 */
export const IdRequestSchema: GenMessage<IdRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 5);

/**
 * @generated from message com.stablemoney.api.personalization.UpdatePageRequest
 */
export type UpdatePageRequest =
  Message<"com.stablemoney.api.personalization.UpdatePageRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string path = 2;
     */
    path: string;

    /**
     * @generated from field: string name = 3;
     */
    name: string;

    /**
     * @generated from field: string content = 4;
     */
    content: string;

    /**
     * @generated from field: string config = 5;
     */
    config: string;
  };

/**
 * Describes the message com.stablemoney.api.personalization.UpdatePageRequest.
 * Use `create(UpdatePageRequestSchema)` to create a new message.
 */
export const UpdatePageRequestSchema: GenMessage<UpdatePageRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 6);

/**
 * @generated from message com.stablemoney.api.personalization.DeletePageResponse
 */
export type DeletePageResponse =
  Message<"com.stablemoney.api.personalization.DeletePageResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;
  };

/**
 * Describes the message com.stablemoney.api.personalization.DeletePageResponse.
 * Use `create(DeletePageResponseSchema)` to create a new message.
 */
export const DeletePageResponseSchema: GenMessage<DeletePageResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_personalization_PersonalizationAdmin, 7);
