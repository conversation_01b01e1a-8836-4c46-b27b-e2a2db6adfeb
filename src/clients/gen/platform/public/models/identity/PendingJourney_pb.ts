// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/PendingJourney.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { RedirectDeeplink } from "../business/BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "../business/BusinessCommon_pb.js";
import type { BankResponse } from "../business/Collection_pb.js";
import { file_public_models_business_Collection } from "../business/Collection_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/PendingJourney.proto.
 */
export const file_public_models_identity_PendingJourney: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_BusinessCommon,
      file_public_models_business_Collection,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.PendingJourney
 */
export type PendingJourney =
  Message<"com.stablemoney.api.identity.PendingJourney"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string icon_url = 2;
     */
    iconUrl: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextResponse title = 3;
     */
    title: RichTextResponse[];

    /**
     * @generated from field: string timestamp = 4;
     */
    timestamp: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 5;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: com.stablemoney.api.identity.PendingJourneyCardType card_type = 6;
     */
    cardType: PendingJourneyCardType;

    /**
     * @generated from field: string bank_id = 7;
     */
    bankId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.PendingJourney.JourneyType journey_type = 9;
     */
    journeyType: PendingJourney_JourneyType;
  };

/**
 * Describes the message com.stablemoney.api.identity.PendingJourney.
 * Use `create(PendingJourneySchema)` to create a new message.
 */
export const PendingJourneySchema: GenMessage<PendingJourney> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 0);

/**
 * @generated from enum com.stablemoney.api.identity.PendingJourney.JourneyType
 */
export enum PendingJourney_JourneyType {
  /**
   * @generated from enum value: ACTION_UNKNOWN = 0;
   */
  ACTION_UNKNOWN = 0,

  /**
   * @generated from enum value: VKYC_FAILED = 2;
   */
  VKYC_FAILED = 2,

  /**
   * @generated from enum value: VKYC_REQUIRED = 4;
   */
  VKYC_REQUIRED = 4,

  /**
   * @generated from enum value: VKYC_RETRY_REQUIRED = 6;
   */
  VKYC_RETRY_REQUIRED = 6,

  /**
   * @generated from enum value: VKYC_INITIATED = 8;
   */
  VKYC_INITIATED = 8,

  /**
   * @generated from enum value: VKYC_EXPIRED = 10;
   */
  VKYC_EXPIRED = 10,
}

/**
 * Describes the enum com.stablemoney.api.identity.PendingJourney.JourneyType.
 */
export const PendingJourney_JourneyTypeSchema: GenEnum<PendingJourney_JourneyType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_PendingJourney, 0, 0);

/**
 * @generated from message com.stablemoney.api.identity.RichTextResponse
 */
export type RichTextResponse =
  Message<"com.stablemoney.api.identity.RichTextResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode title = 1;
     */
    title: RichTextNode[];

    /**
     * @generated from field: string id = 2;
     */
    id: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RichTextResponse.
 * Use `create(RichTextResponseSchema)` to create a new message.
 */
export const RichTextResponseSchema: GenMessage<RichTextResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 1);

/**
 * @generated from message com.stablemoney.api.identity.PendingJourneyResponse
 */
export type PendingJourneyResponse =
  Message<"com.stablemoney.api.identity.PendingJourneyResponse"> & {
    /**
     * @generated from field: optional com.stablemoney.api.identity.BottomSheetResponse bottom_sheet = 1;
     */
    bottomSheet?: BottomSheetResponse;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.PendingJourney pending_journeys = 2;
     */
    pendingJourneys: PendingJourney[];
  };

/**
 * Describes the message com.stablemoney.api.identity.PendingJourneyResponse.
 * Use `create(PendingJourneyResponseSchema)` to create a new message.
 */
export const PendingJourneyResponseSchema: GenMessage<PendingJourneyResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 2);

/**
 * @generated from message com.stablemoney.api.identity.BottomSheetResponse
 */
export type BottomSheetResponse =
  Message<"com.stablemoney.api.identity.BottomSheetResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode title = 1;
     */
    title: RichTextNode[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode sub_title = 2;
     */
    subTitle: RichTextNode[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 3;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: string icon_url = 4;
     */
    iconUrl: string;

    /**
     * @generated from field: string button_cta = 5;
     */
    buttonCta: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BottomSheetResponse.
 * Use `create(BottomSheetResponseSchema)` to create a new message.
 */
export const BottomSheetResponseSchema: GenMessage<BottomSheetResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 3);

/**
 * @generated from message com.stablemoney.api.identity.VkycAttemptConfirmationResponse
 */
export type VkycAttemptConfirmationResponse =
  Message<"com.stablemoney.api.identity.VkycAttemptConfirmationResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.VkycAttemptConfirmationResponse.
 * Use `create(VkycAttemptConfirmationResponseSchema)` to create a new message.
 */
export const VkycAttemptConfirmationResponseSchema: GenMessage<VkycAttemptConfirmationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 4);

/**
 * @generated from message com.stablemoney.api.identity.VkycAttemptConfirmationRequest
 */
export type VkycAttemptConfirmationRequest =
  Message<"com.stablemoney.api.identity.VkycAttemptConfirmationRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: bool was_successful = 2;
     */
    wasSuccessful: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycAttemptConfirmationRequest.
 * Use `create(VkycAttemptConfirmationRequestSchema)` to create a new message.
 */
export const VkycAttemptConfirmationRequestSchema: GenMessage<VkycAttemptConfirmationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 5);

/**
 * @generated from message com.stablemoney.api.identity.RichTextNode
 */
export type RichTextNode =
  Message<"com.stablemoney.api.identity.RichTextNode"> & {
    /**
     * @generated from field: string text = 1;
     */
    text: string;

    /**
     * @generated from field: string color = 2;
     */
    color: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RichTextNode.
 * Use `create(RichTextNodeSchema)` to create a new message.
 */
export const RichTextNodeSchema: GenMessage<RichTextNode> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 6);

/**
 * @generated from message com.stablemoney.api.identity.VkycSlotSelectionResponse
 */
export type VkycSlotSelectionResponse =
  Message<"com.stablemoney.api.identity.VkycSlotSelectionResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.VkycSlotSelectionResponse.
 * Use `create(VkycSlotSelectionResponseSchema)` to create a new message.
 */
export const VkycSlotSelectionResponseSchema: GenMessage<VkycSlotSelectionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 7);

/**
 * @generated from message com.stablemoney.api.identity.VkycSlotSelectionRequest
 */
export type VkycSlotSelectionRequest =
  Message<"com.stablemoney.api.identity.VkycSlotSelectionRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string slot_id = 2;
     */
    slotId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycSlotSelectionRequest.
 * Use `create(VkycSlotSelectionRequestSchema)` to create a new message.
 */
export const VkycSlotSelectionRequestSchema: GenMessage<VkycSlotSelectionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 8);

/**
 * @generated from message com.stablemoney.api.identity.VkycDetailsResponse
 */
export type VkycDetailsResponse =
  Message<"com.stablemoney.api.identity.VkycDetailsResponse"> & {
    /**
     * @generated from field: bool is_expired = 1;
     */
    isExpired: boolean;

    /**
     * @generated from field: string id = 2;
     */
    id: string;

    /**
     * @generated from oneof com.stablemoney.api.identity.VkycDetailsResponse.details
     */
    details:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.VkycPendingPageDetails vkyc_pending_page_details = 3;
           */
          value: VkycPendingPageDetails;
          case: "vkycPendingPageDetails";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.VkycFailurePageDetails vkyc_failure_page_details = 4;
           */
          value: VkycFailurePageDetails;
          case: "vkycFailurePageDetails";
        }
      | { case: undefined; value?: undefined };

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 5;
     */
    bank?: BankResponse;

    /**
     * @generated from field: string faq_category = 6;
     */
    faqCategory: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycDetailsResponse.
 * Use `create(VkycDetailsResponseSchema)` to create a new message.
 */
export const VkycDetailsResponseSchema: GenMessage<VkycDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 9);

/**
 * @generated from message com.stablemoney.api.identity.VkycPendingPageDetails
 */
export type VkycPendingPageDetails =
  Message<"com.stablemoney.api.identity.VkycPendingPageDetails"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode title = 1;
     */
    title: RichTextNode[];

    /**
     * @generated from field: string logo_url = 2;
     */
    logoUrl: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode bottom_text = 3;
     */
    bottomText: RichTextNode[];

    /**
     * @generated from field: string timestamp = 4;
     */
    timestamp: string;

    /**
     * @generated from field: com.stablemoney.api.identity.VKYCSteps steps = 5;
     */
    steps?: VKYCSteps;

    /**
     * @generated from field: com.stablemoney.api.identity.VkycSchedule slots = 6;
     */
    slots?: VkycSchedule;
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycPendingPageDetails.
 * Use `create(VkycPendingPageDetailsSchema)` to create a new message.
 */
export const VkycPendingPageDetailsSchema: GenMessage<VkycPendingPageDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 10);

/**
 * @generated from message com.stablemoney.api.identity.VkycFailurePageDetails
 */
export type VkycFailurePageDetails =
  Message<"com.stablemoney.api.identity.VkycFailurePageDetails"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode title = 1;
     */
    title: RichTextNode[];

    /**
     * @generated from field: string logo_url = 2;
     */
    logoUrl: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode sub_title = 3;
     */
    subTitle: RichTextNode[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode bottom_text = 4;
     */
    bottomText: RichTextNode[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.VkycFailureEvents failure_events = 7;
     */
    failureEvents: VkycFailureEvents[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.TipsForNextAttempt tips_for_next_attempt = 8;
     */
    tipsForNextAttempt: TipsForNextAttempt[];
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycFailurePageDetails.
 * Use `create(VkycFailurePageDetailsSchema)` to create a new message.
 */
export const VkycFailurePageDetailsSchema: GenMessage<VkycFailurePageDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 11);

/**
 * @generated from message com.stablemoney.api.identity.TipsForNextAttempt
 */
export type TipsForNextAttempt =
  Message<"com.stablemoney.api.identity.TipsForNextAttempt"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode text = 1;
     */
    text: RichTextNode[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode subtext = 2;
     */
    subtext: RichTextNode[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankDescription bank_description = 3;
     */
    bankDescription: BankDescription[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 4;
     */
    redirectDeeplink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.identity.TipsForNextAttempt.
 * Use `create(TipsForNextAttemptSchema)` to create a new message.
 */
export const TipsForNextAttemptSchema: GenMessage<TipsForNextAttempt> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 12);

/**
 * @generated from message com.stablemoney.api.identity.BankDescription
 */
export type BankDescription =
  Message<"com.stablemoney.api.identity.BankDescription"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 1;
     */
    bank?: BankResponse;

    /**
     * @generated from field: string description = 2;
     */
    description: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankDescription.
 * Use `create(BankDescriptionSchema)` to create a new message.
 */
export const BankDescriptionSchema: GenMessage<BankDescription> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 13);

/**
 * @generated from message com.stablemoney.api.identity.VkycFailureEvents
 */
export type VkycFailureEvents =
  Message<"com.stablemoney.api.identity.VkycFailureEvents"> & {
    /**
     * @generated from field: string timestamp = 1;
     */
    timestamp: string;

    /**
     * @generated from field: string text = 2;
     */
    text: string;

    /**
     * @generated from field: com.stablemoney.api.identity.VkycFailureEventType type = 3;
     */
    type: VkycFailureEventType;
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycFailureEvents.
 * Use `create(VkycFailureEventsSchema)` to create a new message.
 */
export const VkycFailureEventsSchema: GenMessage<VkycFailureEvents> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 14);

/**
 * @generated from message com.stablemoney.api.identity.VkycSchedule
 */
export type VkycSchedule =
  Message<"com.stablemoney.api.identity.VkycSchedule"> & {
    /**
     * @generated from field: bool is_agent_available = 1;
     */
    isAgentAvailable: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.VkycBookingStatus booking_status = 2;
     */
    bookingStatus?: VkycBookingStatus;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.DaySchedule days = 3;
     */
    days: DaySchedule[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink vkyc_redirect_deeplink = 4;
     */
    vkycRedirectDeeplink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycSchedule.
 * Use `create(VkycScheduleSchema)` to create a new message.
 */
export const VkycScheduleSchema: GenMessage<VkycSchedule> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 15);

/**
 * @generated from message com.stablemoney.api.identity.VkycBookingStatus
 */
export type VkycBookingStatus =
  Message<"com.stablemoney.api.identity.VkycBookingStatus"> & {
    /**
     * @generated from field: bool is_slot_booked = 1;
     */
    isSlotBooked: boolean;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RichTextNode description = 2;
     */
    description: RichTextNode[];
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycBookingStatus.
 * Use `create(VkycBookingStatusSchema)` to create a new message.
 */
export const VkycBookingStatusSchema: GenMessage<VkycBookingStatus> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 16);

/**
 * @generated from message com.stablemoney.api.identity.DaySchedule
 */
export type DaySchedule =
  Message<"com.stablemoney.api.identity.DaySchedule"> & {
    /**
     * @generated from field: string day = 1;
     */
    day: string;

    /**
     * @generated from field: string timestamp = 2;
     */
    timestamp: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.VkycSlot slots = 3;
     */
    slots: VkycSlot[];
  };

/**
 * Describes the message com.stablemoney.api.identity.DaySchedule.
 * Use `create(DayScheduleSchema)` to create a new message.
 */
export const DayScheduleSchema: GenMessage<DaySchedule> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 17);

/**
 * @generated from message com.stablemoney.api.identity.VkycSlot
 */
export type VkycSlot = Message<"com.stablemoney.api.identity.VkycSlot"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string display_text = 2;
   */
  displayText: string;

  /**
   * @generated from field: bool is_recommended = 3;
   */
  isRecommended: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.VkycSlot.
 * Use `create(VkycSlotSchema)` to create a new message.
 */
export const VkycSlotSchema: GenMessage<VkycSlot> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 18);

/**
 * @generated from message com.stablemoney.api.identity.VKYCSteps
 */
export type VKYCSteps = Message<"com.stablemoney.api.identity.VKYCSteps"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.RichTextNode title = 1;
   */
  title: RichTextNode[];

  /**
   * @generated from field: repeated com.stablemoney.api.identity.VKYCStep steps = 2;
   */
  steps: VKYCStep[];
};

/**
 * Describes the message com.stablemoney.api.identity.VKYCSteps.
 * Use `create(VKYCStepsSchema)` to create a new message.
 */
export const VKYCStepsSchema: GenMessage<VKYCSteps> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 19);

/**
 * @generated from message com.stablemoney.api.identity.VKYCStep
 */
export type VKYCStep = Message<"com.stablemoney.api.identity.VKYCStep"> & {
  /**
   * @generated from field: repeated com.stablemoney.api.identity.RichTextNode title = 1;
   */
  title: RichTextNode[];

  /**
   * @generated from field: repeated com.stablemoney.api.identity.RichTextNode short_title = 2;
   */
  shortTitle: RichTextNode[];

  /**
   * @generated from field: com.stablemoney.api.identity.Media media = 3;
   */
  media?: Media;
};

/**
 * Describes the message com.stablemoney.api.identity.VKYCStep.
 * Use `create(VKYCStepSchema)` to create a new message.
 */
export const VKYCStepSchema: GenMessage<VKYCStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 20);

/**
 * @generated from message com.stablemoney.api.identity.Media
 */
export type Media = Message<"com.stablemoney.api.identity.Media"> & {
  /**
   * @generated from field: string url = 1;
   */
  url: string;

  /**
   * @generated from field: com.stablemoney.api.identity.MediaTypes type = 2;
   */
  type: MediaTypes;
};

/**
 * Describes the message com.stablemoney.api.identity.Media.
 * Use `create(MediaSchema)` to create a new message.
 */
export const MediaSchema: GenMessage<Media> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_PendingJourney, 21);

/**
 * @generated from enum com.stablemoney.api.identity.PendingJourneyCardType
 */
export enum PendingJourneyCardType {
  /**
   * @generated from enum value: UNKNOWN_PENDING_JOURNEY_CARD_TYPE = 0;
   */
  UNKNOWN_PENDING_JOURNEY_CARD_TYPE = 0,

  /**
   * @generated from enum value: SIMPLE_PENDING_JOURNEY_CARD_TYPE = 1;
   */
  SIMPLE_PENDING_JOURNEY_CARD_TYPE = 1,

  /**
   * @generated from enum value: VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE = 2;
   */
  VKYC_ATTEMPT_CONFIRMATION_PENDING_JOURNEY_CARD_TYPE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.PendingJourneyCardType.
 */
export const PendingJourneyCardTypeSchema: GenEnum<PendingJourneyCardType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_PendingJourney, 0);

/**
 * @generated from enum com.stablemoney.api.identity.VkycFailureEventType
 */
export enum VkycFailureEventType {
  /**
   * @generated from enum value: UNKNOWN_VKYC_FAILURE_EVENT_TYPE = 0;
   */
  UNKNOWN_VKYC_FAILURE_EVENT_TYPE = 0,

  /**
   * @generated from enum value: SUCCESS_VKYC_FAILURE_EVENT_TYPE = 1;
   */
  SUCCESS_VKYC_FAILURE_EVENT_TYPE = 1,

  /**
   * @generated from enum value: FAILURE_VKYC_FAILURE_EVENT_TYPE = 2;
   */
  FAILURE_VKYC_FAILURE_EVENT_TYPE = 2,

  /**
   * @generated from enum value: NEUTRAL_VKYC_FAILURE_EVENT_TYPE = 3;
   */
  NEUTRAL_VKYC_FAILURE_EVENT_TYPE = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.VkycFailureEventType.
 */
export const VkycFailureEventTypeSchema: GenEnum<VkycFailureEventType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_PendingJourney, 1);

/**
 * @generated from enum com.stablemoney.api.identity.MediaTypes
 */
export enum MediaTypes {
  /**
   * @generated from enum value: UNKNOWN_MEDIA_TYPE = 0;
   */
  UNKNOWN_MEDIA_TYPE = 0,

  /**
   * @generated from enum value: IMAGE_MEDIA_TYPE = 1;
   */
  IMAGE_MEDIA_TYPE = 1,

  /**
   * @generated from enum value: TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE = 2;
   */
  TIMESTAMP_FOR_VKYC_COUNTDOWN_MEDIA_TYPE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.MediaTypes.
 */
export const MediaTypesSchema: GenEnum<MediaTypes> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_PendingJourney, 2);
