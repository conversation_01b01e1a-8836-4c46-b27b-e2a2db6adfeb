// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Demat.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Demat.proto.
 */
export const file_public_models_identity_Demat: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiJwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0RlbWF0LnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IkEKGUdldFByb3ZpZGVyRGV0YWlsc1JlcXVlc3QSJAoccGFydGlhbF9kZW1hdF9hY2NvdW50X251bWJlchgBIAEoCSJWCh9HZXREZW1hdFByb3ZpZGVyRGV0YWlsc1Jlc3BvbnNlEhMKC3Byb3ZpZGVyX2lkGAEgASgJEgwKBG5hbWUYAiABKAkSEAoIaWNvbl91cmwYAyABKAkiNgoWQWRkRGVtYXRBY2NvdW50UmVxdWVzdBIcChRkZW1hdF9hY2NvdW50X251bWJlchgDIAEoCSIZChdBZGREZW1hdEFjY291bnRSZXNwb25zZUIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.GetProviderDetailsRequest
 */
export type GetProviderDetailsRequest =
  Message<"com.stablemoney.api.identity.GetProviderDetailsRequest"> & {
    /**
     * @generated from field: string partial_demat_account_number = 1;
     */
    partialDematAccountNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetProviderDetailsRequest.
 * Use `create(GetProviderDetailsRequestSchema)` to create a new message.
 */
export const GetProviderDetailsRequestSchema: GenMessage<GetProviderDetailsRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Demat, 0);

/**
 * @generated from message com.stablemoney.api.identity.GetDematProviderDetailsResponse
 */
export type GetDematProviderDetailsResponse =
  Message<"com.stablemoney.api.identity.GetDematProviderDetailsResponse"> & {
    /**
     * @generated from field: string provider_id = 1;
     */
    providerId: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string icon_url = 3;
     */
    iconUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetDematProviderDetailsResponse.
 * Use `create(GetDematProviderDetailsResponseSchema)` to create a new message.
 */
export const GetDematProviderDetailsResponseSchema: GenMessage<GetDematProviderDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Demat, 1);

/**
 * @generated from message com.stablemoney.api.identity.AddDematAccountRequest
 */
export type AddDematAccountRequest =
  Message<"com.stablemoney.api.identity.AddDematAccountRequest"> & {
    /**
     * @generated from field: string demat_account_number = 3;
     */
    dematAccountNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddDematAccountRequest.
 * Use `create(AddDematAccountRequestSchema)` to create a new message.
 */
export const AddDematAccountRequestSchema: GenMessage<AddDematAccountRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Demat, 2);

/**
 * @generated from message com.stablemoney.api.identity.AddDematAccountResponse
 */
export type AddDematAccountResponse =
  Message<"com.stablemoney.api.identity.AddDematAccountResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.AddDematAccountResponse.
 * Use `create(AddDematAccountResponseSchema)` to create a new message.
 */
export const AddDematAccountResponseSchema: GenMessage<AddDematAccountResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Demat, 3);
