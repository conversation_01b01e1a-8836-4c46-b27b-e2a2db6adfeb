// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Onboarding.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { KycType, OnBoardingStatus } from "./Kyc_pb.js";
import { file_public_models_identity_Kyc } from "./Kyc_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Onboarding.proto.
 */
export const file_public_models_identity_Onboarding: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CidwdWJsaWMvbW9kZWxzL2lkZW50aXR5L09uYm9hcmRpbmcucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiWAobVXNlck9uYm9hcmRpbmdTdGVwc1Jlc3BvbnNlEjkKBGRhdGEYAiADKAsyKy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlVzZXJTdGF0ZURhdGEijwEKDVVzZXJTdGF0ZURhdGESPgoPb25ib2FyZGluZ19zdGVwGAEgASgOMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5LeWNUeXBlEj4KBnN0YXR1cxgCIAEoDjIuLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuT25Cb2FyZGluZ1N0YXR1cyJiCh1PbmJvYXJkaW5nTW9kdWxlU3RlcHNSZXNwb25zZRJBCgRkYXRhGAEgAygLMjMuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5PbmJvYXJkaW5nTW9kdWxlU3RlcHMikwEKFU9uYm9hcmRpbmdNb2R1bGVTdGVwcxI+Cg9vbmJvYXJkaW5nX3N0ZXAYASABKA4yJS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lkt5Y1R5cGUSFAoMa3ljX3NlcXVlbmNlGAIgASgFEg4KBm1vZHVsZRgDIAEoCRIUCgxpc19za2lwcGFibGUYBCABKAgiVAoPT25ib2FyZGluZ1N0YXRlEjgKBG5leHQYASABKA4yJS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lkt5Y1R5cGVIAIgBAUIHCgVfbmV4dCIYChZTa2lwT25ib2FyZGluZ1Jlc3BvbnNlKn4KEE9uYm9hcmRpbmdNb2R1bGUSHQoZT05CT0FSRElOR19NT0RVTEVfVU5LTk9XThAAEhIKDkFQUF9PTkJPQVJESU5HEAESEQoNRklYRURfREVQT1NJVBACEg8KC01VVFVBTF9GVU5EEAMSEwoPQk9ORF9PTkJPQVJESU5HEARCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM",
    [file_public_models_identity_Kyc],
  );

/**
 * @generated from message com.stablemoney.api.identity.UserOnboardingStepsResponse
 */
export type UserOnboardingStepsResponse =
  Message<"com.stablemoney.api.identity.UserOnboardingStepsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.UserStateData data = 2;
     */
    data: UserStateData[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UserOnboardingStepsResponse.
 * Use `create(UserOnboardingStepsResponseSchema)` to create a new message.
 */
export const UserOnboardingStepsResponseSchema: GenMessage<UserOnboardingStepsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Onboarding, 0);

/**
 * @generated from message com.stablemoney.api.identity.UserStateData
 */
export type UserStateData =
  Message<"com.stablemoney.api.identity.UserStateData"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.KycType onboarding_step = 1;
     */
    onboardingStep: KycType;

    /**
     * @generated from field: com.stablemoney.api.identity.OnBoardingStatus status = 2;
     */
    status: OnBoardingStatus;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserStateData.
 * Use `create(UserStateDataSchema)` to create a new message.
 */
export const UserStateDataSchema: GenMessage<UserStateData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Onboarding, 1);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingModuleStepsResponse
 */
export type OnboardingModuleStepsResponse =
  Message<"com.stablemoney.api.identity.OnboardingModuleStepsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.OnboardingModuleSteps data = 1;
     */
    data: OnboardingModuleSteps[];
  };

/**
 * Describes the message com.stablemoney.api.identity.OnboardingModuleStepsResponse.
 * Use `create(OnboardingModuleStepsResponseSchema)` to create a new message.
 */
export const OnboardingModuleStepsResponseSchema: GenMessage<OnboardingModuleStepsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Onboarding, 2);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingModuleSteps
 */
export type OnboardingModuleSteps =
  Message<"com.stablemoney.api.identity.OnboardingModuleSteps"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.KycType onboarding_step = 1;
     */
    onboardingStep: KycType;

    /**
     * @generated from field: int32 kyc_sequence = 2;
     */
    kycSequence: number;

    /**
     * @generated from field: string module = 3;
     */
    module: string;

    /**
     * @generated from field: bool is_skippable = 4;
     */
    isSkippable: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.OnboardingModuleSteps.
 * Use `create(OnboardingModuleStepsSchema)` to create a new message.
 */
export const OnboardingModuleStepsSchema: GenMessage<OnboardingModuleSteps> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Onboarding, 3);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingState
 */
export type OnboardingState =
  Message<"com.stablemoney.api.identity.OnboardingState"> & {
    /**
     * @generated from field: optional com.stablemoney.api.identity.KycType next = 1;
     */
    next?: KycType;
  };

/**
 * Describes the message com.stablemoney.api.identity.OnboardingState.
 * Use `create(OnboardingStateSchema)` to create a new message.
 */
export const OnboardingStateSchema: GenMessage<OnboardingState> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Onboarding, 4);

/**
 * @generated from message com.stablemoney.api.identity.SkipOnboardingResponse
 */
export type SkipOnboardingResponse =
  Message<"com.stablemoney.api.identity.SkipOnboardingResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SkipOnboardingResponse.
 * Use `create(SkipOnboardingResponseSchema)` to create a new message.
 */
export const SkipOnboardingResponseSchema: GenMessage<SkipOnboardingResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Onboarding, 5);

/**
 * @generated from enum com.stablemoney.api.identity.OnboardingModule
 */
export enum OnboardingModule {
  /**
   * @generated from enum value: ONBOARDING_MODULE_UNKNOWN = 0;
   */
  ONBOARDING_MODULE_UNKNOWN = 0,

  /**
   * @generated from enum value: APP_ONBOARDING = 1;
   */
  APP_ONBOARDING = 1,

  /**
   * @generated from enum value: FIXED_DEPOSIT = 2;
   */
  FIXED_DEPOSIT = 2,

  /**
   * @generated from enum value: MUTUAL_FUND = 3;
   */
  MUTUAL_FUND = 3,

  /**
   * @generated from enum value: BOND_ONBOARDING = 4;
   */
  BOND_ONBOARDING = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.OnboardingModule.
 */
export const OnboardingModuleSchema: GenEnum<OnboardingModule> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Onboarding, 0);
