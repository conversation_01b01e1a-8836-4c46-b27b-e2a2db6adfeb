// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/WidgetCards.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/WidgetCards.proto.
 */
export const file_public_models_identity_WidgetCards: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CihwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1dpZGdldENhcmRzLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IosBChdIb21lTWVzc2FnaW5nV2lkZ2V0SXRlbRINCgV0aXRsZRgBIAEoCRITCgtkZXNjcmlwdGlvbhgCIAEoCRIQCghpY29uX3VybBgDIAEoCRIRCglpY29uX3R5cGUYBCABKAkSEgoKaWNvbl93aWR0aBgFIAEoARITCgtpY29uX2hlaWdodBgGIAEoASJ4ChtIb21lTWVzc2FnaW5nV2lkZ2V0UmVzcG9uc2USWQoaaG9tZV9tZXNzYWdpbmdfd2lkZ2V0X2l0ZW0YASADKAsyNS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkhvbWVNZXNzYWdpbmdXaWRnZXRJdGVtIkkKG01lc3NhZ2VXaWRnZXRNYXBwaW5nUmVxdWVzdBIZChFtZXNzYWdlX3dpZGdldF9pZBgBIAEoCRIPCgd1c2VyX2lkGAIgAygJIk0KHE1lc3NhZ2VXaWRnZXRNYXBwaW5nUmVzcG9uc2USFgoObWFwcGluZ3NfYWRkZWQYASABKAUSFQoNaW52YWxpZF91c2VycxgCIAEoBSpOCgpXaWRnZXRUeXBlEhcKE1dJREdFVF9UWVBFX1VOS05PV04QABIRCg1HTE9CQUxfV0lER0VUEAESFAoQSE9NRV9QQUdFX1dJREdFVBACQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z",
  );

/**
 * @generated from message com.stablemoney.api.identity.HomeMessagingWidgetItem
 */
export type HomeMessagingWidgetItem =
  Message<"com.stablemoney.api.identity.HomeMessagingWidgetItem"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: string icon_url = 3;
     */
    iconUrl: string;

    /**
     * @generated from field: string icon_type = 4;
     */
    iconType: string;

    /**
     * @generated from field: double icon_width = 5;
     */
    iconWidth: number;

    /**
     * @generated from field: double icon_height = 6;
     */
    iconHeight: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.HomeMessagingWidgetItem.
 * Use `create(HomeMessagingWidgetItemSchema)` to create a new message.
 */
export const HomeMessagingWidgetItemSchema: GenMessage<HomeMessagingWidgetItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_WidgetCards, 0);

/**
 * @generated from message com.stablemoney.api.identity.HomeMessagingWidgetResponse
 */
export type HomeMessagingWidgetResponse =
  Message<"com.stablemoney.api.identity.HomeMessagingWidgetResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.HomeMessagingWidgetItem home_messaging_widget_item = 1;
     */
    homeMessagingWidgetItem: HomeMessagingWidgetItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.HomeMessagingWidgetResponse.
 * Use `create(HomeMessagingWidgetResponseSchema)` to create a new message.
 */
export const HomeMessagingWidgetResponseSchema: GenMessage<HomeMessagingWidgetResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_WidgetCards, 1);

/**
 * @generated from message com.stablemoney.api.identity.MessageWidgetMappingRequest
 */
export type MessageWidgetMappingRequest =
  Message<"com.stablemoney.api.identity.MessageWidgetMappingRequest"> & {
    /**
     * @generated from field: string message_widget_id = 1;
     */
    messageWidgetId: string;

    /**
     * @generated from field: repeated string user_id = 2;
     */
    userId: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.MessageWidgetMappingRequest.
 * Use `create(MessageWidgetMappingRequestSchema)` to create a new message.
 */
export const MessageWidgetMappingRequestSchema: GenMessage<MessageWidgetMappingRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_WidgetCards, 2);

/**
 * @generated from message com.stablemoney.api.identity.MessageWidgetMappingResponse
 */
export type MessageWidgetMappingResponse =
  Message<"com.stablemoney.api.identity.MessageWidgetMappingResponse"> & {
    /**
     * @generated from field: int32 mappings_added = 1;
     */
    mappingsAdded: number;

    /**
     * @generated from field: int32 invalid_users = 2;
     */
    invalidUsers: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.MessageWidgetMappingResponse.
 * Use `create(MessageWidgetMappingResponseSchema)` to create a new message.
 */
export const MessageWidgetMappingResponseSchema: GenMessage<MessageWidgetMappingResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_WidgetCards, 3);

/**
 * @generated from enum com.stablemoney.api.identity.WidgetType
 */
export enum WidgetType {
  /**
   * @generated from enum value: WIDGET_TYPE_UNKNOWN = 0;
   */
  WIDGET_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: GLOBAL_WIDGET = 1;
   */
  GLOBAL_WIDGET = 1,

  /**
   * @generated from enum value: HOME_PAGE_WIDGET = 2;
   */
  HOME_PAGE_WIDGET = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.WidgetType.
 */
export const WidgetTypeSchema: GenEnum<WidgetType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_WidgetCards, 0);
