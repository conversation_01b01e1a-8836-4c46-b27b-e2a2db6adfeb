// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Questionnaire.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Questionnaire.proto.
 */
export const file_public_models_identity_Questionnaire: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CipwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1F1ZXN0aW9ubmFpcmUucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiXQoUUXVlc3Rpb25MaXN0UmVzcG9uc2USRQoNcXVlc3Rpb25fZGF0YRgBIAMoCzIuLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUXVlc3Rpb25SZXNwb25zZSL+AQoQUXVlc3Rpb25SZXNwb25zZRIKCgJpZBgBIAEoCRIQCghxdWVzdGlvbhgCIAEoCRITCgtkZXNjcmlwdGlvbhgDIAEoCRITCgtidXR0b25fdGV4dBgEIAEoCRIUCgxpc19za2lwcGFibGUYBSABKAgSQQoNcXVlc3Rpb25fdHlwZRgGIAEoDjIqLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUXVlc3Rpb25UeXBlEkkKC2Fuc3dlcl9kYXRhGAcgAygLMjQuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5RdWVzdGlvbkFuc3dlclJlc3BvbnNlIjQKFlF1ZXN0aW9uQW5zd2VyUmVzcG9uc2USCgoCaWQYASABKAkSDgoGYW5zd2VyGAIgASgJImoKHFF1ZXN0aW9uc0Fuc3dlclN1Ym1pdFJlcXVlc3QSNgoEZGF0YRgBIAMoCzIoLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQW5zd2VyRGF0YRISCgppc19za2lwcGVkGAIgASgIIjYKCkFuc3dlckRhdGESEwoLcXVlc3Rpb25faWQYASABKAkSEwoLYW5zd2VyX2RhdGEYAiADKAkiHwodUXVlc3Rpb25zQW5zd2VyU3VibWl0UmVzcG9uc2UqUQoMUXVlc3Rpb25UeXBlEhkKFVFVRVNUSU9OX1RZUEVfVU5LTk9XThAAEhEKDVNJTkdMRV9DSE9JQ0UQARITCg9NVUxUSVBMRV9DSE9JQ0UQAkIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.QuestionListResponse
 */
export type QuestionListResponse =
  Message<"com.stablemoney.api.identity.QuestionListResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.QuestionResponse question_data = 1;
     */
    questionData: QuestionResponse[];
  };

/**
 * Describes the message com.stablemoney.api.identity.QuestionListResponse.
 * Use `create(QuestionListResponseSchema)` to create a new message.
 */
export const QuestionListResponseSchema: GenMessage<QuestionListResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Questionnaire, 0);

/**
 * @generated from message com.stablemoney.api.identity.QuestionResponse
 */
export type QuestionResponse =
  Message<"com.stablemoney.api.identity.QuestionResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string question = 2;
     */
    question: string;

    /**
     * @generated from field: string description = 3;
     */
    description: string;

    /**
     * @generated from field: string button_text = 4;
     */
    buttonText: string;

    /**
     * @generated from field: bool is_skippable = 5;
     */
    isSkippable: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.QuestionType question_type = 6;
     */
    questionType: QuestionType;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.QuestionAnswerResponse answer_data = 7;
     */
    answerData: QuestionAnswerResponse[];
  };

/**
 * Describes the message com.stablemoney.api.identity.QuestionResponse.
 * Use `create(QuestionResponseSchema)` to create a new message.
 */
export const QuestionResponseSchema: GenMessage<QuestionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Questionnaire, 1);

/**
 * @generated from message com.stablemoney.api.identity.QuestionAnswerResponse
 */
export type QuestionAnswerResponse =
  Message<"com.stablemoney.api.identity.QuestionAnswerResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string answer = 2;
     */
    answer: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.QuestionAnswerResponse.
 * Use `create(QuestionAnswerResponseSchema)` to create a new message.
 */
export const QuestionAnswerResponseSchema: GenMessage<QuestionAnswerResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Questionnaire, 2);

/**
 * @generated from message com.stablemoney.api.identity.QuestionsAnswerSubmitRequest
 */
export type QuestionsAnswerSubmitRequest =
  Message<"com.stablemoney.api.identity.QuestionsAnswerSubmitRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.AnswerData data = 1;
     */
    data: AnswerData[];

    /**
     * @generated from field: bool is_skipped = 2;
     */
    isSkipped: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.QuestionsAnswerSubmitRequest.
 * Use `create(QuestionsAnswerSubmitRequestSchema)` to create a new message.
 */
export const QuestionsAnswerSubmitRequestSchema: GenMessage<QuestionsAnswerSubmitRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Questionnaire, 3);

/**
 * @generated from message com.stablemoney.api.identity.AnswerData
 */
export type AnswerData = Message<"com.stablemoney.api.identity.AnswerData"> & {
  /**
   * @generated from field: string question_id = 1;
   */
  questionId: string;

  /**
   * @generated from field: repeated string answer_data = 2;
   */
  answerData: string[];
};

/**
 * Describes the message com.stablemoney.api.identity.AnswerData.
 * Use `create(AnswerDataSchema)` to create a new message.
 */
export const AnswerDataSchema: GenMessage<AnswerData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Questionnaire, 4);

/**
 * @generated from message com.stablemoney.api.identity.QuestionsAnswerSubmitResponse
 */
export type QuestionsAnswerSubmitResponse =
  Message<"com.stablemoney.api.identity.QuestionsAnswerSubmitResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.QuestionsAnswerSubmitResponse.
 * Use `create(QuestionsAnswerSubmitResponseSchema)` to create a new message.
 */
export const QuestionsAnswerSubmitResponseSchema: GenMessage<QuestionsAnswerSubmitResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Questionnaire, 5);

/**
 * @generated from enum com.stablemoney.api.identity.QuestionType
 */
export enum QuestionType {
  /**
   * @generated from enum value: QUESTION_TYPE_UNKNOWN = 0;
   */
  QUESTION_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: SINGLE_CHOICE = 1;
   */
  SINGLE_CHOICE = 1,

  /**
   * @generated from enum value: MULTIPLE_CHOICE = 2;
   */
  MULTIPLE_CHOICE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.QuestionType.
 */
export const QuestionTypeSchema: GenEnum<QuestionType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Questionnaire, 0);
