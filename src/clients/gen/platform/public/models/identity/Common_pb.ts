// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Common.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Common.proto.
 */
export const file_public_models_identity_Common: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.identity.ErrorResponse
 */
export type ErrorResponse =
  Message<"com.stablemoney.api.identity.ErrorResponse"> & {
    /**
     * @generated from field: string error_code = 1;
     */
    errorCode: string;

    /**
     * @generated from field: string error_message = 2;
     */
    errorMessage: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.ErrorResponse.
 * Use `create(ErrorResponseSchema)` to create a new message.
 */
export const ErrorResponseSchema: GenMessage<ErrorResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 0);

/**
 * @generated from message com.stablemoney.api.identity.ConfigDataResponse
 */
export type ConfigDataResponse =
  Message<"com.stablemoney.api.identity.ConfigDataResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.ConfigData data = 1;
     */
    data: ConfigData[];
  };

/**
 * Describes the message com.stablemoney.api.identity.ConfigDataResponse.
 * Use `create(ConfigDataResponseSchema)` to create a new message.
 */
export const ConfigDataResponseSchema: GenMessage<ConfigDataResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 1);

/**
 * @generated from message com.stablemoney.api.identity.ConfigData
 */
export type ConfigData = Message<"com.stablemoney.api.identity.ConfigData"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.AppConfigType config_name = 1;
   */
  configName: AppConfigType;

  /**
   * @generated from field: string config_value = 2;
   */
  configValue: string;

  /**
   * @generated from field: com.stablemoney.api.identity.AppConfigValueType config_type = 3;
   */
  configType: AppConfigValueType;

  /**
   * @generated from field: int32 min_version = 4;
   */
  minVersion: number;

  /**
   * @generated from field: int32 max_version = 5;
   */
  maxVersion: number;

  /**
   * @generated from field: string description = 6;
   */
  description: string;
};

/**
 * Describes the message com.stablemoney.api.identity.ConfigData.
 * Use `create(ConfigDataSchema)` to create a new message.
 */
export const ConfigDataSchema: GenMessage<ConfigData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 2);

/**
 * @generated from message com.stablemoney.api.identity.PaginationFilter
 */
export type PaginationFilter =
  Message<"com.stablemoney.api.identity.PaginationFilter"> & {
    /**
     * @generated from field: int32 page = 1;
     */
    page: number;

    /**
     * @generated from field: optional int32 size = 2;
     */
    size?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.PaginationFilter.
 * Use `create(PaginationFilterSchema)` to create a new message.
 */
export const PaginationFilterSchema: GenMessage<PaginationFilter> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 3);

/**
 * @generated from message com.stablemoney.api.identity.DataKey
 */
export type DataKey = Message<"com.stablemoney.api.identity.DataKey"> & {
  /**
   * @generated from field: string key = 1;
   */
  key: string;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.DataKey.Variable context_variables = 3;
   */
  contextVariables: DataKey_Variable[];
};

/**
 * Describes the message com.stablemoney.api.identity.DataKey.
 * Use `create(DataKeySchema)` to create a new message.
 */
export const DataKeySchema: GenMessage<DataKey> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 4);

/**
 * @generated from message com.stablemoney.api.identity.DataKey.Variable
 */
export type DataKey_Variable =
  Message<"com.stablemoney.api.identity.DataKey.Variable"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @generated from field: com.stablemoney.api.identity.DataKey.VariableType type = 2;
     */
    type: DataKey_VariableType;

    /**
     * @generated from field: string value = 3;
     */
    value: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.DataKey.Variable.
 * Use `create(DataKey_VariableSchema)` to create a new message.
 */
export const DataKey_VariableSchema: GenMessage<DataKey_Variable> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 4, 0);

/**
 * @generated from enum com.stablemoney.api.identity.DataKey.VariableType
 */
export enum DataKey_VariableType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: STRING = 1;
   */
  STRING = 1,

  /**
   * @generated from enum value: CURRENCY = 2;
   */
  CURRENCY = 2,

  /**
   * @generated from enum value: DATE = 3;
   */
  DATE = 3,

  /**
   * @generated from enum value: DATE_TIME = 4;
   */
  DATE_TIME = 4,

  /**
   * @generated from enum value: USER_FIRST_NAME = 5;
   */
  USER_FIRST_NAME = 5,

  /**
   * @generated from enum value: SHORT_CURRENCY = 6;
   */
  SHORT_CURRENCY = 6,

  /**
   * @generated from enum value: PERCENT = 7;
   */
  PERCENT = 7,

  /**
   * @generated from enum value: PERCENT_2F = 8;
   */
  PERCENT_2F = 8,

  /**
   * @generated from enum value: NUMBER = 9;
   */
  NUMBER = 9,
}

/**
 * Describes the enum com.stablemoney.api.identity.DataKey.VariableType.
 */
export const DataKey_VariableTypeSchema: GenEnum<DataKey_VariableType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 4, 0);

/**
 * @generated from message com.stablemoney.api.identity.PaginationResponse
 */
export type PaginationResponse =
  Message<"com.stablemoney.api.identity.PaginationResponse"> & {
    /**
     * @generated from field: bool hasNextPage = 1;
     */
    hasNextPage: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.PaginationResponse.
 * Use `create(PaginationResponseSchema)` to create a new message.
 */
export const PaginationResponseSchema: GenMessage<PaginationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 5);

/**
 * @generated from message com.stablemoney.api.identity.PaginationRequest
 */
export type PaginationRequest =
  Message<"com.stablemoney.api.identity.PaginationRequest"> & {
    /**
     * @generated from field: int32 page = 1;
     */
    page: number;

    /**
     * @generated from field: int32 size = 2;
     */
    size: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.PaginationRequest.
 * Use `create(PaginationRequestSchema)` to create a new message.
 */
export const PaginationRequestSchema: GenMessage<PaginationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Common, 6);

/**
 * @generated from enum com.stablemoney.api.identity.MessageType
 */
export enum MessageType {
  /**
   * @generated from enum value: UNKNOWN_MESSAGE_TYPE = 0;
   */
  UNKNOWN_MESSAGE_TYPE = 0,

  /**
   * @generated from enum value: PROMOTIONAL_MESSAGE = 1;
   */
  PROMOTIONAL_MESSAGE = 1,

  /**
   * @generated from enum value: TRANSACTIONAL_MESSAGE = 2;
   */
  TRANSACTIONAL_MESSAGE = 2,

  /**
   * @generated from enum value: OTP_MESSAGE = 3;
   */
  OTP_MESSAGE = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.MessageType.
 */
export const MessageTypeSchema: GenEnum<MessageType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 0);

/**
 * @generated from enum com.stablemoney.api.identity.ContentType
 */
export enum ContentType {
  /**
   * @generated from enum value: UNKNOWN_CONTENT_TYPE = 0;
   */
  UNKNOWN_CONTENT_TYPE = 0,

  /**
   * @generated from enum value: TEXT = 1;
   */
  TEXT = 1,

  /**
   * @generated from enum value: UNICODE = 2;
   */
  UNICODE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.ContentType.
 */
export const ContentTypeSchema: GenEnum<ContentType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 1);

/**
 * @generated from enum com.stablemoney.api.identity.OptinStatus
 */
export enum OptinStatus {
  /**
   * @generated from enum value: UNKNOWN_OPTIN_STATUS = 0;
   */
  UNKNOWN_OPTIN_STATUS = 0,

  /**
   * @generated from enum value: OPTED_IN = 1;
   */
  OPTED_IN = 1,

  /**
   * @generated from enum value: OPTED_OUT = 2;
   */
  OPTED_OUT = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.OptinStatus.
 */
export const OptinStatusSchema: GenEnum<OptinStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 2);

/**
 * @generated from enum com.stablemoney.api.identity.WhatsappProviderType
 */
export enum WhatsappProviderType {
  /**
   * @generated from enum value: UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0;
   */
  UNKNOWN_WHATSAPP_PROVIDER_TYPE = 0,

  /**
   * @generated from enum value: GUPSHUP = 1;
   */
  GUPSHUP = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.WhatsappProviderType.
 */
export const WhatsappProviderTypeSchema: GenEnum<WhatsappProviderType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 3);

/**
 * @generated from enum com.stablemoney.api.identity.AppConfigType
 */
export enum AppConfigType {
  /**
   * @generated from enum value: APP_CONFIG_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: BANK_VERIFICATION_RPD = 1;
   */
  BANK_VERIFICATION_RPD = 1,

  /**
   * @generated from enum value: BANK_VERIFICATION_PD = 2;
   */
  BANK_VERIFICATION_PD = 2,

  /**
   * @generated from enum value: BASIC_DETAILS_QUESTIONS = 3;
   */
  BASIC_DETAILS_QUESTIONS = 3,

  /**
   * @generated from enum value: PAN_CVL_CHECK = 4;
   */
  PAN_CVL_CHECK = 4,

  /**
   * @generated from enum value: RPD_SUPPORTED_UPI_APPS = 5;
   */
  RPD_SUPPORTED_UPI_APPS = 5,

  /**
   * @generated from enum value: CREDIT_REPORT = 6;
   */
  CREDIT_REPORT = 6,

  /**
   * @generated from enum value: MOBILE_RESEND = 7;
   */
  MOBILE_RESEND = 7,

  /**
   * @generated from enum value: MOBILE_ATTEMPT = 8;
   */
  MOBILE_ATTEMPT = 8,

  /**
   * @generated from enum value: MOBILE_OTP_LENGTH = 9;
   */
  MOBILE_OTP_LENGTH = 9,

  /**
   * @generated from enum value: EMAIL_OTP_LENGTH = 10;
   */
  EMAIL_OTP_LENGTH = 10,

  /**
   * @generated from enum value: EMAIL_RESEND = 11;
   */
  EMAIL_RESEND = 11,

  /**
   * @generated from enum value: EMAIL_ATTEMPT = 12;
   */
  EMAIL_ATTEMPT = 12,

  /**
   * @generated from enum value: ANDROID_APP_VERSION = 13;
   */
  ANDROID_APP_VERSION = 13,

  /**
   * @generated from enum value: IOS_APP_VERSION = 14;
   */
  IOS_APP_VERSION = 14,

  /**
   * @generated from enum value: WEB_APP_VERSION = 15;
   */
  WEB_APP_VERSION = 15,

  /**
   * @generated from enum value: MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16;
   */
  MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16,

  /**
   * @generated from enum value: EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17;
   */
  EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17,

  /**
   * @generated from enum value: MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18;
   */
  MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18,

  /**
   * @generated from enum value: EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19;
   */
  EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19,

  /**
   * @generated from enum value: EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20;
   */
  EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20,

  /**
   * @generated from enum value: EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21;
   */
  EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21,

  /**
   * @generated from enum value: MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22;
   */
  MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22,

  /**
   * @generated from enum value: MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23;
   */
  MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23,

  /**
   * @generated from enum value: MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24;
   */
  MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24,

  /**
   * @generated from enum value: EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25;
   */
  EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25,

  /**
   * @generated from enum value: MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26;
   */
  MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26,

  /**
   * @generated from enum value: TOKEN_VALIDITY_IN_HOURS = 27;
   */
  TOKEN_VALIDITY_IN_HOURS = 27,

  /**
   * @generated from enum value: REFRESH_TOKEN_VALIDITY_IN_HOURS = 28;
   */
  REFRESH_TOKEN_VALIDITY_IN_HOURS = 28,

  /**
   * @generated from enum value: ENCRYPTION_PUBLIC_KEY = 29;
   */
  ENCRYPTION_PUBLIC_KEY = 29,

  /**
   * @generated from enum value: ZOHO_CONFIG = 30;
   */
  ZOHO_CONFIG = 30,

  /**
   * @generated from enum value: IN_APP_RATING_CONFIG = 31;
   */
  IN_APP_RATING_CONFIG = 31,

  /**
   * @generated from enum value: GT_GRACE_PERIOD_IN_DAYS = 32;
   */
  GT_GRACE_PERIOD_IN_DAYS = 32,

  /**
   * @generated from enum value: RSA_ENCRYPTION_PUBLIC_KEY = 33;
   */
  RSA_ENCRYPTION_PUBLIC_KEY = 33,
}

/**
 * Describes the enum com.stablemoney.api.identity.AppConfigType.
 */
export const AppConfigTypeSchema: GenEnum<AppConfigType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 4);

/**
 * @generated from enum com.stablemoney.api.identity.AppConfigValueType
 */
export enum AppConfigValueType {
  /**
   * @generated from enum value: APP_CONFIG_VALUE_TYPE_UNKNOWN = 0;
   */
  APP_CONFIG_VALUE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: TEXT_TYPE = 1;
   */
  TEXT_TYPE = 1,

  /**
   * @generated from enum value: BOOLEAN_TYPE = 2;
   */
  BOOLEAN_TYPE = 2,

  /**
   * @generated from enum value: STRING_TYPE = 3;
   */
  STRING_TYPE = 3,

  /**
   * @generated from enum value: JSON_TYPE = 4;
   */
  JSON_TYPE = 4,

  /**
   * @generated from enum value: INTEGER_TYPE = 5;
   */
  INTEGER_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.AppConfigValueType.
 */
export const AppConfigValueTypeSchema: GenEnum<AppConfigValueType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 5);

/**
 * @generated from enum com.stablemoney.api.identity.PushNotificationType
 */
export enum PushNotificationType {
  /**
   * @generated from enum value: UNKNOWN_PUSH_NOTIFICATION_TYPE = 0;
   */
  UNKNOWN_PUSH_NOTIFICATION_TYPE = 0,

  /**
   * @generated from enum value: TD_BOOKED_COMPLETE = 1;
   */
  TD_BOOKED_COMPLETE = 1,

  /**
   * @generated from enum value: PAYMENT_SUCCESS = 2;
   */
  PAYMENT_SUCCESS = 2,

  /**
   * @generated from enum value: TD_RESUME = 3;
   */
  TD_RESUME = 3,

  /**
   * @generated from enum value: PAYMENT_FAILURE = 4;
   */
  PAYMENT_FAILURE = 4,

  /**
   * @generated from enum value: VKYC_SUCCESS = 5;
   */
  VKYC_SUCCESS = 5,

  /**
   * @generated from enum value: VKYC_REMINDER = 6;
   */
  VKYC_REMINDER = 6,

  /**
   * @generated from enum value: VKYC_RETRY = 7;
   */
  VKYC_RETRY = 7,

  /**
   * @generated from enum value: TD_WITHDRAWAL_COMPLETE = 8;
   */
  TD_WITHDRAWAL_COMPLETE = 8,

  /**
   * @generated from enum value: TD_REJECTED = 9;
   */
  TD_REJECTED = 9,

  /**
   * @generated from enum value: TD_MATURITY = 10;
   */
  TD_MATURITY = 10,

  /**
   * @generated from enum value: TD_BOOKED_ACK = 11;
   */
  TD_BOOKED_ACK = 11,

  /**
   * @generated from enum value: PROGRESS_SAVED = 12;
   */
  PROGRESS_SAVED = 12,

  /**
   * @generated from enum value: INVEST_INSTANT_FDS = 13;
   */
  INVEST_INSTANT_FDS = 13,

  /**
   * @generated from enum value: TICKET_ADDED = 14;
   */
  TICKET_ADDED = 14,

  /**
   * @generated from enum value: NTB_AUTH_ERROR = 15;
   */
  NTB_AUTH_ERROR = 15,

  /**
   * @generated from enum value: ETB_AUTH_ERROR = 16;
   */
  ETB_AUTH_ERROR = 16,

  /**
   * @generated from enum value: BINDING_SUCCESS = 17;
   */
  BINDING_SUCCESS = 17,

  /**
   * @generated from enum value: VKYC_FAILURE = 18;
   */
  VKYC_FAILURE = 18,

  /**
   * @generated from enum value: TD_MATURITY_INSTRUCTION_UPDATE_DUE = 19;
   */
  TD_MATURITY_INSTRUCTION_UPDATE_DUE = 19,

  /**
   * @generated from enum value: TD_RENEWAL_COMPLETE = 20;
   */
  TD_RENEWAL_COMPLETE = 20,
}

/**
 * Describes the enum com.stablemoney.api.identity.PushNotificationType.
 */
export const PushNotificationTypeSchema: GenEnum<PushNotificationType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Common, 6);
