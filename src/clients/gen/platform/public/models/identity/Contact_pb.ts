// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Contact.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Contact.proto.
 */
export const file_public_models_identity_Contact: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiRwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0NvbnRhY3QucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiUAoWU2F2ZUNvbnRhY3RMaXN0UmVxdWVzdBI2Cgdjb250YWN0GAEgAygLMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Db250YWN0IlkKB0NvbnRhY3QSDAoEbmFtZRgBIAEoCRIUCgxwaG9uZV9udW1iZXIYAiABKAkSGQoMaXNfb25ib2FyZGVkGAMgASgISACIAQFCDwoNX2lzX29uYm9hcmRlZCIZChdTYXZlQ29udGFjdExpc3RSZXNwb25zZSJQChZHZXRDb250YWN0TGlzdFJlc3BvbnNlEjYKB2NvbnRhY3QYASADKAsyJS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkNvbnRhY3RCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM",
  );

/**
 * @generated from message com.stablemoney.api.identity.SaveContactListRequest
 */
export type SaveContactListRequest =
  Message<"com.stablemoney.api.identity.SaveContactListRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.Contact contact = 1;
     */
    contact: Contact[];
  };

/**
 * Describes the message com.stablemoney.api.identity.SaveContactListRequest.
 * Use `create(SaveContactListRequestSchema)` to create a new message.
 */
export const SaveContactListRequestSchema: GenMessage<SaveContactListRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Contact, 0);

/**
 * @generated from message com.stablemoney.api.identity.Contact
 */
export type Contact = Message<"com.stablemoney.api.identity.Contact"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string phone_number = 2;
   */
  phoneNumber: string;

  /**
   * @generated from field: optional bool is_onboarded = 3;
   */
  isOnboarded?: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.Contact.
 * Use `create(ContactSchema)` to create a new message.
 */
export const ContactSchema: GenMessage<Contact> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Contact, 1);

/**
 * @generated from message com.stablemoney.api.identity.SaveContactListResponse
 */
export type SaveContactListResponse =
  Message<"com.stablemoney.api.identity.SaveContactListResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SaveContactListResponse.
 * Use `create(SaveContactListResponseSchema)` to create a new message.
 */
export const SaveContactListResponseSchema: GenMessage<SaveContactListResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Contact, 2);

/**
 * @generated from message com.stablemoney.api.identity.GetContactListResponse
 */
export type GetContactListResponse =
  Message<"com.stablemoney.api.identity.GetContactListResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.Contact contact = 1;
     */
    contact: Contact[];
  };

/**
 * Describes the message com.stablemoney.api.identity.GetContactListResponse.
 * Use `create(GetContactListResponseSchema)` to create a new message.
 */
export const GetContactListResponseSchema: GenMessage<GetContactListResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Contact, 3);
