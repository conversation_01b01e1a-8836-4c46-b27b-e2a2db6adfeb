// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/City.proto (package com.stablemoney.api.location.city, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/City.proto.
 */
export const file_public_models_identity_City: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiFwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0NpdHkucHJvdG8SIWNvbS5zdGFibGVtb25leS5hcGkubG9jYXRpb24uY2l0eSJwCgxDaXR5UmVzcG9uc2USCgoCaWQYASABKAkSDAoEbmFtZRgCIAEoCRINCgVzdGF0ZRgDIAEoCRIPCgdjb3VudHJ5GAQgASgJEhIKCmlzX3BvcHVsYXIYBSABKAgSEgoKYWxpYXNfbGlzdBgGIAMoCSKhAQoRR2V0Q2l0aWVzUmVzcG9uc2USPwoGY2l0aWVzGAEgAygLMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5sb2NhdGlvbi5jaXR5LkNpdHlSZXNwb25zZRJHCg5wb3B1bGFyX2NpdGllcxgCIAMoCzIvLmNvbS5zdGFibGVtb25leS5hcGkubG9jYXRpb24uY2l0eS5DaXR5UmVzcG9uc2U6AhgBImMKGEdldFBvcHVsYXJDaXRpZXNSZXNwb25zZRJHCg5wb3B1bGFyX2NpdGllcxgCIAMoCzIvLmNvbS5zdGFibGVtb25leS5hcGkubG9jYXRpb24uY2l0eS5DaXR5UmVzcG9uc2UiVQoSU2VhcmNoQ2l0eVJlc3BvbnNlEj8KBmNpdGllcxgCIAMoCzIvLmNvbS5zdGFibGVtb25leS5hcGkubG9jYXRpb24uY2l0eS5DaXR5UmVzcG9uc2VCJQohY29tLnN0YWJsZW1vbmV5LmFwaS5sb2NhdGlvbi5jaXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.location.city.CityResponse
 */
export type CityResponse =
  Message<"com.stablemoney.api.location.city.CityResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string state = 3;
     */
    state: string;

    /**
     * @generated from field: string country = 4;
     */
    country: string;

    /**
     * @generated from field: bool is_popular = 5;
     */
    isPopular: boolean;

    /**
     * @generated from field: repeated string alias_list = 6;
     */
    aliasList: string[];
  };

/**
 * Describes the message com.stablemoney.api.location.city.CityResponse.
 * Use `create(CityResponseSchema)` to create a new message.
 */
export const CityResponseSchema: GenMessage<CityResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_City, 0);

/**
 * @generated from message com.stablemoney.api.location.city.GetCitiesResponse
 * @deprecated
 */
export type GetCitiesResponse =
  Message<"com.stablemoney.api.location.city.GetCitiesResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.location.city.CityResponse cities = 1;
     */
    cities: CityResponse[];

    /**
     * @generated from field: repeated com.stablemoney.api.location.city.CityResponse popular_cities = 2;
     */
    popularCities: CityResponse[];
  };

/**
 * Describes the message com.stablemoney.api.location.city.GetCitiesResponse.
 * Use `create(GetCitiesResponseSchema)` to create a new message.
 * @deprecated
 */
export const GetCitiesResponseSchema: GenMessage<GetCitiesResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_City, 1);

/**
 * @generated from message com.stablemoney.api.location.city.GetPopularCitiesResponse
 */
export type GetPopularCitiesResponse =
  Message<"com.stablemoney.api.location.city.GetPopularCitiesResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.location.city.CityResponse popular_cities = 2;
     */
    popularCities: CityResponse[];
  };

/**
 * Describes the message com.stablemoney.api.location.city.GetPopularCitiesResponse.
 * Use `create(GetPopularCitiesResponseSchema)` to create a new message.
 */
export const GetPopularCitiesResponseSchema: GenMessage<GetPopularCitiesResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_City, 2);

/**
 * @generated from message com.stablemoney.api.location.city.SearchCityResponse
 */
export type SearchCityResponse =
  Message<"com.stablemoney.api.location.city.SearchCityResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.location.city.CityResponse cities = 2;
     */
    cities: CityResponse[];
  };

/**
 * Describes the message com.stablemoney.api.location.city.SearchCityResponse.
 * Use `create(SearchCityResponseSchema)` to create a new message.
 */
export const SearchCityResponseSchema: GenMessage<SearchCityResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_City, 3);
