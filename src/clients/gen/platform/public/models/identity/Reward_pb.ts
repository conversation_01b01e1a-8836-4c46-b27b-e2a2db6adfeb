// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Reward.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Reward.proto.
 */
export const file_public_models_identity_Reward: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [file_google_protobuf_timestamp],
  );

/**
 * @generated from message com.stablemoney.api.identity.VoucherItem
 */
export type VoucherItem =
  Message<"com.stablemoney.api.identity.VoucherItem"> & {
    /**
     * @generated from field: string voucher_code = 1;
     */
    voucherCode: string;

    /**
     * @generated from field: double amount = 2;
     */
    amount: number;

    /**
     * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 3;
     */
    rewardCode: RewardCode;

    /**
     * @generated from field: com.stablemoney.api.identity.VoucherStatus voucher_status = 4;
     */
    voucherStatus: VoucherStatus;

    /**
     * @generated from field: optional google.protobuf.Timestamp expiry_date = 5;
     */
    expiryDate?: Timestamp;

    /**
     * @generated from field: com.stablemoney.api.identity.VoucherType voucher_type = 6;
     */
    voucherType: VoucherType;
  };

/**
 * Describes the message com.stablemoney.api.identity.VoucherItem.
 * Use `create(VoucherItemSchema)` to create a new message.
 */
export const VoucherItemSchema: GenMessage<VoucherItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 0);

/**
 * @generated from message com.stablemoney.api.identity.BulkVoucherRequest
 */
export type BulkVoucherRequest =
  Message<"com.stablemoney.api.identity.BulkVoucherRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.VoucherItem voucher_items_list = 1;
     */
    voucherItemsList: VoucherItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BulkVoucherRequest.
 * Use `create(BulkVoucherRequestSchema)` to create a new message.
 */
export const BulkVoucherRequestSchema: GenMessage<BulkVoucherRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 1);

/**
 * @generated from message com.stablemoney.api.identity.AddRewardTransactionRequest
 */
export type AddRewardTransactionRequest =
  Message<"com.stablemoney.api.identity.AddRewardTransactionRequest"> & {
    /**
     * @generated from field: string reward_link = 1;
     */
    rewardLink: string;

    /**
     * @generated from field: string phone_number = 2;
     */
    phoneNumber: string;

    /**
     * @generated from field: google.protobuf.Timestamp transaction_timestamp = 3;
     */
    transactionTimestamp?: Timestamp;
  };

/**
 * Describes the message com.stablemoney.api.identity.AddRewardTransactionRequest.
 * Use `create(AddRewardTransactionRequestSchema)` to create a new message.
 */
export const AddRewardTransactionRequestSchema: GenMessage<AddRewardTransactionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 2);

/**
 * @generated from message com.stablemoney.api.identity.BulkAddRewardTransactionsRequest
 */
export type BulkAddRewardTransactionsRequest =
  Message<"com.stablemoney.api.identity.BulkAddRewardTransactionsRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.AddRewardTransactionRequest transactions = 1;
     */
    transactions: AddRewardTransactionRequest[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BulkAddRewardTransactionsRequest.
 * Use `create(BulkAddRewardTransactionsRequestSchema)` to create a new message.
 */
export const BulkAddRewardTransactionsRequestSchema: GenMessage<BulkAddRewardTransactionsRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 3);

/**
 * @generated from message com.stablemoney.api.identity.ManualAddRewardRequest
 */
export type ManualAddRewardRequest =
  Message<"com.stablemoney.api.identity.ManualAddRewardRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 1;
     */
    rewardCode: RewardCode;

    /**
     * @generated from field: string user_id = 2;
     */
    userId: string;

    /**
     * @generated from field: optional string referrer_user_id = 3;
     */
    referrerUserId?: string;

    /**
     * @generated from field: double amount = 4;
     */
    amount: number;

    /**
     * @generated from field: string bank_id = 5;
     */
    bankId: string;

    /**
     * @generated from field: optional bool consume_booking = 6;
     */
    consumeBooking?: boolean;

    /**
     * @generated from field: optional bool override_payment_time_check = 7;
     */
    overridePaymentTimeCheck?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.ManualAddRewardRequest.
 * Use `create(ManualAddRewardRequestSchema)` to create a new message.
 */
export const ManualAddRewardRequestSchema: GenMessage<ManualAddRewardRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 4);

/**
 * @generated from message com.stablemoney.api.identity.CheckGoldenTicketRequest
 */
export type CheckGoldenTicketRequest =
  Message<"com.stablemoney.api.identity.CheckGoldenTicketRequest"> & {
    /**
     * @generated from field: repeated string userIdList = 1;
     */
    userIdList: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.CheckGoldenTicketRequest.
 * Use `create(CheckGoldenTicketRequestSchema)` to create a new message.
 */
export const CheckGoldenTicketRequestSchema: GenMessage<CheckGoldenTicketRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 5);

/**
 * @generated from message com.stablemoney.api.identity.RewardItem
 */
export type RewardItem = Message<"com.stablemoney.api.identity.RewardItem"> & {
  /**
   * @generated from field: string reward_link = 1;
   */
  rewardLink: string;

  /**
   * @generated from field: google.protobuf.Timestamp reward_sent_timestamp = 2;
   */
  rewardSentTimestamp?: Timestamp;

  /**
   * @generated from field: double reward_amount = 3;
   */
  rewardAmount: number;

  /**
   * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 4;
   */
  rewardCode: RewardCode;

  /**
   * @generated from field: com.stablemoney.api.identity.RewardDeliveryStatus reward_delivery_status = 5;
   */
  rewardDeliveryStatus: RewardDeliveryStatus;
};

/**
 * Describes the message com.stablemoney.api.identity.RewardItem.
 * Use `create(RewardItemSchema)` to create a new message.
 */
export const RewardItemSchema: GenMessage<RewardItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 6);

/**
 * @generated from message com.stablemoney.api.identity.RewardsDataResponse
 */
export type RewardsDataResponse =
  Message<"com.stablemoney.api.identity.RewardsDataResponse"> & {
    /**
     * @generated from field: double total_earnings = 1;
     */
    totalEarnings: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RewardItem rewards = 2;
     */
    rewards: RewardItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.RewardsDataResponse.
 * Use `create(RewardsDataResponseSchema)` to create a new message.
 */
export const RewardsDataResponseSchema: GenMessage<RewardsDataResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 7);

/**
 * @generated from message com.stablemoney.api.identity.RewardTypesResponse
 */
export type RewardTypesResponse =
  Message<"com.stablemoney.api.identity.RewardTypesResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RewardType reward_types = 1;
     */
    rewardTypes: RewardType[];
  };

/**
 * Describes the message com.stablemoney.api.identity.RewardTypesResponse.
 * Use `create(RewardTypesResponseSchema)` to create a new message.
 */
export const RewardTypesResponseSchema: GenMessage<RewardTypesResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 8);

/**
 * @generated from message com.stablemoney.api.identity.RewardTypesRequest
 */
export type RewardTypesRequest =
  Message<"com.stablemoney.api.identity.RewardTypesRequest"> & {
    /**
     * @generated from field: optional com.stablemoney.api.identity.RewardCode rewardCode = 1;
     */
    rewardCode?: RewardCode;
  };

/**
 * Describes the message com.stablemoney.api.identity.RewardTypesRequest.
 * Use `create(RewardTypesRequestSchema)` to create a new message.
 */
export const RewardTypesRequestSchema: GenMessage<RewardTypesRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 9);

/**
 * @generated from message com.stablemoney.api.identity.RewardType
 */
export type RewardType = Message<"com.stablemoney.api.identity.RewardType"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: double amount = 2;
   */
  amount: number;

  /**
   * @generated from field: uint32 max_count = 3;
   */
  maxCount: number;

  /**
   * @generated from field: double min_transaction_amount = 4;
   */
  minTransactionAmount: number;

  /**
   * @generated from field: com.stablemoney.api.identity.RewardCode reward_code = 5;
   */
  rewardCode: RewardCode;

  /**
   * @generated from field: optional uint64 valid_from = 6;
   */
  validFrom?: bigint;

  /**
   * @generated from field: optional uint64 valid_till = 7;
   */
  validTill?: bigint;

  /**
   * @generated from field: optional double instant_reward_tr_amount = 8;
   */
  instantRewardTrAmount?: number;

  /**
   * @generated from field: optional uint32 delivery_delay_in_days = 9;
   */
  deliveryDelayInDays?: number;

  /**
   * @generated from field: optional float referral_percent = 10;
   */
  referralPercent?: number;

  /**
   * @generated from field: optional uint32 referral_level = 11;
   */
  referralLevel?: number;

  /**
   * @generated from field: optional double referer_min_net_worth = 12;
   */
  refererMinNetWorth?: number;

  /**
   * @generated from field: optional double golden_ticket_amount = 13;
   */
  goldenTicketAmount?: number;

  /**
   * @generated from field: optional uint32 randomized_reward_percentage = 14;
   */
  randomizedRewardPercentage?: number;

  /**
   * @generated from field: optional uint32 referral_count = 15;
   */
  referralCount?: number;
};

/**
 * Describes the message com.stablemoney.api.identity.RewardType.
 * Use `create(RewardTypeSchema)` to create a new message.
 */
export const RewardTypeSchema: GenMessage<RewardType> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Reward, 10);

/**
 * @generated from enum com.stablemoney.api.identity.VoucherType
 */
export enum VoucherType {
  /**
   * @generated from enum value: VOUCHER_TYPE_UNKNOWN = 0;
   */
  VOUCHER_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: AMAZON_PAY_VOUCHER = 1;
   */
  AMAZON_PAY_VOUCHER = 1,

  /**
   * @generated from enum value: AMAZON_VOUCHER_CODE = 2;
   */
  AMAZON_VOUCHER_CODE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.VoucherType.
 */
export const VoucherTypeSchema: GenEnum<VoucherType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Reward, 0);

/**
 * @generated from enum com.stablemoney.api.identity.RewardCode
 */
export enum RewardCode {
  /**
   * @generated from enum value: REWARD_CODE_UNKNOWN = 0;
   */
  REWARD_CODE_UNKNOWN = 0,

  /**
   * @generated from enum value: FIRST_FD_REWARD = 1;
   */
  FIRST_FD_REWARD = 1,

  /**
   * @generated from enum value: REFERRAL_REWARD = 2;
   */
  REFERRAL_REWARD = 2,

  /**
   * @generated from enum value: INVESTMENT_BASED_REFERRAL_REWARD = 3;
   */
  INVESTMENT_BASED_REFERRAL_REWARD = 3,

  /**
   * @generated from enum value: REFEREE_FIRST_FD_REWARD = 4;
   */
  REFEREE_FIRST_FD_REWARD = 4,

  /**
   * @generated from enum value: EMERGENCY_FUND_REFERRAL_REWARD = 5;
   */
  EMERGENCY_FUND_REFERRAL_REWARD = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.RewardCode.
 */
export const RewardCodeSchema: GenEnum<RewardCode> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Reward, 1);

/**
 * @generated from enum com.stablemoney.api.identity.VoucherStatus
 */
export enum VoucherStatus {
  /**
   * @generated from enum value: VOUCHER_STATUS_UNKNOWN = 0;
   */
  VOUCHER_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: SENT = 1;
   */
  SENT = 1,

  /**
   * @generated from enum value: AVAILABLE = 2;
   */
  AVAILABLE = 2,

  /**
   * @generated from enum value: EXPIRED = 3;
   */
  EXPIRED = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.VoucherStatus.
 */
export const VoucherStatusSchema: GenEnum<VoucherStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Reward, 2);

/**
 * @generated from enum com.stablemoney.api.identity.RewardDeliveryStatus
 */
export enum RewardDeliveryStatus {
  /**
   * @generated from enum value: REWARD_PROCESSING_STATUS_UNKNOWN = 0;
   */
  REWARD_PROCESSING_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: QUEUED = 1;
   */
  QUEUED = 1,

  /**
   * @generated from enum value: FAILED = 2;
   */
  FAILED = 2,

  /**
   * @generated from enum value: SENT_FROM_BACKEND = 3;
   */
  SENT_FROM_BACKEND = 3,

  /**
   * @generated from enum value: DELIVERED = 4;
   */
  DELIVERED = 4,

  /**
   * @generated from enum value: OPENED = 5;
   */
  OPENED = 5,

  /**
   * @generated from enum value: REWARD_TO_BE_SENT = 6;
   */
  REWARD_TO_BE_SENT = 6,

  /**
   * @generated from enum value: REWARD_NOT_ELIGIBLE = 7;
   */
  REWARD_NOT_ELIGIBLE = 7,
}

/**
 * Describes the enum com.stablemoney.api.identity.RewardDeliveryStatus.
 */
export const RewardDeliveryStatusSchema: GenEnum<RewardDeliveryStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Reward, 3);

/**
 * @generated from enum com.stablemoney.api.identity.BlacklistUserType
 */
export enum BlacklistUserType {
  /**
   * @generated from enum value: BLACKLIST_USER_TYPE_UNKNOWN = 0;
   */
  BLACKLIST_USER_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: INTERNAL = 1;
   */
  INTERNAL = 1,

  /**
   * @generated from enum value: EXTERNAL = 2;
   */
  EXTERNAL = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.BlacklistUserType.
 */
export const BlacklistUserTypeSchema: GenEnum<BlacklistUserType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Reward, 4);

/**
 * @generated from enum com.stablemoney.api.identity.ReferralStatus
 */
export enum ReferralStatus {
  /**
   * @generated from enum value: REFERRAL_STATUS_UNKNOWN = 0;
   */
  REFERRAL_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: BOOKING_NOT_INITIATED = 1;
   */
  BOOKING_NOT_INITIATED = 1,

  /**
   * @generated from enum value: BOOKING_DONE = 2;
   */
  BOOKING_DONE = 2,

  /**
   * @generated from enum value: REWARD_PROCESSED = 3;
   */
  REWARD_PROCESSED = 3,

  /**
   * @generated from enum value: REWARD_NOT_APPLICABLE = 4;
   */
  REWARD_NOT_APPLICABLE = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.ReferralStatus.
 */
export const ReferralStatusSchema: GenEnum<ReferralStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Reward, 5);
