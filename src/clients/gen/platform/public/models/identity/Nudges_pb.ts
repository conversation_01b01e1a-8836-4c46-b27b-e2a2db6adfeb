// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Nudges.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Nudges.proto.
 */
export const file_public_models_identity_Nudges: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiNwdWJsaWMvbW9kZWxzL2lkZW50aXR5L051ZGdlcy5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSJkCg1OdWRnZVJlc3BvbnNlEg0KBW51ZGdlGAEgASgJEhIKCm51ZGdlX3R5cGUYAiABKAkSEAoIbnVkZ2VfaWQYAyABKAkSEAoIcHJpb3JpdHkYBCABKAUSDAoEbmFtZRgFIAEoCSKKAQoYVXBkYXRlTnVkZ2VBY3Rpb25SZXF1ZXN0EhIKCm51ZGdlX3R5cGUYASABKAkSSAoRbnVkZ2VfYWN0aW9uX3R5cGUYAiABKA4yLS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk51ZGdlQWN0aW9uVHlwZRIQCghudWRnZV9pZBgDIAEoCSqVAgoJTnVkZ2VUeXBlEhYKElVOS05PV05fTlVER0VfVFlQRRAAEhIKDlJFRkVSUkFMX05VREdFEAESFwoTUkVGRVJSQUxfVEVTVF9OVURHRRACEhkKFUZJUlNUX0ZEX1JFV0FSRF9OVURHRRADEh4KGkZVTkRJTkdfQU5OT1VOQ0VNRU5UX05VREdFEAQSHAoYUFJPRklMRV9DT01QTEVUSU9OX05VREdFEAUSDgoKQkFOS19OVURHRRAGEhoKFk1BVFVSRURfRkRfQ0FSRFNfTlVER0UQBxIgChxNQVRVUklOR19TT09OX0ZEX0NBUkRTX05VREdFEAgSHAoYR0VORVJJQ19FWFBSRVNTSU9OX05VREdFEAoqUQoPTnVkZ2VBY3Rpb25UeXBlEhgKFFVOS05PV05fTlVER0VfQUNUSU9OEAASEQoNTlVER0VfU1VDQ0VTUxABEhEKDU5VREdFX0RJU01JU1MQAkIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.NudgeResponse
 */
export type NudgeResponse =
  Message<"com.stablemoney.api.identity.NudgeResponse"> & {
    /**
     * @generated from field: string nudge = 1;
     */
    nudge: string;

    /**
     * @generated from field: string nudge_type = 2;
     */
    nudgeType: string;

    /**
     * @generated from field: string nudge_id = 3;
     */
    nudgeId: string;

    /**
     * @generated from field: int32 priority = 4;
     */
    priority: number;

    /**
     * @generated from field: string name = 5;
     */
    name: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.NudgeResponse.
 * Use `create(NudgeResponseSchema)` to create a new message.
 */
export const NudgeResponseSchema: GenMessage<NudgeResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Nudges, 0);

/**
 * @generated from message com.stablemoney.api.identity.UpdateNudgeActionRequest
 */
export type UpdateNudgeActionRequest =
  Message<"com.stablemoney.api.identity.UpdateNudgeActionRequest"> & {
    /**
     * @generated from field: string nudge_type = 1;
     */
    nudgeType: string;

    /**
     * @generated from field: com.stablemoney.api.identity.NudgeActionType nudge_action_type = 2;
     */
    nudgeActionType: NudgeActionType;

    /**
     * @generated from field: string nudge_id = 3;
     */
    nudgeId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateNudgeActionRequest.
 * Use `create(UpdateNudgeActionRequestSchema)` to create a new message.
 */
export const UpdateNudgeActionRequestSchema: GenMessage<UpdateNudgeActionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Nudges, 1);

/**
 * @generated from enum com.stablemoney.api.identity.NudgeType
 */
export enum NudgeType {
  /**
   * @generated from enum value: UNKNOWN_NUDGE_TYPE = 0;
   */
  UNKNOWN_NUDGE_TYPE = 0,

  /**
   * @generated from enum value: REFERRAL_NUDGE = 1;
   */
  REFERRAL_NUDGE = 1,

  /**
   * @generated from enum value: REFERRAL_TEST_NUDGE = 2;
   */
  REFERRAL_TEST_NUDGE = 2,

  /**
   * @generated from enum value: FIRST_FD_REWARD_NUDGE = 3;
   */
  FIRST_FD_REWARD_NUDGE = 3,

  /**
   * @generated from enum value: FUNDING_ANNOUNCEMENT_NUDGE = 4;
   */
  FUNDING_ANNOUNCEMENT_NUDGE = 4,

  /**
   * @generated from enum value: PROFILE_COMPLETION_NUDGE = 5;
   */
  PROFILE_COMPLETION_NUDGE = 5,

  /**
   * @generated from enum value: BANK_NUDGE = 6;
   */
  BANK_NUDGE = 6,

  /**
   * @generated from enum value: MATURED_FD_CARDS_NUDGE = 7;
   */
  MATURED_FD_CARDS_NUDGE = 7,

  /**
   * @generated from enum value: MATURING_SOON_FD_CARDS_NUDGE = 8;
   */
  MATURING_SOON_FD_CARDS_NUDGE = 8,

  /**
   * @generated from enum value: GENERIC_EXPRESSION_NUDGE = 10;
   */
  GENERIC_EXPRESSION_NUDGE = 10,
}

/**
 * Describes the enum com.stablemoney.api.identity.NudgeType.
 */
export const NudgeTypeSchema: GenEnum<NudgeType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Nudges, 0);

/**
 * @generated from enum com.stablemoney.api.identity.NudgeActionType
 */
export enum NudgeActionType {
  /**
   * @generated from enum value: UNKNOWN_NUDGE_ACTION = 0;
   */
  UNKNOWN_NUDGE_ACTION = 0,

  /**
   * @generated from enum value: NUDGE_SUCCESS = 1;
   */
  NUDGE_SUCCESS = 1,

  /**
   * @generated from enum value: NUDGE_DISMISS = 2;
   */
  NUDGE_DISMISS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.NudgeActionType.
 */
export const NudgeActionTypeSchema: GenEnum<NudgeActionType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Nudges, 1);
