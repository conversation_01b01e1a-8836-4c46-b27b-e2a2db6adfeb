// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/MobileVerification.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { OTPChallenge } from "./Auth_pb.js";
import { file_public_models_identity_Auth } from "./Auth_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/MobileVerification.proto.
 */
export const file_public_models_identity_MobileVerification: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci9wdWJsaWMvbW9kZWxzL2lkZW50aXR5L01vYmlsZVZlcmlmaWNhdGlvbi5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSJJCiFJbml0aWF0ZU1vYmlsZVZlcmlmaWNhdGlvblJlcXVlc3QSDgoGbW9iaWxlGAEgASgJEhQKDGNvdW50cnlfY29kZRgCIAEoCSJnCiJJbml0aWF0ZU1vYmlsZVZlcmlmaWNhdGlvblJlc3BvbnNlEkEKDW90cF9jaGFsbGVuZ2UYASABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk9UUENoYWxsZW5nZSJMCiRSZXNwb25kVG9Nb2JpbGVWZXJpZmljYXRpb25DaGFsbGVuZ2USFAoMY2hhbGxlbmdlX2lkGAEgASgJEg4KBmFuc3dlchgCIAEoCSJQCixSZXNwb25kVG9Nb2JpbGVWZXJpZmljYXRpb25DaGFsbGVuZ2VSZXNwb25zZRIPCgdleHBpcmVkGAEgASgIEg8KB21lc3NhZ2UYAiABKAlCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM",
    [file_public_models_identity_Auth],
  );

/**
 * @generated from message com.stablemoney.api.identity.InitiateMobileVerificationRequest
 */
export type InitiateMobileVerificationRequest =
  Message<"com.stablemoney.api.identity.InitiateMobileVerificationRequest"> & {
    /**
     * @generated from field: string mobile = 1;
     */
    mobile: string;

    /**
     * @generated from field: string country_code = 2;
     */
    countryCode: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateMobileVerificationRequest.
 * Use `create(InitiateMobileVerificationRequestSchema)` to create a new message.
 */
export const InitiateMobileVerificationRequestSchema: GenMessage<InitiateMobileVerificationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_MobileVerification, 0);

/**
 * @generated from message com.stablemoney.api.identity.InitiateMobileVerificationResponse
 */
export type InitiateMobileVerificationResponse =
  Message<"com.stablemoney.api.identity.InitiateMobileVerificationResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.OTPChallenge otp_challenge = 1;
     */
    otpChallenge?: OTPChallenge;
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateMobileVerificationResponse.
 * Use `create(InitiateMobileVerificationResponseSchema)` to create a new message.
 */
export const InitiateMobileVerificationResponseSchema: GenMessage<InitiateMobileVerificationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_MobileVerification, 1);

/**
 * @generated from message com.stablemoney.api.identity.RespondToMobileVerificationChallenge
 */
export type RespondToMobileVerificationChallenge =
  Message<"com.stablemoney.api.identity.RespondToMobileVerificationChallenge"> & {
    /**
     * @generated from field: string challenge_id = 1;
     */
    challengeId: string;

    /**
     * @generated from field: string answer = 2;
     */
    answer: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RespondToMobileVerificationChallenge.
 * Use `create(RespondToMobileVerificationChallengeSchema)` to create a new message.
 */
export const RespondToMobileVerificationChallengeSchema: GenMessage<RespondToMobileVerificationChallenge> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_MobileVerification, 2);

/**
 * @generated from message com.stablemoney.api.identity.RespondToMobileVerificationChallengeResponse
 */
export type RespondToMobileVerificationChallengeResponse =
  Message<"com.stablemoney.api.identity.RespondToMobileVerificationChallengeResponse"> & {
    /**
     * @generated from field: bool expired = 1;
     */
    expired: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RespondToMobileVerificationChallengeResponse.
 * Use `create(RespondToMobileVerificationChallengeResponseSchema)` to create a new message.
 */
export const RespondToMobileVerificationChallengeResponseSchema: GenMessage<RespondToMobileVerificationChallengeResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_MobileVerification, 3);
