// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Kyc.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Kyc.proto.
 */
export const file_public_models_identity_Kyc: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.identity.GenerateTokenRequest
 */
export type GenerateTokenRequest =
  Message<"com.stablemoney.api.identity.GenerateTokenRequest"> & {};

/**
 * Describes the message com.stablemoney.api.identity.GenerateTokenRequest.
 * Use `create(GenerateTokenRequestSchema)` to create a new message.
 */
export const GenerateTokenRequestSchema: GenMessage<GenerateTokenRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 0);

/**
 * @generated from message com.stablemoney.api.identity.GenerateTokenResponse
 */
export type GenerateTokenResponse =
  Message<"com.stablemoney.api.identity.GenerateTokenResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string access_token = 2;
     */
    accessToken: string;

    /**
     * @generated from field: string customer_identifier = 3;
     */
    customerIdentifier: string;

    /**
     * @generated from field: bool is_new = 4;
     */
    isNew: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.GenerateTokenResponse.
 * Use `create(GenerateTokenResponseSchema)` to create a new message.
 */
export const GenerateTokenResponseSchema: GenMessage<GenerateTokenResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 1);

/**
 * @generated from message com.stablemoney.api.identity.InitiateKycRequest
 */
export type InitiateKycRequest =
  Message<"com.stablemoney.api.identity.InitiateKycRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.KycType kyc_type = 1;
     */
    kycType: KycType;

    /**
     * @generated from oneof com.stablemoney.api.identity.InitiateKycRequest.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.PanKycRequest pan_kyc_request = 2;
           */
          value: PanKycRequest;
          case: "panKycRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.WetSignatureRequest wet_signature_request = 3;
           */
          value: WetSignatureRequest;
          case: "wetSignatureRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.SelfieRequest selfie_request = 4;
           */
          value: SelfieRequest;
          case: "selfieRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.KycRequest kyc_request = 5;
           */
          value: KycRequest;
          case: "kycRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.EsignRequest esign_request = 6;
           */
          value: EsignRequest;
          case: "esignRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateKycRequest.
 * Use `create(InitiateKycRequestSchema)` to create a new message.
 */
export const InitiateKycRequestSchema: GenMessage<InitiateKycRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 2);

/**
 * @generated from message com.stablemoney.api.identity.EsignRequest
 */
export type EsignRequest =
  Message<"com.stablemoney.api.identity.EsignRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.EsignStep step = 1;
     */
    step: EsignStep;

    /**
     * @generated from oneof com.stablemoney.api.identity.EsignRequest.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.GenerateTokenRequest generate_token_request = 2;
           */
          value: GenerateTokenRequest;
          case: "generateTokenRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.StatusRequest status_request = 3;
           */
          value: StatusRequest;
          case: "statusRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.EsignRequest.
 * Use `create(EsignRequestSchema)` to create a new message.
 */
export const EsignRequestSchema: GenMessage<EsignRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 3);

/**
 * @generated from message com.stablemoney.api.identity.KycRequest
 */
export type KycRequest = Message<"com.stablemoney.api.identity.KycRequest"> & {
  /**
   * @generated from field: com.stablemoney.api.identity.KycStep step = 1;
   */
  step: KycStep;

  /**
   * @generated from oneof com.stablemoney.api.identity.KycRequest.result
   */
  result:
    | {
        /**
         * @generated from field: com.stablemoney.api.identity.GenerateTokenRequest generate_token_request = 2;
         */
        value: GenerateTokenRequest;
        case: "generateTokenRequest";
      }
    | {
        /**
         * @generated from field: com.stablemoney.api.identity.StatusRequest status_request = 3;
         */
        value: StatusRequest;
        case: "statusRequest";
      }
    | { case: undefined; value?: undefined };
};

/**
 * Describes the message com.stablemoney.api.identity.KycRequest.
 * Use `create(KycRequestSchema)` to create a new message.
 */
export const KycRequestSchema: GenMessage<KycRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 4);

/**
 * @generated from message com.stablemoney.api.identity.InitiateKycResponse
 */
export type InitiateKycResponse =
  Message<"com.stablemoney.api.identity.InitiateKycResponse"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.InitiateKycResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.PanKycResponse pan_kyc_response = 1;
           */
          value: PanKycResponse;
          case: "panKycResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.WetSignatureResponse wet_signature_response = 2;
           */
          value: WetSignatureResponse;
          case: "wetSignatureResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.SelfieResponse selfie_response = 3;
           */
          value: SelfieResponse;
          case: "selfieResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.KycResponse kyc_response = 4;
           */
          value: KycResponse;
          case: "kycResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.EsignKycResponse esign_response = 5;
           */
          value: EsignKycResponse;
          case: "esignResponse";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateKycResponse.
 * Use `create(InitiateKycResponseSchema)` to create a new message.
 */
export const InitiateKycResponseSchema: GenMessage<InitiateKycResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 5);

/**
 * @generated from message com.stablemoney.api.identity.EsignKycResponse
 */
export type EsignKycResponse =
  Message<"com.stablemoney.api.identity.EsignKycResponse"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.EsignKycResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.GenerateTokenResponse generate_token_response = 1;
           */
          value: GenerateTokenResponse;
          case: "generateTokenResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.StatusResponse status_response = 2;
           */
          value: StatusResponse;
          case: "statusResponse";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.EsignKycResponse.
 * Use `create(EsignKycResponseSchema)` to create a new message.
 */
export const EsignKycResponseSchema: GenMessage<EsignKycResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 6);

/**
 * @generated from message com.stablemoney.api.identity.KycResponse
 */
export type KycResponse =
  Message<"com.stablemoney.api.identity.KycResponse"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.KycResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.GenerateTokenResponse generate_token_response = 1;
           */
          value: GenerateTokenResponse;
          case: "generateTokenResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.StatusResponse status_response = 2;
           */
          value: StatusResponse;
          case: "statusResponse";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.KycResponse.
 * Use `create(KycResponseSchema)` to create a new message.
 */
export const KycResponseSchema: GenMessage<KycResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 7);

/**
 * @generated from message com.stablemoney.api.identity.SelfieRequest
 */
export type SelfieRequest =
  Message<"com.stablemoney.api.identity.SelfieRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.SelfieKycStep step = 1;
     */
    step: SelfieKycStep;

    /**
     * @generated from oneof com.stablemoney.api.identity.SelfieRequest.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.StartSelfiStepRequest start_selfi_step_request = 2;
           */
          value: StartSelfiStepRequest;
          case: "startSelfiStepRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.SetSelfiStatusRequest set_selfi_status_request = 3;
           */
          value: SetSelfiStatusRequest;
          case: "setSelfiStatusRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.SelfieRequest.
 * Use `create(SelfieRequestSchema)` to create a new message.
 */
export const SelfieRequestSchema: GenMessage<SelfieRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 8);

/**
 * @generated from message com.stablemoney.api.identity.StartSelfiStepRequest
 */
export type StartSelfiStepRequest =
  Message<"com.stablemoney.api.identity.StartSelfiStepRequest"> & {};

/**
 * Describes the message com.stablemoney.api.identity.StartSelfiStepRequest.
 * Use `create(StartSelfiStepRequestSchema)` to create a new message.
 */
export const StartSelfiStepRequestSchema: GenMessage<StartSelfiStepRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 9);

/**
 * @generated from message com.stablemoney.api.identity.SetSelfiStatusRequest
 */
export type SetSelfiStatusRequest =
  Message<"com.stablemoney.api.identity.SetSelfiStatusRequest"> & {
    /**
     * @generated from field: string transaction_id = 1;
     */
    transactionId: string;

    /**
     * @generated from field: string status = 2;
     */
    status: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SetSelfiStatusRequest.
 * Use `create(SetSelfiStatusRequestSchema)` to create a new message.
 */
export const SetSelfiStatusRequestSchema: GenMessage<SetSelfiStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 10);

/**
 * @generated from message com.stablemoney.api.identity.SelfieResponse
 */
export type SelfieResponse =
  Message<"com.stablemoney.api.identity.SelfieResponse"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.SelfieResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.StartSelfiStepResponse start_selfi_step_response = 1;
           */
          value: StartSelfiStepResponse;
          case: "startSelfiStepResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.SetSelfiStatusResponse set_selfi_status_response = 2;
           */
          value: SetSelfiStatusResponse;
          case: "setSelfiStatusResponse";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.SelfieResponse.
 * Use `create(SelfieResponseSchema)` to create a new message.
 */
export const SelfieResponseSchema: GenMessage<SelfieResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 11);

/**
 * @generated from message com.stablemoney.api.identity.StartSelfiStepResponse
 */
export type StartSelfiStepResponse =
  Message<"com.stablemoney.api.identity.StartSelfiStepResponse"> & {
    /**
     * @generated from field: string selfie = 1;
     */
    selfie: string;

    /**
     * @generated from field: string workflow_id = 2;
     */
    workflowId: string;

    /**
     * @generated from field: string access_token = 3;
     */
    accessToken: string;

    /**
     * @generated from field: string transaction_id = 4;
     */
    transactionId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.StartSelfiStepResponse.
 * Use `create(StartSelfiStepResponseSchema)` to create a new message.
 */
export const StartSelfiStepResponseSchema: GenMessage<StartSelfiStepResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 12);

/**
 * @generated from message com.stablemoney.api.identity.SetSelfiStatusResponse
 */
export type SetSelfiStatusResponse =
  Message<"com.stablemoney.api.identity.SetSelfiStatusResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SetSelfiStatusResponse.
 * Use `create(SetSelfiStatusResponseSchema)` to create a new message.
 */
export const SetSelfiStatusResponseSchema: GenMessage<SetSelfiStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 13);

/**
 * @generated from message com.stablemoney.api.identity.PanKycResponse
 */
export type PanKycResponse =
  Message<"com.stablemoney.api.identity.PanKycResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.PanKycStep step = 1;
     */
    step: PanKycStep;

    /**
     * @generated from oneof com.stablemoney.api.identity.PanKycResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.KycFetchNameByPanResponse kyc_fetch_name_by_pan_response = 2;
           */
          value: KycFetchNameByPanResponse;
          case: "kycFetchNameByPanResponse";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse kyc_validate_name_and_get_pan_status_response = 3;
           */
          value: KycValidateNameAndGetPanStatusResponse;
          case: "kycValidateNameAndGetPanStatusResponse";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.PanKycResponse.
 * Use `create(PanKycResponseSchema)` to create a new message.
 */
export const PanKycResponseSchema: GenMessage<PanKycResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 14);

/**
 * @generated from message com.stablemoney.api.identity.PanKycRequest
 */
export type PanKycRequest =
  Message<"com.stablemoney.api.identity.PanKycRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.PanKycStep step = 1;
     */
    step: PanKycStep;

    /**
     * @generated from oneof com.stablemoney.api.identity.PanKycRequest.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.KycFetchNameByPanRequest kyc_fetch_name_by_pan_request = 2;
           */
          value: KycFetchNameByPanRequest;
          case: "kycFetchNameByPanRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest kyc_validate_name_and_get_kyc_status_request = 3;
           */
          value: KycValidateNameAndGetKycStatusRequest;
          case: "kycValidateNameAndGetKycStatusRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.PanKycRequest.
 * Use `create(PanKycRequestSchema)` to create a new message.
 */
export const PanKycRequestSchema: GenMessage<PanKycRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 15);

/**
 * @generated from message com.stablemoney.api.identity.KycFetchNameByPanResponse
 */
export type KycFetchNameByPanResponse =
  Message<"com.stablemoney.api.identity.KycFetchNameByPanResponse"> & {
    /**
     * @generated from field: string pan = 1;
     */
    pan: string;

    /**
     * @generated from field: string full_name = 2;
     */
    fullName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.KycFetchNameByPanResponse.
 * Use `create(KycFetchNameByPanResponseSchema)` to create a new message.
 */
export const KycFetchNameByPanResponseSchema: GenMessage<KycFetchNameByPanResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 16);

/**
 * @generated from message com.stablemoney.api.identity.KycFetchNameByPanRequest
 */
export type KycFetchNameByPanRequest =
  Message<"com.stablemoney.api.identity.KycFetchNameByPanRequest"> & {
    /**
     * @generated from field: string pan = 1;
     */
    pan: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.KycFetchNameByPanRequest.
 * Use `create(KycFetchNameByPanRequestSchema)` to create a new message.
 */
export const KycFetchNameByPanRequestSchema: GenMessage<KycFetchNameByPanRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 17);

/**
 * @generated from message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusRequest
 */
export type KycValidateNameAndGetPanStatusRequest =
  Message<"com.stablemoney.api.identity.KycValidateNameAndGetPanStatusRequest"> & {
    /**
     * @generated from field: bool is_name_match = 1;
     */
    isNameMatch: boolean;

    /**
     * @generated from field: string dob = 2;
     */
    dob: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusRequest.
 * Use `create(KycValidateNameAndGetPanStatusRequestSchema)` to create a new message.
 */
export const KycValidateNameAndGetPanStatusRequestSchema: GenMessage<KycValidateNameAndGetPanStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 18);

/**
 * @generated from message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse
 */
export type KycValidateNameAndGetPanStatusResponse =
  Message<"com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse"> & {
    /**
     * @generated from field: bool pan_kyc_status = 1;
     */
    panKycStatus: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.KycValidateNameAndGetPanStatusResponse.
 * Use `create(KycValidateNameAndGetPanStatusResponseSchema)` to create a new message.
 */
export const KycValidateNameAndGetPanStatusResponseSchema: GenMessage<KycValidateNameAndGetPanStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 19);

/**
 * @generated from message com.stablemoney.api.identity.StatusRequest
 */
export type StatusRequest =
  Message<"com.stablemoney.api.identity.StatusRequest"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.StatusRequest.
 * Use `create(StatusRequestSchema)` to create a new message.
 */
export const StatusRequestSchema: GenMessage<StatusRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 20);

/**
 * @generated from message com.stablemoney.api.identity.StatusResponse
 */
export type StatusResponse =
  Message<"com.stablemoney.api.identity.StatusResponse"> & {
    /**
     * @generated from field: string kyc_status = 1;
     */
    kycStatus: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: string digilocker_status = 3;
     */
    digilockerStatus: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.StatusResponse.
 * Use `create(StatusResponseSchema)` to create a new message.
 */
export const StatusResponseSchema: GenMessage<StatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 21);

/**
 * @generated from message com.stablemoney.api.identity.WetSignatureRequest
 */
export type WetSignatureRequest =
  Message<"com.stablemoney.api.identity.WetSignatureRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.RaaDuration raa_duration = 1;
     */
    raaDuration: RaaDuration;

    /**
     * @generated from field: bool is_pep = 2;
     */
    isPep: boolean;

    /**
     * @generated from field: bool is_indian_citizen = 3;
     */
    isIndianCitizen: boolean;

    /**
     * @generated from field: string document_id = 4;
     */
    documentId: string;

    /**
     * @generated from field: bool credit_report_consent = 5;
     */
    creditReportConsent: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.WetSignatureRequest.
 * Use `create(WetSignatureRequestSchema)` to create a new message.
 */
export const WetSignatureRequestSchema: GenMessage<WetSignatureRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 22);

/**
 * @generated from message com.stablemoney.api.identity.WetSignatureResponse
 */
export type WetSignatureResponse =
  Message<"com.stablemoney.api.identity.WetSignatureResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.WetSignatureResponse.
 * Use `create(WetSignatureResponseSchema)` to create a new message.
 */
export const WetSignatureResponseSchema: GenMessage<WetSignatureResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 23);

/**
 * @generated from message com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest
 */
export type KycValidateNameAndGetKycStatusRequest =
  Message<"com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest"> & {
    /**
     * @generated from field: bool is_name_match = 1;
     */
    isNameMatch: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.KycValidateNameAndGetKycStatusRequest.
 * Use `create(KycValidateNameAndGetKycStatusRequestSchema)` to create a new message.
 */
export const KycValidateNameAndGetKycStatusRequestSchema: GenMessage<KycValidateNameAndGetKycStatusRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Kyc, 24);

/**
 * @generated from enum com.stablemoney.api.identity.KycType
 */
export enum KycType {
  /**
   * @generated from enum value: KYC_TYPE_UNKNOWN = 0;
   */
  KYC_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: POI = 1;
   */
  POI = 1,

  /**
   * @generated from enum value: KYC = 2;
   */
  KYC = 2,

  /**
   * @generated from enum value: POA = 3;
   */
  POA = 3,

  /**
   * @generated from enum value: SELFIE = 4;
   */
  SELFIE = 4,

  /**
   * @generated from enum value: ESIGN = 5;
   */
  ESIGN = 5,

  /**
   * @generated from enum value: BANK_ACCOUNT = 6;
   */
  BANK_ACCOUNT = 6,

  /**
   * @generated from enum value: DEMAT_ACCOUNT = 7;
   */
  DEMAT_ACCOUNT = 7,

  /**
   * @generated from enum value: NOMINEE = 8;
   */
  NOMINEE = 8,

  /**
   * @generated from enum value: WET_SIGNATURE = 9;
   */
  WET_SIGNATURE = 9,

  /**
   * @generated from enum value: USER_PROFILE = 10;
   */
  USER_PROFILE = 10,

  /**
   * @generated from enum value: PAN_KYC = 11;
   */
  PAN_KYC = 11,

  /**
   * @generated from enum value: NAME = 12;
   */
  NAME = 12,

  /**
   * @generated from enum value: QUESTIONNAIRE = 13;
   */
  QUESTIONNAIRE = 13,

  /**
   * @generated from enum value: EMAIL = 14;
   */
  EMAIL = 14,

  /**
   * @generated from enum value: WHITELIST_CHECK = 15;
   */
  WHITELIST_CHECK = 15,
}

/**
 * Describes the enum com.stablemoney.api.identity.KycType.
 */
export const KycTypeSchema: GenEnum<KycType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 0);

/**
 * @generated from enum com.stablemoney.api.identity.ProofType
 */
export enum ProofType {
  /**
   * @generated from enum value: PROOF_TYPE_UNKNOWN = 0;
   */
  PROOF_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: PAN = 1;
   */
  PAN = 1,

  /**
   * @generated from enum value: AADHAR = 2;
   */
  AADHAR = 2,

  /**
   * @generated from enum value: PASSPORT = 3;
   */
  PASSPORT = 3,

  /**
   * @generated from enum value: DRIVING_LICENSE = 4;
   */
  DRIVING_LICENSE = 4,

  /**
   * @generated from enum value: VOTER_ID = 5;
   */
  VOTER_ID = 5,

  /**
   * @generated from enum value: GOVT_ID = 6;
   */
  GOVT_ID = 6,

  /**
   * @generated from enum value: REGULATORY_ID = 7;
   */
  REGULATORY_ID = 7,

  /**
   * @generated from enum value: PSU_ID = 8;
   */
  PSU_ID = 8,

  /**
   * @generated from enum value: BANK_ID = 9;
   */
  BANK_ID = 9,

  /**
   * @generated from enum value: PUBLIC_FINANCIAL_INSTITUTION_ID = 10;
   */
  PUBLIC_FINANCIAL_INSTITUTION_ID = 10,

  /**
   * @generated from enum value: COLLEGE_ID = 11;
   */
  COLLEGE_ID = 11,

  /**
   * @generated from enum value: PROFESSIONAL_BODY_ID = 12;
   */
  PROFESSIONAL_BODY_ID = 12,

  /**
   * @generated from enum value: CREDIT_CARD = 13;
   */
  CREDIT_CARD = 13,

  /**
   * @generated from enum value: OTHER_ID = 16;
   */
  OTHER_ID = 16,

  /**
   * @generated from enum value: BANK_PASSBOOK = 17;
   */
  BANK_PASSBOOK = 17,

  /**
   * @generated from enum value: BANK_ACCOUNT_STATEMENT = 18;
   */
  BANK_ACCOUNT_STATEMENT = 18,

  /**
   * @generated from enum value: RATION_CARD = 19;
   */
  RATION_CARD = 19,

  /**
   * @generated from enum value: LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20;
   */
  LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20,

  /**
   * @generated from enum value: LAND_LINE_TELEPHONE_BILL = 21;
   */
  LAND_LINE_TELEPHONE_BILL = 21,

  /**
   * @generated from enum value: ELECTRICITY_BILL = 22;
   */
  ELECTRICITY_BILL = 22,

  /**
   * @generated from enum value: GAS_BILL = 23;
   */
  GAS_BILL = 23,

  /**
   * @generated from enum value: FLAT_MAINTENANCE_BILL = 24;
   */
  FLAT_MAINTENANCE_BILL = 24,

  /**
   * @generated from enum value: INSURANCE_COPY = 25;
   */
  INSURANCE_COPY = 25,

  /**
   * @generated from enum value: SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26;
   */
  SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26,

  /**
   * @generated from enum value: POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27;
   */
  POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27,

  /**
   * @generated from enum value: POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28;
   */
  POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28,

  /**
   * @generated from enum value: POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29;
   */
  POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29,

  /**
   * @generated from enum value: POA_ISSUED_BY_PARLIAMENT = 30;
   */
  POA_ISSUED_BY_PARLIAMENT = 30,

  /**
   * @generated from enum value: POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31;
   */
  POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31,

  /**
   * @generated from enum value: POA_ISSUED_BY_NOTARY_PUBLIC = 32;
   */
  POA_ISSUED_BY_NOTARY_PUBLIC = 32,

  /**
   * @generated from enum value: POA_ISSUED_BY_GAZETTED_OFFICER = 33;
   */
  POA_ISSUED_BY_GAZETTED_OFFICER = 33,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34;
   */
  ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35;
   */
  ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36;
   */
  ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37;
   */
  ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38;
   */
  ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39;
   */
  ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39,

  /**
   * @generated from enum value: ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40;
   */
  ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40,
}

/**
 * Describes the enum com.stablemoney.api.identity.ProofType.
 */
export const ProofTypeSchema: GenEnum<ProofType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 1);

/**
 * @generated from enum com.stablemoney.api.identity.KycProvider
 */
export enum KycProvider {
  /**
   * @generated from enum value: KYC_PROVIDER_UNKNOWN = 0;
   */
  KYC_PROVIDER_UNKNOWN = 0,

  /**
   * @generated from enum value: NONE = 1;
   */
  NONE = 1,

  /**
   * @generated from enum value: CDSL = 2;
   */
  CDSL = 2,

  /**
   * @generated from enum value: DIGIO = 3;
   */
  DIGIO = 3,

  /**
   * @generated from enum value: HYPERVERGE = 4;
   */
  HYPERVERGE = 4,

  /**
   * @generated from enum value: TARRAKKI = 5;
   */
  TARRAKKI = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.KycProvider.
 */
export const KycProviderSchema: GenEnum<KycProvider> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 2);

/**
 * @generated from enum com.stablemoney.api.identity.OnBoardingStatus
 */
export enum OnBoardingStatus {
  /**
   * @generated from enum value: ONBOARDING_STATUS_UNKNOWN = 0;
   */
  ONBOARDING_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: PENDING = 1;
   */
  PENDING = 1,

  /**
   * @generated from enum value: COMPLETE = 2;
   */
  COMPLETE = 2,

  /**
   * @generated from enum value: REJECTED = 3;
   */
  REJECTED = 3,

  /**
   * @generated from enum value: SKIPPED = 4;
   */
  SKIPPED = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.OnBoardingStatus.
 */
export const OnBoardingStatusSchema: GenEnum<OnBoardingStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 3);

/**
 * @generated from enum com.stablemoney.api.identity.EsignStep
 */
export enum EsignStep {
  /**
   * @generated from enum value: ESIGN_STEP_UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ESIGN_STEP_GENERATE_TOKEN = 1;
   */
  GENERATE_TOKEN = 1,

  /**
   * @generated from enum value: ESIGN_STEP_STATUS = 2;
   */
  STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.EsignStep.
 */
export const EsignStepSchema: GenEnum<EsignStep> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 4);

/**
 * @generated from enum com.stablemoney.api.identity.PanKycStep
 */
export enum PanKycStep {
  /**
   * @generated from enum value: UNKNOWN_PAN_STEP = 0;
   */
  UNKNOWN_PAN_STEP = 0,

  /**
   * @generated from enum value: NAME_FETCH = 1;
   */
  NAME_FETCH = 1,

  /**
   * @generated from enum value: PAN_STATUS = 2;
   */
  PAN_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.PanKycStep.
 */
export const PanKycStepSchema: GenEnum<PanKycStep> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 5);

/**
 * @generated from enum com.stablemoney.api.identity.KycStep
 */
export enum KycStep {
  /**
   * @generated from enum value: UNKNOWN_STEP = 0;
   */
  UNKNOWN_STEP = 0,

  /**
   * @generated from enum value: GENERATE_TOKEN = 1;
   */
  GENERATE_TOKEN = 1,

  /**
   * @generated from enum value: GET_STATUS = 2;
   */
  GET_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.KycStep.
 */
export const KycStepSchema: GenEnum<KycStep> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 6);

/**
 * @generated from enum com.stablemoney.api.identity.SelfieKycStep
 */
export enum SelfieKycStep {
  /**
   * @generated from enum value: UNKNOWN_SELFIE_STEP = 0;
   */
  UNKNOWN_SELFIE_STEP = 0,

  /**
   * @generated from enum value: START_SELFIE_STEP = 1;
   */
  START_SELFIE_STEP = 1,

  /**
   * @generated from enum value: SET_STATUS = 2;
   */
  SET_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.SelfieKycStep.
 */
export const SelfieKycStepSchema: GenEnum<SelfieKycStep> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 7);

/**
 * @generated from enum com.stablemoney.api.identity.RaaDuration
 */
export enum RaaDuration {
  /**
   * @generated from enum value: UNKNOWN_RAA_DURATION = 0;
   */
  UNKNOWN_RAA_DURATION = 0,

  /**
   * @generated from enum value: RAA_60_DAYS = 1;
   */
  RAA_60_DAYS = 1,

  /**
   * @generated from enum value: RAA_90_DAYS = 2;
   */
  RAA_90_DAYS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.RaaDuration.
 */
export const RaaDurationSchema: GenEnum<RaaDuration> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Kyc, 8);
