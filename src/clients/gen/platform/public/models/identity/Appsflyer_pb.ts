// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Appsflyer.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Appsflyer.proto.
 */
export const file_public_models_identity_Appsflyer: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiZwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0FwcHNmbHllci5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSIoChlBcHBzRmx5ZXJVcGRhdGVUdGxSZXF1ZXN0EgsKA3VybBgBIAEoCSIcChpBcHBzRmx5ZXJVcGRhdGVUdGxSZXNwb25zZUIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.AppsFlyerUpdateTtlRequest
 */
export type AppsFlyerUpdateTtlRequest =
  Message<"com.stablemoney.api.identity.AppsFlyerUpdateTtlRequest"> & {
    /**
     * @generated from field: string url = 1;
     */
    url: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AppsFlyerUpdateTtlRequest.
 * Use `create(AppsFlyerUpdateTtlRequestSchema)` to create a new message.
 */
export const AppsFlyerUpdateTtlRequestSchema: GenMessage<AppsFlyerUpdateTtlRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Appsflyer, 0);

/**
 * @generated from message com.stablemoney.api.identity.AppsFlyerUpdateTtlResponse
 */
export type AppsFlyerUpdateTtlResponse =
  Message<"com.stablemoney.api.identity.AppsFlyerUpdateTtlResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.AppsFlyerUpdateTtlResponse.
 * Use `create(AppsFlyerUpdateTtlResponseSchema)` to create a new message.
 */
export const AppsFlyerUpdateTtlResponseSchema: GenMessage<AppsFlyerUpdateTtlResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Appsflyer, 1);
