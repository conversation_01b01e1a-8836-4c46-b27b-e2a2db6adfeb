// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Whitelist.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Whitelist.proto.
 */
export const file_public_models_identity_Whitelist: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiZwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1doaXRlbGlzdC5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSIrChFXaGl0ZWxpc3RSZXNwb25zZRIWCg5pc193aGl0ZWxpc3RlZBgBIAEoCEIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.WhitelistResponse
 */
export type WhitelistResponse =
  Message<"com.stablemoney.api.identity.WhitelistResponse"> & {
    /**
     * @generated from field: bool is_whitelisted = 1;
     */
    isWhitelisted: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.WhitelistResponse.
 * Use `create(WhitelistResponseSchema)` to create a new message.
 */
export const WhitelistResponseSchema: GenMessage<WhitelistResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Whitelist, 0);
