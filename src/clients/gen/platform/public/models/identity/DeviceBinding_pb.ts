// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/DeviceBinding.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/DeviceBinding.proto.
 */
export const file_public_models_identity_DeviceBinding: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CipwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0RldmljZUJpbmRpbmcucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiYgoURGV2aWNlQmluZGluZ1JlcXVlc3QSEQoJZGV2aWNlX2lkGAEgASgJEjcKCHNpbV9kYXRhGAIgAygLMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5TaW1EYXRhIjYKB1NpbURhdGESGQoRc2ltX3NlcmlhbF9udW1iZXIYASABKAkSEAoIc2ltX25hbWUYAiABKAkiUwoVRGV2aWNlQmluZGluZ1Jlc3BvbnNlEg8KB21lc3NhZ2UYASABKAkSEgoKcmVxdWVzdF9pZBgCIAEoCRIVCg1tb2JpbGVfbnVtYmVyGAMgASgJIr8BChFJbmJvdW5kU21zUmVxdWVzdBIXCg9sb25nY29kZV9udW1iZXIYASABKAkSFwoPY3VzdG9tZXJfbnVtYmVyGAIgASgJEg0KBXByaWNlGAggASgJEg4KBnN0YXR1cxgJIAEoCRIOCgZjaXJjbGUYAyABKAkSEgoKY29tcGFueV9pZBgEIAEoBRIPCgdtZXNzYWdlGAUgASgJEg8KB2tleXdvcmQYBiABKAkSEwoLcmVjZWl2ZWRfYXQYByABKAkicQobRGV2aWNlQmluZGluZ1N0YXR1c1Jlc3BvbnNlEkEKBnN0YXR1cxgBIAEoDjIxLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuRGV2aWNlQmluZGluZ1N0YXR1cxIPCgdtZXNzYWdlGAIgASgJKr0BChNEZXZpY2VCaW5kaW5nU3RhdHVzEiEKHURFVklDRV9CSU5ESU5HX1NUQVRVU19VTktOT1dOEAASIQodREVWSUNFX0JJTkRJTkdfU1RBVFVTX1BFTkRJTkcQARIiCh5ERVZJQ0VfQklORElOR19TVEFUVVNfQVBQUk9WRUQQAhIiCh5ERVZJQ0VfQklORElOR19TVEFUVVNfUkVKRUNURUQQAxIYChRSRVFVRVNUX0lEX05PVF9GT1VORBAEQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z",
  );

/**
 * @generated from message com.stablemoney.api.identity.DeviceBindingRequest
 */
export type DeviceBindingRequest =
  Message<"com.stablemoney.api.identity.DeviceBindingRequest"> & {
    /**
     * @generated from field: string device_id = 1;
     */
    deviceId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.SimData sim_data = 2;
     */
    simData: SimData[];
  };

/**
 * Describes the message com.stablemoney.api.identity.DeviceBindingRequest.
 * Use `create(DeviceBindingRequestSchema)` to create a new message.
 */
export const DeviceBindingRequestSchema: GenMessage<DeviceBindingRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_DeviceBinding, 0);

/**
 * @generated from message com.stablemoney.api.identity.SimData
 */
export type SimData = Message<"com.stablemoney.api.identity.SimData"> & {
  /**
   * @generated from field: string sim_serial_number = 1;
   */
  simSerialNumber: string;

  /**
   * @generated from field: string sim_name = 2;
   */
  simName: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SimData.
 * Use `create(SimDataSchema)` to create a new message.
 */
export const SimDataSchema: GenMessage<SimData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_DeviceBinding, 1);

/**
 * @generated from message com.stablemoney.api.identity.DeviceBindingResponse
 */
export type DeviceBindingResponse =
  Message<"com.stablemoney.api.identity.DeviceBindingResponse"> & {
    /**
     * @generated from field: string message = 1;
     */
    message: string;

    /**
     * @generated from field: string request_id = 2;
     */
    requestId: string;

    /**
     * @generated from field: string mobile_number = 3;
     */
    mobileNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.DeviceBindingResponse.
 * Use `create(DeviceBindingResponseSchema)` to create a new message.
 */
export const DeviceBindingResponseSchema: GenMessage<DeviceBindingResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_DeviceBinding, 2);

/**
 * @generated from message com.stablemoney.api.identity.InboundSmsRequest
 */
export type InboundSmsRequest =
  Message<"com.stablemoney.api.identity.InboundSmsRequest"> & {
    /**
     * @generated from field: string longcode_number = 1;
     */
    longcodeNumber: string;

    /**
     * @generated from field: string customer_number = 2;
     */
    customerNumber: string;

    /**
     * @generated from field: string price = 8;
     */
    price: string;

    /**
     * @generated from field: string status = 9;
     */
    status: string;

    /**
     * @generated from field: string circle = 3;
     */
    circle: string;

    /**
     * @generated from field: int32 company_id = 4;
     */
    companyId: number;

    /**
     * @generated from field: string message = 5;
     */
    message: string;

    /**
     * @generated from field: string keyword = 6;
     */
    keyword: string;

    /**
     * @generated from field: string received_at = 7;
     */
    receivedAt: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.InboundSmsRequest.
 * Use `create(InboundSmsRequestSchema)` to create a new message.
 */
export const InboundSmsRequestSchema: GenMessage<InboundSmsRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_DeviceBinding, 3);

/**
 * @generated from message com.stablemoney.api.identity.DeviceBindingStatusResponse
 */
export type DeviceBindingStatusResponse =
  Message<"com.stablemoney.api.identity.DeviceBindingStatusResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.DeviceBindingStatus status = 1;
     */
    status: DeviceBindingStatus;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.DeviceBindingStatusResponse.
 * Use `create(DeviceBindingStatusResponseSchema)` to create a new message.
 */
export const DeviceBindingStatusResponseSchema: GenMessage<DeviceBindingStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_DeviceBinding, 4);

/**
 * @generated from enum com.stablemoney.api.identity.DeviceBindingStatus
 */
export enum DeviceBindingStatus {
  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_UNKNOWN = 0;
   */
  DEVICE_BINDING_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_PENDING = 1;
   */
  DEVICE_BINDING_STATUS_PENDING = 1,

  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_APPROVED = 2;
   */
  DEVICE_BINDING_STATUS_APPROVED = 2,

  /**
   * @generated from enum value: DEVICE_BINDING_STATUS_REJECTED = 3;
   */
  DEVICE_BINDING_STATUS_REJECTED = 3,

  /**
   * @generated from enum value: REQUEST_ID_NOT_FOUND = 4;
   */
  REQUEST_ID_NOT_FOUND = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.DeviceBindingStatus.
 */
export const DeviceBindingStatusSchema: GenEnum<DeviceBindingStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_DeviceBinding, 0);
