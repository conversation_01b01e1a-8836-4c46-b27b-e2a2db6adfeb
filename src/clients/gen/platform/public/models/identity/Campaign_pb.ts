// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Campaign.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Campaign.proto.
 */
export const file_public_models_identity_Campaign: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.identity.Address
 */
export type Address = Message<"com.stablemoney.api.identity.Address"> & {
  /**
   * @generated from field: string address_line1 = 1;
   */
  addressLine1: string;

  /**
   * @generated from field: optional string address_line2 = 2;
   */
  addressLine2?: string;

  /**
   * @generated from field: optional string address_line3 = 3;
   */
  addressLine3?: string;

  /**
   * @generated from field: string city_id = 4;
   */
  cityId: string;

  /**
   * @generated from field: string pin_code = 5;
   */
  pinCode: string;
};

/**
 * Describes the message com.stablemoney.api.identity.Address.
 * Use `create(AddressSchema)` to create a new message.
 */
export const AddressSchema: GenMessage<Address> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 0);

/**
 * @generated from message com.stablemoney.api.identity.FundingCampaignMetadata
 */
export type FundingCampaignMetadata =
  Message<"com.stablemoney.api.identity.FundingCampaignMetadata"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GiftOrderStatus order_status = 1;
     */
    orderStatus: GiftOrderStatus;

    /**
     * @generated from field: bool can_user_refer = 2;
     */
    canUserRefer: boolean;

    /**
     * @generated from field: int32 referrals_left_count = 3;
     */
    referralsLeftCount: number;

    /**
     * @generated from field: optional string tracking_link_url = 4;
     */
    trackingLinkUrl?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.FundingCampaignMetadata.
 * Use `create(FundingCampaignMetadataSchema)` to create a new message.
 */
export const FundingCampaignMetadataSchema: GenMessage<FundingCampaignMetadata> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 1);

/**
 * @generated from message com.stablemoney.api.identity.TShirtCampaignMetadata
 */
export type TShirtCampaignMetadata =
  Message<"com.stablemoney.api.identity.TShirtCampaignMetadata"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GiftOrderStatus gift_order_status = 1;
     */
    giftOrderStatus: GiftOrderStatus;

    /**
     * @generated from field: optional com.stablemoney.api.identity.Address address = 2;
     */
    address?: Address;

    /**
     * @generated from field: optional string t_shirt_size = 3;
     */
    tShirtSize?: string;

    /**
     * @generated from field: int32 eligibility_referral_count = 4;
     */
    eligibilityReferralCount: number;

    /**
     * @generated from field: int32 current_referral_count = 5;
     */
    currentReferralCount: number;

    /**
     * @generated from field: string campaign_referral_link = 6;
     */
    campaignReferralLink: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.TShirtCampaignMetadata.
 * Use `create(TShirtCampaignMetadataSchema)` to create a new message.
 */
export const TShirtCampaignMetadataSchema: GenMessage<TShirtCampaignMetadata> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 2);

/**
 * @generated from message com.stablemoney.api.identity.BondsReferralCampaignMetadata
 */
export type BondsReferralCampaignMetadata =
  Message<"com.stablemoney.api.identity.BondsReferralCampaignMetadata"> & {
    /**
     * @generated from field: string campaign_referral_link = 1;
     */
    campaignReferralLink: string;

    /**
     * @generated from field: string share_text = 2;
     */
    shareText: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BondsReferralCampaignMetadata.
 * Use `create(BondsReferralCampaignMetadataSchema)` to create a new message.
 */
export const BondsReferralCampaignMetadataSchema: GenMessage<BondsReferralCampaignMetadata> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 3);

/**
 * @generated from message com.stablemoney.api.identity.CampaignResponse
 */
export type CampaignResponse =
  Message<"com.stablemoney.api.identity.CampaignResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.CampaignType campaign_type = 1;
     */
    campaignType: CampaignType;

    /**
     * @generated from field: bool is_user_eligible = 2;
     */
    isUserEligible: boolean;

    /**
     * @generated from oneof com.stablemoney.api.identity.CampaignResponse.metadata
     */
    metadata:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.FundingCampaignMetadata funding_campaign_metadata = 3;
           */
          value: FundingCampaignMetadata;
          case: "fundingCampaignMetadata";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.TShirtCampaignMetadata t_shirt_campaign_metadata = 4;
           */
          value: TShirtCampaignMetadata;
          case: "tShirtCampaignMetadata";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.BondsReferralCampaignMetadata bonds_referral_campaign_metadata = 5;
           */
          value: BondsReferralCampaignMetadata;
          case: "bondsReferralCampaignMetadata";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.CampaignResponse.
 * Use `create(CampaignResponseSchema)` to create a new message.
 */
export const CampaignResponseSchema: GenMessage<CampaignResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 4);

/**
 * @generated from message com.stablemoney.api.identity.TShirtCampaignRequest
 */
export type TShirtCampaignRequest =
  Message<"com.stablemoney.api.identity.TShirtCampaignRequest"> & {
    /**
     * @generated from field: optional string t_shirt_size = 1;
     */
    tShirtSize?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.Address address = 2;
     */
    address?: Address;
  };

/**
 * Describes the message com.stablemoney.api.identity.TShirtCampaignRequest.
 * Use `create(TShirtCampaignRequestSchema)` to create a new message.
 */
export const TShirtCampaignRequestSchema: GenMessage<TShirtCampaignRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 5);

/**
 * @generated from message com.stablemoney.api.identity.PlaceOrderRequest
 */
export type PlaceOrderRequest =
  Message<"com.stablemoney.api.identity.PlaceOrderRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.CampaignType campaign_type = 1;
     */
    campaignType: CampaignType;

    /**
     * @generated from oneof com.stablemoney.api.identity.PlaceOrderRequest.metadata
     */
    metadata:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.TShirtCampaignRequest t_shirt_campaign_request = 2;
           */
          value: TShirtCampaignRequest;
          case: "tShirtCampaignRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.PlaceOrderRequest.
 * Use `create(PlaceOrderRequestSchema)` to create a new message.
 */
export const PlaceOrderRequestSchema: GenMessage<PlaceOrderRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 6);

/**
 * @generated from message com.stablemoney.api.identity.PlaceOrderResponse
 */
export type PlaceOrderResponse =
  Message<"com.stablemoney.api.identity.PlaceOrderResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GiftOrderStatus orderStatus = 1;
     */
    orderStatus: GiftOrderStatus;
  };

/**
 * Describes the message com.stablemoney.api.identity.PlaceOrderResponse.
 * Use `create(PlaceOrderResponseSchema)` to create a new message.
 */
export const PlaceOrderResponseSchema: GenMessage<PlaceOrderResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Campaign, 7);

/**
 * @generated from enum com.stablemoney.api.identity.CampaignType
 */
export enum CampaignType {
  /**
   * @generated from enum value: UNKNOWN_CAMPAIGN_TYPE = 0;
   */
  UNKNOWN_CAMPAIGN_TYPE = 0,

  /**
   * @generated from enum value: FUNDING_SWEETS_CAMPAIGN_TYPE = 1;
   */
  FUNDING_SWEETS_CAMPAIGN_TYPE = 1,

  /**
   * @generated from enum value: EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE = 2;
   */
  EMERGENCY_FUND_REFERRAL_CAMPAIGN_TYPE = 2,

  /**
   * @generated from enum value: DEFAULT_REFERRAL_CAMPAIGN_TYPE = 3;
   */
  DEFAULT_REFERRAL_CAMPAIGN_TYPE = 3,

  /**
   * @generated from enum value: T_SHIRT_CAMPAIGN_TYPE = 4;
   */
  T_SHIRT_CAMPAIGN_TYPE = 4,

  /**
   * @generated from enum value: BONDS_REFERRAL_CAMPAIGN_TYPE = 5;
   */
  BONDS_REFERRAL_CAMPAIGN_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.CampaignType.
 */
export const CampaignTypeSchema: GenEnum<CampaignType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Campaign, 0);

/**
 * @generated from enum com.stablemoney.api.identity.GiftOrderStatus
 */
export enum GiftOrderStatus {
  /**
   * @generated from enum value: UNKNOWN_GIFT_ORDER_STATUS = 0;
   */
  UNKNOWN_GIFT_ORDER_STATUS = 0,

  /**
   * @generated from enum value: ORDER_PLACED_GIFT_ORDER_STATUS = 1;
   */
  ORDER_PLACED_GIFT_ORDER_STATUS = 1,

  /**
   * @generated from enum value: FAILED_GIFT_ORDER_STATUS = 2;
   */
  FAILED_GIFT_ORDER_STATUS = 2,

  /**
   * @generated from enum value: INITIATED_GIFT_ORDER_STATUS = 3;
   */
  INITIATED_GIFT_ORDER_STATUS = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.GiftOrderStatus.
 */
export const GiftOrderStatusSchema: GenEnum<GiftOrderStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Campaign, 1);
