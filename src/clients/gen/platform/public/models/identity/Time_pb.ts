// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Time.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Time.proto.
 */
export const file_public_models_identity_Time: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiFwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1RpbWUucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkiawoTQ3VycmVudFRpbWVSZXNwb25zZRIYChBjdXJyZW50X3RpbWV6b25lGAEgASgJEhgKEGN1cnJlbnRfdGltZV9pc28YAiABKAkSIAoYY3VycmVudF90aW1lX2Vwb2NoX21pbGxpGAMgASgDQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z",
  );

/**
 * @generated from message com.stablemoney.api.identity.CurrentTimeResponse
 */
export type CurrentTimeResponse =
  Message<"com.stablemoney.api.identity.CurrentTimeResponse"> & {
    /**
     * @generated from field: string current_timezone = 1;
     */
    currentTimezone: string;

    /**
     * @generated from field: string current_time_iso = 2;
     */
    currentTimeIso: string;

    /**
     * @generated from field: int64 current_time_epoch_milli = 3;
     */
    currentTimeEpochMilli: bigint;
  };

/**
 * Describes the message com.stablemoney.api.identity.CurrentTimeResponse.
 * Use `create(CurrentTimeResponseSchema)` to create a new message.
 */
export const CurrentTimeResponseSchema: GenMessage<CurrentTimeResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Time, 0);
