// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/ProfileCompletion.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Gender, MaritalStatus } from "./Profile_pb.js";
import { file_public_models_identity_Profile } from "./Profile_pb.js";
import type { CityResponse } from "./City_pb.js";
import { file_public_models_identity_City } from "./City_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/ProfileCompletion.proto.
 */
export const file_public_models_identity_ProfileCompletion: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [file_public_models_identity_Profile, file_public_models_identity_City],
  );

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionStepsResponse
 */
export type ProfileCompletionStepsResponse =
  Message<"com.stablemoney.api.identity.ProfileCompletionStepsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.ProfileCompletionStep steps = 1;
     */
    steps: ProfileCompletionStep[];

    /**
     * @generated from field: string next_step = 2;
     */
    nextStep: string;

    /**
     * @generated from field: bool is_completed = 3;
     */
    isCompleted: boolean;

    /**
     * @generated from field: double completion_percentage = 4;
     */
    completionPercentage: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionStepsResponse.
 * Use `create(ProfileCompletionStepsResponseSchema)` to create a new message.
 */
export const ProfileCompletionStepsResponseSchema: GenMessage<ProfileCompletionStepsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 0);

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionStep
 */
export type ProfileCompletionStep =
  Message<"com.stablemoney.api.identity.ProfileCompletionStep"> & {
    /**
     * @generated from field: string step_name = 1;
     */
    stepName: string;

    /**
     * @generated from field: bool is_completed = 2;
     */
    isCompleted: boolean;

    /**
     * @generated from field: int32 priority = 3;
     */
    priority: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionStep.
 * Use `create(ProfileCompletionStepSchema)` to create a new message.
 */
export const ProfileCompletionStepSchema: GenMessage<ProfileCompletionStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 1);

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionRequest
 */
export type ProfileCompletionRequest =
  Message<"com.stablemoney.api.identity.ProfileCompletionRequest"> & {
    /**
     * @generated from field: string step_name = 1;
     */
    stepName: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.PersonalDetails personal_details = 2;
     */
    personalDetails?: PersonalDetails;

    /**
     * @generated from field: optional string dob = 3;
     */
    dob?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.DemographicDetails demographic_details = 4;
     */
    demographicDetails?: DemographicDetails;

    /**
     * @generated from field: optional com.stablemoney.api.identity.EmploymentDetails employment_details = 5;
     */
    employmentDetails?: EmploymentDetails;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.UserBank user_bank = 6;
     */
    userBank: UserBank[];
  };

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionRequest.
 * Use `create(ProfileCompletionRequestSchema)` to create a new message.
 */
export const ProfileCompletionRequestSchema: GenMessage<ProfileCompletionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 2);

/**
 * @generated from message com.stablemoney.api.identity.UserBank
 */
export type UserBank = Message<"com.stablemoney.api.identity.UserBank"> & {
  /**
   * @generated from field: string bank_id = 1;
   */
  bankId: string;

  /**
   * @generated from field: bool has_fd_with_bank = 2;
   */
  hasFdWithBank: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.UserBank.
 * Use `create(UserBankSchema)` to create a new message.
 */
export const UserBankSchema: GenMessage<UserBank> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 3);

/**
 * @generated from message com.stablemoney.api.identity.ProfileCompletionResponse
 */
export type ProfileCompletionResponse =
  Message<"com.stablemoney.api.identity.ProfileCompletionResponse"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string image_url = 2;
     */
    imageUrl: string;

    /**
     * @generated from field: string image_type = 3;
     */
    imageType: string;

    /**
     * @generated from field: string description = 4;
     */
    description: string;

    /**
     * @generated from field: double completion_percentage = 5;
     */
    completionPercentage: number;

    /**
     * @generated from field: string button_text = 6;
     */
    buttonText: string;

    /**
     * @generated from field: string next_step = 7;
     */
    nextStep: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.ProfileCompletionResponse.
 * Use `create(ProfileCompletionResponseSchema)` to create a new message.
 */
export const ProfileCompletionResponseSchema: GenMessage<ProfileCompletionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 4);

/**
 * @generated from message com.stablemoney.api.identity.PersonalDetails
 */
export type PersonalDetails =
  Message<"com.stablemoney.api.identity.PersonalDetails"> & {
    /**
     * @generated from field: string first_name = 1;
     */
    firstName: string;

    /**
     * @generated from field: string last_name = 2;
     */
    lastName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.PersonalDetails.
 * Use `create(PersonalDetailsSchema)` to create a new message.
 */
export const PersonalDetailsSchema: GenMessage<PersonalDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 5);

/**
 * @generated from message com.stablemoney.api.identity.DemographicDetails
 */
export type DemographicDetails =
  Message<"com.stablemoney.api.identity.DemographicDetails"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.Gender gender = 1;
     */
    gender: Gender;

    /**
     * @generated from field: com.stablemoney.api.identity.MaritalStatus marital_status = 2;
     */
    maritalStatus: MaritalStatus;

    /**
     * @generated from field: string city_id = 3;
     */
    cityId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.DemographicDetails.
 * Use `create(DemographicDetailsSchema)` to create a new message.
 */
export const DemographicDetailsSchema: GenMessage<DemographicDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 6);

/**
 * @generated from message com.stablemoney.api.identity.EmploymentDetails
 */
export type EmploymentDetails =
  Message<"com.stablemoney.api.identity.EmploymentDetails"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.ProfileCompletionEmploymentType employment_type = 1;
     */
    employmentType: ProfileCompletionEmploymentType;

    /**
     * @generated from field: com.stablemoney.api.identity.ProfileCompletionIncomeRange monthly_income = 2;
     */
    monthlyIncome: ProfileCompletionIncomeRange;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmploymentDetails.
 * Use `create(EmploymentDetailsSchema)` to create a new message.
 */
export const EmploymentDetailsSchema: GenMessage<EmploymentDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 7);

/**
 * @generated from message com.stablemoney.api.identity.GetProfileResponse
 */
export type GetProfileResponse =
  Message<"com.stablemoney.api.identity.GetProfileResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.PersonalDetails personal_details = 1;
     */
    personalDetails?: PersonalDetails;

    /**
     * @generated from field: string dob = 2;
     */
    dob: string;

    /**
     * @generated from field: com.stablemoney.api.identity.DemographicDetailsResponse demographic_details = 3;
     */
    demographicDetails?: DemographicDetailsResponse;

    /**
     * @generated from field: com.stablemoney.api.identity.EmploymentDetails employment_details = 4;
     */
    employmentDetails?: EmploymentDetails;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.Bank banks = 5;
     */
    banks: Bank[];

    /**
     * @generated from field: string background_image_url = 6;
     */
    backgroundImageUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetProfileResponse.
 * Use `create(GetProfileResponseSchema)` to create a new message.
 */
export const GetProfileResponseSchema: GenMessage<GetProfileResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 8);

/**
 * @generated from message com.stablemoney.api.identity.DemographicDetailsResponse
 */
export type DemographicDetailsResponse =
  Message<"com.stablemoney.api.identity.DemographicDetailsResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.Gender gender = 1;
     */
    gender: Gender;

    /**
     * @generated from field: com.stablemoney.api.identity.MaritalStatus marital_status = 2;
     */
    maritalStatus: MaritalStatus;

    /**
     * @generated from field: com.stablemoney.api.location.city.CityResponse city = 3;
     */
    city?: CityResponse;
  };

/**
 * Describes the message com.stablemoney.api.identity.DemographicDetailsResponse.
 * Use `create(DemographicDetailsResponseSchema)` to create a new message.
 */
export const DemographicDetailsResponseSchema: GenMessage<DemographicDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 9);

/**
 * @generated from message com.stablemoney.api.identity.Bank
 */
export type Bank = Message<"com.stablemoney.api.identity.Bank"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string id = 2;
   */
  id: string;

  /**
   * @generated from field: string icon_url = 3;
   */
  iconUrl: string;

  /**
   * @generated from field: bool has_fd_with_bank = 4;
   */
  hasFdWithBank: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.Bank.
 * Use `create(BankSchema)` to create a new message.
 */
export const BankSchema: GenMessage<Bank> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_ProfileCompletion, 10);

/**
 * @generated from enum com.stablemoney.api.identity.ProfileCompletionIncomeRange
 */
export enum ProfileCompletionIncomeRange {
  /**
   * @generated from enum value: UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE = 0;
   */
  UNKNOWN_PROFILE_COMPLETION_INCOME_RANGE = 0,

  /**
   * @generated from enum value: LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE = 1;
   */
  LESS_THAN_25K_PROFILE_COMPLETION_INCOME_RANGE = 1,

  /**
   * @generated from enum value: BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE = 2;
   */
  BETWEEN_25K_AND_50K_PROFILE_COMPLETION_INCOME_RANGE = 2,

  /**
   * @generated from enum value: BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE = 3;
   */
  BETWEEN_50K_AND_1L_PROFILE_COMPLETION_INCOME_RANGE = 3,

  /**
   * @generated from enum value: BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE = 4;
   */
  BETWEEN_1L_AND_2L_PROFILE_COMPLETION_INCOME_RANGE = 4,

  /**
   * @generated from enum value: BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE = 5;
   */
  BETWEEN_2L_AND_5L_PROFILE_COMPLETION_INCOME_RANGE = 5,

  /**
   * @generated from enum value: ABOVE_5L_PROFILE_COMPLETION_INCOME = 6;
   */
  ABOVE_5L_PROFILE_COMPLETION_INCOME = 6,
}

/**
 * Describes the enum com.stablemoney.api.identity.ProfileCompletionIncomeRange.
 */
export const ProfileCompletionIncomeRangeSchema: GenEnum<ProfileCompletionIncomeRange> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_ProfileCompletion, 0);

/**
 * @generated from enum com.stablemoney.api.identity.ProfileCompletionEmploymentType
 */
export enum ProfileCompletionEmploymentType {
  /**
   * @generated from enum value: UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 0;
   */
  UNKNOWN_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 0,

  /**
   * @generated from enum value: SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 1;
   */
  SALARIED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 1,

  /**
   * @generated from enum value: SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 2;
   */
  SELF_EMPLOYED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 2,

  /**
   * @generated from enum value: RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 3;
   */
  RETIRED_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 3,

  /**
   * @generated from enum value: HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 4;
   */
  HOUSEWIFE_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 4,

  /**
   * @generated from enum value: OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 5;
   */
  OTHER_PROFILE_COMPLETION_EMPLOYMENT_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.ProfileCompletionEmploymentType.
 */
export const ProfileCompletionEmploymentTypeSchema: GenEnum<ProfileCompletionEmploymentType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_ProfileCompletion, 1);
