// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Credit.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Credit.proto.
 */
export const file_public_models_identity_Credit: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.identity.CreditReportSummary
 */
export type CreditReportSummary =
  Message<"com.stablemoney.api.identity.CreditReportSummary"> & {
    /**
     * @generated from field: int32 credit_score = 1;
     */
    creditScore: number;

    /**
     * @generated from field: int32 min_credit_score = 2;
     */
    minCreditScore: number;

    /**
     * @generated from field: int32 max_credit_score = 3;
     */
    maxCreditScore: number;

    /**
     * @generated from field: string last_updated_date = 4;
     */
    lastUpdatedDate: string;

    /**
     * @generated from field: string summary_heading = 5;
     */
    summaryHeading: string;

    /**
     * @generated from field: string summary_description = 6;
     */
    summaryDescription: string;

    /**
     * @generated from field: string summary_logo_url = 7;
     */
    summaryLogoUrl: string;

    /**
     * @generated from field: double credit_score_percentage = 8;
     */
    creditScorePercentage: number;

    /**
     * @generated from field: string heading = 9;
     */
    heading: string;

    /**
     * @generated from field: string experian_logo_url = 10;
     */
    experianLogoUrl: string;

    /**
     * @generated from field: bool refresh_button = 11;
     */
    refreshButton: boolean;

    /**
     * @generated from field: string refresh_button_cta = 12;
     */
    refreshButtonCta: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditReportSummary.
 * Use `create(CreditReportSummarySchema)` to create a new message.
 */
export const CreditReportSummarySchema: GenMessage<CreditReportSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 0);

/**
 * @generated from message com.stablemoney.api.identity.SectionItem
 */
export type SectionItem =
  Message<"com.stablemoney.api.identity.SectionItem"> & {
    /**
     * @generated from field: string item_logo = 1;
     */
    itemLogo: string;

    /**
     * @generated from field: string item_title = 2;
     */
    itemTitle: string;

    /**
     * @generated from field: string item_description = 3;
     */
    itemDescription: string;

    /**
     * @generated from field: double item_value = 4;
     */
    itemValue: number;

    /**
     * @generated from field: string item_value_str = 5;
     */
    itemValueStr: string;

    /**
     * @generated from field: string item_value_description = 6;
     */
    itemValueDescription: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SectionItem.
 * Use `create(SectionItemSchema)` to create a new message.
 */
export const SectionItemSchema: GenMessage<SectionItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 1);

/**
 * @generated from message com.stablemoney.api.identity.CreditReportSection
 */
export type CreditReportSection =
  Message<"com.stablemoney.api.identity.CreditReportSection"> & {
    /**
     * @generated from field: string section_title = 1;
     */
    sectionTitle: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.SectionItem section_item = 2;
     */
    sectionItem: SectionItem[];

    /**
     * @generated from field: string bottom_sheet_header = 3;
     */
    bottomSheetHeader: string;

    /**
     * @generated from field: string bottom_sheet_header_color = 4;
     */
    bottomSheetHeaderColor: string;

    /**
     * @generated from field: string bottom_sheet_description = 5;
     */
    bottomSheetDescription: string;

    /**
     * @generated from field: string bottom_sheet_cta = 6;
     */
    bottomSheetCta: string;

    /**
     * @generated from field: int32 size = 7;
     */
    size: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditReportSection.
 * Use `create(CreditReportSectionSchema)` to create a new message.
 */
export const CreditReportSectionSchema: GenMessage<CreditReportSection> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 2);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatsDetailSection
 */
export type CreditScoreStatsDetailSection =
  Message<"com.stablemoney.api.identity.CreditScoreStatsDetailSection"> & {
    /**
     * @generated from field: optional string section_title = 1;
     */
    sectionTitle?: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.SectionItem section_item = 2;
     */
    sectionItem: SectionItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatsDetailSection.
 * Use `create(CreditScoreStatsDetailSectionSchema)` to create a new message.
 */
export const CreditScoreStatsDetailSectionSchema: GenMessage<CreditScoreStatsDetailSection> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 3);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatsDetails
 */
export type CreditScoreStatsDetails =
  Message<"com.stablemoney.api.identity.CreditScoreStatsDetails"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.CreditScoreStatsDetailSection credit_score_stats_detail_sections = 1;
     */
    creditScoreStatsDetailSections: CreditScoreStatsDetailSection[];

    /**
     * @generated from field: string left_heading = 2;
     */
    leftHeading: string;

    /**
     * @generated from field: string right_heading = 3;
     */
    rightHeading: string;

    /**
     * @generated from field: string left_heading_value = 4;
     */
    leftHeadingValue: string;

    /**
     * @generated from field: string right_heading_value = 5;
     */
    rightHeadingValue: string;

    /**
     * @generated from field: string question = 6;
     */
    question: string;

    /**
     * @generated from field: string answer = 7;
     */
    answer: string;

    /**
     * @generated from field: string cta_text = 8;
     */
    ctaText: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatsDetails.
 * Use `create(CreditScoreStatsDetailsSchema)` to create a new message.
 */
export const CreditScoreStatsDetailsSchema: GenMessage<CreditScoreStatsDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 4);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatItem
 */
export type CreditScoreStatItem =
  Message<"com.stablemoney.api.identity.CreditScoreStatItem"> & {
    /**
     * @generated from field: string stat_title = 1;
     */
    statTitle: string;

    /**
     * @generated from field: string stat_impact = 2;
     */
    statImpact: string;

    /**
     * @generated from field: string stat_value = 3;
     */
    statValue: string;

    /**
     * @generated from field: string stat_rating = 4;
     */
    statRating: string;

    /**
     * @generated from field: string stat_rating_color = 5;
     */
    statRatingColor: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.CreditScoreStatsDetails credit_score_stats_details = 6;
     */
    creditScoreStatsDetails?: CreditScoreStatsDetails;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatItem.
 * Use `create(CreditScoreStatItemSchema)` to create a new message.
 */
export const CreditScoreStatItemSchema: GenMessage<CreditScoreStatItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 5);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStats
 */
export type CreditScoreStats =
  Message<"com.stablemoney.api.identity.CreditScoreStats"> & {
    /**
     * @generated from field: string heading = 1;
     */
    heading: string;

    /**
     * @generated from field: string body = 2;
     */
    body: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.CreditScoreStatItem stats = 3;
     */
    stats: CreditScoreStatItem[];

    /**
     * @generated from field: string icon_url = 4;
     */
    iconUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStats.
 * Use `create(CreditScoreStatsSchema)` to create a new message.
 */
export const CreditScoreStatsSchema: GenMessage<CreditScoreStats> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 6);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreStatsV2
 */
export type CreditScoreStatsV2 =
  Message<"com.stablemoney.api.identity.CreditScoreStatsV2"> & {
    /**
     * @generated from field: string heading = 1;
     */
    heading: string;

    /**
     * @generated from field: string body = 2;
     */
    body: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.CreditScoreStatItem stats = 3;
     */
    stats: CreditScoreStatItem[];

    /**
     * @generated from field: string icon_url = 4;
     */
    iconUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreStatsV2.
 * Use `create(CreditScoreStatsV2Schema)` to create a new message.
 */
export const CreditScoreStatsV2Schema: GenMessage<CreditScoreStatsV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 7);

/**
 * @generated from message com.stablemoney.api.identity.CreditReport
 */
export type CreditReport =
  Message<"com.stablemoney.api.identity.CreditReport"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.CreditReportSummary credit_report_summary = 1;
     */
    creditReportSummary?: CreditReportSummary;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.CreditReportSection sections = 2;
     */
    sections: CreditReportSection[];

    /**
     * @generated from field: optional com.stablemoney.api.identity.CreditScoreStats credit_score_stats = 3;
     */
    creditScoreStats?: CreditScoreStats;

    /**
     * @generated from field: string experian_url = 4;
     */
    experianUrl: string;

    /**
     * @generated from field: string unique_tr_id = 5;
     */
    uniqueTrId: string;

    /**
     * @generated from field: string experian_ref_id = 6;
     */
    experianRefId: string;

    /**
     * @generated from field: string experian_cta = 7;
     */
    experianCta: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.CreditScoreStatsV2 credit_score_stats_v2 = 8;
     */
    creditScoreStatsV2?: CreditScoreStatsV2;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditReport.
 * Use `create(CreditReportSchema)` to create a new message.
 */
export const CreditReportSchema: GenMessage<CreditReport> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 8);

/**
 * @generated from message com.stablemoney.api.identity.CreditScoreDashboardResponse
 */
export type CreditScoreDashboardResponse =
  Message<"com.stablemoney.api.identity.CreditScoreDashboardResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.CreditScoreStatus credit_score_status = 1;
     */
    creditScoreStatus: CreditScoreStatus;

    /**
     * @generated from field: string section_title = 2;
     */
    sectionTitle: string;

    /**
     * @generated from field: string section_body = 3;
     */
    sectionBody: string;

    /**
     * @generated from field: optional int32 credit_score = 4;
     */
    creditScore?: number;

    /**
     * @generated from field: optional string credit_score_description = 5;
     */
    creditScoreDescription?: string;

    /**
     * @generated from field: optional string credit_score_color = 6;
     */
    creditScoreColor?: string;

    /**
     * @generated from field: optional string cta_text = 7;
     */
    ctaText?: string;

    /**
     * @generated from field: optional string last_updated_date = 8;
     */
    lastUpdatedDate?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.CreditReport credit_report = 9;
     */
    creditReport?: CreditReport;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditScoreDashboardResponse.
 * Use `create(CreditScoreDashboardResponseSchema)` to create a new message.
 */
export const CreditScoreDashboardResponseSchema: GenMessage<CreditScoreDashboardResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Credit, 9);

/**
 * @generated from enum com.stablemoney.api.identity.CreditScoreStatus
 */
export enum CreditScoreStatus {
  /**
   * @generated from enum value: UNKNOWN_CREDIT_SCORE_STATUS = 0;
   */
  UNKNOWN_CREDIT_SCORE_STATUS = 0,

  /**
   * @generated from enum value: NOT_INITIATED_CREDIT_SCORE_STATUS = 1;
   */
  NOT_INITIATED_CREDIT_SCORE_STATUS = 1,

  /**
   * @generated from enum value: ACTIVE_CREDIT_SCORE_STATUS = 2;
   */
  ACTIVE_CREDIT_SCORE_STATUS = 2,

  /**
   * @generated from enum value: CONSENT_REQUIRED_CREDIT_SCORE_STATUS = 3;
   */
  CONSENT_REQUIRED_CREDIT_SCORE_STATUS = 3,

  /**
   * @generated from enum value: NOT_FOUND_CREDIT_SCORE_STATUS = 4;
   */
  NOT_FOUND_CREDIT_SCORE_STATUS = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.CreditScoreStatus.
 */
export const CreditScoreStatusSchema: GenEnum<CreditScoreStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Credit, 0);
