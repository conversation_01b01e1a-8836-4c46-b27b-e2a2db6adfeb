// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Device.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Device.proto.
 */
export const file_public_models_identity_Device: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiNwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0RldmljZS5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSLcAwoKVXNlckRldmljZRIKCgJpZBgBIAEoCRI9CgtkZXZpY2VfdHlwZRgCIAEoDjIoLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuRGV2aWNlVHlwZRISCgpvc192ZXJzaW9uGAMgASgJEg0KBW1vZGVsGAQgASgJEhMKC2FwcF92ZXJzaW9uGAUgASgJEhgKEGxhc3RfYWN0aXZlX3RpbWUYBiABKAkSGgoSbm90aWZpY2F0aW9uX3Rva2VuGAcgASgJEg0KBWFhX2lkGAggASgJEhEKBGlkZnYYCSABKAlIAIgBARIZCgxhcHBzZmx5ZXJfaWQYCiABKAlIAYgBARIdChBhcHBfdmVyc2lvbl9uYW1lGAsgASgJSAKIAQESEAoDYWllGAwgASgISAOIAQESEAoDYXR0GA0gASgDSASIAQESFgoJYXBwX3N0b3JlGA4gASgJSAWIAQESHAoPYXBwX2luc3RhbmNlX2lkGBAgASgJSAaIAQFCBwoFX2lkZnZCDwoNX2FwcHNmbHllcl9pZEITChFfYXBwX3ZlcnNpb25fbmFtZUIGCgRfYWllQgYKBF9hdHRCDAoKX2FwcF9zdG9yZUISChBfYXBwX2luc3RhbmNlX2lkIlQKE1VwZGF0ZURldmljZVJlcXVlc3QSPQoLdXNlcl9kZXZpY2UYASABKAsyKC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlVzZXJEZXZpY2UiFgoUVXBkYXRlRGV2aWNlUmVzcG9uc2UqVAoKRGV2aWNlVHlwZRIXChNDTElFTlRfVFlQRV9VTktOT1dOEAASCwoHQU5EUk9JRBABEgcKA0lPUxACEgcKA1dFQhADEg4KCk1PQklMRV9XRUIQBEIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.UserDevice
 */
export type UserDevice = Message<"com.stablemoney.api.identity.UserDevice"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: com.stablemoney.api.identity.DeviceType device_type = 2;
   */
  deviceType: DeviceType;

  /**
   * @generated from field: string os_version = 3;
   */
  osVersion: string;

  /**
   * @generated from field: string model = 4;
   */
  model: string;

  /**
   * @generated from field: string app_version = 5;
   */
  appVersion: string;

  /**
   * @generated from field: string last_active_time = 6;
   */
  lastActiveTime: string;

  /**
   * @generated from field: string notification_token = 7;
   */
  notificationToken: string;

  /**
   * @generated from field: string aa_id = 8;
   */
  aaId: string;

  /**
   * @generated from field: optional string idfv = 9;
   */
  idfv?: string;

  /**
   * @generated from field: optional string appsflyer_id = 10;
   */
  appsflyerId?: string;

  /**
   * @generated from field: optional string app_version_name = 11;
   */
  appVersionName?: string;

  /**
   * @generated from field: optional bool aie = 12;
   */
  aie?: boolean;

  /**
   * @generated from field: optional int64 att = 13;
   */
  att?: bigint;

  /**
   * @generated from field: optional string app_store = 14;
   */
  appStore?: string;

  /**
   * @generated from field: optional string app_instance_id = 16;
   */
  appInstanceId?: string;
};

/**
 * Describes the message com.stablemoney.api.identity.UserDevice.
 * Use `create(UserDeviceSchema)` to create a new message.
 */
export const UserDeviceSchema: GenMessage<UserDevice> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Device, 0);

/**
 * @generated from message com.stablemoney.api.identity.UpdateDeviceRequest
 */
export type UpdateDeviceRequest =
  Message<"com.stablemoney.api.identity.UpdateDeviceRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.UserDevice user_device = 1;
     */
    userDevice?: UserDevice;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateDeviceRequest.
 * Use `create(UpdateDeviceRequestSchema)` to create a new message.
 */
export const UpdateDeviceRequestSchema: GenMessage<UpdateDeviceRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Device, 1);

/**
 * @generated from message com.stablemoney.api.identity.UpdateDeviceResponse
 */
export type UpdateDeviceResponse =
  Message<"com.stablemoney.api.identity.UpdateDeviceResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.UpdateDeviceResponse.
 * Use `create(UpdateDeviceResponseSchema)` to create a new message.
 */
export const UpdateDeviceResponseSchema: GenMessage<UpdateDeviceResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Device, 2);

/**
 * @generated from enum com.stablemoney.api.identity.DeviceType
 */
export enum DeviceType {
  /**
   * @generated from enum value: CLIENT_TYPE_UNKNOWN = 0;
   */
  CLIENT_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: ANDROID = 1;
   */
  ANDROID = 1,

  /**
   * @generated from enum value: IOS = 2;
   */
  IOS = 2,

  /**
   * @generated from enum value: WEB = 3;
   */
  WEB = 3,

  /**
   * @generated from enum value: MOBILE_WEB = 4;
   */
  MOBILE_WEB = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.DeviceType.
 */
export const DeviceTypeSchema: GenEnum<DeviceType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Device, 0);
