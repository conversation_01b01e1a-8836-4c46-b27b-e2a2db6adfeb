// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/SendAppLink.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/SendAppLink.proto.
 */
export const file_public_models_identity_SendAppLink: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CihwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1NlbmRBcHBMaW5rLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IioKElNlbmRBcHBMaW5rUmVxdWVzdBIUCgxwaG9uZV9udW1iZXIYASABKAkiFQoTU2VuZEFwcExpbmtSZXNwb25zZUIkCiBjb20uc3RhYmxlbW9uZXkuYXBpLm5vdGlmaWNhdGlvblABYgZwcm90bzM",
  );

/**
 * @generated from message com.stablemoney.api.identity.SendAppLinkRequest
 */
export type SendAppLinkRequest =
  Message<"com.stablemoney.api.identity.SendAppLinkRequest"> & {
    /**
     * @generated from field: string phone_number = 1;
     */
    phoneNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SendAppLinkRequest.
 * Use `create(SendAppLinkRequestSchema)` to create a new message.
 */
export const SendAppLinkRequestSchema: GenMessage<SendAppLinkRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_SendAppLink, 0);

/**
 * @generated from message com.stablemoney.api.identity.SendAppLinkResponse
 */
export type SendAppLinkResponse =
  Message<"com.stablemoney.api.identity.SendAppLinkResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SendAppLinkResponse.
 * Use `create(SendAppLinkResponseSchema)` to create a new message.
 */
export const SendAppLinkResponseSchema: GenMessage<SendAppLinkResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_SendAppLink, 1);
