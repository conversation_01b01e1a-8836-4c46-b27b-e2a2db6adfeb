// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/StableExchange.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/StableExchange.proto.
 */
export const file_public_models_identity_StableExchange: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CitwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1N0YWJsZUV4Y2hhbmdlLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IqEFCgxRdWVzdGlvbkRhdGESEAoIcXVlc3Rpb24YASABKAkSEwoLcXVlc3Rpb25faWQYAiABKAkSEwoLcGVyc3BlY3RpdmUYAyABKAkSEQoJdXNlcl9uYW1lGAQgASgJEhAKCHVzZXJfYWdlGAUgASgFEhMKC3VzZXJfZ2VuZGVyGAYgASgJEhEKCXVzZXJfY2l0eRgHIAEoCRISCgp1c2VyX2ltYWdlGAggASgJEhYKDnJlc3BvbnNlX2NvdW50GAkgASgJEhkKEXJlZ2lzdHJhdGlvbl9kYXRlGAogASgJEj0KC2Fuc3dlcl9kYXRhGAsgAygLMiguY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5BbnN3ZXJMaXN0EhcKD2lzX3JlbWluZGVyX3NldBgMIAEoCBIWCg5yZW1pbmRlcl9jb3VudBgNIAEoBRIcChRxdWVzdGlvbl9wb3N0ZWRfZGF0ZRgOIAEoCRIcChRpc19xdWVzdGlvbl9hbnN3ZXJlZBgPIAEoCBIaChJpc19xdWVzdGlvbl9wb3N0ZWQYECABKAgSGAoQYWdyZWVfcGVyY2VudGFnZRgRIAEoCRIXCg93ZWxfZG9uZV9zdHJpbmcYEiABKAkSGAoQcmVzcG9uZGVyX2ltYWdlcxgTIAMoCRJMChNyZW1pbmRlcl9hc2tfc3RyaW5nGBQgASgLMi8uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5SZW1pbmRlckFza1N0cmluZxIXCg91c2VyX2ZpcnN0X25hbWUYFSABKAkSIwobcXVlc3Rpb25fcG9zdGVkX2RhdGVfc3RyaW5nGBYgASgJEiAKGHF1ZXN0aW9uX3Bvc3RlZF9lbmRfZGF0ZRgXIAEoCSJaChVUb2RheVF1ZXN0aW9uUmVzcG9uc2USQQoNcXVlc3Rpb25fZGF0YRgBIAEoCzIqLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUXVlc3Rpb25EYXRhIkMKCkFuc3dlckxpc3QSEQoJYW5zd2VyX2lkGAEgASgJEg4KBmFuc3dlchgCIAEoCRISCgpwZXJjZW50YWdlGAMgASgBIlgKE0FsbFF1ZXN0aW9uUmVzcG9uc2USQQoNcXVlc3Rpb25fZGF0YRgBIAMoCzIqLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUXVlc3Rpb25EYXRhIlsKEVJlbWluZGVyQXNrU3RyaW5nEhcKD3JlbWluZGVyX3N0cmluZxgBIAEoCRISCgphc2tfc3RyaW5nGAIgAygJEhkKEWNvbXBsZXRpb25fc3RyaW5nGAMgASgJIj0KE1N1Ym1pdEFuc3dlclJlcXVlc3QSEQoJYW5zd2VyX2lkGAEgASgJEhMKC3F1ZXN0aW9uX2lkGAIgASgJIlkKFFN1Ym1pdEFuc3dlclJlc3BvbnNlEkEKDXF1ZXN0aW9uX2RhdGEYASABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlF1ZXN0aW9uRGF0YSIpChVTdWJtaXRRdWVzdGlvblJlcXVlc3QSEAoIcXVlc3Rpb24YASABKAkiGAoWU3VibWl0UXVlc3Rpb25SZXNwb25zZSJbChZTdWJtaXRSZW1pbmRlclJlc3BvbnNlEkEKDXF1ZXN0aW9uX2RhdGEYASABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlF1ZXN0aW9uRGF0YSIsChVTdWJtaXRSZW1pbmRlclJlcXVlc3QSEwoLaXNfcmVtaW5kZXIYASABKAhCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eVABYgZwcm90bzM",
  );

/**
 * @generated from message com.stablemoney.api.identity.QuestionData
 */
export type QuestionData =
  Message<"com.stablemoney.api.identity.QuestionData"> & {
    /**
     * @generated from field: string question = 1;
     */
    question: string;

    /**
     * @generated from field: string question_id = 2;
     */
    questionId: string;

    /**
     * @generated from field: string perspective = 3;
     */
    perspective: string;

    /**
     * @generated from field: string user_name = 4;
     */
    userName: string;

    /**
     * @generated from field: int32 user_age = 5;
     */
    userAge: number;

    /**
     * @generated from field: string user_gender = 6;
     */
    userGender: string;

    /**
     * @generated from field: string user_city = 7;
     */
    userCity: string;

    /**
     * @generated from field: string user_image = 8;
     */
    userImage: string;

    /**
     * @generated from field: string response_count = 9;
     */
    responseCount: string;

    /**
     * @generated from field: string registration_date = 10;
     */
    registrationDate: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.AnswerList answer_data = 11;
     */
    answerData: AnswerList[];

    /**
     * @generated from field: bool is_reminder_set = 12;
     */
    isReminderSet: boolean;

    /**
     * @generated from field: int32 reminder_count = 13;
     */
    reminderCount: number;

    /**
     * @generated from field: string question_posted_date = 14;
     */
    questionPostedDate: string;

    /**
     * @generated from field: bool is_question_answered = 15;
     */
    isQuestionAnswered: boolean;

    /**
     * @generated from field: bool is_question_posted = 16;
     */
    isQuestionPosted: boolean;

    /**
     * @generated from field: string agree_percentage = 17;
     */
    agreePercentage: string;

    /**
     * @generated from field: string wel_done_string = 18;
     */
    welDoneString: string;

    /**
     * @generated from field: repeated string responder_images = 19;
     */
    responderImages: string[];

    /**
     * @generated from field: com.stablemoney.api.identity.ReminderAskString reminder_ask_string = 20;
     */
    reminderAskString?: ReminderAskString;

    /**
     * @generated from field: string user_first_name = 21;
     */
    userFirstName: string;

    /**
     * @generated from field: string question_posted_date_string = 22;
     */
    questionPostedDateString: string;

    /**
     * @generated from field: string question_posted_end_date = 23;
     */
    questionPostedEndDate: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.QuestionData.
 * Use `create(QuestionDataSchema)` to create a new message.
 */
export const QuestionDataSchema: GenMessage<QuestionData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 0);

/**
 * @generated from message com.stablemoney.api.identity.TodayQuestionResponse
 */
export type TodayQuestionResponse =
  Message<"com.stablemoney.api.identity.TodayQuestionResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.QuestionData question_data = 1;
     */
    questionData?: QuestionData;
  };

/**
 * Describes the message com.stablemoney.api.identity.TodayQuestionResponse.
 * Use `create(TodayQuestionResponseSchema)` to create a new message.
 */
export const TodayQuestionResponseSchema: GenMessage<TodayQuestionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 1);

/**
 * @generated from message com.stablemoney.api.identity.AnswerList
 */
export type AnswerList = Message<"com.stablemoney.api.identity.AnswerList"> & {
  /**
   * @generated from field: string answer_id = 1;
   */
  answerId: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;

  /**
   * @generated from field: double percentage = 3;
   */
  percentage: number;
};

/**
 * Describes the message com.stablemoney.api.identity.AnswerList.
 * Use `create(AnswerListSchema)` to create a new message.
 */
export const AnswerListSchema: GenMessage<AnswerList> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 2);

/**
 * @generated from message com.stablemoney.api.identity.AllQuestionResponse
 */
export type AllQuestionResponse =
  Message<"com.stablemoney.api.identity.AllQuestionResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.QuestionData question_data = 1;
     */
    questionData: QuestionData[];
  };

/**
 * Describes the message com.stablemoney.api.identity.AllQuestionResponse.
 * Use `create(AllQuestionResponseSchema)` to create a new message.
 */
export const AllQuestionResponseSchema: GenMessage<AllQuestionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 3);

/**
 * @generated from message com.stablemoney.api.identity.ReminderAskString
 */
export type ReminderAskString =
  Message<"com.stablemoney.api.identity.ReminderAskString"> & {
    /**
     * @generated from field: string reminder_string = 1;
     */
    reminderString: string;

    /**
     * @generated from field: repeated string ask_string = 2;
     */
    askString: string[];

    /**
     * @generated from field: string completion_string = 3;
     */
    completionString: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.ReminderAskString.
 * Use `create(ReminderAskStringSchema)` to create a new message.
 */
export const ReminderAskStringSchema: GenMessage<ReminderAskString> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 4);

/**
 * @generated from message com.stablemoney.api.identity.SubmitAnswerRequest
 */
export type SubmitAnswerRequest =
  Message<"com.stablemoney.api.identity.SubmitAnswerRequest"> & {
    /**
     * @generated from field: string answer_id = 1;
     */
    answerId: string;

    /**
     * @generated from field: string question_id = 2;
     */
    questionId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SubmitAnswerRequest.
 * Use `create(SubmitAnswerRequestSchema)` to create a new message.
 */
export const SubmitAnswerRequestSchema: GenMessage<SubmitAnswerRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 5);

/**
 * @generated from message com.stablemoney.api.identity.SubmitAnswerResponse
 */
export type SubmitAnswerResponse =
  Message<"com.stablemoney.api.identity.SubmitAnswerResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.QuestionData question_data = 1;
     */
    questionData?: QuestionData;
  };

/**
 * Describes the message com.stablemoney.api.identity.SubmitAnswerResponse.
 * Use `create(SubmitAnswerResponseSchema)` to create a new message.
 */
export const SubmitAnswerResponseSchema: GenMessage<SubmitAnswerResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 6);

/**
 * @generated from message com.stablemoney.api.identity.SubmitQuestionRequest
 */
export type SubmitQuestionRequest =
  Message<"com.stablemoney.api.identity.SubmitQuestionRequest"> & {
    /**
     * @generated from field: string question = 1;
     */
    question: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SubmitQuestionRequest.
 * Use `create(SubmitQuestionRequestSchema)` to create a new message.
 */
export const SubmitQuestionRequestSchema: GenMessage<SubmitQuestionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 7);

/**
 * @generated from message com.stablemoney.api.identity.SubmitQuestionResponse
 */
export type SubmitQuestionResponse =
  Message<"com.stablemoney.api.identity.SubmitQuestionResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SubmitQuestionResponse.
 * Use `create(SubmitQuestionResponseSchema)` to create a new message.
 */
export const SubmitQuestionResponseSchema: GenMessage<SubmitQuestionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 8);

/**
 * @generated from message com.stablemoney.api.identity.SubmitReminderResponse
 */
export type SubmitReminderResponse =
  Message<"com.stablemoney.api.identity.SubmitReminderResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.QuestionData question_data = 1;
     */
    questionData?: QuestionData;
  };

/**
 * Describes the message com.stablemoney.api.identity.SubmitReminderResponse.
 * Use `create(SubmitReminderResponseSchema)` to create a new message.
 */
export const SubmitReminderResponseSchema: GenMessage<SubmitReminderResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 9);

/**
 * @generated from message com.stablemoney.api.identity.SubmitReminderRequest
 */
export type SubmitReminderRequest =
  Message<"com.stablemoney.api.identity.SubmitReminderRequest"> & {
    /**
     * @generated from field: bool is_reminder = 1;
     */
    isReminder: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.SubmitReminderRequest.
 * Use `create(SubmitReminderRequestSchema)` to create a new message.
 */
export const SubmitReminderRequestSchema: GenMessage<SubmitReminderRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_StableExchange, 10);
