// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/UserApps.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { DeviceType } from "./Device_pb.js";
import { file_public_models_identity_Device } from "./Device_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/UserApps.proto.
 */
export const file_public_models_identity_UserApps: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiVwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1VzZXJBcHBzLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IqYBChBJbnN0YWxsZWRBcHBJdGVtEhAKCGFwcF9uYW1lGAEgASgJEhQKDHBhY2thZ2VfbmFtZRgCIAEoCRIUCgx2ZXJzaW9uX25hbWUYAyABKAkSFQoNaXNfc3lzdGVtX2FwcBgEIAEoCBI9CgtkZXZpY2VfdHlwZRgFIAEoDjIoLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuRGV2aWNlVHlwZSJiChhVc2VySW5zdGFsbGVkQXBwc1JlcXVlc3QSRgoOaW5zdGFsbGVkX2FwcHMYASADKAsyLi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lkluc3RhbGxlZEFwcEl0ZW0iGwoZVXNlckluc3RhbGxlZEFwcHNSZXNwb25zZSIxCgdBcHBJdGVtEhAKCGFwcF9uYW1lGAEgASgJEhQKDHBhY2thZ2VfbmFtZRgCIAEoCSJPChBBcHBzTGlzdFJlc3BvbnNlEjsKDGFwcEl0ZW1zTGlzdBgBIAMoCzIlLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQXBwSXRlbUIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
    [file_public_models_identity_Device],
  );

/**
 * @generated from message com.stablemoney.api.identity.InstalledAppItem
 */
export type InstalledAppItem =
  Message<"com.stablemoney.api.identity.InstalledAppItem"> & {
    /**
     * @generated from field: string app_name = 1;
     */
    appName: string;

    /**
     * @generated from field: string package_name = 2;
     */
    packageName: string;

    /**
     * @generated from field: string version_name = 3;
     */
    versionName: string;

    /**
     * @generated from field: bool is_system_app = 4;
     */
    isSystemApp: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.DeviceType device_type = 5;
     */
    deviceType: DeviceType;
  };

/**
 * Describes the message com.stablemoney.api.identity.InstalledAppItem.
 * Use `create(InstalledAppItemSchema)` to create a new message.
 */
export const InstalledAppItemSchema: GenMessage<InstalledAppItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserApps, 0);

/**
 * @generated from message com.stablemoney.api.identity.UserInstalledAppsRequest
 */
export type UserInstalledAppsRequest =
  Message<"com.stablemoney.api.identity.UserInstalledAppsRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.InstalledAppItem installed_apps = 1;
     */
    installedApps: InstalledAppItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UserInstalledAppsRequest.
 * Use `create(UserInstalledAppsRequestSchema)` to create a new message.
 */
export const UserInstalledAppsRequestSchema: GenMessage<UserInstalledAppsRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserApps, 1);

/**
 * @generated from message com.stablemoney.api.identity.UserInstalledAppsResponse
 */
export type UserInstalledAppsResponse =
  Message<"com.stablemoney.api.identity.UserInstalledAppsResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.UserInstalledAppsResponse.
 * Use `create(UserInstalledAppsResponseSchema)` to create a new message.
 */
export const UserInstalledAppsResponseSchema: GenMessage<UserInstalledAppsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserApps, 2);

/**
 * @generated from message com.stablemoney.api.identity.AppItem
 */
export type AppItem = Message<"com.stablemoney.api.identity.AppItem"> & {
  /**
   * @generated from field: string app_name = 1;
   */
  appName: string;

  /**
   * @generated from field: string package_name = 2;
   */
  packageName: string;
};

/**
 * Describes the message com.stablemoney.api.identity.AppItem.
 * Use `create(AppItemSchema)` to create a new message.
 */
export const AppItemSchema: GenMessage<AppItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserApps, 3);

/**
 * @generated from message com.stablemoney.api.identity.AppsListResponse
 */
export type AppsListResponse =
  Message<"com.stablemoney.api.identity.AppsListResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.AppItem appItemsList = 1;
     */
    appItemsList: AppItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.AppsListResponse.
 * Use `create(AppsListResponseSchema)` to create a new message.
 */
export const AppsListResponseSchema: GenMessage<AppsListResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserApps, 4);
