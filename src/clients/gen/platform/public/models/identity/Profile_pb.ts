// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Profile.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { OnboardingModule } from "./Onboarding_pb.js";
import { file_public_models_identity_Onboarding } from "./Onboarding_pb.js";
import type { UserDevice } from "./Device_pb.js";
import { file_public_models_identity_Device } from "./Device_pb.js";
import type { CampaignType } from "./Campaign_pb.js";
import { file_public_models_identity_Campaign } from "./Campaign_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Profile.proto.
 */
export const file_public_models_identity_Profile: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_identity_Onboarding,
      file_public_models_identity_Device,
      file_public_models_identity_Campaign,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.UserProfileInternalResponse
 */
export type UserProfileInternalResponse =
  Message<"com.stablemoney.api.identity.UserProfileInternalResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.UserProfileResponse profile = 1;
     */
    profile?: UserProfileResponse;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.UserDevice user_devices = 2;
     */
    userDevices: UserDevice[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileInternalResponse.
 * Use `create(UserProfileInternalResponseSchema)` to create a new message.
 */
export const UserProfileInternalResponseSchema: GenMessage<UserProfileInternalResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 0);

/**
 * @generated from message com.stablemoney.api.identity.UserData
 */
export type UserData = Message<"com.stablemoney.api.identity.UserData"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string email = 2;
   */
  email: string;

  /**
   * @generated from field: bool email_verified = 3;
   */
  emailVerified: boolean;

  /**
   * @generated from field: string mobile = 4;
   */
  mobile: string;

  /**
   * @generated from field: bool mobile_verified = 5;
   */
  mobileVerified: boolean;

  /**
   * @generated from field: string name = 6;
   */
  name: string;

  /**
   * @generated from field: string masked_email = 8;
   */
  maskedEmail: string;

  /**
   * @generated from field: string first_name = 9;
   */
  firstName: string;

  /**
   * @generated from field: string last_name = 10;
   */
  lastName: string;

  /**
   * @generated from field: string social_name = 11;
   */
  socialName: string;

  /**
   * @generated from field: string profile_image_url = 12;
   */
  profileImageUrl: string;

  /**
   * @generated from field: string user_registration_time = 13;
   */
  userRegistrationTime: string;
};

/**
 * Describes the message com.stablemoney.api.identity.UserData.
 * Use `create(UserDataSchema)` to create a new message.
 */
export const UserDataSchema: GenMessage<UserData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 1);

/**
 * @generated from message com.stablemoney.api.identity.UserProfileResponse
 */
export type UserProfileResponse =
  Message<"com.stablemoney.api.identity.UserProfileResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.UserData data = 2;
     */
    data?: UserData;

    /**
     * @generated from field: com.stablemoney.api.identity.UserProfileData profile_data = 3;
     */
    profileData?: UserProfileData;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.OnboardingModuleStatusData module_status = 4;
     */
    moduleStatus: OnboardingModuleStatusData[];

    /**
     * @generated from field: string life_time_status = 5;
     */
    lifeTimeStatus: string;

    /**
     * @generated from field: string current_status = 6;
     */
    currentStatus: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileResponse.
 * Use `create(UserProfileResponseSchema)` to create a new message.
 */
export const UserProfileResponseSchema: GenMessage<UserProfileResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 2);

/**
 * @generated from message com.stablemoney.api.identity.UserProfileData
 */
export type UserProfileData =
  Message<"com.stablemoney.api.identity.UserProfileData"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string pan_number = 2;
     */
    panNumber: string;

    /**
     * @generated from field: string aadhar_number = 3;
     */
    aadharNumber: string;

    /**
     * @generated from field: string dob = 4;
     */
    dob: string;

    /**
     * @generated from field: com.stablemoney.api.identity.Gender gender = 5;
     */
    gender: Gender;

    /**
     * @generated from field: com.stablemoney.api.identity.IncomeRange income_range = 6;
     */
    incomeRange: IncomeRange;

    /**
     * @generated from field: com.stablemoney.api.identity.EmploymentType employment_type = 7;
     */
    employmentType: EmploymentType;

    /**
     * @generated from field: com.stablemoney.api.identity.TradingExperience trading_experience = 8;
     */
    tradingExperience: TradingExperience;

    /**
     * @generated from field: com.stablemoney.api.identity.MaritalStatus marital_status = 9;
     */
    maritalStatus: MaritalStatus;

    /**
     * @generated from field: string father_name = 10;
     */
    fatherName: string;

    /**
     * @generated from field: string mother_name = 11;
     */
    motherName: string;

    /**
     * @generated from field: string e_sign_url = 12;
     */
    eSignUrl: string;

    /**
     * @generated from field: string income_tax_department_name = 13;
     */
    incomeTaxDepartmentName: string;

    /**
     * @generated from field: string kra_name = 14;
     */
    kraName: string;

    /**
     * @generated from field: int32 fd_booking_count = 16;
     */
    fdBookingCount: number;

    /**
     * @generated from field: bool first_fd_reward_claimed = 17;
     */
    firstFdRewardClaimed: boolean;

    /**
     * @generated from field: bool is_gold_member = 18;
     */
    isGoldMember: boolean;

    /**
     * @generated from field: bool is_upswing_ticket_raised = 19;
     */
    isUpswingTicketRaised: boolean;

    /**
     * @generated from field: string in_app_review_cohort = 20 [deprecated = true];
     * @deprecated
     */
    inAppReviewCohort: string;

    /**
     * @generated from field: string ticket_cohort = 21;
     */
    ticketCohort: string;

    /**
     * @generated from field: bool has_lifetime_investment = 22;
     */
    hasLifetimeInvestment: boolean;

    /**
     * 1. app_home_ui_250_reward  2. app_home_ui_200_reward 3. app_home_ui_no_reward
     *
     * @generated from field: string new_user_home_ui_config = 23;
     */
    newUserHomeUiConfig: string;

    /**
     * 1. app_reward_ui_250_reward  2. app_reward_ui_200_reward 3. app_reward_ui_no_reward
     *
     * @generated from field: string new_user_reward_ui_config = 24;
     */
    newUserRewardUiConfig: string;

    /**
     * @generated from field: bool show_credit_score = 25;
     */
    showCreditScore: boolean;

    /**
     * @generated from field: string referer_name = 26;
     */
    refererName: string;

    /**
     * @generated from field: string rewards_info_string = 27;
     */
    rewardsInfoString: string;

    /**
     * @generated from field: bool has_my_investments = 28;
     */
    hasMyInvestments: boolean;

    /**
     * @generated from field: bool is_special_gold_member = 29;
     */
    isSpecialGoldMember: boolean;

    /**
     * @generated from field: bool is_rating_available = 30;
     */
    isRatingAvailable: boolean;

    /**
     * @generated from field: bool show_credit_refresh = 31;
     */
    showCreditRefresh: boolean;

    /**
     * app_home_fincare/app_home/gt_home_fincare/gt_home
     *
     * @generated from field: string investment_user_home_ui_config = 33;
     */
    investmentUserHomeUiConfig: string;

    /**
     * @generated from field: double profile_completion_percentage = 34;
     */
    profileCompletionPercentage: number;

    /**
     * @generated from field: bool is_senior_citizen = 35;
     */
    isSeniorCitizen: boolean;

    /**
     * @generated from field: bool view_senior_citizen_rates = 36;
     */
    viewSeniorCitizenRates: boolean;

    /**
     * @generated from field: bool is_referer_gold_member = 37;
     */
    isRefererGoldMember: boolean;

    /**
     * @generated from field: string appsflyer_referral_link = 38;
     */
    appsflyerReferralLink: string;

    /**
     * This is the campaign name through which this user was referred
     *
     * @generated from field: optional com.stablemoney.api.identity.CampaignType referral_campaign = 39;
     */
    referralCampaign?: CampaignType;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserProfileData.
 * Use `create(UserProfileDataSchema)` to create a new message.
 */
export const UserProfileDataSchema: GenMessage<UserProfileData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 3);

/**
 * @generated from message com.stablemoney.api.identity.UpdateProfileRequest
 */
export type UpdateProfileRequest =
  Message<"com.stablemoney.api.identity.UpdateProfileRequest"> & {
    /**
     * @generated from field: optional string dob = 1;
     */
    dob?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.IncomeRange income_range = 2;
     */
    incomeRange?: IncomeRange;

    /**
     * @generated from field: optional com.stablemoney.api.identity.EmploymentType employment_type = 3;
     */
    employmentType?: EmploymentType;

    /**
     * @generated from field: optional com.stablemoney.api.identity.TradingExperience trading_experience = 4;
     */
    tradingExperience?: TradingExperience;

    /**
     * @generated from field: optional com.stablemoney.api.identity.MaritalStatus marital_status = 5;
     */
    maritalStatus?: MaritalStatus;

    /**
     * @generated from field: optional string father_name = 6;
     */
    fatherName?: string;

    /**
     * @generated from field: optional string mother_name = 7;
     */
    motherName?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.Gender gender = 8;
     */
    gender?: Gender;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateProfileRequest.
 * Use `create(UpdateProfileRequestSchema)` to create a new message.
 */
export const UpdateProfileRequestSchema: GenMessage<UpdateProfileRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 4);

/**
 * @generated from message com.stablemoney.api.identity.UpdateProfileResponse
 */
export type UpdateProfileResponse =
  Message<"com.stablemoney.api.identity.UpdateProfileResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.UpdateProfileResponse.
 * Use `create(UpdateProfileResponseSchema)` to create a new message.
 */
export const UpdateProfileResponseSchema: GenMessage<UpdateProfileResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 5);

/**
 * @generated from message com.stablemoney.api.identity.OnboardingModuleStatusData
 */
export type OnboardingModuleStatusData =
  Message<"com.stablemoney.api.identity.OnboardingModuleStatusData"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.OnboardingModule name = 1;
     */
    name: OnboardingModule;

    /**
     * @generated from field: bool status = 2;
     */
    status: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.OnboardingModuleStatusData.
 * Use `create(OnboardingModuleStatusDataSchema)` to create a new message.
 */
export const OnboardingModuleStatusDataSchema: GenMessage<OnboardingModuleStatusData> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 6);

/**
 * @generated from message com.stablemoney.api.identity.UpdateNameResponse
 */
export type UpdateNameResponse =
  Message<"com.stablemoney.api.identity.UpdateNameResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.UpdateNameResponse.
 * Use `create(UpdateNameResponseSchema)` to create a new message.
 */
export const UpdateNameResponseSchema: GenMessage<UpdateNameResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 7);

/**
 * @generated from message com.stablemoney.api.identity.UpdateMediaSourceRequest
 */
export type UpdateMediaSourceRequest =
  Message<"com.stablemoney.api.identity.UpdateMediaSourceRequest"> & {
    /**
     * @generated from field: string media_source = 1;
     */
    mediaSource: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateMediaSourceRequest.
 * Use `create(UpdateMediaSourceRequestSchema)` to create a new message.
 */
export const UpdateMediaSourceRequestSchema: GenMessage<UpdateMediaSourceRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 8);

/**
 * @generated from message com.stablemoney.api.identity.UpdateMediaSourceResponse
 */
export type UpdateMediaSourceResponse =
  Message<"com.stablemoney.api.identity.UpdateMediaSourceResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.UpdateMediaSourceResponse.
 * Use `create(UpdateMediaSourceResponseSchema)` to create a new message.
 */
export const UpdateMediaSourceResponseSchema: GenMessage<UpdateMediaSourceResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 9);

/**
 * @generated from message com.stablemoney.api.identity.UpdateNameRequest
 */
export type UpdateNameRequest =
  Message<"com.stablemoney.api.identity.UpdateNameRequest"> & {
    /**
     * @generated from field: string first_name = 1;
     */
    firstName: string;

    /**
     * @generated from field: string last_name = 2;
     */
    lastName: string;

    /**
     * @generated from field: optional bool update_name = 3;
     */
    updateName?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateNameRequest.
 * Use `create(UpdateNameRequestSchema)` to create a new message.
 */
export const UpdateNameRequestSchema: GenMessage<UpdateNameRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 10);

/**
 * @generated from message com.stablemoney.api.identity.UpdateAcquisitionParamsRequest
 */
export type UpdateAcquisitionParamsRequest =
  Message<"com.stablemoney.api.identity.UpdateAcquisitionParamsRequest"> & {
    /**
     * @generated from field: map<string, string> params = 1;
     */
    params: { [key: string]: string };
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateAcquisitionParamsRequest.
 * Use `create(UpdateAcquisitionParamsRequestSchema)` to create a new message.
 */
export const UpdateAcquisitionParamsRequestSchema: GenMessage<UpdateAcquisitionParamsRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 11);

/**
 * @generated from message com.stablemoney.api.identity.UpdateAcquisitionParamsResponse
 */
export type UpdateAcquisitionParamsResponse =
  Message<"com.stablemoney.api.identity.UpdateAcquisitionParamsResponse"> & {
    /**
     * @generated from field: bool params_updated = 1;
     */
    paramsUpdated: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateAcquisitionParamsResponse.
 * Use `create(UpdateAcquisitionParamsResponseSchema)` to create a new message.
 */
export const UpdateAcquisitionParamsResponseSchema: GenMessage<UpdateAcquisitionParamsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 12);

/**
 * @generated from message com.stablemoney.api.identity.DeleteUserRequest
 */
export type DeleteUserRequest =
  Message<"com.stablemoney.api.identity.DeleteUserRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string reason = 2;
     */
    reason: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.DeleteUserRequest.
 * Use `create(DeleteUserRequestSchema)` to create a new message.
 */
export const DeleteUserRequestSchema: GenMessage<DeleteUserRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 13);

/**
 * @generated from message com.stablemoney.api.identity.DeleteUserResponse
 */
export type DeleteUserResponse =
  Message<"com.stablemoney.api.identity.DeleteUserResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.DeleteUserResponse.
 * Use `create(DeleteUserResponseSchema)` to create a new message.
 */
export const DeleteUserResponseSchema: GenMessage<DeleteUserResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 14);

/**
 * @generated from message com.stablemoney.api.identity.CreditScorePanResponse
 */
export type CreditScorePanResponse =
  Message<"com.stablemoney.api.identity.CreditScorePanResponse"> & {
    /**
     * @generated from field: string pan_number = 1;
     */
    panNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.CreditScorePanResponse.
 * Use `create(CreditScorePanResponseSchema)` to create a new message.
 */
export const CreditScorePanResponseSchema: GenMessage<CreditScorePanResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 15);

/**
 * @generated from message com.stablemoney.api.identity.CityDataResponse
 */
export type CityDataResponse =
  Message<"com.stablemoney.api.identity.CityDataResponse"> & {
    /**
     * @generated from field: bool is_city_investor_visible = 1;
     */
    isCityInvestorVisible: boolean;

    /**
     * @generated from field: string city_name = 2;
     */
    cityName: string;

    /**
     * @generated from field: int64 investor_city_count = 3;
     */
    investorCityCount: bigint;
  };

/**
 * Describes the message com.stablemoney.api.identity.CityDataResponse.
 * Use `create(CityDataResponseSchema)` to create a new message.
 */
export const CityDataResponseSchema: GenMessage<CityDataResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 16);

/**
 * @generated from message com.stablemoney.api.identity.PanDetailsResponse
 */
export type PanDetailsResponse =
  Message<"com.stablemoney.api.identity.PanDetailsResponse"> & {
    /**
     * @generated from field: string pan_number = 1;
     */
    panNumber: string;

    /**
     * @generated from field: string dob = 2;
     */
    dob: string;

    /**
     * @generated from field: bool is_pan_available = 3;
     */
    isPanAvailable: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.PanDetailsResponse.
 * Use `create(PanDetailsResponseSchema)` to create a new message.
 */
export const PanDetailsResponseSchema: GenMessage<PanDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 17);

/**
 * @generated from message com.stablemoney.api.identity.UserDetailsResponse
 */
export type UserDetailsResponse =
  Message<"com.stablemoney.api.identity.UserDetailsResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: optional string first_name = 2;
     */
    firstName?: string;

    /**
     * @generated from field: optional string last_name = 3;
     */
    lastName?: string;

    /**
     * @generated from field: optional string profile_image_url = 4;
     */
    profileImageUrl?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserDetailsResponse.
 * Use `create(UserDetailsResponseSchema)` to create a new message.
 */
export const UserDetailsResponseSchema: GenMessage<UserDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Profile, 18);

/**
 * @generated from enum com.stablemoney.api.identity.IncomeRange
 */
export enum IncomeRange {
  /**
   * @generated from enum value: UNKNOWN_INCOME_RANGE = 0;
   */
  UNKNOWN_INCOME_RANGE = 0,

  /**
   * @generated from enum value: LESS_THAN_1L = 1;
   */
  LESS_THAN_1L = 1,

  /**
   * @generated from enum value: BETWEEN_1L_AND_5L = 2;
   */
  BETWEEN_1L_AND_5L = 2,

  /**
   * @generated from enum value: BETWEEN_5L_AND_10L = 3;
   */
  BETWEEN_5L_AND_10L = 3,

  /**
   * @generated from enum value: BETWEEN_10L_AND_25L = 4;
   */
  BETWEEN_10L_AND_25L = 4,

  /**
   * @generated from enum value: BETWEEN_25L_AND_ABOVE = 5;
   */
  BETWEEN_25L_AND_ABOVE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.IncomeRange.
 */
export const IncomeRangeSchema: GenEnum<IncomeRange> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Profile, 0);

/**
 * @generated from enum com.stablemoney.api.identity.EmploymentType
 */
export enum EmploymentType {
  /**
   * @generated from enum value: UNKNOWN_EMPLOYMENT_TYPE = 0;
   */
  UNKNOWN_EMPLOYMENT_TYPE = 0,

  /**
   * @generated from enum value: PRIVATE_SECTOR_SERVICE = 1;
   */
  PRIVATE_SECTOR_SERVICE = 1,

  /**
   * @generated from enum value: PUBLIC_SECTOR = 2;
   */
  PUBLIC_SECTOR = 2,

  /**
   * @generated from enum value: BUSINESS = 3;
   */
  BUSINESS = 3,

  /**
   * @generated from enum value: PROFESSIONAL = 4;
   */
  PROFESSIONAL = 4,

  /**
   * @generated from enum value: AGRICULTURIST = 5;
   */
  AGRICULTURIST = 5,

  /**
   * @generated from enum value: RETIRED = 6;
   */
  RETIRED = 6,

  /**
   * @generated from enum value: HOUSEWIFE = 7;
   */
  HOUSEWIFE = 7,

  /**
   * @generated from enum value: STUDENT = 8;
   */
  STUDENT = 8,

  /**
   * @generated from enum value: FOREX_DEALER = 9;
   */
  FOREX_DEALER = 9,

  /**
   * @generated from enum value: GOVERNMENT_SERVICE = 10;
   */
  GOVERNMENT_SERVICE = 10,

  /**
   * @generated from enum value: OTHERS_EMPLOYMENT_TYPE = 11;
   */
  OTHERS_EMPLOYMENT_TYPE = 11,

  /**
   * @generated from enum value: SELF_EMPLOYED = 12;
   */
  SELF_EMPLOYED = 12,
}

/**
 * Describes the enum com.stablemoney.api.identity.EmploymentType.
 */
export const EmploymentTypeSchema: GenEnum<EmploymentType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Profile, 1);

/**
 * @generated from enum com.stablemoney.api.identity.Gender
 */
export enum Gender {
  /**
   * @generated from enum value: UNKNOWN_GENDER = 0;
   */
  UNKNOWN_GENDER = 0,

  /**
   * @generated from enum value: MALE = 1;
   */
  MALE = 1,

  /**
   * @generated from enum value: FEMALE = 2;
   */
  FEMALE = 2,

  /**
   * @generated from enum value: OTHER_GENDER = 3;
   */
  OTHER_GENDER = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.Gender.
 */
export const GenderSchema: GenEnum<Gender> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Profile, 2);

/**
 * @generated from enum com.stablemoney.api.identity.MaritalStatus
 */
export enum MaritalStatus {
  /**
   * @generated from enum value: UNKNOWN_MARITAL_STATUS = 0;
   */
  UNKNOWN_MARITAL_STATUS = 0,

  /**
   * @generated from enum value: SINGLE = 1;
   */
  SINGLE = 1,

  /**
   * @generated from enum value: MARRIED = 2;
   */
  MARRIED = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.MaritalStatus.
 */
export const MaritalStatusSchema: GenEnum<MaritalStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Profile, 3);

/**
 * @generated from enum com.stablemoney.api.identity.TradingExperience
 */
export enum TradingExperience {
  /**
   * @generated from enum value: UNKNOWN_TRADING_EXPERIENCE = 0;
   */
  UNKNOWN_TRADING_EXPERIENCE = 0,

  /**
   * @generated from enum value: LESS_THAN_1_MONTH = 1;
   */
  LESS_THAN_1_MONTH = 1,

  /**
   * @generated from enum value: BETWEEN_1_MONTH_AND_6_MONTH = 2;
   */
  BETWEEN_1_MONTH_AND_6_MONTH = 2,

  /**
   * @generated from enum value: BETWEEN_6_MONTH_AND_1_YEAR = 3;
   */
  BETWEEN_6_MONTH_AND_1_YEAR = 3,

  /**
   * @generated from enum value: BETWEEN_1_YEAR_AND_ABOVE = 4;
   */
  BETWEEN_1_YEAR_AND_ABOVE = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.TradingExperience.
 */
export const TradingExperienceSchema: GenEnum<TradingExperience> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Profile, 4);
