// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Auth.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { UserDevice } from "./Device_pb.js";
import { file_public_models_identity_Device } from "./Device_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Auth.proto.
 */
export const file_public_models_identity_Auth: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiFwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0F1dGgucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkingMKE0luaXRpYXRlQXV0aFJlcXVlc3QSOQoJYXV0aF90eXBlGAEgASgOMiYuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5BdXRoVHlwZRI9Cgt1c2VyX2RldmljZRgCIAEoCzIoLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuVXNlckRldmljZRIPCgVlbWFpbBgDIAEoCUgAEk4KE2FwcGxlX2xvZ2luX3JlcXVlc3QYBCABKAsyLy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkFwcGxlTG9naW5SZXF1ZXN0SAASUAoUZ29vZ2xlX2xvZ2luX3JlcXVlc3QYBSABKAsyMC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lkdvb2dsZUxvZ2luUmVxdWVzdEgAElAKFG1vYmlsZV9sb2dpbl9yZXF1ZXN0GAYgASgLMjAuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Nb2JpbGVMb2dpblJlcXVlc3RIAEIICgZyZXN1bHQiPQoRQXBwbGVMb2dpblJlcXVlc3QSGgoSYXV0aG9yaXNhdGlvbl9jb2RlGAEgASgJEgwKBG5hbWUYAiABKAkiJgoSR29vZ2xlTG9naW5SZXF1ZXN0EhAKCGlkX3Rva2VuGAEgASgJIssBChRJbml0aWF0ZUF1dGhSZXNwb25zZRIPCgd1c2VyX2lkGAEgASgJEkMKDW90cF9jaGFsbGVuZ2UYAiABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk9UUENoYWxsZW5nZUgAElMKFWF1dGhlbnRpY2F0aW9uX3Jlc3VsdBgDIAEoCzIyLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQXV0aGVudGljYXRpb25SZXN1bHRIAEIICgZyZXN1bHQiZAoUQXV0aGVudGljYXRpb25SZXN1bHQSDQoFdG9rZW4YASABKAkSFQoNcmVmcmVzaF90b2tlbhgCIAEoCRISCgp0b2tlbl90eXBlGAMgASgJEhIKCmV4cGlyZXNfaW4YBCABKAkihAEKHlJlc3BvbmRUb0F1dGhDaGFsbGVuZ2VSZXNwb25zZRIPCgd1c2VyX2lkGAEgASgJElEKFWF1dGhlbnRpY2F0aW9uX3Jlc3VsdBgCIAEoCzIyLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQXV0aGVudGljYXRpb25SZXN1bHQiNAoMT1RQQ2hhbGxlbmdlEhQKDGNoYWxsZW5nZV9pZBgBIAEoCRIOCgZleHBpcnkYAiABKAMiVgodUmVzcG9uZFRvQXV0aENoYWxsZW5nZVJlcXVlc3QSDwoHdXNlcl9pZBgBIAEoCRIUCgxjaGFsbGVuZ2VfaWQYAiABKAkSDgoGYW5zd2VyGAMgASgJIiQKE1JlZnJlc2hUb2tlblJlcXVlc3QSDQoFdG9rZW4YASABKAkiegoUUmVmcmVzaFRva2VuUmVzcG9uc2USDwoHdXNlcl9pZBgBIAEoCRJRChVhdXRoZW50aWNhdGlvbl9yZXN1bHQYAiABKAsyMi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkF1dGhlbnRpY2F0aW9uUmVzdWx0ImwKEk1vYmlsZUxvZ2luUmVxdWVzdBIOCgZtb2JpbGUYASABKAkSFAoMY291bnRyeV9jb2RlGAIgASgJEhgKEGVuY3J5cHRlZF9tb2JpbGUYAyABKAkSFgoOZW5jcnlwdGlvbl9rZXkYBCABKAki4QIKFUluaXRpYXRlVmVyaWZ5UmVxdWVzdBI5CglhdXRoX3R5cGUYASABKA4yJi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkF1dGhUeXBlEg8KBWVtYWlsGAIgASgJSAASTgoTYXBwbGVfbG9naW5fcmVxdWVzdBgDIAEoCzIvLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQXBwbGVMb2dpblJlcXVlc3RIABJQChRnb29nbGVfbG9naW5fcmVxdWVzdBgEIAEoCzIwLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuR29vZ2xlTG9naW5SZXF1ZXN0SAASUAoUbW9iaWxlX2xvZ2luX3JlcXVlc3QYBSABKAsyMC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk1vYmlsZUxvZ2luUmVxdWVzdEgAQggKBnJlc3VsdCKEAQoWSW5pdGlhdGVWZXJpZnlSZXNwb25zZRIbChFpc19lbWFpbF92ZXJpZmllZBgBIAEoCEgAEkMKDW90cF9jaGFsbGVuZ2UYAiABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk9UUENoYWxsZW5nZUgAQggKBnJlc3VsdCJHCh9SZXNwb25kVG9WZXJpZnlDaGFsbGVuZ2VSZXF1ZXN0EhQKDGNoYWxsZW5nZV9pZBgBIAEoCRIOCgZhbnN3ZXIYAiABKAkiIgogUmVzcG9uZFRvVmVyaWZ5Q2hhbGxlbmdlUmVzcG9uc2UiLwobWm9ob1Rva2VuR2VuZXJhdGlvblJlc3BvbnNlEhAKCGp3dFRva2VuGAEgASgJKlgKCEF1dGhUeXBlEhYKEkxPR0lOX1RZUEVfVU5LTk9XThAAEg0KCUVNQUlMX09UUBABEgoKBkdPT0dMRRACEgkKBUFQUExFEAMSDgoKTU9CSUxFX09UUBAEKj4KC0F1dGhQcm9jZXNzEhgKFEFVVEhfUFJPQ0VTU19VTktOT1dOEAASCQoFTE9HSU4QARIKCgZWRVJJRlkQAkIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
    [file_public_models_identity_Device],
  );

/**
 * @generated from message com.stablemoney.api.identity.InitiateAuthRequest
 */
export type InitiateAuthRequest =
  Message<"com.stablemoney.api.identity.InitiateAuthRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.AuthType auth_type = 1;
     */
    authType: AuthType;

    /**
     * @generated from field: com.stablemoney.api.identity.UserDevice user_device = 2;
     */
    userDevice?: UserDevice;

    /**
     * @generated from oneof com.stablemoney.api.identity.InitiateAuthRequest.result
     */
    result:
      | {
          /**
           * @generated from field: string email = 3;
           */
          value: string;
          case: "email";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.AppleLoginRequest apple_login_request = 4;
           */
          value: AppleLoginRequest;
          case: "appleLoginRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.GoogleLoginRequest google_login_request = 5;
           */
          value: GoogleLoginRequest;
          case: "googleLoginRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.MobileLoginRequest mobile_login_request = 6;
           */
          value: MobileLoginRequest;
          case: "mobileLoginRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateAuthRequest.
 * Use `create(InitiateAuthRequestSchema)` to create a new message.
 */
export const InitiateAuthRequestSchema: GenMessage<InitiateAuthRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 0);

/**
 * @generated from message com.stablemoney.api.identity.AppleLoginRequest
 */
export type AppleLoginRequest =
  Message<"com.stablemoney.api.identity.AppleLoginRequest"> & {
    /**
     * @generated from field: string authorisation_code = 1;
     */
    authorisationCode: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AppleLoginRequest.
 * Use `create(AppleLoginRequestSchema)` to create a new message.
 */
export const AppleLoginRequestSchema: GenMessage<AppleLoginRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 1);

/**
 * @generated from message com.stablemoney.api.identity.GoogleLoginRequest
 */
export type GoogleLoginRequest =
  Message<"com.stablemoney.api.identity.GoogleLoginRequest"> & {
    /**
     * @generated from field: string id_token = 1;
     */
    idToken: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoogleLoginRequest.
 * Use `create(GoogleLoginRequestSchema)` to create a new message.
 */
export const GoogleLoginRequestSchema: GenMessage<GoogleLoginRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 2);

/**
 * @generated from message com.stablemoney.api.identity.InitiateAuthResponse
 */
export type InitiateAuthResponse =
  Message<"com.stablemoney.api.identity.InitiateAuthResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from oneof com.stablemoney.api.identity.InitiateAuthResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.OTPChallenge otp_challenge = 2;
           */
          value: OTPChallenge;
          case: "otpChallenge";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.AuthenticationResult authentication_result = 3;
           */
          value: AuthenticationResult;
          case: "authenticationResult";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateAuthResponse.
 * Use `create(InitiateAuthResponseSchema)` to create a new message.
 */
export const InitiateAuthResponseSchema: GenMessage<InitiateAuthResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 3);

/**
 * @generated from message com.stablemoney.api.identity.AuthenticationResult
 */
export type AuthenticationResult =
  Message<"com.stablemoney.api.identity.AuthenticationResult"> & {
    /**
     * @generated from field: string token = 1;
     */
    token: string;

    /**
     * @generated from field: string refresh_token = 2;
     */
    refreshToken: string;

    /**
     * @generated from field: string token_type = 3;
     */
    tokenType: string;

    /**
     * @generated from field: string expires_in = 4;
     */
    expiresIn: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AuthenticationResult.
 * Use `create(AuthenticationResultSchema)` to create a new message.
 */
export const AuthenticationResultSchema: GenMessage<AuthenticationResult> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 4);

/**
 * @generated from message com.stablemoney.api.identity.RespondToAuthChallengeResponse
 */
export type RespondToAuthChallengeResponse =
  Message<"com.stablemoney.api.identity.RespondToAuthChallengeResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.AuthenticationResult authentication_result = 2;
     */
    authenticationResult?: AuthenticationResult;
  };

/**
 * Describes the message com.stablemoney.api.identity.RespondToAuthChallengeResponse.
 * Use `create(RespondToAuthChallengeResponseSchema)` to create a new message.
 */
export const RespondToAuthChallengeResponseSchema: GenMessage<RespondToAuthChallengeResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 5);

/**
 * @generated from message com.stablemoney.api.identity.OTPChallenge
 */
export type OTPChallenge =
  Message<"com.stablemoney.api.identity.OTPChallenge"> & {
    /**
     * @generated from field: string challenge_id = 1;
     */
    challengeId: string;

    /**
     * @generated from field: int64 expiry = 2;
     */
    expiry: bigint;
  };

/**
 * Describes the message com.stablemoney.api.identity.OTPChallenge.
 * Use `create(OTPChallengeSchema)` to create a new message.
 */
export const OTPChallengeSchema: GenMessage<OTPChallenge> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 6);

/**
 * @generated from message com.stablemoney.api.identity.RespondToAuthChallengeRequest
 */
export type RespondToAuthChallengeRequest =
  Message<"com.stablemoney.api.identity.RespondToAuthChallengeRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: string challenge_id = 2;
     */
    challengeId: string;

    /**
     * @generated from field: string answer = 3;
     */
    answer: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RespondToAuthChallengeRequest.
 * Use `create(RespondToAuthChallengeRequestSchema)` to create a new message.
 */
export const RespondToAuthChallengeRequestSchema: GenMessage<RespondToAuthChallengeRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 7);

/**
 * @generated from message com.stablemoney.api.identity.RefreshTokenRequest
 */
export type RefreshTokenRequest =
  Message<"com.stablemoney.api.identity.RefreshTokenRequest"> & {
    /**
     * @generated from field: string token = 1;
     */
    token: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RefreshTokenRequest.
 * Use `create(RefreshTokenRequestSchema)` to create a new message.
 */
export const RefreshTokenRequestSchema: GenMessage<RefreshTokenRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 8);

/**
 * @generated from message com.stablemoney.api.identity.RefreshTokenResponse
 */
export type RefreshTokenResponse =
  Message<"com.stablemoney.api.identity.RefreshTokenResponse"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.AuthenticationResult authentication_result = 2;
     */
    authenticationResult?: AuthenticationResult;
  };

/**
 * Describes the message com.stablemoney.api.identity.RefreshTokenResponse.
 * Use `create(RefreshTokenResponseSchema)` to create a new message.
 */
export const RefreshTokenResponseSchema: GenMessage<RefreshTokenResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 9);

/**
 * @generated from message com.stablemoney.api.identity.MobileLoginRequest
 */
export type MobileLoginRequest =
  Message<"com.stablemoney.api.identity.MobileLoginRequest"> & {
    /**
     * @generated from field: string mobile = 1;
     */
    mobile: string;

    /**
     * @generated from field: string country_code = 2;
     */
    countryCode: string;

    /**
     * @generated from field: string encrypted_mobile = 3;
     */
    encryptedMobile: string;

    /**
     * @generated from field: string encryption_key = 4;
     */
    encryptionKey: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.MobileLoginRequest.
 * Use `create(MobileLoginRequestSchema)` to create a new message.
 */
export const MobileLoginRequestSchema: GenMessage<MobileLoginRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 10);

/**
 * @generated from message com.stablemoney.api.identity.InitiateVerifyRequest
 */
export type InitiateVerifyRequest =
  Message<"com.stablemoney.api.identity.InitiateVerifyRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.AuthType auth_type = 1;
     */
    authType: AuthType;

    /**
     * @generated from oneof com.stablemoney.api.identity.InitiateVerifyRequest.result
     */
    result:
      | {
          /**
           * @generated from field: string email = 2;
           */
          value: string;
          case: "email";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.AppleLoginRequest apple_login_request = 3;
           */
          value: AppleLoginRequest;
          case: "appleLoginRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.GoogleLoginRequest google_login_request = 4;
           */
          value: GoogleLoginRequest;
          case: "googleLoginRequest";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.MobileLoginRequest mobile_login_request = 5;
           */
          value: MobileLoginRequest;
          case: "mobileLoginRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateVerifyRequest.
 * Use `create(InitiateVerifyRequestSchema)` to create a new message.
 */
export const InitiateVerifyRequestSchema: GenMessage<InitiateVerifyRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 11);

/**
 * @generated from message com.stablemoney.api.identity.InitiateVerifyResponse
 */
export type InitiateVerifyResponse =
  Message<"com.stablemoney.api.identity.InitiateVerifyResponse"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.InitiateVerifyResponse.result
     */
    result:
      | {
          /**
           * @generated from field: bool is_email_verified = 1;
           */
          value: boolean;
          case: "isEmailVerified";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.OTPChallenge otp_challenge = 2;
           */
          value: OTPChallenge;
          case: "otpChallenge";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateVerifyResponse.
 * Use `create(InitiateVerifyResponseSchema)` to create a new message.
 */
export const InitiateVerifyResponseSchema: GenMessage<InitiateVerifyResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 12);

/**
 * @generated from message com.stablemoney.api.identity.RespondToVerifyChallengeRequest
 */
export type RespondToVerifyChallengeRequest =
  Message<"com.stablemoney.api.identity.RespondToVerifyChallengeRequest"> & {
    /**
     * @generated from field: string challenge_id = 1;
     */
    challengeId: string;

    /**
     * @generated from field: string answer = 2;
     */
    answer: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RespondToVerifyChallengeRequest.
 * Use `create(RespondToVerifyChallengeRequestSchema)` to create a new message.
 */
export const RespondToVerifyChallengeRequestSchema: GenMessage<RespondToVerifyChallengeRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 13);

/**
 * @generated from message com.stablemoney.api.identity.RespondToVerifyChallengeResponse
 */
export type RespondToVerifyChallengeResponse =
  Message<"com.stablemoney.api.identity.RespondToVerifyChallengeResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.RespondToVerifyChallengeResponse.
 * Use `create(RespondToVerifyChallengeResponseSchema)` to create a new message.
 */
export const RespondToVerifyChallengeResponseSchema: GenMessage<RespondToVerifyChallengeResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 14);

/**
 * @generated from message com.stablemoney.api.identity.ZohoTokenGenerationResponse
 */
export type ZohoTokenGenerationResponse =
  Message<"com.stablemoney.api.identity.ZohoTokenGenerationResponse"> & {
    /**
     * @generated from field: string jwtToken = 1;
     */
    jwtToken: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.ZohoTokenGenerationResponse.
 * Use `create(ZohoTokenGenerationResponseSchema)` to create a new message.
 */
export const ZohoTokenGenerationResponseSchema: GenMessage<ZohoTokenGenerationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Auth, 15);

/**
 * @generated from enum com.stablemoney.api.identity.AuthType
 */
export enum AuthType {
  /**
   * @generated from enum value: LOGIN_TYPE_UNKNOWN = 0;
   */
  LOGIN_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: EMAIL_OTP = 1;
   */
  EMAIL_OTP = 1,

  /**
   * @generated from enum value: GOOGLE = 2;
   */
  GOOGLE = 2,

  /**
   * @generated from enum value: APPLE = 3;
   */
  APPLE = 3,

  /**
   * @generated from enum value: MOBILE_OTP = 4;
   */
  MOBILE_OTP = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.AuthType.
 */
export const AuthTypeSchema: GenEnum<AuthType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Auth, 0);

/**
 * @generated from enum com.stablemoney.api.identity.AuthProcess
 */
export enum AuthProcess {
  /**
   * @generated from enum value: AUTH_PROCESS_UNKNOWN = 0;
   */
  AUTH_PROCESS_UNKNOWN = 0,

  /**
   * @generated from enum value: LOGIN = 1;
   */
  LOGIN = 1,

  /**
   * @generated from enum value: VERIFY = 2;
   */
  VERIFY = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.AuthProcess.
 */
export const AuthProcessSchema: GenEnum<AuthProcess> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Auth, 1);
