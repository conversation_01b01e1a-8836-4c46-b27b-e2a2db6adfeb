// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/UserRating.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/UserRating.proto.
 */
export const file_public_models_identity_UserRating: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CidwdWJsaWMvbW9kZWxzL2lkZW50aXR5L1VzZXJSYXRpbmcucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkivwEKEVBvc3RSYXRpbmdSZXF1ZXN0EhMKBnJhdGluZxgBIAEoAUgAiAEBEhMKBnJldmlldxgCIAEoCUgBiAEBEh4KEWlzX3Jldmlld19za2lwcGVkGAMgASgISAKIAQESDwoHdXNlcl9pZBgEIAEoCRIjChtpc19nb29nbGVfcGxheV9yYXRpbmdfc2hvd24YBiABKAhCCQoHX3JhdGluZ0IJCgdfcmV2aWV3QhQKEl9pc19yZXZpZXdfc2tpcHBlZCIUChJQb3N0UmF0aW5nUmVzcG9uc2UiJAoRVXNlclJhdGluZ1JlcXVlc3QSDwoHdXNlcl9pZBgBIAEoCSKJAQoSVXNlclJhdGluZ1Jlc3BvbnNlEg4KBnJhdGluZxgBIAEoARIOCgZyZXZpZXcYAiABKAkSGQoRaXNfcmV2aWV3X3NraXBwZWQYAyABKAgSHAoUaW5fYXBwX3Jldmlld19jb2hvcnQYBCABKAkSGgoSc2hvd19pbl9hcHBfcmV2aWV3GAYgASgIIoABChRFbWFpbEZlZWRiYWNrUmVxdWVzdBITCgtjdXN0b21lcl9pZBgBIAEoCRISCgpib29raW5nX2lkGAIgASgJEg4KBnJhdGluZxgDIAEoBRIeChZnb29kX2V4cGVyaWVuY2Vfb3B0aW9uGAQgAygJEg8KB21lc3NhZ2UYBSABKAkiJwoVRW1haWxGZWVkYmFja1Jlc3BvbnNlEg4KBnN0YXR1cxgBIAEoCCpmCgxSYXRpbmdTb3VyY2USGQoVVU5LTk9XTl9SQVRJTkdfU09VUkNFEAASGAoUSU5fQVBQX1JBVElOR19TT1VSQ0UQARIhCh1URF9CT09LRURfRU1BSUxfUkFUSU5HX1NPVVJDRRACQiAKHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHlQAWIGcHJvdG8z",
  );

/**
 * @generated from message com.stablemoney.api.identity.PostRatingRequest
 */
export type PostRatingRequest =
  Message<"com.stablemoney.api.identity.PostRatingRequest"> & {
    /**
     * @generated from field: optional double rating = 1;
     */
    rating?: number;

    /**
     * @generated from field: optional string review = 2;
     */
    review?: string;

    /**
     * @generated from field: optional bool is_review_skipped = 3;
     */
    isReviewSkipped?: boolean;

    /**
     * @generated from field: string user_id = 4;
     */
    userId: string;

    /**
     * @generated from field: bool is_google_play_rating_shown = 6;
     */
    isGooglePlayRatingShown: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.PostRatingRequest.
 * Use `create(PostRatingRequestSchema)` to create a new message.
 */
export const PostRatingRequestSchema: GenMessage<PostRatingRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserRating, 0);

/**
 * @generated from message com.stablemoney.api.identity.PostRatingResponse
 */
export type PostRatingResponse =
  Message<"com.stablemoney.api.identity.PostRatingResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.PostRatingResponse.
 * Use `create(PostRatingResponseSchema)` to create a new message.
 */
export const PostRatingResponseSchema: GenMessage<PostRatingResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserRating, 1);

/**
 * @generated from message com.stablemoney.api.identity.UserRatingRequest
 */
export type UserRatingRequest =
  Message<"com.stablemoney.api.identity.UserRatingRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserRatingRequest.
 * Use `create(UserRatingRequestSchema)` to create a new message.
 */
export const UserRatingRequestSchema: GenMessage<UserRatingRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserRating, 2);

/**
 * @generated from message com.stablemoney.api.identity.UserRatingResponse
 */
export type UserRatingResponse =
  Message<"com.stablemoney.api.identity.UserRatingResponse"> & {
    /**
     * @generated from field: double rating = 1;
     */
    rating: number;

    /**
     * @generated from field: string review = 2;
     */
    review: string;

    /**
     * @generated from field: bool is_review_skipped = 3;
     */
    isReviewSkipped: boolean;

    /**
     * @generated from field: string in_app_review_cohort = 4;
     */
    inAppReviewCohort: string;

    /**
     * @generated from field: bool show_in_app_review = 6;
     */
    showInAppReview: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserRatingResponse.
 * Use `create(UserRatingResponseSchema)` to create a new message.
 */
export const UserRatingResponseSchema: GenMessage<UserRatingResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserRating, 3);

/**
 * @generated from message com.stablemoney.api.identity.EmailFeedbackRequest
 */
export type EmailFeedbackRequest =
  Message<"com.stablemoney.api.identity.EmailFeedbackRequest"> & {
    /**
     * @generated from field: string customer_id = 1;
     */
    customerId: string;

    /**
     * @generated from field: string booking_id = 2;
     */
    bookingId: string;

    /**
     * @generated from field: int32 rating = 3;
     */
    rating: number;

    /**
     * @generated from field: repeated string good_experience_option = 4;
     */
    goodExperienceOption: string[];

    /**
     * @generated from field: string message = 5;
     */
    message: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmailFeedbackRequest.
 * Use `create(EmailFeedbackRequestSchema)` to create a new message.
 */
export const EmailFeedbackRequestSchema: GenMessage<EmailFeedbackRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserRating, 4);

/**
 * @generated from message com.stablemoney.api.identity.EmailFeedbackResponse
 */
export type EmailFeedbackResponse =
  Message<"com.stablemoney.api.identity.EmailFeedbackResponse"> & {
    /**
     * @generated from field: bool status = 1;
     */
    status: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmailFeedbackResponse.
 * Use `create(EmailFeedbackResponseSchema)` to create a new message.
 */
export const EmailFeedbackResponseSchema: GenMessage<EmailFeedbackResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_UserRating, 5);

/**
 * @generated from enum com.stablemoney.api.identity.RatingSource
 */
export enum RatingSource {
  /**
   * @generated from enum value: UNKNOWN_RATING_SOURCE = 0;
   */
  UNKNOWN_RATING_SOURCE = 0,

  /**
   * @generated from enum value: IN_APP_RATING_SOURCE = 1;
   */
  IN_APP_RATING_SOURCE = 1,

  /**
   * @generated from enum value: TD_BOOKED_EMAIL_RATING_SOURCE = 2;
   */
  TD_BOOKED_EMAIL_RATING_SOURCE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.RatingSource.
 */
export const RatingSourceSchema: GenEnum<RatingSource> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_UserRating, 0);
