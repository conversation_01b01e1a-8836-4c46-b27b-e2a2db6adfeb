// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Document.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/identity/Document.proto.
 */
export const file_public_models_identity_Document: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiVwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0RvY3VtZW50LnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IicKEVNoYXJlVGV4dFJlc3BvbnNlEhIKCnNoYXJlX3RleHQYASABKAkiLQoUUmVmZXJyYWxMaW5rUmVzcG9uc2USFQoNcmVmZXJyYWxfbGluaxgBIAEoCUIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from message com.stablemoney.api.identity.ShareTextResponse
 */
export type ShareTextResponse =
  Message<"com.stablemoney.api.identity.ShareTextResponse"> & {
    /**
     * @generated from field: string share_text = 1;
     */
    shareText: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.ShareTextResponse.
 * Use `create(ShareTextResponseSchema)` to create a new message.
 */
export const ShareTextResponseSchema: GenMessage<ShareTextResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Document, 0);

/**
 * @generated from message com.stablemoney.api.identity.ReferralLinkResponse
 */
export type ReferralLinkResponse =
  Message<"com.stablemoney.api.identity.ReferralLinkResponse"> & {
    /**
     * @generated from field: string referral_link = 1;
     */
    referralLink: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.ReferralLinkResponse.
 * Use `create(ReferralLinkResponseSchema)` to create a new message.
 */
export const ReferralLinkResponseSchema: GenMessage<ReferralLinkResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_identity_Document, 1);
