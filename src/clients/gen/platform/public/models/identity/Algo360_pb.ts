// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/identity/Algo360.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv2";

/**
 * Describes the file public/models/identity/Algo360.proto.
 */
export const file_public_models_identity_Algo360: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiRwdWJsaWMvbW9kZWxzL2lkZW50aXR5L0FsZ28zNjAucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkq0QEKDkFsZ29TdGF0dXNUeXBlEhUKEURBVEFfSU5HRVNURURfTkVXEAASHQoZREFUQV9JTkdFU1RFRF9JTkNSRU1FTlRBTBABEhwKGEFMR08zNjBfUFJPQ0VTU19GSU5JU0hFRBACEigKJEFMR08zNjBfUFJJT1JJVFlfVkFSSUFCTEVTX1BST0NFU1NFRBADEiMKH0FMR08zNjBfR0FQX1ZBUklBQkxFU19QUk9DRVNTRUQQBBIcChhVTktOT1dOX0FMR09fU1RBVFVTX1RZUEUQBUIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5UAFiBnByb3RvMw",
  );

/**
 * @generated from enum com.stablemoney.api.identity.AlgoStatusType
 */
export enum AlgoStatusType {
  /**
   * @generated from enum value: DATA_INGESTED_NEW = 0;
   */
  DATA_INGESTED_NEW = 0,

  /**
   * @generated from enum value: DATA_INGESTED_INCREMENTAL = 1;
   */
  DATA_INGESTED_INCREMENTAL = 1,

  /**
   * @generated from enum value: ALGO360_PROCESS_FINISHED = 2;
   */
  ALGO360_PROCESS_FINISHED = 2,

  /**
   * @generated from enum value: ALGO360_PRIORITY_VARIABLES_PROCESSED = 3;
   */
  ALGO360_PRIORITY_VARIABLES_PROCESSED = 3,

  /**
   * @generated from enum value: ALGO360_GAP_VARIABLES_PROCESSED = 4;
   */
  ALGO360_GAP_VARIABLES_PROCESSED = 4,

  /**
   * @generated from enum value: UNKNOWN_ALGO_STATUS_TYPE = 5;
   */
  UNKNOWN_ALGO_STATUS_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.AlgoStatusType.
 */
export const AlgoStatusTypeSchema: GenEnum<AlgoStatusType> =
  /*@__PURE__*/
  enumDesc(file_public_models_identity_Algo360, 0);
