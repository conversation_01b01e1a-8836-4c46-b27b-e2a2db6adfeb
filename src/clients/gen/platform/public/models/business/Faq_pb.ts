// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/Faq.proto (package com.stablemoney.api.business.faq, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/Faq.proto.
 */
export const file_public_models_business_Faq: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.business.faq.BankFaqResponse
 */
export type BankFaqResponse =
  Message<"com.stablemoney.api.business.faq.BankFaqResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 1;
     */
    bankFaq: Faq[];
  };

/**
 * Describes the message com.stablemoney.api.business.faq.BankFaqResponse.
 * Use `create(BankFaqResponseSchema)` to create a new message.
 */
export const BankFaqResponseSchema: GenMessage<BankFaqResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 0);

/**
 * @generated from message com.stablemoney.api.business.faq.Faq
 */
export type Faq = Message<"com.stablemoney.api.business.faq.Faq"> & {
  /**
   * @generated from field: string question = 1;
   */
  question: string;

  /**
   * @generated from field: string answer = 2;
   */
  answer: string;

  /**
   * @generated from field: string html_answer = 3;
   */
  htmlAnswer: string;

  /**
   * @generated from field: repeated string bank_faq_category_list = 4;
   */
  bankFaqCategoryList: string[];

  /**
   * @generated from field: string question_tag = 5;
   */
  questionTag: string;

  /**
   * @generated from field: optional string faq_id = 6;
   */
  faqId?: string;

  /**
   * @generated from field: int32 min_version_number = 7;
   */
  minVersionNumber: number;

  /**
   * @generated from field: int32 max_version_number = 8;
   */
  maxVersionNumber: number;

  /**
   * @generated from field: optional string user_name = 9;
   */
  userName?: string;

  /**
   * @generated from field: optional string user_location = 10;
   */
  userLocation?: string;

  /**
   * @generated from field: optional string user_profile_url = 11;
   */
  userProfileUrl?: string;

  /**
   * @generated from field: optional int32 upvotes = 12;
   */
  upvotes?: number;

  /**
   * @generated from field: optional int32 downvotes = 13;
   */
  downvotes?: number;
};

/**
 * Describes the message com.stablemoney.api.business.faq.Faq.
 * Use `create(FaqSchema)` to create a new message.
 */
export const FaqSchema: GenMessage<Faq> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 1);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqResponse
 */
export type SupportFaqResponse =
  Message<"com.stablemoney.api.business.faq.SupportFaqResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 1;
     */
    bankFaq: Faq[];
  };

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqResponse.
 * Use `create(SupportFaqResponseSchema)` to create a new message.
 */
export const SupportFaqResponseSchema: GenMessage<SupportFaqResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 2);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqV2
 */
export type SupportFaqV2 =
  Message<"com.stablemoney.api.business.faq.SupportFaqV2"> & {
    /**
     * @generated from field: com.stablemoney.api.business.faq.Faq faq = 1;
     */
    faq?: Faq;

    /**
     * @generated from field: string support_faq_icon_url = 3;
     */
    supportFaqIconUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqV2.
 * Use `create(SupportFaqV2Schema)` to create a new message.
 */
export const SupportFaqV2Schema: GenMessage<SupportFaqV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 3);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqResponseV2
 */
export type SupportFaqResponseV2 =
  Message<"com.stablemoney.api.business.faq.SupportFaqResponseV2"> & {
    /**
     * @generated from field: string category_id = 1;
     */
    categoryId: string;

    /**
     * @generated from field: string category_name = 2;
     */
    categoryName: string;

    /**
     * @generated from field: string category_icon_url = 3;
     */
    categoryIconUrl: string;

    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.SupportFaqV2 faqs = 4;
     */
    faqs: SupportFaqV2[];

    /**
     * @generated from field: string category_description = 5;
     */
    categoryDescription: string;
  };

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqResponseV2.
 * Use `create(SupportFaqResponseV2Schema)` to create a new message.
 */
export const SupportFaqResponseV2Schema: GenMessage<SupportFaqResponseV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 4);

/**
 * @generated from message com.stablemoney.api.business.faq.SupportFaqCategoryResponseV2
 */
export type SupportFaqCategoryResponseV2 =
  Message<"com.stablemoney.api.business.faq.SupportFaqCategoryResponseV2"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.SupportFaqResponseV2 category_support_faq = 1;
     */
    categorySupportFaq: SupportFaqResponseV2[];

    /**
     * @generated from field: string top_category_id = 2;
     */
    topCategoryId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.faq.SupportFaqCategoryResponseV2.
 * Use `create(SupportFaqCategoryResponseV2Schema)` to create a new message.
 */
export const SupportFaqCategoryResponseV2Schema: GenMessage<SupportFaqCategoryResponseV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 5);

/**
 * @generated from message com.stablemoney.api.business.faq.AskQuestionRequest
 */
export type AskQuestionRequest =
  Message<"com.stablemoney.api.business.faq.AskQuestionRequest"> & {
    /**
     * @generated from field: string question = 1;
     */
    question: string;
  };

/**
 * Describes the message com.stablemoney.api.business.faq.AskQuestionRequest.
 * Use `create(AskQuestionRequestSchema)` to create a new message.
 */
export const AskQuestionRequestSchema: GenMessage<AskQuestionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 6);

/**
 * @generated from message com.stablemoney.api.business.faq.GetFaqsRequest
 */
export type GetFaqsRequest =
  Message<"com.stablemoney.api.business.faq.GetFaqsRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.business.faq.BusinessUnit business_unit = 1;
     */
    businessUnit: BusinessUnit;

    /**
     * @generated from field: string namespace = 2;
     */
    namespace: string;

    /**
     * @generated from field: string identifier = 3;
     */
    identifier: string;
  };

/**
 * Describes the message com.stablemoney.api.business.faq.GetFaqsRequest.
 * Use `create(GetFaqsRequestSchema)` to create a new message.
 */
export const GetFaqsRequestSchema: GenMessage<GetFaqsRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 7);

/**
 * @generated from message com.stablemoney.api.business.faq.GetFaqsResponse
 */
export type GetFaqsResponse =
  Message<"com.stablemoney.api.business.faq.GetFaqsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq faqs = 1;
     */
    faqs: Faq[];
  };

/**
 * Describes the message com.stablemoney.api.business.faq.GetFaqsResponse.
 * Use `create(GetFaqsResponseSchema)` to create a new message.
 */
export const GetFaqsResponseSchema: GenMessage<GetFaqsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Faq, 8);

/**
 * @generated from enum com.stablemoney.api.business.faq.FaqType
 */
export enum FaqType {
  /**
   * @generated from enum value: UNKNOWN_FAQ_TYPE = 0;
   */
  UNKNOWN_FAQ_TYPE = 0,

  /**
   * @generated from enum value: DEFAULT_FAQ_TYPE = 1;
   */
  DEFAULT_FAQ_TYPE = 1,

  /**
   * @generated from enum value: REWARD_FAQ_TYPE = 2;
   */
  REWARD_FAQ_TYPE = 2,

  /**
   * @generated from enum value: REFERRAL_FAQ_TYPE = 3;
   */
  REFERRAL_FAQ_TYPE = 3,

  /**
   * @generated from enum value: CATEGORY_FAQ_TYPE = 4;
   */
  CATEGORY_FAQ_TYPE = 4,
}

/**
 * Describes the enum com.stablemoney.api.business.faq.FaqType.
 */
export const FaqTypeSchema: GenEnum<FaqType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Faq, 0);

/**
 * @generated from enum com.stablemoney.api.business.faq.BusinessUnit
 */
export enum BusinessUnit {
  /**
   * @generated from enum value: UNKNOWN_BUSINESS_UNIT = 0;
   */
  UNKNOWN_BUSINESS_UNIT = 0,

  /**
   * @generated from enum value: ALPHA = 1;
   */
  ALPHA = 1,

  /**
   * @generated from enum value: FINSERV = 2;
   */
  FINSERV = 2,

  /**
   * @generated from enum value: BROKING = 3;
   */
  BROKING = 3,
}

/**
 * Describes the enum com.stablemoney.api.business.faq.BusinessUnit.
 */
export const BusinessUnitSchema: GenEnum<BusinessUnit> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Faq, 1);
