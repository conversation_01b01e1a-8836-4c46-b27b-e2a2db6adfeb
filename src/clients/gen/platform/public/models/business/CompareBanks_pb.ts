// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/CompareBanks.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type {
  BankResponse,
  FixedDepositResponse,
  Tag,
} from "./Collection_pb.js";
import { file_public_models_business_Collection } from "./Collection_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/CompareBanks.proto.
 */
export const file_public_models_business_CompareBanks: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CilwdWJsaWMvbW9kZWxzL2J1c2luZXNzL0NvbXBhcmVCYW5rcy5wcm90bxIcY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eSLDAgoTUmVjb21tZW5kZWRCYW5rSXRlbRJBCg1iYW5rX3Jlc3BvbnNlGAEgASgLMiouY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5CYW5rUmVzcG9uc2USIQoZbG93ZXN0X2ZpeGVkX2RlcG9zaXRfcmF0ZRgCIAEoARIiChpoaWdoZXN0X2ZpeGVkX2RlcG9zaXRfcmF0ZRgDIAEoARJaCh5oaWdoZXN0X2ZpeGVkX2RlcG9zaXRfcmVzcG9uc2UYBCABKAsyMi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkZpeGVkRGVwb3NpdFJlc3BvbnNlEhYKDmlzX3JlY29tbWVuZGVkGAUgASgIEi4KA3RhZxgGIAEoCzIhLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuVGFnImsKF1JlY29tbWVuZGVkQmFua1Jlc3BvbnNlElAKFXJlY29tbWVuZGVkX2JhbmtfbGlzdBgBIAMoCzIxLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUmVjb21tZW5kZWRCYW5rSXRlbSLEAQoPQ29tcGFyZUJhbmtDZWxsEhYKDHN0cmluZ192YWx1ZRgCIAEoCUgAEhQKCmJvb2xfdmFsdWUYAyABKAhIABIZCgxzdHJpbmdfY29sb3IYBCABKAlIAYgBARIUCgdjYXB0aW9uGAUgASgJSAKIAQESGgoNY2FwdGlvbl9jb2xvchgGIAEoCUgDiAEBQgcKBXZhbHVlQg8KDV9zdHJpbmdfY29sb3JCCgoIX2NhcHRpb25CEAoOX2NhcHRpb25fY29sb3IiWwoPQ29tcGFyZUJhbmtJdGVtEkgKEWNvbXBhcmVfYmFua19saXN0GAEgAygLMi0uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Db21wYXJlQmFua0NlbGwikAIKE0NvbXBhcmVCYW5rUmVzcG9uc2USUAoVcmVjb21tZW5kZWRfYmFua19saXN0GAEgAygLMjEuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5SZWNvbW1lbmRlZEJhbmtJdGVtEg4KBnRpdGxlcxgCIAMoCRJNChZjb21wYXJlX2JhbmtfaXRlbV9saXN0GAMgAygLMi0uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Db21wYXJlQmFua0l0ZW0SSAoNYWRkX2JhbmtfbGlzdBgEIAMoCzIxLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUmVjb21tZW5kZWRCYW5rSXRlbSJrChVDb21wYXJlQmFua1Jlc3BvbnNlVjISUgoZY29tcGFyZV9iYW5rX2l0ZW1fbGlzdF92MhgBIAMoCzIvLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQ29tcGFyZUJhbmtJdGVtVjIiVwoRQ29tcGFyZUJhbmtJdGVtVjISEQoJYmFua19uYW1lGAEgASgJEhEKCWJhbmtfbG9nbxgCIAEoCRILCgNyb2kYAyABKAkSDwoHYmFua19pZBgEIAEoCUIgChxjb20uc3RhYmxlbW9uZXkuYXBpLmJ1c2luZXNzUAFiBnByb3RvMw",
    [file_public_models_business_Collection],
  );

/**
 * @generated from message com.stablemoney.api.identity.RecommendedBankItem
 */
export type RecommendedBankItem =
  Message<"com.stablemoney.api.identity.RecommendedBankItem"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank_response = 1;
     */
    bankResponse?: BankResponse;

    /**
     * @generated from field: double lowest_fixed_deposit_rate = 2;
     */
    lowestFixedDepositRate: number;

    /**
     * @generated from field: double highest_fixed_deposit_rate = 3;
     */
    highestFixedDepositRate: number;

    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse highest_fixed_deposit_response = 4;
     */
    highestFixedDepositResponse?: FixedDepositResponse;

    /**
     * @generated from field: bool is_recommended = 5;
     */
    isRecommended: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.Tag tag = 6;
     */
    tag?: Tag;
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendedBankItem.
 * Use `create(RecommendedBankItemSchema)` to create a new message.
 */
export const RecommendedBankItemSchema: GenMessage<RecommendedBankItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_CompareBanks, 0);

/**
 * @generated from message com.stablemoney.api.identity.RecommendedBankResponse
 */
export type RecommendedBankResponse =
  Message<"com.stablemoney.api.identity.RecommendedBankResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankItem recommended_bank_list = 1;
     */
    recommendedBankList: RecommendedBankItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendedBankResponse.
 * Use `create(RecommendedBankResponseSchema)` to create a new message.
 */
export const RecommendedBankResponseSchema: GenMessage<RecommendedBankResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_CompareBanks, 1);

/**
 * @generated from message com.stablemoney.api.identity.CompareBankCell
 */
export type CompareBankCell =
  Message<"com.stablemoney.api.identity.CompareBankCell"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.CompareBankCell.value
     */
    value:
      | {
          /**
           * @generated from field: string string_value = 2;
           */
          value: string;
          case: "stringValue";
        }
      | {
          /**
           * @generated from field: bool bool_value = 3;
           */
          value: boolean;
          case: "boolValue";
        }
      | { case: undefined; value?: undefined };

    /**
     * @generated from field: optional string string_color = 4;
     */
    stringColor?: string;

    /**
     * @generated from field: optional string caption = 5;
     */
    caption?: string;

    /**
     * @generated from field: optional string caption_color = 6;
     */
    captionColor?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.CompareBankCell.
 * Use `create(CompareBankCellSchema)` to create a new message.
 */
export const CompareBankCellSchema: GenMessage<CompareBankCell> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_CompareBanks, 2);

/**
 * @generated from message com.stablemoney.api.identity.CompareBankItem
 */
export type CompareBankItem =
  Message<"com.stablemoney.api.identity.CompareBankItem"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.CompareBankCell compare_bank_list = 1;
     */
    compareBankList: CompareBankCell[];
  };

/**
 * Describes the message com.stablemoney.api.identity.CompareBankItem.
 * Use `create(CompareBankItemSchema)` to create a new message.
 */
export const CompareBankItemSchema: GenMessage<CompareBankItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_CompareBanks, 3);

/**
 * @generated from message com.stablemoney.api.identity.CompareBankResponse
 */
export type CompareBankResponse =
  Message<"com.stablemoney.api.identity.CompareBankResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankItem recommended_bank_list = 1;
     */
    recommendedBankList: RecommendedBankItem[];

    /**
     * @generated from field: repeated string titles = 2;
     */
    titles: string[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.CompareBankItem compare_bank_item_list = 3;
     */
    compareBankItemList: CompareBankItem[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankItem add_bank_list = 4;
     */
    addBankList: RecommendedBankItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.CompareBankResponse.
 * Use `create(CompareBankResponseSchema)` to create a new message.
 */
export const CompareBankResponseSchema: GenMessage<CompareBankResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_CompareBanks, 4);

/**
 * @generated from message com.stablemoney.api.identity.CompareBankResponseV2
 */
export type CompareBankResponseV2 =
  Message<"com.stablemoney.api.identity.CompareBankResponseV2"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.CompareBankItemV2 compare_bank_item_list_v2 = 1;
     */
    compareBankItemListV2: CompareBankItemV2[];
  };

/**
 * Describes the message com.stablemoney.api.identity.CompareBankResponseV2.
 * Use `create(CompareBankResponseV2Schema)` to create a new message.
 */
export const CompareBankResponseV2Schema: GenMessage<CompareBankResponseV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_CompareBanks, 5);

/**
 * @generated from message com.stablemoney.api.identity.CompareBankItemV2
 */
export type CompareBankItemV2 =
  Message<"com.stablemoney.api.identity.CompareBankItemV2"> & {
    /**
     * @generated from field: string bank_name = 1;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo = 2;
     */
    bankLogo: string;

    /**
     * @generated from field: string roi = 3;
     */
    roi: string;

    /**
     * @generated from field: string bank_id = 4;
     */
    bankId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.CompareBankItemV2.
 * Use `create(CompareBankItemV2Schema)` to create a new message.
 */
export const CompareBankItemV2Schema: GenMessage<CompareBankItemV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_CompareBanks, 6);
