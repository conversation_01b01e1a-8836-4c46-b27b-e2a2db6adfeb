// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/Investment.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type {
  BankResponse,
  FixedDepositResponse,
  MaturityInstruction,
} from "./Collection_pb.js";
import { file_public_models_business_Collection } from "./Collection_pb.js";
import type { RedirectDeeplink } from "./BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "./BusinessCommon_pb.js";
import type { DataKey } from "../identity/Common_pb.js";
import { file_public_models_identity_Common } from "../identity/Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/Investment.proto.
 */
export const file_public_models_business_Investment: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_Collection,
      file_public_models_business_BusinessCommon,
      file_public_models_identity_Common,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.BanksVkycDoneOrNbfcsWithAnInvestment
 */
export type BanksVkycDoneOrNbfcsWithAnInvestment =
  Message<"com.stablemoney.api.identity.BanksVkycDoneOrNbfcsWithAnInvestment"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankInvestmentInfo bank_investment_info = 1;
     */
    bankInvestmentInfo: BankInvestmentInfo[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BanksVkycDoneOrNbfcsWithAnInvestment.
 * Use `create(BanksVkycDoneOrNbfcsWithAnInvestmentSchema)` to create a new message.
 */
export const BanksVkycDoneOrNbfcsWithAnInvestmentSchema: GenMessage<BanksVkycDoneOrNbfcsWithAnInvestment> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 0);

/**
 * @generated from message com.stablemoney.api.identity.BankInvestmentInfo
 */
export type BankInvestmentInfo =
  Message<"com.stablemoney.api.identity.BankInvestmentInfo"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse fd_response = 1;
     */
    fdResponse?: FixedDepositResponse;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 2;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: bool is_completed = 3;
     */
    isCompleted: boolean;

    /**
     * @generated from field: double total_investment = 4;
     */
    totalInvestment: number;

    /**
     * @generated from field: double target_investment_amount = 5;
     */
    targetInvestmentAmount: number;

    /**
     * @generated from field: repeated string tags = 6;
     */
    tags: string[];

    /**
     * @generated from field: double progress_percentage = 7;
     */
    progressPercentage: number;

    /**
     * @generated from field: string progress_title = 8;
     */
    progressTitle: string;

    /**
     * @generated from field: string progress_subtitle = 9;
     */
    progressSubtitle: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankInvestmentInfo.
 * Use `create(BankInvestmentInfoSchema)` to create a new message.
 */
export const BankInvestmentInfoSchema: GenMessage<BankInvestmentInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 1);

/**
 * @generated from message com.stablemoney.api.identity.UserInvestments
 */
export type UserInvestments =
  Message<"com.stablemoney.api.identity.UserInvestments"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.UserInvestmentSummary user_investment_summary = 1;
     */
    userInvestmentSummary?: UserInvestmentSummary;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InvestmentsGroupedByStatus investments_grouped_by_status = 2;
     */
    investmentsGroupedByStatus: InvestmentsGroupedByStatus[];

    /**
     * @generated from field: bool show_investments = 3;
     */
    showInvestments: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 4;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: string redirect_cta = 5;
     */
    redirectCta: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserInvestments.
 * Use `create(UserInvestmentsSchema)` to create a new message.
 */
export const UserInvestmentsSchema: GenMessage<UserInvestments> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 2);

/**
 * @generated from message com.stablemoney.api.identity.UserInvestmentSummary
 */
export type UserInvestmentSummary =
  Message<"com.stablemoney.api.identity.UserInvestmentSummary"> & {
    /**
     * @generated from field: double total_investment = 1;
     */
    totalInvestment: number;

    /**
     * @generated from field: double total_gain = 2;
     */
    totalGain: number;

    /**
     * @generated from field: double maturity_amount = 3;
     */
    maturityAmount: number;

    /**
     * @generated from field: double current_value = 4;
     */
    currentValue: number;

    /**
     * @generated from field: string total_investment_text = 5;
     */
    totalInvestmentText: string;

    /**
     * @generated from field: string total_gain_text = 6;
     */
    totalGainText: string;

    /**
     * @generated from field: string maturity_amount_text = 7;
     */
    maturityAmountText: string;

    /**
     * @generated from field: string current_value_text = 8;
     */
    currentValueText: string;

    /**
     * @generated from field: string total_investment_color = 9;
     */
    totalInvestmentColor: string;

    /**
     * @generated from field: string total_gain_color = 10;
     */
    totalGainColor: string;

    /**
     * @generated from field: string maturity_amount_color = 11;
     */
    maturityAmountColor: string;

    /**
     * @generated from field: string current_value_color = 12;
     */
    currentValueColor: string;

    /**
     * @generated from field: string icon_url = 13;
     */
    iconUrl: string;

    /**
     * @generated from field: string total_investment_text_color = 14;
     */
    totalInvestmentTextColor: string;

    /**
     * @generated from field: string total_gain_text_color = 15;
     */
    totalGainTextColor: string;

    /**
     * @generated from field: string maturity_amount_text_color = 16;
     */
    maturityAmountTextColor: string;

    /**
     * @generated from field: string current_value_text_color = 17;
     */
    currentValueTextColor: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserInvestmentSummary.
 * Use `create(UserInvestmentSummarySchema)` to create a new message.
 */
export const UserInvestmentSummarySchema: GenMessage<UserInvestmentSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 3);

/**
 * @generated from message com.stablemoney.api.identity.InvestmentsGroupedByStatus
 */
export type InvestmentsGroupedByStatus =
  Message<"com.stablemoney.api.identity.InvestmentsGroupedByStatus"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BookingStatus investment_status = 1;
     */
    investmentStatus: BookingStatus;

    /**
     * @generated from field: string investment_status_label = 2;
     */
    investmentStatusLabel: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InvestmentInfo investment_info = 3;
     */
    investmentInfo: InvestmentInfo[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 4;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: string redirect_cta = 5;
     */
    redirectCta: string;

    /**
     * @generated from field: bool show_view_all = 6;
     */
    showViewAll: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.InvestmentsGroupedByStatus.
 * Use `create(InvestmentsGroupedByStatusSchema)` to create a new message.
 */
export const InvestmentsGroupedByStatusSchema: GenMessage<InvestmentsGroupedByStatus> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 4);

/**
 * @generated from message com.stablemoney.api.identity.InvestmentInfo
 */
export type InvestmentInfo =
  Message<"com.stablemoney.api.identity.InvestmentInfo"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank_response = 1;
     */
    bankResponse?: BankResponse;

    /**
     * @generated from field: string left_top_value = 2;
     */
    leftTopValue: string;

    /**
     * @generated from field: string left_bottom_value = 3;
     */
    leftBottomValue: string;

    /**
     * @generated from field: string right_top_value = 4;
     */
    rightTopValue: string;

    /**
     * @generated from field: string right_bottom_value = 5;
     */
    rightBottomValue: string;

    /**
     * @generated from field: string subtitle = 6;
     */
    subtitle: string;

    /**
     * @generated from field: string subtitle_color = 7;
     */
    subtitleColor: string;

    /**
     * @generated from field: string message = 8;
     */
    message: string;

    /**
     * @generated from field: string message_text_color = 9;
     */
    messageTextColor: string;

    /**
     * @generated from field: string message_bg_color = 10;
     */
    messageBgColor: string;

    /**
     * @generated from field: string message_icon_color = 11;
     */
    messageIconColor: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 12;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: string cta_text = 13;
     */
    ctaText: string;

    /**
     * @generated from field: string id = 14;
     */
    id: string;

    /**
     * @generated from field: com.stablemoney.api.identity.BookingStatus booking_status = 15;
     */
    bookingStatus: BookingStatus;

    /**
     * @generated from field: string left_top_text = 16;
     */
    leftTopText: string;

    /**
     * @generated from field: string left_bottom_text = 17;
     */
    leftBottomText: string;

    /**
     * @generated from field: string right_top_text = 18;
     */
    rightTopText: string;

    /**
     * @generated from field: string right_bottom_text = 19;
     */
    rightBottomText: string;

    /**
     * @generated from field: string left_top_color = 20;
     */
    leftTopColor: string;

    /**
     * @generated from field: string left_bottom_color = 21;
     */
    leftBottomColor: string;

    /**
     * @generated from field: string right_top_color = 22;
     */
    rightTopColor: string;

    /**
     * @generated from field: string right_bottom_color = 23;
     */
    rightBottomColor: string;

    /**
     * @generated from field: string left_top_text_color = 24;
     */
    leftTopTextColor: string;

    /**
     * @generated from field: string left_bottom_text_color = 25;
     */
    leftBottomTextColor: string;

    /**
     * @generated from field: string right_top_text_color = 26;
     */
    rightTopTextColor: string;

    /**
     * @generated from field: string right_bottom_text_color = 27;
     */
    rightBottomTextColor: string;

    /**
     * @generated from field: string maturity_date = 28;
     */
    maturityDate: string;

    /**
     * @generated from field: string tenure = 29;
     */
    tenure: string;

    /**
     * @generated from field: string investment_date = 30;
     */
    investmentDate: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.RedirectDeeplink withdraw_deeplink = 31;
     */
    withdrawDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: bool is_tax_saving = 32;
     */
    isTaxSaving: boolean;

    /**
     * @generated from field: string booking_date = 33;
     */
    bookingDate: string;

    /**
     * @generated from field: com.stablemoney.api.identity.MaturityInstruction maturity_instruction = 35;
     */
    maturityInstruction: MaturityInstruction;

    /**
     * @generated from field: bool is_withdrawal_confirmation_required = 36;
     */
    isWithdrawalConfirmationRequired: boolean;

    /**
     * @generated from field: map<string, string> withdrawal_metadata = 37;
     */
    withdrawalMetadata: { [key: string]: string };
  };

/**
 * Describes the message com.stablemoney.api.identity.InvestmentInfo.
 * Use `create(InvestmentInfoSchema)` to create a new message.
 */
export const InvestmentInfoSchema: GenMessage<InvestmentInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 5);

/**
 * @generated from message com.stablemoney.api.identity.InvestmentNomineeDetails
 */
export type InvestmentNomineeDetails =
  Message<"com.stablemoney.api.identity.InvestmentNomineeDetails"> & {
    /**
     * @generated from field: string nominee_name_text = 1;
     */
    nomineeNameText: string;

    /**
     * @generated from field: string nominee_name_value = 2;
     */
    nomineeNameValue: string;

    /**
     * @generated from field: string nominee_name_color = 3;
     */
    nomineeNameColor: string;

    /**
     * @generated from field: string nominee_relation_text = 4;
     */
    nomineeRelationText: string;

    /**
     * @generated from field: string nominee_relation_value = 5;
     */
    nomineeRelationValue: string;

    /**
     * @generated from field: string nominee_relation_color = 6;
     */
    nomineeRelationColor: string;

    /**
     * @generated from field: string nominee_dob_text = 7;
     */
    nomineeDobText: string;

    /**
     * @generated from field: string nominee_dob_value = 8;
     */
    nomineeDobValue: string;

    /**
     * @generated from field: string nominee_dob_color = 9;
     */
    nomineeDobColor: string;

    /**
     * @generated from field: string heading = 10;
     */
    heading: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.InvestmentNomineeDetails.
 * Use `create(InvestmentNomineeDetailsSchema)` to create a new message.
 */
export const InvestmentNomineeDetailsSchema: GenMessage<InvestmentNomineeDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 6);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalBankAccountDetails
 */
export type WithdrawalBankAccountDetails =
  Message<"com.stablemoney.api.identity.WithdrawalBankAccountDetails"> & {
    /**
     * @generated from field: string account_holder_name_text = 1;
     */
    accountHolderNameText: string;

    /**
     * @generated from field: string account_holder_name_value = 2;
     */
    accountHolderNameValue: string;

    /**
     * @generated from field: string account_holder_name_color = 3;
     */
    accountHolderNameColor: string;

    /**
     * @generated from field: string account_number_text = 4;
     */
    accountNumberText: string;

    /**
     * @generated from field: string account_number_value = 5;
     */
    accountNumberValue: string;

    /**
     * @generated from field: string account_number_color = 6;
     */
    accountNumberColor: string;

    /**
     * @generated from field: string ifsc_code_text = 7;
     */
    ifscCodeText: string;

    /**
     * @generated from field: string ifsc_code_value = 8;
     */
    ifscCodeValue: string;

    /**
     * @generated from field: string ifsc_code_color = 9;
     */
    ifscCodeColor: string;

    /**
     * @generated from field: string heading = 10;
     */
    heading: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalBankAccountDetails.
 * Use `create(WithdrawalBankAccountDetailsSchema)` to create a new message.
 */
export const WithdrawalBankAccountDetailsSchema: GenMessage<WithdrawalBankAccountDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 7);

/**
 * @generated from message com.stablemoney.api.identity.SavingsBankAccountDetails
 */
export type SavingsBankAccountDetails =
  Message<"com.stablemoney.api.identity.SavingsBankAccountDetails"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.InvestmentKeyValue investment_key_value_details = 1;
     */
    investmentKeyValueDetails: InvestmentKeyValue[];

    /**
     * @generated from field: string heading = 2;
     */
    heading: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SavingsBankAccountDetails.
 * Use `create(SavingsBankAccountDetailsSchema)` to create a new message.
 */
export const SavingsBankAccountDetailsSchema: GenMessage<SavingsBankAccountDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 8);

/**
 * @generated from message com.stablemoney.api.identity.InvestmentKeyValue
 */
export type InvestmentKeyValue =
  Message<"com.stablemoney.api.identity.InvestmentKeyValue"> & {
    /**
     * @generated from field: string label_text = 1;
     */
    labelText: string;

    /**
     * @generated from field: string label_value = 2;
     */
    labelValue: string;

    /**
     * @generated from field: string label_value_color = 3;
     */
    labelValueColor: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.InvestmentKeyValue.
 * Use `create(InvestmentKeyValueSchema)` to create a new message.
 */
export const InvestmentKeyValueSchema: GenMessage<InvestmentKeyValue> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 9);

/**
 * @generated from message com.stablemoney.api.identity.InvestmentDetailsResponse
 */
export type InvestmentDetailsResponse =
  Message<"com.stablemoney.api.identity.InvestmentDetailsResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.InvestmentInfo investment_info = 1;
     */
    investmentInfo?: InvestmentInfo;

    /**
     * @generated from field: optional com.stablemoney.api.identity.InvestmentNomineeDetails nominee_details = 2;
     */
    nomineeDetails?: InvestmentNomineeDetails;

    /**
     * @generated from field: optional com.stablemoney.api.identity.WithdrawalBankAccountDetails withdrawal_bank_account_details = 3;
     */
    withdrawalBankAccountDetails?: WithdrawalBankAccountDetails;

    /**
     * @generated from field: optional com.stablemoney.api.identity.InterestPayoutDetails interest_payout_details = 4;
     */
    interestPayoutDetails?: InterestPayoutDetails;

    /**
     * @generated from field: optional com.stablemoney.api.identity.SavingsBankAccountDetails savings_bank_acount_details = 5;
     */
    savingsBankAcountDetails?: SavingsBankAccountDetails;
  };

/**
 * Describes the message com.stablemoney.api.identity.InvestmentDetailsResponse.
 * Use `create(InvestmentDetailsResponseSchema)` to create a new message.
 */
export const InvestmentDetailsResponseSchema: GenMessage<InvestmentDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 10);

/**
 * @generated from message com.stablemoney.api.identity.InterestPayoutDetails
 */
export type InterestPayoutDetails =
  Message<"com.stablemoney.api.identity.InterestPayoutDetails"> & {
    /**
     * @generated from field: string heading = 1;
     */
    heading: string;

    /**
     * @generated from field: string payout_type_text = 2;
     */
    payoutTypeText: string;

    /**
     * @generated from field: string payout_type_value = 3;
     */
    payoutTypeValue: string;

    /**
     * @generated from field: string gains_till_date_text = 4;
     */
    gainsTillDateText: string;

    /**
     * @generated from field: string gains_till_date_value = 5;
     */
    gainsTillDateValue: string;

    /**
     * @generated from field: string interest_paid_till_date_text = 6;
     */
    interestPaidTillDateText: string;

    /**
     * @generated from field: string interest_paid_till_date_value = 7;
     */
    interestPaidTillDateValue: string;

    /**
     * @generated from field: string gains_this_month_text = 8;
     */
    gainsThisMonthText: string;

    /**
     * @generated from field: string gains_this_month_value = 9;
     */
    gainsThisMonthValue: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.InterestPayoutDetails.
 * Use `create(InterestPayoutDetailsSchema)` to create a new message.
 */
export const InterestPayoutDetailsSchema: GenMessage<InterestPayoutDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 11);

/**
 * @generated from message com.stablemoney.api.identity.PrematureWithdrawalDetailsResponse
 */
export type PrematureWithdrawalDetailsResponse =
  Message<"com.stablemoney.api.identity.PrematureWithdrawalDetailsResponse"> & {
    /**
     * @generated from field: double interest_gained_till_date = 1;
     */
    interestGainedTillDate: number;

    /**
     * @generated from field: double interest_gained_if_withdrawn_today = 2;
     */
    interestGainedIfWithdrawnToday: number;

    /**
     * @generated from field: double interest_gained_if_withdrawn_after_some_days = 3;
     */
    interestGainedIfWithdrawnAfterSomeDays: number;

    /**
     * @generated from field: int32 days_to_increase_interest = 4;
     */
    daysToIncreaseInterest: number;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 5;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: bool is_withdrawal_possible = 6;
     */
    isWithdrawalPossible: boolean;

    /**
     * @generated from field: int32 lock_in_period_in_days = 7;
     */
    lockInPeriodInDays: number;

    /**
     * @generated from field: string lock_in_period = 8;
     */
    lockInPeriod: string;

    /**
     * @generated from field: string lock_in_period_end_date = 9;
     */
    lockInPeriodEndDate: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.PrematureWithdrawalDetailsResponse.
 * Use `create(PrematureWithdrawalDetailsResponseSchema)` to create a new message.
 */
export const PrematureWithdrawalDetailsResponseSchema: GenMessage<PrematureWithdrawalDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 12);

/**
 * @generated from message com.stablemoney.api.identity.VkycExpiryResponse
 */
export type VkycExpiryResponse =
  Message<"com.stablemoney.api.identity.VkycExpiryResponse"> & {
    /**
     * @generated from field: string expiry_date = 1;
     */
    expiryDate: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.VkycExpiryResponse.
 * Use `create(VkycExpiryResponseSchema)` to create a new message.
 */
export const VkycExpiryResponseSchema: GenMessage<VkycExpiryResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 13);

/**
 * @generated from message com.stablemoney.api.identity.PostWithdrawalDetail
 */
export type PostWithdrawalDetail =
  Message<"com.stablemoney.api.identity.PostWithdrawalDetail"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.PostWithdrawalDetail.UserFixedDeposit user_fixed_deposit = 1;
     */
    userFixedDeposit?: PostWithdrawalDetail_UserFixedDeposit;

    /**
     * @generated from field: bool survey_submitted = 2;
     */
    surveySubmitted: boolean;

    /**
     * @generated from field: string survey_response = 3;
     */
    surveyResponse: string;

    /**
     * @generated from field: int64 withdrawal_timestamp = 4;
     */
    withdrawalTimestamp: bigint;

    /**
     * @generated from field: int64 estimated_refund_time = 5;
     */
    estimatedRefundTime: bigint;

    /**
     * @generated from field: bool refund_processed = 8;
     */
    refundProcessed: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline withdrawal_timeline = 10;
     */
    withdrawalTimeline?: PostWithdrawalDetail_WithdrawalTimeline;
  };

/**
 * Describes the message com.stablemoney.api.identity.PostWithdrawalDetail.
 * Use `create(PostWithdrawalDetailSchema)` to create a new message.
 */
export const PostWithdrawalDetailSchema: GenMessage<PostWithdrawalDetail> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 14);

/**
 * @generated from message com.stablemoney.api.identity.PostWithdrawalDetail.UserFixedDeposit
 */
export type PostWithdrawalDetail_UserFixedDeposit =
  Message<"com.stablemoney.api.identity.PostWithdrawalDetail.UserFixedDeposit"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string fd_identifier = 2;
     */
    fdIdentifier: string;

    /**
     * @generated from field: string journey_id = 4;
     */
    journeyId: string;

    /**
     * @generated from field: string bank_name = 6;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo_url = 8;
     */
    bankLogoUrl: string;

    /**
     * @generated from field: string tenure = 10;
     */
    tenure: string;

    /**
     * @generated from field: double investment_amount = 12;
     */
    investmentAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.PostWithdrawalDetail.UserFixedDeposit.
 * Use `create(PostWithdrawalDetail_UserFixedDepositSchema)` to create a new message.
 */
export const PostWithdrawalDetail_UserFixedDepositSchema: GenMessage<PostWithdrawalDetail_UserFixedDeposit> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 14, 0);

/**
 * @generated from message com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline
 */
export type PostWithdrawalDetail_WithdrawalTimeline =
  Message<"com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline.TimelineItem items = 3;
     */
    items: PostWithdrawalDetail_WithdrawalTimeline_TimelineItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline.
 * Use `create(PostWithdrawalDetail_WithdrawalTimelineSchema)` to create a new message.
 */
export const PostWithdrawalDetail_WithdrawalTimelineSchema: GenMessage<PostWithdrawalDetail_WithdrawalTimeline> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 14, 1);

/**
 * @generated from message com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline.TimelineItem
 */
export type PostWithdrawalDetail_WithdrawalTimeline_TimelineItem =
  Message<"com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline.TimelineItem"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.DataKey title = 1;
     */
    title?: DataKey;

    /**
     * @generated from field: com.stablemoney.api.identity.DataKey description = 2;
     */
    description?: DataKey;

    /**
     * @generated from field: bool complete = 3;
     */
    complete: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.PostWithdrawalDetail.WithdrawalTimeline.TimelineItem.
 * Use `create(PostWithdrawalDetail_WithdrawalTimeline_TimelineItemSchema)` to create a new message.
 */
export const PostWithdrawalDetail_WithdrawalTimeline_TimelineItemSchema: GenMessage<PostWithdrawalDetail_WithdrawalTimeline_TimelineItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 14, 1, 0);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalReasonSubmitRequest
 */
export type WithdrawalReasonSubmitRequest =
  Message<"com.stablemoney.api.identity.WithdrawalReasonSubmitRequest"> & {
    /**
     * @generated from field: string user_fixed_deposit_id = 1;
     */
    userFixedDepositId: string;

    /**
     * @generated from field: string reason_code = 2;
     */
    reasonCode: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalReasonSubmitRequest.
 * Use `create(WithdrawalReasonSubmitRequestSchema)` to create a new message.
 */
export const WithdrawalReasonSubmitRequestSchema: GenMessage<WithdrawalReasonSubmitRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 15);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalReasonSubmitResponse
 */
export type WithdrawalReasonSubmitResponse =
  Message<"com.stablemoney.api.identity.WithdrawalReasonSubmitResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalReasonSubmitResponse.
 * Use `create(WithdrawalReasonSubmitResponseSchema)` to create a new message.
 */
export const WithdrawalReasonSubmitResponseSchema: GenMessage<WithdrawalReasonSubmitResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Investment, 16);

/**
 * @generated from enum com.stablemoney.api.identity.BookingStatus
 */
export enum BookingStatus {
  /**
   * @generated from enum value: UNKNOWN_INVESTMENT_STATUS = 0;
   */
  UNKNOWN_INVESTMENT_STATUS = 0,

  /**
   * @generated from enum value: ACTIVE_INVESTMENT_STATUS = 1;
   */
  ACTIVE_INVESTMENT_STATUS = 1,

  /**
   * @generated from enum value: MATURED_INVESTMENT_STATUS = 2;
   */
  MATURED_INVESTMENT_STATUS = 2,

  /**
   * @generated from enum value: WITHDRAWN_INVESTMENT_STATUS = 3;
   */
  WITHDRAWN_INVESTMENT_STATUS = 3,

  /**
   * @generated from enum value: IN_PROGRESS_INVESTMENT_STATUS = 4;
   */
  IN_PROGRESS_INVESTMENT_STATUS = 4,

  /**
   * @generated from enum value: FAILED_INVESTMENT_STATUS = 5;
   */
  FAILED_INVESTMENT_STATUS = 5,

  /**
   * @generated from enum value: RENEWED_INVESTMENT_STATUS = 6;
   */
  RENEWED_INVESTMENT_STATUS = 6,

  /**
   * @generated from enum value: CANCELLED_INVESTMENT_STATUS = 7;
   */
  CANCELLED_INVESTMENT_STATUS = 7,
}

/**
 * Describes the enum com.stablemoney.api.identity.BookingStatus.
 */
export const BookingStatusSchema: GenEnum<BookingStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Investment, 0);
