// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/FixedDeposit.proto (package com.stablemoney.api.business, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { BankType, RedirectDeeplink } from "./BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "./BusinessCommon_pb.js";
import type { DataKey } from "../identity/Common_pb.js";
import { file_public_models_identity_Common } from "../identity/Common_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/FixedDeposit.proto.
 */
export const file_public_models_business_FixedDeposit: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_BusinessCommon,
      file_public_models_identity_Common,
    ],
  );

/**
 * @generated from message com.stablemoney.api.business.InitiateFdRequest
 */
export type InitiateFdRequest =
  Message<"com.stablemoney.api.business.InitiateFdRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.business.FdProvider provider = 1;
     */
    provider: FdProvider;

    /**
     * @generated from field: string bank_id = 2;
     */
    bankId: string;

    /**
     * @generated from field: com.stablemoney.api.business.FdData fd_data = 3;
     */
    fdData?: FdData;
  };

/**
 * Describes the message com.stablemoney.api.business.InitiateFdRequest.
 * Use `create(InitiateFdRequestSchema)` to create a new message.
 */
export const InitiateFdRequestSchema: GenMessage<InitiateFdRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 0);

/**
 * @generated from message com.stablemoney.api.business.InitiateFdResponse
 */
export type InitiateFdResponse =
  Message<"com.stablemoney.api.business.InitiateFdResponse"> & {
    /**
     * @generated from oneof com.stablemoney.api.business.InitiateFdResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.business.InitiateFdResponse.UpswingJourney upswing_journey = 1;
           */
          value: InitiateFdResponse_UpswingJourney;
          case: "upswingJourney";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.business.InitiateFdResponse.WebRedirectionJourney web_redirection_journey = 2;
           */
          value: InitiateFdResponse_WebRedirectionJourney;
          case: "webRedirectionJourney";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.business.InitiateFdResponse.StableMoneyJourney stable_money_journey = 3;
           */
          value: InitiateFdResponse_StableMoneyJourney;
          case: "stableMoneyJourney";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.business.InitiateFdResponse.
 * Use `create(InitiateFdResponseSchema)` to create a new message.
 */
export const InitiateFdResponseSchema: GenMessage<InitiateFdResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 1);

/**
 * @generated from message com.stablemoney.api.business.InitiateFdResponse.UpswingJourney
 */
export type InitiateFdResponse_UpswingJourney =
  Message<"com.stablemoney.api.business.InitiateFdResponse.UpswingJourney"> & {
    /**
     * @generated from field: string ici = 1;
     */
    ici: string;

    /**
     * @generated from field: string token = 2;
     */
    token: string;
  };

/**
 * Describes the message com.stablemoney.api.business.InitiateFdResponse.UpswingJourney.
 * Use `create(InitiateFdResponse_UpswingJourneySchema)` to create a new message.
 */
export const InitiateFdResponse_UpswingJourneySchema: GenMessage<InitiateFdResponse_UpswingJourney> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 1, 0);

/**
 * @generated from message com.stablemoney.api.business.InitiateFdResponse.WebRedirectionJourney
 */
export type InitiateFdResponse_WebRedirectionJourney =
  Message<"com.stablemoney.api.business.InitiateFdResponse.WebRedirectionJourney"> & {
    /**
     * @generated from field: string journey_id = 1;
     */
    journeyId: string;

    /**
     * @generated from field: string redirection_url = 2;
     */
    redirectionUrl: string;

    /**
     * @generated from field: optional com.stablemoney.api.business.FdPrefillData fdPrefillData = 3;
     */
    fdPrefillData?: FdPrefillData;
  };

/**
 * Describes the message com.stablemoney.api.business.InitiateFdResponse.WebRedirectionJourney.
 * Use `create(InitiateFdResponse_WebRedirectionJourneySchema)` to create a new message.
 */
export const InitiateFdResponse_WebRedirectionJourneySchema: GenMessage<InitiateFdResponse_WebRedirectionJourney> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 1, 1);

/**
 * @generated from message com.stablemoney.api.business.InitiateFdResponse.StableMoneyJourney
 */
export type InitiateFdResponse_StableMoneyJourney =
  Message<"com.stablemoney.api.business.InitiateFdResponse.StableMoneyJourney"> & {};

/**
 * Describes the message com.stablemoney.api.business.InitiateFdResponse.StableMoneyJourney.
 * Use `create(InitiateFdResponse_StableMoneyJourneySchema)` to create a new message.
 */
export const InitiateFdResponse_StableMoneyJourneySchema: GenMessage<InitiateFdResponse_StableMoneyJourney> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 1, 2);

/**
 * @generated from message com.stablemoney.api.business.FincareInvestmentSummary
 */
export type FincareInvestmentSummary =
  Message<"com.stablemoney.api.business.FincareInvestmentSummary"> & {
    /**
     * @generated from field: com.stablemoney.api.business.TotalInvestedAmount totalInvestedAmount = 1;
     */
    totalInvestedAmount?: TotalInvestedAmount;

    /**
     * @generated from field: com.stablemoney.api.business.TotalInterestEarned totalInterestEarned = 2;
     */
    totalInterestEarned?: TotalInterestEarned;

    /**
     * @generated from field: string bank_name = 3;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo = 4;
     */
    bankLogo: string;

    /**
     * @generated from field: optional bool fincare_maximum_amount_check = 5;
     */
    fincareMaximumAmountCheck?: boolean;

    /**
     * @generated from field: optional bool fincare_vkyc_completed = 6;
     */
    fincareVkycCompleted?: boolean;

    /**
     * @generated from field: optional int32 active_term_deposit_count = 7;
     */
    activeTermDepositCount?: number;
  };

/**
 * Describes the message com.stablemoney.api.business.FincareInvestmentSummary.
 * Use `create(FincareInvestmentSummarySchema)` to create a new message.
 */
export const FincareInvestmentSummarySchema: GenMessage<FincareInvestmentSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 2);

/**
 * @generated from message com.stablemoney.api.business.UnityInvestmentSummary
 */
export type UnityInvestmentSummary =
  Message<"com.stablemoney.api.business.UnityInvestmentSummary"> & {
    /**
     * @generated from field: com.stablemoney.api.business.TotalInvestedAmount totalInvestedAmount = 1;
     */
    totalInvestedAmount?: TotalInvestedAmount;

    /**
     * @generated from field: com.stablemoney.api.business.TotalInterestEarned totalInterestEarned = 2;
     */
    totalInterestEarned?: TotalInterestEarned;

    /**
     * @generated from field: string bank_name = 3;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo = 4;
     */
    bankLogo: string;

    /**
     * @generated from field: string bank_id = 5;
     */
    bankId: string;

    /**
     * @generated from field: optional int32 active_term_deposit_count = 6;
     */
    activeTermDepositCount?: number;
  };

/**
 * Describes the message com.stablemoney.api.business.UnityInvestmentSummary.
 * Use `create(UnityInvestmentSummarySchema)` to create a new message.
 */
export const UnityInvestmentSummarySchema: GenMessage<UnityInvestmentSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 3);

/**
 * @generated from message com.stablemoney.api.business.UjjivanInvestmentSummary
 */
export type UjjivanInvestmentSummary =
  Message<"com.stablemoney.api.business.UjjivanInvestmentSummary"> & {
    /**
     * @generated from field: com.stablemoney.api.business.TotalInvestedAmount totalInvestedAmount = 1;
     */
    totalInvestedAmount?: TotalInvestedAmount;

    /**
     * @generated from field: com.stablemoney.api.business.TotalInterestEarned totalInterestEarned = 2;
     */
    totalInterestEarned?: TotalInterestEarned;

    /**
     * @generated from field: string bank_name = 3;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo = 4;
     */
    bankLogo: string;

    /**
     * @generated from field: string bank_id = 5;
     */
    bankId: string;

    /**
     * @generated from field: optional int32 active_term_deposit_count = 6;
     */
    activeTermDepositCount?: number;
  };

/**
 * Describes the message com.stablemoney.api.business.UjjivanInvestmentSummary.
 * Use `create(UjjivanInvestmentSummarySchema)` to create a new message.
 */
export const UjjivanInvestmentSummarySchema: GenMessage<UjjivanInvestmentSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 4);

/**
 * @generated from message com.stablemoney.api.business.IndusindInvestmentSummary
 */
export type IndusindInvestmentSummary =
  Message<"com.stablemoney.api.business.IndusindInvestmentSummary"> & {
    /**
     * @generated from field: com.stablemoney.api.business.TotalInvestedAmount totalInvestedAmount = 1;
     */
    totalInvestedAmount?: TotalInvestedAmount;

    /**
     * @generated from field: com.stablemoney.api.business.TotalInterestEarned totalInterestEarned = 2;
     */
    totalInterestEarned?: TotalInterestEarned;

    /**
     * @generated from field: string bank_name = 3;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo = 4;
     */
    bankLogo: string;

    /**
     * @generated from field: string bank_id = 5;
     */
    bankId: string;

    /**
     * @generated from field: optional int32 active_term_deposit_count = 6;
     */
    activeTermDepositCount?: number;
  };

/**
 * Describes the message com.stablemoney.api.business.IndusindInvestmentSummary.
 * Use `create(IndusindInvestmentSummarySchema)` to create a new message.
 */
export const IndusindInvestmentSummarySchema: GenMessage<IndusindInvestmentSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 5);

/**
 * @generated from message com.stablemoney.api.business.NetWorthSummaryResponse
 */
export type NetWorthSummaryResponse =
  Message<"com.stablemoney.api.business.NetWorthSummaryResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.business.TotalInvestedAmount totalInvestedAmount = 1;
     */
    totalInvestedAmount?: TotalInvestedAmount;

    /**
     * @generated from field: com.stablemoney.api.business.TotalInterestEarned totalInterestEarned = 2;
     */
    totalInterestEarned?: TotalInterestEarned;

    /**
     * @generated from field: com.stablemoney.api.business.CurrentAmount currentAmount = 3;
     */
    currentAmount?: CurrentAmount;

    /**
     * @generated from field: int32 activeTermDepositCount = 4;
     */
    activeTermDepositCount: number;

    /**
     * @generated from field: bool is_invested = 5;
     */
    isInvested: boolean;

    /**
     * @generated from field: bool is_pending = 6;
     */
    isPending: boolean;

    /**
     * @generated from field: string pending_event_time = 7;
     */
    pendingEventTime: string;

    /**
     * @generated from field: string pending_event_type = 8;
     */
    pendingEventType: string;

    /**
     * @generated from field: bool is_vkyc_pending = 9;
     */
    isVkycPending: boolean;

    /**
     * @generated from field: bool is_upswing_initiated = 10;
     */
    isUpswingInitiated: boolean;

    /**
     * @generated from field: optional com.stablemoney.api.business.FincareInvestmentSummary fincareInvestmentSummary = 11;
     */
    fincareInvestmentSummary?: FincareInvestmentSummary;

    /**
     * @generated from field: optional com.stablemoney.api.business.UnityInvestmentSummary unity_investment_summary = 12;
     */
    unityInvestmentSummary?: UnityInvestmentSummary;

    /**
     * @generated from field: optional com.stablemoney.api.business.EmergencyFundSummary emergencyFundSummary = 13;
     */
    emergencyFundSummary?: EmergencyFundSummary;

    /**
     * @generated from field: optional com.stablemoney.api.business.UjjivanInvestmentSummary ujjivan_investment_summary = 14;
     */
    ujjivanInvestmentSummary?: UjjivanInvestmentSummary;

    /**
     * @generated from field: optional com.stablemoney.api.business.IndusindInvestmentSummary indusind_investment_summary = 15;
     */
    indusindInvestmentSummary?: IndusindInvestmentSummary;

    /**
     * @generated from field: double inProgressTotal = 16;
     */
    inProgressTotal: number;
  };

/**
 * Describes the message com.stablemoney.api.business.NetWorthSummaryResponse.
 * Use `create(NetWorthSummaryResponseSchema)` to create a new message.
 */
export const NetWorthSummaryResponseSchema: GenMessage<NetWorthSummaryResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 6);

/**
 * @generated from message com.stablemoney.api.business.EmergencyFundSummary
 */
export type EmergencyFundSummary =
  Message<"com.stablemoney.api.business.EmergencyFundSummary"> & {
    /**
     * @generated from field: bool is_emergency_fund_created = 1;
     */
    isEmergencyFundCreated: boolean;

    /**
     * @generated from field: double target_amount = 2;
     */
    targetAmount: number;

    /**
     * @generated from field: double current_amount = 3;
     */
    currentAmount: number;

    /**
     * @generated from field: double progress_percentage = 4;
     */
    progressPercentage: number;

    /**
     * @generated from field: string progress_description = 5;
     */
    progressDescription: string;

    /**
     * @generated from field: bool is_active = 6;
     */
    isActive: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.EmergencyFundSummary.
 * Use `create(EmergencyFundSummarySchema)` to create a new message.
 */
export const EmergencyFundSummarySchema: GenMessage<EmergencyFundSummary> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 7);

/**
 * @generated from message com.stablemoney.api.business.TotalInvestedAmount
 */
export type TotalInvestedAmount =
  Message<"com.stablemoney.api.business.TotalInvestedAmount"> & {
    /**
     * @generated from field: double amount = 1;
     */
    amount: number;

    /**
     * @generated from field: string currency = 2;
     */
    currency: string;
  };

/**
 * Describes the message com.stablemoney.api.business.TotalInvestedAmount.
 * Use `create(TotalInvestedAmountSchema)` to create a new message.
 */
export const TotalInvestedAmountSchema: GenMessage<TotalInvestedAmount> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 8);

/**
 * @generated from message com.stablemoney.api.business.TotalInterestEarned
 */
export type TotalInterestEarned =
  Message<"com.stablemoney.api.business.TotalInterestEarned"> & {
    /**
     * @generated from field: double amount = 1;
     */
    amount: number;

    /**
     * @generated from field: string currency = 2;
     */
    currency: string;
  };

/**
 * Describes the message com.stablemoney.api.business.TotalInterestEarned.
 * Use `create(TotalInterestEarnedSchema)` to create a new message.
 */
export const TotalInterestEarnedSchema: GenMessage<TotalInterestEarned> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 9);

/**
 * @generated from message com.stablemoney.api.business.CurrentAmount
 */
export type CurrentAmount =
  Message<"com.stablemoney.api.business.CurrentAmount"> & {
    /**
     * @generated from field: double amount = 1;
     */
    amount: number;

    /**
     * @generated from field: string currency = 2;
     */
    currency: string;
  };

/**
 * Describes the message com.stablemoney.api.business.CurrentAmount.
 * Use `create(CurrentAmountSchema)` to create a new message.
 */
export const CurrentAmountSchema: GenMessage<CurrentAmount> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 10);

/**
 * @generated from message com.stablemoney.api.business.FincarePayload
 */
export type FincarePayload =
  Message<"com.stablemoney.api.business.FincarePayload"> & {
    /**
     * @generated from field: string session_id = 1;
     */
    sessionId: string;

    /**
     * @generated from field: string utm_medium = 2;
     */
    utmMedium: string;

    /**
     * @generated from field: string utm_source = 3;
     */
    utmSource: string;

    /**
     * @generated from field: string utm_campaign = 4;
     */
    utmCampaign: string;

    /**
     * @generated from field: string referral_link = 5;
     */
    referralLink: string;

    /**
     * @generated from field: optional double fd_rate = 6;
     */
    fdRate?: number;

    /**
     * @generated from field: optional string fd_maturity_date = 7;
     */
    fdMaturityDate?: string;

    /**
     * @generated from field: optional double fd_amount = 8;
     */
    fdAmount?: number;

    /**
     * @generated from field: string kyc_status = 9;
     */
    kycStatus: string;

    /**
     * @generated from field: string account_created_status = 10;
     */
    accountCreatedStatus: string;

    /**
     * @generated from field: optional bool is_senior_citizen = 11;
     */
    isSeniorCitizen?: boolean;

    /**
     * @generated from field: optional int32 tenure_in_days = 12;
     */
    tenureInDays?: number;

    /**
     * @generated from field: optional string interest_payout_type = 13;
     */
    interestPayoutType?: string;

    /**
     * @generated from field: optional string maturity_instruction = 14;
     */
    maturityInstruction?: string;

    /**
     * @generated from field: optional bool fd_active = 15;
     */
    fdActive?: boolean;

    /**
     * @generated from field: optional string fd_closing_date = 16;
     */
    fdClosingDate?: string;

    /**
     * @generated from field: optional string fd_booking_date = 17;
     */
    fdBookingDate?: string;

    /**
     * @generated from field: optional string prod_type = 18;
     */
    prodType?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FincarePayload.
 * Use `create(FincarePayloadSchema)` to create a new message.
 */
export const FincarePayloadSchema: GenMessage<FincarePayload> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 11);

/**
 * @generated from message com.stablemoney.api.business.TenurePrefillInfo
 */
export type TenurePrefillInfo =
  Message<"com.stablemoney.api.business.TenurePrefillInfo"> & {
    /**
     * @generated from field: optional string tenure_html_tag = 1;
     */
    tenureHtmlTag?: string;

    /**
     * @generated from field: optional int32 tenure_year = 2;
     */
    tenureYear?: number;

    /**
     * @generated from field: optional int32 tenure_month = 3;
     */
    tenureMonth?: number;

    /**
     * @generated from field: optional int32 tenure_day = 4;
     */
    tenureDay?: number;
  };

/**
 * Describes the message com.stablemoney.api.business.TenurePrefillInfo.
 * Use `create(TenurePrefillInfoSchema)` to create a new message.
 */
export const TenurePrefillInfoSchema: GenMessage<TenurePrefillInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 12);

/**
 * @generated from message com.stablemoney.api.business.NomineePrefillInfo
 */
export type NomineePrefillInfo =
  Message<"com.stablemoney.api.business.NomineePrefillInfo"> & {
    /**
     * @generated from field: optional string nominee_name = 1;
     */
    nomineeName?: string;

    /**
     * @generated from field: optional string nominee_relationship = 2;
     */
    nomineeRelationship?: string;

    /**
     * @generated from field: optional string nominee_date_of_birth = 3;
     */
    nomineeDateOfBirth?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.NomineePrefillInfo.
 * Use `create(NomineePrefillInfoSchema)` to create a new message.
 */
export const NomineePrefillInfoSchema: GenMessage<NomineePrefillInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 13);

/**
 * @generated from message com.stablemoney.api.business.BankAccountPrefillInfo
 */
export type BankAccountPrefillInfo =
  Message<"com.stablemoney.api.business.BankAccountPrefillInfo"> & {
    /**
     * @generated from field: optional string account_holder_name = 1;
     */
    accountHolderName?: string;

    /**
     * @generated from field: optional string bank_account_number = 2;
     */
    bankAccountNumber?: string;

    /**
     * @generated from field: optional string ifsc_code = 3;
     */
    ifscCode?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.BankAccountPrefillInfo.
 * Use `create(BankAccountPrefillInfoSchema)` to create a new message.
 */
export const BankAccountPrefillInfoSchema: GenMessage<BankAccountPrefillInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 14);

/**
 * @generated from message com.stablemoney.api.business.FdPrefillData
 */
export type FdPrefillData =
  Message<"com.stablemoney.api.business.FdPrefillData"> & {
    /**
     * @generated from field: double investment_amount = 1;
     */
    investmentAmount: number;

    /**
     * @generated from field: string maturity_instruction = 2;
     */
    maturityInstruction: string;

    /**
     * @generated from field: string interest_payout_type = 3;
     */
    interestPayoutType: string;

    /**
     * @generated from field: com.stablemoney.api.business.TenurePrefillInfo tenure_prefill_info = 4;
     */
    tenurePrefillInfo?: TenurePrefillInfo;

    /**
     * @generated from field: com.stablemoney.api.business.NomineePrefillInfo nominee_prefill_info = 5;
     */
    nomineePrefillInfo?: NomineePrefillInfo;

    /**
     * @generated from field: com.stablemoney.api.business.BankAccountPrefillInfo bank_account_prefill_info = 6;
     */
    bankAccountPrefillInfo?: BankAccountPrefillInfo;

    /**
     * @generated from field: bool is_tax_saving = 7;
     */
    isTaxSaving: boolean;

    /**
     * @generated from field: optional string pan_number = 8;
     */
    panNumber?: string;

    /**
     * @generated from field: optional string dob = 9;
     */
    dob?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdPrefillData.
 * Use `create(FdPrefillDataSchema)` to create a new message.
 */
export const FdPrefillDataSchema: GenMessage<FdPrefillData> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 15);

/**
 * @generated from message com.stablemoney.api.business.FdEventResponse
 */
export type FdEventResponse =
  Message<"com.stablemoney.api.business.FdEventResponse"> & {
    /**
     * @generated from field: string booking_url = 1;
     */
    bookingUrl: string;

    /**
     * @generated from field: string booking_id = 2;
     */
    bookingId: string;

    /**
     * @generated from field: com.stablemoney.api.business.FdPrefillData fd_prefill_data = 3;
     */
    fdPrefillData?: FdPrefillData;

    /**
     * @generated from field: string fd_id = 4;
     */
    fdId: string;

    /**
     * @generated from field: optional bool isBlock = 5;
     */
    isBlock?: boolean;

    /**
     * @generated from field: optional bool isInput = 6;
     */
    isInput?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.FdEventResponse.
 * Use `create(FdEventResponseSchema)` to create a new message.
 */
export const FdEventResponseSchema: GenMessage<FdEventResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 16);

/**
 * @generated from message com.stablemoney.api.business.EtbResponse
 */
export type EtbResponse =
  Message<"com.stablemoney.api.business.EtbResponse"> & {
    /**
     * @generated from field: bool is_etb_known = 1;
     */
    isEtbKnown: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.EtbResponse.
 * Use `create(EtbResponseSchema)` to create a new message.
 */
export const EtbResponseSchema: GenMessage<EtbResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 17);

/**
 * @generated from message com.stablemoney.api.business.SetEtbRequest
 */
export type SetEtbRequest =
  Message<"com.stablemoney.api.business.SetEtbRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: bool has_account = 2;
     */
    hasAccount: boolean;

    /**
     * @generated from field: bool has_fd = 3;
     */
    hasFd: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.SetEtbRequest.
 * Use `create(SetEtbRequestSchema)` to create a new message.
 */
export const SetEtbRequestSchema: GenMessage<SetEtbRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 18);

/**
 * @generated from message com.stablemoney.api.business.GetEtbRequest
 */
export type GetEtbRequest =
  Message<"com.stablemoney.api.business.GetEtbRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.GetEtbRequest.
 * Use `create(GetEtbRequestSchema)` to create a new message.
 */
export const GetEtbRequestSchema: GenMessage<GetEtbRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 19);

/**
 * @generated from message com.stablemoney.api.business.PanStatusResponse
 */
export type PanStatusResponse =
  Message<"com.stablemoney.api.business.PanStatusResponse"> & {
    /**
     * @generated from field: bool is_pan_needed = 1;
     */
    isPanNeeded: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.PanStatusResponse.
 * Use `create(PanStatusResponseSchema)` to create a new message.
 */
export const PanStatusResponseSchema: GenMessage<PanStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 20);

/**
 * @generated from message com.stablemoney.api.business.EventMetadata
 */
export type EventMetadata =
  Message<"com.stablemoney.api.business.EventMetadata"> & {};

/**
 * Describes the message com.stablemoney.api.business.EventMetadata.
 * Use `create(EventMetadataSchema)` to create a new message.
 */
export const EventMetadataSchema: GenMessage<EventMetadata> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 21);

/**
 * @generated from message com.stablemoney.api.business.FdData
 */
export type FdData = Message<"com.stablemoney.api.business.FdData"> & {
  /**
   * @generated from field: string fd_id = 1;
   */
  fdId: string;

  /**
   * @generated from field: double investment_amount = 2;
   */
  investmentAmount: number;

  /**
   * @generated from field: string interest_payout_type = 3;
   */
  interestPayoutType: string;

  /**
   * @generated from field: string maturity_instruction = 4;
   */
  maturityInstruction: string;

  /**
   * @generated from field: optional string pan_number = 5;
   */
  panNumber?: string;

  /**
   * @generated from field: optional string dob = 6;
   */
  dob?: string;

  /**
   * @generated from field: optional bool pan_consent = 7;
   */
  panConsent?: boolean;
};

/**
 * Describes the message com.stablemoney.api.business.FdData.
 * Use `create(FdDataSchema)` to create a new message.
 */
export const FdDataSchema: GenMessage<FdData> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 22);

/**
 * @generated from message com.stablemoney.api.business.FdEventRequest
 */
export type FdEventRequest =
  Message<"com.stablemoney.api.business.FdEventRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: string event_type = 2;
     */
    eventType: string;

    /**
     * @generated from field: optional com.stablemoney.api.business.FdData fd_data = 3;
     */
    fdData?: FdData;

    /**
     * @generated from field: optional com.stablemoney.api.business.EventMetadata event_metadata = 4;
     */
    eventMetadata?: EventMetadata;

    /**
     * @generated from field: optional string booking_id = 5;
     */
    bookingId?: string;

    /**
     * @generated from field: optional string page_html = 6;
     */
    pageHtml?: string;

    /**
     * @generated from field: optional string page_url = 7;
     */
    pageUrl?: string;

    /**
     * @generated from field: optional string timestamp = 8;
     */
    timestamp?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdEventRequest.
 * Use `create(FdEventRequestSchema)` to create a new message.
 */
export const FdEventRequestSchema: GenMessage<FdEventRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 23);

/**
 * @generated from message com.stablemoney.api.business.FdCalculationRequest
 */
export type FdCalculationRequest =
  Message<"com.stablemoney.api.business.FdCalculationRequest"> & {
    /**
     * @generated from field: string fd_id = 1;
     */
    fdId: string;

    /**
     * @generated from field: double investment_amount = 2;
     */
    investmentAmount: number;

    /**
     * @generated from field: string interest_payout_type = 3;
     */
    interestPayoutType: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdCalculationRequest.
 * Use `create(FdCalculationRequestSchema)` to create a new message.
 */
export const FdCalculationRequestSchema: GenMessage<FdCalculationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 24);

/**
 * @generated from message com.stablemoney.api.business.FdCalculationResponse
 */
export type FdCalculationResponse =
  Message<"com.stablemoney.api.business.FdCalculationResponse"> & {
    /**
     * @generated from field: double maturity_amount = 1;
     */
    maturityAmount: number;

    /**
     * @generated from field: string maturity_description = 2;
     */
    maturityDescription: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdCalculationResponse.
 * Use `create(FdCalculationResponseSchema)` to create a new message.
 */
export const FdCalculationResponseSchema: GenMessage<FdCalculationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 25);

/**
 * @generated from message com.stablemoney.api.business.FdCalculationResponseV2
 */
export type FdCalculationResponseV2 =
  Message<"com.stablemoney.api.business.FdCalculationResponseV2"> & {
    /**
     * @generated from field: double maturity_amount = 1;
     */
    maturityAmount: number;

    /**
     * @generated from field: double interest_gained = 2;
     */
    interestGained: number;

    /**
     * @generated from field: string maturity_description = 3;
     */
    maturityDescription: string;
  };

/**
 * Describes the message com.stablemoney.api.business.FdCalculationResponseV2.
 * Use `create(FdCalculationResponseV2Schema)` to create a new message.
 */
export const FdCalculationResponseV2Schema: GenMessage<FdCalculationResponseV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 26);

/**
 * @generated from message com.stablemoney.api.business.NearMaturityInvestments
 */
export type NearMaturityInvestments =
  Message<"com.stablemoney.api.business.NearMaturityInvestments"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.InvestmentItem investment_items = 1;
     */
    investmentItems: InvestmentItem[];
  };

/**
 * Describes the message com.stablemoney.api.business.NearMaturityInvestments.
 * Use `create(NearMaturityInvestmentsSchema)` to create a new message.
 */
export const NearMaturityInvestmentsSchema: GenMessage<NearMaturityInvestments> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 27);

/**
 * @generated from message com.stablemoney.api.business.NearMaturityInvestments.FixedDeposit
 */
export type NearMaturityInvestments_FixedDeposit =
  Message<"com.stablemoney.api.business.NearMaturityInvestments.FixedDeposit"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: string bank_name = 2;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo_url = 3;
     */
    bankLogoUrl: string;

    /**
     * @generated from field: com.stablemoney.api.identity.BankType bank_type = 4;
     */
    bankType: BankType;

    /**
     * @generated from field: string tenure = 5;
     */
    tenure: string;

    /**
     * @generated from field: double interest_rate = 6;
     */
    interestRate: number;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deep_link = 7;
     */
    redirectDeepLink?: RedirectDeeplink;

    /**
     * @generated from field: repeated string description = 8;
     */
    description: string[];
  };

/**
 * Describes the message com.stablemoney.api.business.NearMaturityInvestments.FixedDeposit.
 * Use `create(NearMaturityInvestments_FixedDepositSchema)` to create a new message.
 */
export const NearMaturityInvestments_FixedDepositSchema: GenMessage<NearMaturityInvestments_FixedDeposit> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 27, 0);

/**
 * @generated from message com.stablemoney.api.business.LastMaturedInvestment
 */
export type LastMaturedInvestment =
  Message<"com.stablemoney.api.business.LastMaturedInvestment"> & {
    /**
     * @generated from field: com.stablemoney.api.business.InvestmentItem investment_item = 1;
     */
    investmentItem?: InvestmentItem;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.DataKey pointers = 3;
     */
    pointers: DataKey[];

    /**
     * @generated from field: repeated com.stablemoney.api.business.NearMaturityInvestments.FixedDeposit suggested_fds = 5;
     */
    suggestedFds: NearMaturityInvestments_FixedDeposit[];
  };

/**
 * Describes the message com.stablemoney.api.business.LastMaturedInvestment.
 * Use `create(LastMaturedInvestmentSchema)` to create a new message.
 */
export const LastMaturedInvestmentSchema: GenMessage<LastMaturedInvestment> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 28);

/**
 * @generated from message com.stablemoney.api.business.NearMaturityInvestmentDetail
 */
export type NearMaturityInvestmentDetail =
  Message<"com.stablemoney.api.business.NearMaturityInvestmentDetail"> & {
    /**
     * @generated from field: com.stablemoney.api.business.InvestmentItem investment_item = 1;
     */
    investmentItem?: InvestmentItem;

    /**
     * @generated from field: com.stablemoney.api.business.NearMaturityInvestmentDetail.Recommendation recommendation = 2;
     */
    recommendation?: NearMaturityInvestmentDetail_Recommendation;

    /**
     * @generated from field: repeated com.stablemoney.api.business.NearMaturityInvestments.FixedDeposit suggested_fds = 3;
     */
    suggestedFds: NearMaturityInvestments_FixedDeposit[];

    /**
     * @generated from field: com.stablemoney.api.business.NearMaturityInvestmentDetail.Term term = 4;
     */
    term: NearMaturityInvestmentDetail_Term;
  };

/**
 * Describes the message com.stablemoney.api.business.NearMaturityInvestmentDetail.
 * Use `create(NearMaturityInvestmentDetailSchema)` to create a new message.
 */
export const NearMaturityInvestmentDetailSchema: GenMessage<NearMaturityInvestmentDetail> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 29);

/**
 * @generated from message com.stablemoney.api.business.NearMaturityInvestmentDetail.Recommendation
 */
export type NearMaturityInvestmentDetail_Recommendation =
  Message<"com.stablemoney.api.business.NearMaturityInvestmentDetail.Recommendation"> & {
    /**
     * @generated from field: com.stablemoney.api.business.NearMaturityInvestmentDetail.Recommendation.Type type = 1;
     */
    type: NearMaturityInvestmentDetail_Recommendation_Type;

    /**
     * @generated from field: double amount = 2;
     */
    amount: number;

    /**
     * @generated from field: double interest_rate = 3;
     */
    interestRate: number;

    /**
     * @generated from field: string tenure = 4;
     */
    tenure: string;
  };

/**
 * Describes the message com.stablemoney.api.business.NearMaturityInvestmentDetail.Recommendation.
 * Use `create(NearMaturityInvestmentDetail_RecommendationSchema)` to create a new message.
 */
export const NearMaturityInvestmentDetail_RecommendationSchema: GenMessage<NearMaturityInvestmentDetail_Recommendation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 29, 0);

/**
 * @generated from enum com.stablemoney.api.business.NearMaturityInvestmentDetail.Recommendation.Type
 */
export enum NearMaturityInvestmentDetail_Recommendation_Type {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: NONE = 1;
   */
  NONE = 1,

  /**
   * @generated from enum value: REINVEST = 2;
   */
  REINVEST = 2,

  /**
   * @generated from enum value: WITHDRAW = 3;
   */
  WITHDRAW = 3,
}

/**
 * Describes the enum com.stablemoney.api.business.NearMaturityInvestmentDetail.Recommendation.Type.
 */
export const NearMaturityInvestmentDetail_Recommendation_TypeSchema: GenEnum<NearMaturityInvestmentDetail_Recommendation_Type> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FixedDeposit, 29, 0, 0);

/**
 * @generated from enum com.stablemoney.api.business.NearMaturityInvestmentDetail.Term
 */
export enum NearMaturityInvestmentDetail_Term {
  /**
   * @generated from enum value: SHORT = 0;
   */
  SHORT = 0,

  /**
   * @generated from enum value: MEDIUM = 1;
   */
  MEDIUM = 1,

  /**
   * @generated from enum value: LONG = 2;
   */
  LONG = 2,
}

/**
 * Describes the enum com.stablemoney.api.business.NearMaturityInvestmentDetail.Term.
 */
export const NearMaturityInvestmentDetail_TermSchema: GenEnum<NearMaturityInvestmentDetail_Term> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FixedDeposit, 29, 0);

/**
 * @generated from message com.stablemoney.api.business.InvestmentItem
 */
export type InvestmentItem =
  Message<"com.stablemoney.api.business.InvestmentItem"> & {
    /**
     * @generated from field: double investment_amount = 1;
     */
    investmentAmount: number;

    /**
     * @generated from field: string fd_maturity_date = 2;
     */
    fdMaturityDate: string;

    /**
     * @generated from field: string interest_payout_type = 3;
     */
    interestPayoutType: string;

    /**
     * @generated from field: double current_gains = 4;
     */
    currentGains: number;

    /**
     * @generated from field: string bank_name = 5;
     */
    bankName: string;

    /**
     * @generated from field: string bank_logo_url = 6;
     */
    bankLogoUrl: string;

    /**
     * @generated from field: string tenure = 7;
     */
    tenure: string;

    /**
     * @generated from field: string fd_booking_date = 8;
     */
    fdBookingDate: string;

    /**
     * @generated from field: double interest_rate = 9;
     */
    interestRate: number;

    /**
     * @generated from field: double maturity_amount = 10;
     */
    maturityAmount: number;

    /**
     * @generated from field: string booking_id = 11;
     */
    bookingId: string;

    /**
     * @generated from field: string investment_status = 12;
     */
    investmentStatus: string;

    /**
     * @generated from field: string invested_at = 13;
     */
    investedAt: string;

    /**
     * @generated from field: string maturity_instruction = 15;
     */
    maturityInstruction: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deep_link = 16;
     */
    redirectDeepLink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.business.InvestmentItem.
 * Use `create(InvestmentItemSchema)` to create a new message.
 */
export const InvestmentItemSchema: GenMessage<InvestmentItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 30);

/**
 * @generated from message com.stablemoney.api.business.InvestmentResponse
 */
export type InvestmentResponse =
  Message<"com.stablemoney.api.business.InvestmentResponse"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.business.InvestmentItem pendingInvestedItems = 2;
     */
    pendingInvestedItems: InvestmentItem[];

    /**
     * @generated from field: repeated com.stablemoney.api.business.InvestmentItem bookedInvestedItems = 3;
     */
    bookedInvestedItems: InvestmentItem[];

    /**
     * @generated from field: com.stablemoney.api.business.TotalInvestedAmount totalInvestedAmount = 4;
     */
    totalInvestedAmount?: TotalInvestedAmount;

    /**
     * @generated from field: com.stablemoney.api.business.TotalInterestEarned totalInterestEarned = 5;
     */
    totalInterestEarned?: TotalInterestEarned;
  };

/**
 * Describes the message com.stablemoney.api.business.InvestmentResponse.
 * Use `create(InvestmentResponseSchema)` to create a new message.
 */
export const InvestmentResponseSchema: GenMessage<InvestmentResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 31);

/**
 * @generated from message com.stablemoney.api.business.FincareSheetRow
 */
export type FincareSheetRow =
  Message<"com.stablemoney.api.business.FincareSheetRow"> & {
    /**
     * @generated from field: string hmac = 1;
     */
    hmac: string;

    /**
     * @generated from field: com.stablemoney.api.business.FincarePayload fincare_payload = 2;
     */
    fincarePayload?: FincarePayload;
  };

/**
 * Describes the message com.stablemoney.api.business.FincareSheetRow.
 * Use `create(FincareSheetRowSchema)` to create a new message.
 */
export const FincareSheetRowSchema: GenMessage<FincareSheetRow> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 32);

/**
 * @generated from message com.stablemoney.api.business.FincareSheetRequest
 */
export type FincareSheetRequest =
  Message<"com.stablemoney.api.business.FincareSheetRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.FincareSheetRow fincare_sheet = 1;
     */
    fincareSheet: FincareSheetRow[];
  };

/**
 * Describes the message com.stablemoney.api.business.FincareSheetRequest.
 * Use `create(FincareSheetRequestSchema)` to create a new message.
 */
export const FincareSheetRequestSchema: GenMessage<FincareSheetRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 33);

/**
 * @generated from message com.stablemoney.api.business.UjjivanPayload
 */
export type UjjivanPayload =
  Message<"com.stablemoney.api.business.UjjivanPayload"> & {
    /**
     * @generated from field: string application_id = 1;
     */
    applicationId: string;

    /**
     * @generated from field: string application_status = 2;
     */
    applicationStatus: string;

    /**
     * @generated from field: string creation_date = 3;
     */
    creationDate: string;

    /**
     * @generated from field: string last_update_date = 4;
     */
    lastUpdateDate: string;

    /**
     * @generated from field: optional double amount = 5;
     */
    amount?: number;

    /**
     * @generated from field: optional string user_id = 6;
     */
    userId?: string;

    /**
     * @generated from field: optional string investment_id = 7;
     */
    investmentId?: string;
  };

/**
 * Describes the message com.stablemoney.api.business.UjjivanPayload.
 * Use `create(UjjivanPayloadSchema)` to create a new message.
 */
export const UjjivanPayloadSchema: GenMessage<UjjivanPayload> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 34);

/**
 * @generated from message com.stablemoney.api.business.UjjivanSheetRow
 */
export type UjjivanSheetRow =
  Message<"com.stablemoney.api.business.UjjivanSheetRow"> & {
    /**
     * @generated from field: string hmac = 1;
     */
    hmac: string;

    /**
     * @generated from field: com.stablemoney.api.business.UjjivanPayload payload = 2;
     */
    payload?: UjjivanPayload;
  };

/**
 * Describes the message com.stablemoney.api.business.UjjivanSheetRow.
 * Use `create(UjjivanSheetRowSchema)` to create a new message.
 */
export const UjjivanSheetRowSchema: GenMessage<UjjivanSheetRow> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 35);

/**
 * @generated from message com.stablemoney.api.business.UjjivanSheetRequest
 */
export type UjjivanSheetRequest =
  Message<"com.stablemoney.api.business.UjjivanSheetRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.UjjivanSheetRow sheet = 1;
     */
    sheet: UjjivanSheetRow[];
  };

/**
 * Describes the message com.stablemoney.api.business.UjjivanSheetRequest.
 * Use `create(UjjivanSheetRequestSchema)` to create a new message.
 */
export const UjjivanSheetRequestSchema: GenMessage<UjjivanSheetRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 36);

/**
 * @generated from message com.stablemoney.api.business.SheetResponse
 */
export type SheetResponse =
  Message<"com.stablemoney.api.business.SheetResponse"> & {
    /**
     * @generated from field: repeated string successes = 1;
     */
    successes: string[];

    /**
     * @generated from field: repeated string failures = 2;
     */
    failures: string[];

    /**
     * @generated from field: repeated string skipped = 3;
     */
    skipped: string[];
  };

/**
 * Describes the message com.stablemoney.api.business.SheetResponse.
 * Use `create(SheetResponseSchema)` to create a new message.
 */
export const SheetResponseSchema: GenMessage<SheetResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 37);

/**
 * @generated from message com.stablemoney.api.business.UserFixedDepositSummaryRequest
 */
export type UserFixedDepositSummaryRequest =
  Message<"com.stablemoney.api.business.UserFixedDepositSummaryRequest"> & {
    /**
     * @generated from field: string user_id = 1;
     */
    userId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.UserFixedDepositSummaryRequest.
 * Use `create(UserFixedDepositSummaryRequestSchema)` to create a new message.
 */
export const UserFixedDepositSummaryRequestSchema: GenMessage<UserFixedDepositSummaryRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 38);

/**
 * @generated from message com.stablemoney.api.business.UserFixedDepositSummaryResponse
 */
export type UserFixedDepositSummaryResponse =
  Message<"com.stablemoney.api.business.UserFixedDepositSummaryResponse"> & {
    /**
     * @generated from field: double total_invested_amount = 1;
     */
    totalInvestedAmount: number;

    /**
     * @generated from field: double total_interest_earned = 2;
     */
    totalInterestEarned: number;

    /**
     * @generated from field: double current_amount = 3;
     */
    currentAmount: number;

    /**
     * @generated from field: optional com.stablemoney.api.business.EmergencyFundSummary emergency_fund_summary = 4;
     */
    emergencyFundSummary?: EmergencyFundSummary;

    /**
     * @generated from field: int32 active_term_deposit_count = 5;
     */
    activeTermDepositCount: number;

    /**
     * @generated from field: double in_progress_total = 6;
     */
    inProgressTotal: number;
  };

/**
 * Describes the message com.stablemoney.api.business.UserFixedDepositSummaryResponse.
 * Use `create(UserFixedDepositSummaryResponseSchema)` to create a new message.
 */
export const UserFixedDepositSummaryResponseSchema: GenMessage<UserFixedDepositSummaryResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 39);

/**
 * @generated from message com.stablemoney.api.business.WithdrawalCalculatorRequest
 */
export type WithdrawalCalculatorRequest =
  Message<"com.stablemoney.api.business.WithdrawalCalculatorRequest"> & {
    /**
     * @generated from field: string fd_id = 1;
     */
    fdId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.WithdrawalCalculatorRequest.
 * Use `create(WithdrawalCalculatorRequestSchema)` to create a new message.
 */
export const WithdrawalCalculatorRequestSchema: GenMessage<WithdrawalCalculatorRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 40);

/**
 * @generated from message com.stablemoney.api.business.WithdrawalCalculatorResponse
 */
export type WithdrawalCalculatorResponse =
  Message<"com.stablemoney.api.business.WithdrawalCalculatorResponse"> & {
    /**
     * @generated from field: double invested_amount = 1;
     */
    investedAmount: number;

    /**
     * @generated from field: double withdrawal_amount = 2;
     */
    withdrawalAmount: number;

    /**
     * @generated from field: double interest_earned = 3;
     */
    interestEarned: number;

    /**
     * @generated from field: double penalty_interest = 4;
     */
    penaltyInterest: number;

    /**
     * @generated from field: string fd_account_number = 5;
     */
    fdAccountNumber: string;

    /**
     * @generated from field: double applicable_interest_rate = 6;
     */
    applicableInterestRate: number;

    /**
     * @generated from field: double maturity_amount = 7;
     */
    maturityAmount: number;

    /**
     * @generated from field: double maturity_interest = 8;
     */
    maturityInterest: number;

    /**
     * @generated from field: string fd_active_days = 9;
     */
    fdActiveDays: string;

    /**
     * @generated from field: string booking_date = 10;
     */
    bookingDate: string;

    /**
     * @generated from field: string fd_booked_tenure = 11;
     */
    fdBookedTenure: string;

    /**
     * @generated from field: string bank_logo = 12;
     */
    bankLogo: string;

    /**
     * @generated from field: optional com.stablemoney.api.business.WithdrawalCreditBankDetails withdrawal_credit_bank_details = 13;
     */
    withdrawalCreditBankDetails?: WithdrawalCreditBankDetails;

    /**
     * @generated from field: optional com.stablemoney.api.identity.RedirectDeeplink redirect_deep_link = 14;
     */
    redirectDeepLink?: RedirectDeeplink;

    /**
     * @generated from field: map<string, string> withdrawal_metadata = 15;
     */
    withdrawalMetadata: { [key: string]: string };
  };

/**
 * Describes the message com.stablemoney.api.business.WithdrawalCalculatorResponse.
 * Use `create(WithdrawalCalculatorResponseSchema)` to create a new message.
 */
export const WithdrawalCalculatorResponseSchema: GenMessage<WithdrawalCalculatorResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 41);

/**
 * @generated from message com.stablemoney.api.business.WithdrawalCreditBankDetails
 */
export type WithdrawalCreditBankDetails =
  Message<"com.stablemoney.api.business.WithdrawalCreditBankDetails"> & {
    /**
     * @generated from field: string bank_name = 1;
     */
    bankName: string;

    /**
     * @generated from field: string account_number = 2;
     */
    accountNumber: string;

    /**
     * @generated from field: string payment_mode = 3;
     */
    paymentMode: string;

    /**
     * @generated from field: string credit_expected_date = 4;
     */
    creditExpectedDate: string;
  };

/**
 * Describes the message com.stablemoney.api.business.WithdrawalCreditBankDetails.
 * Use `create(WithdrawalCreditBankDetailsSchema)` to create a new message.
 */
export const WithdrawalCreditBankDetailsSchema: GenMessage<WithdrawalCreditBankDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 42);

/**
 * @generated from message com.stablemoney.api.business.WithdrawalRequest
 */
export type WithdrawalRequest =
  Message<"com.stablemoney.api.business.WithdrawalRequest"> & {
    /**
     * @generated from field: string fd_id = 1;
     */
    fdId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.WithdrawalRequest.
 * Use `create(WithdrawalRequestSchema)` to create a new message.
 */
export const WithdrawalRequestSchema: GenMessage<WithdrawalRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 43);

/**
 * @generated from message com.stablemoney.api.business.WithdrawalResponse
 */
export type WithdrawalResponse =
  Message<"com.stablemoney.api.business.WithdrawalResponse"> & {
    /**
     * @generated from field: string message = 1;
     */
    message: string;
  };

/**
 * Describes the message com.stablemoney.api.business.WithdrawalResponse.
 * Use `create(WithdrawalResponseSchema)` to create a new message.
 */
export const WithdrawalResponseSchema: GenMessage<WithdrawalResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDeposit, 44);

/**
 * @generated from enum com.stablemoney.api.business.FdProvider
 */
export enum FdProvider {
  /**
   * @generated from enum value: FD_PROVIDER_UNKNOWN = 0;
   */
  FD_PROVIDER_UNKNOWN = 0,

  /**
   * @generated from enum value: UPSWING = 1;
   */
  UPSWING = 1,

  /**
   * @generated from enum value: FINCARE = 2;
   */
  FINCARE = 2,

  /**
   * @generated from enum value: UNITY = 3;
   */
  UNITY = 3,

  /**
   * @generated from enum value: UJJIVAN = 4;
   */
  UJJIVAN = 4,

  /**
   * @generated from enum value: INDUSIND = 5;
   */
  INDUSIND = 5,

  /**
   * @generated from enum value: IDFC = 6;
   */
  IDFC = 6,

  /**
   * @generated from enum value: BANDHAN = 7;
   */
  BANDHAN = 7,
}

/**
 * Describes the enum com.stablemoney.api.business.FdProvider.
 */
export const FdProviderSchema: GenEnum<FdProvider> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FixedDeposit, 0);

/**
 * @generated from enum com.stablemoney.api.business.InvestmentStatus
 */
export enum InvestmentStatus {
  /**
   * Valid statuses in user investment
   *
   * @generated from enum value: INVESTMENT_STATUS_UNKNOWN = 0;
   */
  INVESTMENT_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: FD_INITIATE = 1;
   */
  FD_INITIATE = 1,

  /**
   * @generated from enum value: FD_ACCOUNT_CREATED = 2;
   */
  FD_ACCOUNT_CREATED = 2,

  /**
   * @generated from enum value: PRE_PAYMENT_VKYC_MAKER_SUCCESS = 3;
   */
  PRE_PAYMENT_VKYC_MAKER_SUCCESS = 3,

  /**
   * @generated from enum value: FD_REVIEW = 4;
   */
  FD_REVIEW = 4,

  /**
   * @generated from enum value: PAYMENT_FAILURE = 5;
   */
  PAYMENT_FAILURE = 5,

  /**
   * @generated from enum value: PAYMENT_SUCCESS = 6;
   */
  PAYMENT_SUCCESS = 6,

  /**
   * @generated from enum value: VKYC_PENDING = 7;
   */
  VKYC_PENDING = 7,

  /**
   * @generated from enum value: VKYC_FAILURE = 8;
   */
  VKYC_FAILURE = 8,

  /**
   * @generated from enum value: VKYC_MAKER_SUCCESS = 9;
   */
  VKYC_MAKER_SUCCESS = 9,

  /**
   * means VKYC was attempted
   *
   * @generated from enum value: VKYC_ATTEMPTED = 44;
   */
  VKYC_ATTEMPTED = 44,

  /**
   * @generated from enum value: VKYC_SUCCESS = 10;
   */
  VKYC_SUCCESS = 10,

  /**
   * @generated from enum value: BOOKING_SUCCESS = 11;
   */
  BOOKING_SUCCESS = 11,

  /**
   * @generated from enum value: WITHDRAWN = 12;
   */
  WITHDRAWN = 12,

  /**
   * @generated from enum value: MATURED = 13;
   */
  MATURED = 13,

  /**
   * @generated from enum value: FD_APPLY = 14;
   */
  FD_APPLY = 14,

  /**
   * @generated from enum value: REFUND = 49;
   */
  REFUND = 49,

  /**
   * @generated from enum value: DISCARDED = 50;
   */
  DISCARDED = 50,

  /**
   * Not persisted in user investment
   *
   * @generated from enum value: FD_ACCEPT_TERMS = 15;
   */
  FD_ACCEPT_TERMS = 15,

  /**
   * @generated from enum value: FD_CALCULATE = 16;
   */
  FD_CALCULATE = 16,

  /**
   * @generated from enum value: FD_VERIFY = 17;
   */
  FD_VERIFY = 17,

  /**
   * @generated from enum value: FD_PAYMENT_INITIATED = 18;
   */
  FD_PAYMENT_INITIATED = 18,

  /**
   * @generated from enum value: FD_PAYMENT_FAILED = 19;
   */
  FD_PAYMENT_FAILED = 19,

  /**
   * @generated from enum value: FD_PAYMENT_SUCCESS = 20;
   */
  FD_PAYMENT_SUCCESS = 20,

  /**
   * @generated from enum value: FD_PERSONAL_FORM = 21;
   */
  FD_PERSONAL_FORM = 21,

  /**
   * @generated from enum value: FD_PERSONAL_VERIFY = 22;
   */
  FD_PERSONAL_VERIFY = 22,

  /**
   * @generated from enum value: FD_VKYC = 23;
   */
  FD_VKYC = 23,

  /**
   * @generated from enum value: FD_VKYC_INTIATED = 24;
   */
  FD_VKYC_INTIATED = 24,

  /**
   * @generated from enum value: FD_VKYC_RESULT_PAGE = 25;
   */
  FD_VKYC_RESULT_PAGE = 25,

  /**
   * @generated from enum value: FD_PENDING_AUTHENTICATE = 26;
   */
  FD_PENDING_AUTHENTICATE = 26,

  /**
   * @generated from enum value: FD_WELCOME_BACK = 27;
   */
  FD_WELCOME_BACK = 27,

  /**
   * @generated from enum value: FD_ETB_PAYMENT_INITIATED = 28;
   */
  FD_ETB_PAYMENT_INITIATED = 28,

  /**
   * @generated from enum value: FD_ETB_CALCULATE = 29;
   */
  FD_ETB_CALCULATE = 29,

  /**
   * @generated from enum value: FD_ETB_PERSONAL_FORM = 30;
   */
  FD_ETB_PERSONAL_FORM = 30,

  /**
   * @generated from enum value: FD_ETB_PERSONAL_VERIFY = 31;
   */
  FD_ETB_PERSONAL_VERIFY = 31,

  /**
   * @generated from enum value: FD_ETB_FD_CHECK = 32;
   */
  FD_ETB_FD_CHECK = 32,

  /**
   * @generated from enum value: FD_ETB_PAYMENT_FAILED = 33;
   */
  FD_ETB_PAYMENT_FAILED = 33,

  /**
   * @generated from enum value: FD_ETB_PAYMENT_SUCCESS = 34;
   */
  FD_ETB_PAYMENT_SUCCESS = 34,

  /**
   * @generated from enum value: FD_ETB_PAYMENT_PENDING = 35;
   */
  FD_ETB_PAYMENT_PENDING = 35,

  /**
   * @generated from enum value: FD_ETB_PENDING_AUTHENTICATE = 36;
   */
  FD_ETB_PENDING_AUTHENTICATE = 36,

  /**
   * @generated from enum value: FD_ETB_WELCOME_BACK = 37;
   */
  FD_ETB_WELCOME_BACK = 37,

  /**
   * @generated from enum value: FD_EXISTING_FLOW_MESSAGE = 38;
   */
  FD_EXISTING_FLOW_MESSAGE = 38,

  /**
   * @generated from enum value: FD_APPLY_ETB = 39;
   */
  FD_APPLY_ETB = 39,

  /**
   * @generated from enum value: FD_AMOUNT_BEFORE_PAYMENT_INFO = 40;
   */
  FD_AMOUNT_BEFORE_PAYMENT_INFO = 40,

  /**
   * @generated from enum value: FD_ACC_CREATED_INFO = 41;
   */
  FD_ACC_CREATED_INFO = 41,

  /**
   * @generated from enum value: ETB_FD_ACCOUNT_CREATED = 42;
   */
  ETB_FD_ACCOUNT_CREATED = 42,

  /**
   * @generated from enum value: FD_PAYMENT_SUCCESS_INFO = 43;
   */
  FD_PAYMENT_SUCCESS_INFO = 43,

  /**
   * @generated from enum value: UJJIVAN_MAIN_PAGE = 45;
   */
  UJJIVAN_MAIN_PAGE = 45,

  /**
   * @generated from enum value: VKYC_LINK_COPIED = 46;
   */
  VKYC_LINK_COPIED = 46,

  /**
   * @generated from enum value: NTB_USER_EXISTS = 47;
   */
  NTB_USER_EXISTS = 47,

  /**
   * @generated from enum value: ETB_BY_MISTAKE_USER = 48;
   */
  ETB_BY_MISTAKE_USER = 48,

  /**
   * @generated from enum value: UTM_INGESTED = 51;
   */
  UTM_INGESTED = 51,

  /**
   * @generated from enum value: NTB_AUTH_ERROR = 52;
   */
  NTB_AUTH_ERROR = 52,

  /**
   * @generated from enum value: ETB_AUTH_ERROR = 53;
   */
  ETB_AUTH_ERROR = 53,

  /**
   * @generated from enum value: ETB_NO_PENDING_JOURNEY = 54;
   */
  ETB_NO_PENDING_JOURNEY = 54,

  /**
   * @generated from enum value: SM_AADHAAR_FAILED = 55;
   */
  SM_AADHAAR_FAILED = 55,

  /**
   * @generated from enum value: NTB_BY_MISTAKE_USER = 56;
   */
  NTB_BY_MISTAKE_USER = 56,
}

/**
 * Describes the enum com.stablemoney.api.business.InvestmentStatus.
 */
export const InvestmentStatusSchema: GenEnum<InvestmentStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FixedDeposit, 1);

/**
 * @generated from enum com.stablemoney.api.business.FdEvent
 */
export enum FdEvent {
  /**
   * @generated from enum value: FD_EVENT_UNKNOWN = 0;
   */
  FD_EVENT_UNKNOWN = 0,

  /**
   * @generated from enum value: HALF_KYC_EVENT = 1;
   */
  HALF_KYC_EVENT = 1,

  /**
   * @generated from enum value: FULL_KYC_EVENT = 2;
   */
  FULL_KYC_EVENT = 2,

  /**
   * @generated from enum value: FD_INITIATE_EVENT = 3;
   */
  FD_INITIATE_EVENT = 3,

  /**
   * @generated from enum value: PAYMENT_SUCCESS_EVENT = 4;
   */
  PAYMENT_SUCCESS_EVENT = 4,

  /**
   * @generated from enum value: PERSONAL_DETAILS_EVENT = 5;
   */
  PERSONAL_DETAILS_EVENT = 5,
}

/**
 * Describes the enum com.stablemoney.api.business.FdEvent.
 */
export const FdEventSchema: GenEnum<FdEvent> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FixedDeposit, 2);
