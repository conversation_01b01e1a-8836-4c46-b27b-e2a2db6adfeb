// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/Collection.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type {
  BankType,
  InterestPayoutType,
  InvestabilityRolloutStatus,
  InvestabilityStatus,
  InvestorType,
  RedirectDeeplink,
  TenureFormatType,
} from "./BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "./BusinessCommon_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/Collection.proto.
 */
export const file_public_models_business_Collection: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [file_public_models_business_BusinessCommon],
  );

/**
 * @generated from message com.stablemoney.api.identity.CollectionResponse
 */
export type CollectionResponse =
  Message<"com.stablemoney.api.identity.CollectionResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string title = 2;
     */
    title: string;

    /**
     * @generated from field: string description = 3;
     */
    description: string;

    /**
     * @generated from field: string short_title = 4;
     */
    shortTitle: string;

    /**
     * @generated from field: string short_description = 5;
     */
    shortDescription: string;

    /**
     * @generated from field: string icon_url = 6;
     */
    iconUrl: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.CollectionItem collection_item = 7;
     */
    collectionItem: CollectionItem[];

    /**
     * @generated from field: int32 total_count = 8;
     */
    totalCount: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.SortOption sort_options = 9;
     */
    sortOptions: SortOption[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FilterByInstitutionTypeOption filter_by_institution_type_options = 10;
     */
    filterByInstitutionTypeOptions: FilterByInstitutionTypeOption[];

    /**
     * @generated from field: com.stablemoney.api.identity.CollectionWidgetType collection_widget_type = 11;
     */
    collectionWidgetType: CollectionWidgetType;
  };

/**
 * Describes the message com.stablemoney.api.identity.CollectionResponse.
 * Use `create(CollectionResponseSchema)` to create a new message.
 */
export const CollectionResponseSchema: GenMessage<CollectionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 0);

/**
 * @generated from message com.stablemoney.api.identity.SortOption
 */
export type SortOption = Message<"com.stablemoney.api.identity.SortOption"> & {
  /**
   * @generated from field: string display_text = 1;
   */
  displayText: string;

  /**
   * @generated from field: bool is_selected = 2;
   */
  isSelected: boolean;

  /**
   * @generated from field: string sort_key = 3;
   */
  sortKey: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SortOption.
 * Use `create(SortOptionSchema)` to create a new message.
 */
export const SortOptionSchema: GenMessage<SortOption> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 1);

/**
 * @generated from message com.stablemoney.api.identity.FilterByInstitutionTypeOption
 */
export type FilterByInstitutionTypeOption =
  Message<"com.stablemoney.api.identity.FilterByInstitutionTypeOption"> & {
    /**
     * @generated from field: string display_text = 1;
     */
    displayText: string;

    /**
     * @generated from field: bool is_selected = 2;
     */
    isSelected: boolean;

    /**
     * @generated from field: string filter_key = 3;
     */
    filterKey: string;

    /**
     * @generated from field: int64 count = 4;
     */
    count: bigint;
  };

/**
 * Describes the message com.stablemoney.api.identity.FilterByInstitutionTypeOption.
 * Use `create(FilterByInstitutionTypeOptionSchema)` to create a new message.
 */
export const FilterByInstitutionTypeOptionSchema: GenMessage<FilterByInstitutionTypeOption> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 2);

/**
 * @generated from message com.stablemoney.api.identity.CollectionItem
 */
export type CollectionItem =
  Message<"com.stablemoney.api.identity.CollectionItem"> & {
    /**
     * @generated from field: string description = 1;
     */
    description: string;

    /**
     * @generated from oneof com.stablemoney.api.identity.CollectionItem.item
     */
    item:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.FixedDepositResponse fixed_deposit = 2;
           */
          value: FixedDepositResponse;
          case: "fixedDeposit";
        }
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.BankResponseWithFdData bank_response_with_fd_data = 3;
           */
          value: BankResponseWithFdData;
          case: "bankResponseWithFdData";
        }
      | { case: undefined; value?: undefined };

    /**
     * @generated from field: com.stablemoney.api.identity.Tag tag = 4 [deprecated = true];
     * @deprecated
     */
    tag?: Tag;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink deeplink = 6;
     */
    deeplink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.identity.CollectionItem.
 * Use `create(CollectionItemSchema)` to create a new message.
 */
export const CollectionItemSchema: GenMessage<CollectionItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 3);

/**
 * @generated from message com.stablemoney.api.identity.Tag
 */
export type Tag = Message<"com.stablemoney.api.identity.Tag"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string color = 2;
   */
  color: string;

  /**
   * @generated from field: string background_color = 3;
   */
  backgroundColor: string;

  /**
   * @generated from field: double width = 4;
   */
  width: number;
};

/**
 * Describes the message com.stablemoney.api.identity.Tag.
 * Use `create(TagSchema)` to create a new message.
 */
export const TagSchema: GenMessage<Tag> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 4);

/**
 * @generated from message com.stablemoney.api.identity.FixedDepositResponse
 */
export type FixedDepositResponse =
  Message<"com.stablemoney.api.identity.FixedDepositResponse"> & {
    /**
     * @generated from field: string fd_id = 1;
     */
    fdId: string;

    /**
     * @generated from field: string raw_tenure = 2;
     */
    rawTenure: string;

    /**
     * @generated from field: com.stablemoney.api.identity.TenureFormatType tenure_format_type = 3;
     */
    tenureFormatType: TenureFormatType;

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 4;
     */
    bank?: BankResponse;

    /**
     * @generated from field: double rate = 5;
     */
    rate: number;

    /**
     * @generated from field: double annualized_rate = 6;
     */
    annualizedRate: number;

    /**
     * @generated from field: double min_deposit = 7;
     */
    minDeposit: number;

    /**
     * @generated from field: double max_deposit = 8;
     */
    maxDeposit: number;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestorType investor_type = 10;
     */
    investorType: InvestorType;

    /**
     * @generated from field: int32 lock_in_period_in_days = 11;
     */
    lockInPeriodInDays: number;

    /**
     * @generated from field: bool is_pre_mature_withdrawal_allowed = 12;
     */
    isPreMatureWithdrawalAllowed: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.InterestPayoutType interest_payout_type = 14;
     */
    interestPayoutType: InterestPayoutType;

    /**
     * @generated from field: string breakage_charges_and_description = 15;
     */
    breakageChargesAndDescription: string;

    /**
     * @generated from field: bool is_loan_against_fd_allowed = 16;
     */
    isLoanAgainstFdAllowed: boolean;

    /**
     * @generated from field: bool is_partial_withdrawal_allowed = 17;
     */
    isPartialWithdrawalAllowed: boolean;

    /**
     * @generated from field: int32 min_tenure_in_days = 18;
     */
    minTenureInDays: number;

    /**
     * @generated from field: int32 max_tenure_in_days = 19;
     */
    maxTenureInDays: number;

    /**
     * @generated from field: int32 in_denomination_of = 20;
     */
    inDenominationOf: number;

    /**
     * @generated from field: bool is_tax_saving = 21;
     */
    isTaxSaving: boolean;

    /**
     * @generated from field: string tenure = 22;
     */
    tenure: string;

    /**
     * @generated from field: int32 tenure_in_days = 23;
     */
    tenureInDays: number;

    /**
     * @generated from field: int32 tenure_in_months = 24;
     */
    tenureInMonths: number;

    /**
     * @generated from field: int32 tenure_in_years = 25;
     */
    tenureInYears: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.MaturityInstruction maturity_instructions = 26;
     */
    maturityInstructions: MaturityInstruction[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InterestPayoutType interest_payout_types = 27;
     */
    interestPayoutTypes: InterestPayoutType[];

    /**
     * @generated from field: string tag = 28;
     */
    tag: string;

    /**
     * @generated from field: double xirr = 29;
     */
    xirr: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.FixedDepositResponse.
 * Use `create(FixedDepositResponseSchema)` to create a new message.
 */
export const FixedDepositResponseSchema: GenMessage<FixedDepositResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 5);

/**
 * @generated from message com.stablemoney.api.identity.BankResponse
 */
export type BankResponse =
  Message<"com.stablemoney.api.identity.BankResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string logo_url = 3;
     */
    logoUrl: string;

    /**
     * @generated from field: string website_url = 4;
     */
    websiteUrl: string;

    /**
     * @generated from field: bool is_insured = 5;
     */
    isInsured: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.BankType bank_type = 6;
     */
    bankType: BankType;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestabilityStatus investability_status = 7;
     */
    investabilityStatus: InvestabilityStatus;

    /**
     * @generated from field: bool is_popular = 8;
     */
    isPopular: boolean;

    /**
     * @generated from field: string operating_bank_id = 9;
     */
    operatingBankId: string;

    /**
     * @generated from field: string customer_care_number = 10;
     */
    customerCareNumber: string;

    /**
     * @generated from field: int32 establishment_year = 11;
     */
    establishmentYear: number;

    /**
     * @generated from field: string rbi_license_url = 12;
     */
    rbiLicenseUrl: string;

    /**
     * @generated from field: string display_name = 13;
     */
    displayName: string;

    /**
     * @generated from field: string color = 14;
     */
    color: string;

    /**
     * @generated from field: com.stablemoney.api.identity.TagConfig tag_config = 15;
     */
    tagConfig?: TagConfig;

    /**
     * @generated from field: string cover_image_url = 16;
     */
    coverImageUrl: string;

    /**
     * @generated from field: string icon_bg_color = 17;
     */
    iconBgColor: string;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestabilityRolloutStatus investability_rollout_status = 18;
     */
    investabilityRolloutStatus: InvestabilityRolloutStatus;

    /**
     * @generated from field: map<string, string> properties = 20;
     */
    properties: { [key: string]: string };

    /**
     * @generated from field: string short_name = 22;
     */
    shortName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankResponse.
 * Use `create(BankResponseSchema)` to create a new message.
 */
export const BankResponseSchema: GenMessage<BankResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 6);

/**
 * @generated from message com.stablemoney.api.identity.TagConfig
 */
export type TagConfig = Message<"com.stablemoney.api.identity.TagConfig"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string icon_url = 2;
   */
  iconUrl: string;

  /**
   * @generated from field: string color = 3;
   */
  color: string;

  /**
   * @generated from field: string bg_color = 4;
   */
  bgColor: string;

  /**
   * @generated from field: string type = 5;
   */
  type: string;
};

/**
 * Describes the message com.stablemoney.api.identity.TagConfig.
 * Use `create(TagConfigSchema)` to create a new message.
 */
export const TagConfigSchema: GenMessage<TagConfig> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 7);

/**
 * @generated from message com.stablemoney.api.identity.BankResponseWithFdData
 */
export type BankResponseWithFdData =
  Message<"com.stablemoney.api.identity.BankResponseWithFdData"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 1;
     */
    bank?: BankResponse;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FixedDepositResponse fds_for_bank_card = 2;
     */
    fdsForBankCard: FixedDepositResponse[];

    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse highest_interest_rate_fd = 3;
     */
    highestInterestRateFd?: FixedDepositResponse;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankResponseWithFdData.
 * Use `create(BankResponseWithFdDataSchema)` to create a new message.
 */
export const BankResponseWithFdDataSchema: GenMessage<BankResponseWithFdData> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 8);

/**
 * @generated from message com.stablemoney.api.identity.BulkCollectionsResponse
 */
export type BulkCollectionsResponse =
  Message<"com.stablemoney.api.identity.BulkCollectionsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.CollectionResponse collection = 1;
     */
    collection: CollectionResponse[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BulkCollectionsResponse.
 * Use `create(BulkCollectionsResponseSchema)` to create a new message.
 */
export const BulkCollectionsResponseSchema: GenMessage<BulkCollectionsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 9);

/**
 * @generated from message com.stablemoney.api.identity.AllCollectionsInfo
 */
export type AllCollectionsInfo =
  Message<"com.stablemoney.api.identity.AllCollectionsInfo"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.CollectionResponse collection = 1;
     */
    collection: CollectionResponse[];
  };

/**
 * Describes the message com.stablemoney.api.identity.AllCollectionsInfo.
 * Use `create(AllCollectionsInfoSchema)` to create a new message.
 */
export const AllCollectionsInfoSchema: GenMessage<AllCollectionsInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 10);

/**
 * @generated from message com.stablemoney.api.identity.UserOngoingBookingStatusResponse
 */
export type UserOngoingBookingStatusResponse =
  Message<"com.stablemoney.api.identity.UserOngoingBookingStatusResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.OngoingBookingStatusData ongoing_booking_status = 1;
     */
    ongoingBookingStatus: OngoingBookingStatusData[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UserOngoingBookingStatusResponse.
 * Use `create(UserOngoingBookingStatusResponseSchema)` to create a new message.
 */
export const UserOngoingBookingStatusResponseSchema: GenMessage<UserOngoingBookingStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 11);

/**
 * @generated from message com.stablemoney.api.identity.OngoingBookingStatusData
 */
export type OngoingBookingStatusData =
  Message<"com.stablemoney.api.identity.OngoingBookingStatusData"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string sub_title = 2;
     */
    subTitle: string;

    /**
     * @generated from field: bool is_agents_active = 3;
     */
    isAgentsActive: boolean;

    /**
     * @generated from field: string agent_title = 4;
     */
    agentTitle: string;

    /**
     * @generated from field: string agent_sub_title = 5;
     */
    agentSubTitle: string;

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 6;
     */
    bank?: BankResponse;

    /**
     * @generated from field: string cta = 7;
     */
    cta: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 8;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: com.stablemoney.api.identity.OngoingBookingStatus state = 9;
     */
    state: OngoingBookingStatus;

    /**
     * @generated from field: string payment_success_time = 10;
     */
    paymentSuccessTime: string;

    /**
     * @generated from field: string latest_status = 11;
     */
    latestStatus: string;

    /**
     * @generated from field: string message = 12;
     */
    message: string;

    /**
     * @generated from field: double amount = 13;
     */
    amount: number;

    /**
     * @generated from field: double interest_rate = 14;
     */
    interestRate: number;

    /**
     * @generated from field: string tenure_days = 15;
     */
    tenureDays: string;

    /**
     * @generated from field: string distinct_id = 16;
     */
    distinctId: string;

    /**
     * @generated from field: bool is_withdrawal_card = 17;
     */
    isWithdrawalCard: boolean;

    /**
     * @generated from field: string withdrawal_date = 18;
     */
    withdrawalDate: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.OngoingBookingStatusData.
 * Use `create(OngoingBookingStatusDataSchema)` to create a new message.
 */
export const OngoingBookingStatusDataSchema: GenMessage<OngoingBookingStatusData> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 12);

/**
 * @generated from message com.stablemoney.api.identity.OngoingBookingStatusDataV2
 */
export type OngoingBookingStatusDataV2 =
  Message<"com.stablemoney.api.identity.OngoingBookingStatusDataV2"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string sub_title = 2;
     */
    subTitle: string;

    /**
     * @generated from field: bool is_agents_active = 3;
     */
    isAgentsActive: boolean;

    /**
     * @generated from field: string agent_title = 4;
     */
    agentTitle: string;

    /**
     * @generated from field: string agent_sub_title = 5;
     */
    agentSubTitle: string;

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 6;
     */
    bank?: BankResponse;

    /**
     * @generated from field: com.stablemoney.api.identity.OngoingBookingStatus state = 7;
     */
    state: OngoingBookingStatus;

    /**
     * @generated from field: string payment_success_time = 8;
     */
    paymentSuccessTime: string;

    /**
     * @generated from field: optional string primary_cta = 9;
     */
    primaryCta?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.RedirectDeeplink primary_redirect_deeplink = 10;
     */
    primaryRedirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: optional string secondary_cta = 11;
     */
    secondaryCta?: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.RedirectDeeplink secondary_redirect_deeplink = 12;
     */
    secondaryRedirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: optional string message = 13;
     */
    message?: string;

    /**
     * @generated from field: string bank_id = 14;
     */
    bankId: string;

    /**
     * @generated from field: double amount = 15;
     */
    amount: number;

    /**
     * @generated from field: double interest_rate = 16;
     */
    interestRate: number;

    /**
     * @generated from field: string tenure_days = 17;
     */
    tenureDays: string;

    /**
     * @generated from field: string tag = 18;
     */
    tag: string;

    /**
     * @generated from field: string tag_color = 19;
     */
    tagColor: string;

    /**
     * @generated from field: string distinct_id = 20;
     */
    distinctId: string;

    /**
     * @generated from field: bool is_withdrawal_card = 21;
     */
    isWithdrawalCard: boolean;

    /**
     * @generated from field: string withdrawal_date = 22;
     */
    withdrawalDate: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.OngoingBookingStatusDataV2.
 * Use `create(OngoingBookingStatusDataV2Schema)` to create a new message.
 */
export const OngoingBookingStatusDataV2Schema: GenMessage<OngoingBookingStatusDataV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 13);

/**
 * @generated from message com.stablemoney.api.identity.UserOngoingBookingStatusResponseV2
 */
export type UserOngoingBookingStatusResponseV2 =
  Message<"com.stablemoney.api.identity.UserOngoingBookingStatusResponseV2"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.OngoingBookingStatusDataV2 ongoing_booking_status = 1;
     */
    ongoingBookingStatus: OngoingBookingStatusDataV2[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UserOngoingBookingStatusResponseV2.
 * Use `create(UserOngoingBookingStatusResponseV2Schema)` to create a new message.
 */
export const UserOngoingBookingStatusResponseV2Schema: GenMessage<UserOngoingBookingStatusResponseV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 14);

/**
 * @generated from message com.stablemoney.api.identity.PaymentFailureItem
 */
export type PaymentFailureItem =
  Message<"com.stablemoney.api.identity.PaymentFailureItem"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: double amount = 2;
     */
    amount: number;

    /**
     * @generated from field: optional string bank_name = 3;
     */
    bankName?: string;

    /**
     * @generated from field: optional string bank_id = 4;
     */
    bankId?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.PaymentFailureItem.
 * Use `create(PaymentFailureItemSchema)` to create a new message.
 */
export const PaymentFailureItemSchema: GenMessage<PaymentFailureItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 15);

/**
 * @generated from message com.stablemoney.api.identity.PaymentFailureCardResponse
 */
export type PaymentFailureCardResponse =
  Message<"com.stablemoney.api.identity.PaymentFailureCardResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.PaymentFailureItem paymentFailureItemList = 1;
     */
    paymentFailureItemList: PaymentFailureItem[];

    /**
     * @generated from field: bool hasPaymentSuccess = 2;
     */
    hasPaymentSuccess: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.PaymentFailureCardResponse.
 * Use `create(PaymentFailureCardResponseSchema)` to create a new message.
 */
export const PaymentFailureCardResponseSchema: GenMessage<PaymentFailureCardResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 16);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalData
 */
export type WithdrawalData =
  Message<"com.stablemoney.api.identity.WithdrawalData"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: com.stablemoney.api.identity.UserCancellationAction user_cancellation_action = 2;
     */
    userCancellationAction: UserCancellationAction;

    /**
     * @generated from field: com.stablemoney.api.identity.WithdrawalStatus withdrawal_status = 3;
     */
    withdrawalStatus: WithdrawalStatus;

    /**
     * @generated from field: string withdrawal_time = 4;
     */
    withdrawalTime: string;

    /**
     * @generated from field: string withdrawal_cancellation_deadline = 5;
     */
    withdrawalCancellationDeadline: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.WithdrawalTimeLineNode withdrawal_time_line = 6;
     */
    withdrawalTimeLine: WithdrawalTimeLineNode[];

    /**
     * @generated from field: string bottom_info_text = 7;
     */
    bottomInfoText: string;

    /**
     * @generated from field: bool has_money_credited = 8;
     */
    hasMoneyCredited: boolean;

    /**
     * @generated from field: bool has_answered_cancellation_questions = 9;
     */
    hasAnsweredCancellationQuestions: boolean;

    /**
     * @generated from field: string withdrawal_reason = 10;
     */
    withdrawalReason: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalData.
 * Use `create(WithdrawalDataSchema)` to create a new message.
 */
export const WithdrawalDataSchema: GenMessage<WithdrawalData> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 17);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalTimeLineNode
 */
export type WithdrawalTimeLineNode =
  Message<"com.stablemoney.api.identity.WithdrawalTimeLineNode"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string sub_title = 2;
     */
    subTitle: string;

    /**
     * @generated from field: bool is_completed = 3;
     */
    isCompleted: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalTimeLineNode.
 * Use `create(WithdrawalTimeLineNodeSchema)` to create a new message.
 */
export const WithdrawalTimeLineNodeSchema: GenMessage<WithdrawalTimeLineNode> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 18);

/**
 * @generated from message com.stablemoney.api.identity.SubmitWithdrawalReasonRequest
 */
export type SubmitWithdrawalReasonRequest =
  Message<"com.stablemoney.api.identity.SubmitWithdrawalReasonRequest"> & {
    /**
     * @generated from field: string withdrawal_id = 1;
     */
    withdrawalId: string;

    /**
     * @generated from field: string reason = 2;
     */
    reason: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SubmitWithdrawalReasonRequest.
 * Use `create(SubmitWithdrawalReasonRequestSchema)` to create a new message.
 */
export const SubmitWithdrawalReasonRequestSchema: GenMessage<SubmitWithdrawalReasonRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 19);

/**
 * @generated from message com.stablemoney.api.identity.SubmitWithdrawalReasonResponse
 */
export type SubmitWithdrawalReasonResponse =
  Message<"com.stablemoney.api.identity.SubmitWithdrawalReasonResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SubmitWithdrawalReasonResponse.
 * Use `create(SubmitWithdrawalReasonResponseSchema)` to create a new message.
 */
export const SubmitWithdrawalReasonResponseSchema: GenMessage<SubmitWithdrawalReasonResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 20);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalCancellationRequest
 * @deprecated
 */
export type WithdrawalCancellationRequest =
  Message<"com.stablemoney.api.identity.WithdrawalCancellationRequest"> & {
    /**
     * @generated from field: string withdrawal_id = 1;
     */
    withdrawalId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.WithdrawalCancellationQuestionAndAnswer qa = 2;
     */
    qa: WithdrawalCancellationQuestionAndAnswer[];
  };

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalCancellationRequest.
 * Use `create(WithdrawalCancellationRequestSchema)` to create a new message.
 * @deprecated
 */
export const WithdrawalCancellationRequestSchema: GenMessage<WithdrawalCancellationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 21);

/**
 * @generated from message com.stablemoney.api.identity.UserWithdrawalCancellationActionRequest
 */
export type UserWithdrawalCancellationActionRequest =
  Message<"com.stablemoney.api.identity.UserWithdrawalCancellationActionRequest"> & {
    /**
     * @generated from field: string withdrawal_id = 1;
     */
    withdrawalId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.UserCancellationAction user_cancellation_action = 2;
     */
    userCancellationAction: UserCancellationAction;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserWithdrawalCancellationActionRequest.
 * Use `create(UserWithdrawalCancellationActionRequestSchema)` to create a new message.
 */
export const UserWithdrawalCancellationActionRequestSchema: GenMessage<UserWithdrawalCancellationActionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 22);

/**
 * @generated from message com.stablemoney.api.identity.UserWithdrawalCancellationActionResponse
 */
export type UserWithdrawalCancellationActionResponse =
  Message<"com.stablemoney.api.identity.UserWithdrawalCancellationActionResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.UserWithdrawalCancellationActionResponse.
 * Use `create(UserWithdrawalCancellationActionResponseSchema)` to create a new message.
 */
export const UserWithdrawalCancellationActionResponseSchema: GenMessage<UserWithdrawalCancellationActionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 23);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalCancellationQuestionAndAnswer
 */
export type WithdrawalCancellationQuestionAndAnswer =
  Message<"com.stablemoney.api.identity.WithdrawalCancellationQuestionAndAnswer"> & {
    /**
     * @generated from field: string question = 1;
     */
    question: string;

    /**
     * @generated from field: string answer = 2;
     */
    answer: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalCancellationQuestionAndAnswer.
 * Use `create(WithdrawalCancellationQuestionAndAnswerSchema)` to create a new message.
 */
export const WithdrawalCancellationQuestionAndAnswerSchema: GenMessage<WithdrawalCancellationQuestionAndAnswer> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 24);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalCancellationResponse
 * @deprecated
 */
export type WithdrawalCancellationResponse =
  Message<"com.stablemoney.api.identity.WithdrawalCancellationResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalCancellationResponse.
 * Use `create(WithdrawalCancellationResponseSchema)` to create a new message.
 * @deprecated
 */
export const WithdrawalCancellationResponseSchema: GenMessage<WithdrawalCancellationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 25);

/**
 * @generated from message com.stablemoney.api.identity.BankDetails
 */
export type BankDetails =
  Message<"com.stablemoney.api.identity.BankDetails"> & {
    /**
     * @generated from field: string bankId = 1;
     */
    bankId: string;

    /**
     * @generated from field: string logo = 2;
     */
    logo: string;

    /**
     * @generated from field: string bank_name = 3;
     */
    bankName: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankDetails.
 * Use `create(BankDetailsSchema)` to create a new message.
 */
export const BankDetailsSchema: GenMessage<BankDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 26);

/**
 * @generated from message com.stablemoney.api.identity.LocationBasedRecommendedFd
 */
export type LocationBasedRecommendedFd =
  Message<"com.stablemoney.api.identity.LocationBasedRecommendedFd"> & {
    /**
     * @generated from field: double rate = 1;
     */
    rate: number;

    /**
     * @generated from field: string tenure = 2;
     */
    tenure: string;

    /**
     * @generated from field: com.stablemoney.api.identity.BankDetails bank_details = 3;
     */
    bankDetails?: BankDetails;
  };

/**
 * Describes the message com.stablemoney.api.identity.LocationBasedRecommendedFd.
 * Use `create(LocationBasedRecommendedFdSchema)` to create a new message.
 */
export const LocationBasedRecommendedFdSchema: GenMessage<LocationBasedRecommendedFd> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Collection, 27);

/**
 * @generated from enum com.stablemoney.api.identity.CollectionDataType
 */
export enum CollectionDataType {
  /**
   * @generated from enum value: UNKNOWN_COLLECTION_DATA_TYPE = 0;
   */
  UNKNOWN_COLLECTION_DATA_TYPE = 0,

  /**
   * @generated from enum value: AGGREGATED_BANK_DATA = 1;
   */
  AGGREGATED_BANK_DATA = 1,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE = 2;
   */
  HIGHEST_INTEREST_RATE = 2,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_SHORT_TERM = 3;
   */
  HIGHEST_INTEREST_RATE_SHORT_TERM = 3,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_MEDIUM_TERM = 4;
   */
  HIGHEST_INTEREST_RATE_MEDIUM_TERM = 4,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_LONG_TERM = 5;
   */
  HIGHEST_INTEREST_RATE_LONG_TERM = 5,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_TAX_SAVING = 6;
   */
  HIGHEST_INTEREST_RATE_TAX_SAVING = 6,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA = 7;
   */
  HIGHEST_INTEREST_RATE_1_YEAR_WITH_DELTA = 7,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA = 8;
   */
  HIGHEST_INTEREST_RATE_2_YEAR_WITH_DELTA = 8,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA = 9;
   */
  HIGHEST_INTEREST_RATE_3_YEAR_WITH_DELTA = 9,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA = 10;
   */
  HIGHEST_INTEREST_RATE_4_YEAR_WITH_DELTA = 10,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA = 11;
   */
  HIGHEST_INTEREST_RATE_5_YEAR_WITH_DELTA = 11,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA = 12;
   */
  HIGHEST_INTEREST_RATE_1_YEAR_WITHOUT_DELTA = 12,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA = 13;
   */
  HIGHEST_INTEREST_RATE_2_YEAR_WITHOUT_DELTA = 13,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA = 14;
   */
  HIGHEST_INTEREST_RATE_3_YEAR_WITHOUT_DELTA = 14,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA = 15;
   */
  HIGHEST_INTEREST_RATE_4_YEAR_WITHOUT_DELTA = 15,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA = 16;
   */
  HIGHEST_INTEREST_RATE_5_YEAR_WITHOUT_DELTA = 16,

  /**
   * @generated from enum value: HIGHEST_INTEREST_SPECIAL_RATE_FD = 17;
   */
  HIGHEST_INTEREST_SPECIAL_RATE_FD = 17,

  /**
   * @generated from enum value: HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD = 18;
   */
  HIGHEST_INTEREST_ZERO_LOCK_IN_PERIOD_FD = 18,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_FOR_WOMEN_FD = 19;
   */
  HIGHEST_INTEREST_RATE_FOR_WOMEN_FD = 19,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD = 20;
   */
  HIGHEST_INTEREST_RATE_FOR_SENIOR_CITIZEN_FD = 20,

  /**
   * @generated from enum value: HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN = 21;
   */
  HIGHEST_INTEREST_RATE_FOR_GENERAL_CITIZEN = 21,
}

/**
 * Describes the enum com.stablemoney.api.identity.CollectionDataType.
 */
export const CollectionDataTypeSchema: GenEnum<CollectionDataType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 0);

/**
 * @generated from enum com.stablemoney.api.identity.SortTypes
 */
export enum SortTypes {
  /**
   * @generated from enum value: UNKNOWN_SORT_TYPE = 0;
   */
  UNKNOWN_SORT_TYPE = 0,

  /**
   * @generated from enum value: INTEREST_RATE_SORT = 1;
   */
  INTEREST_RATE_SORT = 1,

  /**
   * @generated from enum value: DEFAULT_SORT = 2;
   */
  DEFAULT_SORT = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.SortTypes.
 */
export const SortTypesSchema: GenEnum<SortTypes> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 1);

/**
 * @generated from enum com.stablemoney.api.identity.MaturityInstruction
 */
export enum MaturityInstruction {
  /**
   * @generated from enum value: UNKNOWN_MATURITY_INSTRUCTION = 0;
   */
  UNKNOWN_MATURITY_INSTRUCTION = 0,

  /**
   * @generated from enum value: AUTO_RENEWAL_WITH_MATURITY_AMOUNT = 1;
   */
  AUTO_RENEWAL_WITH_MATURITY_AMOUNT = 1,

  /**
   * @generated from enum value: AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT = 2;
   */
  AUTO_RENEWAL_WITH_PRINCIPAL_AMOUNT = 2,

  /**
   * @generated from enum value: CLOSE_ACCOUNT = 3;
   */
  CLOSE_ACCOUNT = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.MaturityInstruction.
 */
export const MaturityInstructionSchema: GenEnum<MaturityInstruction> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 2);

/**
 * @generated from enum com.stablemoney.api.identity.CollectionDataStrategyType
 */
export enum CollectionDataStrategyType {
  /**
   * @generated from enum value: UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE = 0;
   */
  UNKNOWN_COLLECTION_DATA_STRATEGY_TYPE = 0,

  /**
   * @generated from enum value: SIMPLE_COLLECTION_DATA_STRATEGY = 1;
   */
  SIMPLE_COLLECTION_DATA_STRATEGY = 1,

  /**
   * @generated from enum value: AGGREGATED_COLLECTION_DATA_STRATEGY = 2;
   */
  AGGREGATED_COLLECTION_DATA_STRATEGY = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.CollectionDataStrategyType.
 */
export const CollectionDataStrategyTypeSchema: GenEnum<CollectionDataStrategyType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 3);

/**
 * @generated from enum com.stablemoney.api.identity.CollectionWidgetType
 */
export enum CollectionWidgetType {
  /**
   * @generated from enum value: UNKNOWN_WIDGET_TYPE = 0;
   */
  UNKNOWN_WIDGET_TYPE = 0,

  /**
   * @generated from enum value: FD_CARD = 1;
   */
  FD_CARD = 1,

  /**
   * @generated from enum value: BANK_CARD = 2;
   */
  BANK_CARD = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.CollectionWidgetType.
 */
export const CollectionWidgetTypeSchema: GenEnum<CollectionWidgetType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 4);

/**
 * @generated from enum com.stablemoney.api.identity.OngoingBookingStatus
 */
export enum OngoingBookingStatus {
  /**
   * @generated from enum value: UNKNOWN_BOOKING_STATUS = 0;
   */
  UNKNOWN_BOOKING_STATUS = 0,

  /**
   * @generated from enum value: VKYC_PENDING_STATUS = 1;
   */
  VKYC_PENDING_STATUS = 1,

  /**
   * @generated from enum value: BOOKING_IN_PROGRESS = 2;
   */
  BOOKING_IN_PROGRESS = 2,

  /**
   * @generated from enum value: VKYC_SUCCESS_STATUS = 3;
   */
  VKYC_SUCCESS_STATUS = 3,

  /**
   * @generated from enum value: RESUME_JOURNEY = 4;
   */
  RESUME_JOURNEY = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.OngoingBookingStatus.
 */
export const OngoingBookingStatusSchema: GenEnum<OngoingBookingStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 5);

/**
 * @generated from enum com.stablemoney.api.identity.UserCancellationAction
 */
export enum UserCancellationAction {
  /**
   * @generated from enum value: UNKNOWN_CANCELLATION_ACTION = 0;
   */
  UNKNOWN_CANCELLATION_ACTION = 0,

  /**
   * @generated from enum value: NO_ACTION = 1;
   */
  NO_ACTION = 1,

  /**
   * @generated from enum value: CANCEL_CANCELLATION = 2;
   */
  CANCEL_CANCELLATION = 2,

  /**
   * @generated from enum value: DO_NOT_CANCEL_CANCEL_CANCELLATION = 3;
   */
  DO_NOT_CANCEL_CANCEL_CANCELLATION = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.UserCancellationAction.
 */
export const UserCancellationActionSchema: GenEnum<UserCancellationAction> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 6);

/**
 * @generated from enum com.stablemoney.api.identity.WithdrawalStatus
 */
export enum WithdrawalStatus {
  /**
   * @generated from enum value: UNKNOWN_WITHDRAWAL_STATUS = 0;
   */
  UNKNOWN_WITHDRAWAL_STATUS = 0,

  /**
   * @generated from enum value: WITHDRAWAL_INITIATED = 1;
   */
  WITHDRAWAL_INITIATED = 1,

  /**
   * @generated from enum value: WITHDRAWAL_IN_PROCESSING = 2;
   */
  WITHDRAWAL_IN_PROCESSING = 2,

  /**
   * @generated from enum value: WITHDRAWAL_PROCESSED = 3;
   */
  WITHDRAWAL_PROCESSED = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.WithdrawalStatus.
 */
export const WithdrawalStatusSchema: GenEnum<WithdrawalStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Collection, 7);
