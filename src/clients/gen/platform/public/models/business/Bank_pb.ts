// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/Bank.proto (package com.stablemoney.api.bank, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { RedirectDeeplink } from "./BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "./BusinessCommon_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/Bank.proto.
 */
export const file_public_models_business_Bank: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiFwdWJsaWMvbW9kZWxzL2J1c2luZXNzL0JhbmsucHJvdG8SGGNvbS5zdGFibGVtb25leS5hcGkuYmFuayJ9CgRCYW5rEgoKAmlkGAEgASgJEgwKBG5hbWUYAiABKAkSEAoIbG9nb191cmwYAyABKAkSSQoRcmVkaXJlY3RfZGVlcGxpbmsYBCABKAsyLi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlJlZGlyZWN0RGVlcGxpbmsiiwEKGEFnZW50QXZhaWxhYmlsaXR5RGV0YWlscxIUCgxpc19hdmFpbGFibGUYASABKAgSKAogYXZhaWxhYmlsaXR5X3RpbWluZ3NfZGVzY3JpcHRpb24YAiABKAkSEgoKaXNfaG9saWRheRgDIAEoCBIbChNob2xpZGF5X2Rlc2NyaXB0aW9uGAQgASgJIi8KE1ZreWNFeHBpcnlUaW1lc3RhbXASGAoQdmt5Y19leHBpcnlfZGF0ZRgBIAEoCUIcChhjb20uc3RhYmxlbW9uZXkuYXBpLmJhbmtQAWIGcHJvdG8z",
    [file_public_models_business_BusinessCommon],
  );

/**
 * @generated from message com.stablemoney.api.bank.Bank
 */
export type Bank = Message<"com.stablemoney.api.bank.Bank"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string name = 2;
   */
  name: string;

  /**
   * @generated from field: string logo_url = 3;
   */
  logoUrl: string;

  /**
   * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 4;
   */
  redirectDeeplink?: RedirectDeeplink;
};

/**
 * Describes the message com.stablemoney.api.bank.Bank.
 * Use `create(BankSchema)` to create a new message.
 */
export const BankSchema: GenMessage<Bank> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Bank, 0);

/**
 * @generated from message com.stablemoney.api.bank.AgentAvailabilityDetails
 */
export type AgentAvailabilityDetails =
  Message<"com.stablemoney.api.bank.AgentAvailabilityDetails"> & {
    /**
     * @generated from field: bool is_available = 1;
     */
    isAvailable: boolean;

    /**
     * @generated from field: string availability_timings_description = 2;
     */
    availabilityTimingsDescription: string;

    /**
     * @generated from field: bool is_holiday = 3;
     */
    isHoliday: boolean;

    /**
     * @generated from field: string holiday_description = 4;
     */
    holidayDescription: string;
  };

/**
 * Describes the message com.stablemoney.api.bank.AgentAvailabilityDetails.
 * Use `create(AgentAvailabilityDetailsSchema)` to create a new message.
 */
export const AgentAvailabilityDetailsSchema: GenMessage<AgentAvailabilityDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Bank, 1);

/**
 * @generated from message com.stablemoney.api.bank.VkycExpiryTimestamp
 */
export type VkycExpiryTimestamp =
  Message<"com.stablemoney.api.bank.VkycExpiryTimestamp"> & {
    /**
     * @generated from field: string vkyc_expiry_date = 1;
     */
    vkycExpiryDate: string;
  };

/**
 * Describes the message com.stablemoney.api.bank.VkycExpiryTimestamp.
 * Use `create(VkycExpiryTimestampSchema)` to create a new message.
 */
export const VkycExpiryTimestampSchema: GenMessage<VkycExpiryTimestamp> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Bank, 2);
