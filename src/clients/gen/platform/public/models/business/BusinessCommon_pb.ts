// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/BusinessCommon.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/BusinessCommon.proto.
 */
export const file_public_models_business_BusinessCommon: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.identity.RedirectDeeplink
 */
export type RedirectDeeplink =
  Message<"com.stablemoney.api.identity.RedirectDeeplink"> & {
    /**
     * @generated from field: string path = 1;
     */
    path: string;

    /**
     * @generated from field: string path_type = 2;
     */
    pathType: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RedirectDeeplink.
 * Use `create(RedirectDeeplinkSchema)` to create a new message.
 */
export const RedirectDeeplinkSchema: GenMessage<RedirectDeeplink> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BusinessCommon, 0);

/**
 * @generated from enum com.stablemoney.api.identity.BankType
 */
export enum BankType {
  /**
   * @generated from enum value: UNKNOWN_BANK_TYPE = 0;
   */
  UNKNOWN_BANK_TYPE = 0,

  /**
   * @generated from enum value: NBFC_BANK_TYPE = 1;
   */
  NBFC_BANK_TYPE = 1,

  /**
   * @generated from enum value: PRIVATE_BANK_TYPE = 2;
   */
  PRIVATE_BANK_TYPE = 2,

  /**
   * @generated from enum value: PUBLIC_BANK_TYPE = 3;
   */
  PUBLIC_BANK_TYPE = 3,

  /**
   * @generated from enum value: COOPERATIVE_BANK_TYPE = 4;
   */
  COOPERATIVE_BANK_TYPE = 4,

  /**
   * @generated from enum value: SMALL_FINANCE_BANK_TYPE = 5;
   */
  SMALL_FINANCE_BANK_TYPE = 5,

  /**
   * @generated from enum value: POST_OFFICE_SAVINGS_BANK_TYPE = 6;
   */
  POST_OFFICE_SAVINGS_BANK_TYPE = 6,

  /**
   * @generated from enum value: FOREIGN_BANK_TYPE = 7;
   */
  FOREIGN_BANK_TYPE = 7,

  /**
   * @generated from enum value: PAYMENTS_BANK_TYPE = 8;
   */
  PAYMENTS_BANK_TYPE = 8,
}

/**
 * Describes the enum com.stablemoney.api.identity.BankType.
 */
export const BankTypeSchema: GenEnum<BankType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 0);

/**
 * @generated from enum com.stablemoney.api.identity.InvestorType
 */
export enum InvestorType {
  /**
   * @generated from enum value: UNKNOWN_INVESTOR_TYPE = 0;
   */
  UNKNOWN_INVESTOR_TYPE = 0,

  /**
   * @generated from enum value: GENERAL_PUBLIC_INVESTOR_TYPE = 1;
   */
  GENERAL_PUBLIC_INVESTOR_TYPE = 1,

  /**
   * @generated from enum value: SENIOR_CITIZEN_INVESTOR_TYPE = 2;
   */
  SENIOR_CITIZEN_INVESTOR_TYPE = 2,

  /**
   * @generated from enum value: WOMEN_INVESTOR_TYPE = 3;
   */
  WOMEN_INVESTOR_TYPE = 3,

  /**
   * @generated from enum value: WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE = 4;
   */
  WOMEN_SENIOR_CITIZEN_INVESTOR_TYPE = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.InvestorType.
 */
export const InvestorTypeSchema: GenEnum<InvestorType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 1);

/**
 * @generated from enum com.stablemoney.api.identity.OfferingStatus
 */
export enum OfferingStatus {
  /**
   * @generated from enum value: UNKNOWN_OFFERING_STATUS = 0;
   */
  UNKNOWN_OFFERING_STATUS = 0,

  /**
   * @generated from enum value: ACTIVE_OFFERING_STATUS = 1;
   */
  ACTIVE_OFFERING_STATUS = 1,

  /**
   * @generated from enum value: INACTIVE_OFFERING_STATUS = 2;
   */
  INACTIVE_OFFERING_STATUS = 2,

  /**
   * @generated from enum value: COMING_SOON_OFFERING_STATUS = 3;
   */
  COMING_SOON_OFFERING_STATUS = 3,

  /**
   * @generated from enum value: DELETED_OFFERING_STATUS = 4;
   */
  DELETED_OFFERING_STATUS = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.OfferingStatus.
 */
export const OfferingStatusSchema: GenEnum<OfferingStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 2);

/**
 * @generated from enum com.stablemoney.api.identity.InterestPayoutType
 */
export enum InterestPayoutType {
  /**
   * @generated from enum value: UNKNOWN_INTEREST_PAYOUT_TYPE = 0;
   */
  UNKNOWN_INTEREST_PAYOUT_TYPE = 0,

  /**
   * @generated from enum value: MONTHLY_INTEREST_PAYOUT_TYPE = 1;
   */
  MONTHLY_INTEREST_PAYOUT_TYPE = 1,

  /**
   * @generated from enum value: QUARTERLY_INTEREST_PAYOUT_TYPE = 2;
   */
  QUARTERLY_INTEREST_PAYOUT_TYPE = 2,

  /**
   * @generated from enum value: HALF_YEARLY_INTEREST_PAYOUT_TYPE = 3;
   */
  HALF_YEARLY_INTEREST_PAYOUT_TYPE = 3,

  /**
   * @generated from enum value: YEARLY_INTEREST_PAYOUT_TYPE = 4;
   */
  YEARLY_INTEREST_PAYOUT_TYPE = 4,

  /**
   * @generated from enum value: MATURITY_INTEREST_PAYOUT_TYPE = 5;
   */
  MATURITY_INTEREST_PAYOUT_TYPE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.InterestPayoutType.
 */
export const InterestPayoutTypeSchema: GenEnum<InterestPayoutType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 3);

/**
 * @generated from enum com.stablemoney.api.identity.CompoundingFrequencyType
 */
export enum CompoundingFrequencyType {
  /**
   * @generated from enum value: UNKNOWN_COMPOUNDING_FREQUENCY_TYPE = 0;
   */
  UNKNOWN_COMPOUNDING_FREQUENCY_TYPE = 0,

  /**
   * @generated from enum value: MONTHLY_COMPOUNDING_FREQUENCY_TYPE = 1;
   */
  MONTHLY_COMPOUNDING_FREQUENCY_TYPE = 1,

  /**
   * @generated from enum value: QUARTERLY_COMPOUNDING_FREQUENCY_TYPE = 2;
   */
  QUARTERLY_COMPOUNDING_FREQUENCY_TYPE = 2,

  /**
   * @generated from enum value: HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE = 3;
   */
  HALF_YEARLY_COMPOUNDING_FREQUENCY_TYPE = 3,

  /**
   * @generated from enum value: YEARLY_COMPOUNDING_FREQUENCY_TYPE = 4;
   */
  YEARLY_COMPOUNDING_FREQUENCY_TYPE = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.CompoundingFrequencyType.
 */
export const CompoundingFrequencyTypeSchema: GenEnum<CompoundingFrequencyType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 4);

/**
 * @generated from enum com.stablemoney.api.identity.UiScreenType
 */
export enum UiScreenType {
  /**
   * @generated from enum value: UNKNOWN_UI_SCREEN = 0;
   */
  UNKNOWN_UI_SCREEN = 0,

  /**
   * @generated from enum value: HOME_SCREEN = 1;
   */
  HOME_SCREEN = 1,

  /**
   * @generated from enum value: EXPLORE_SCREEN = 2;
   */
  EXPLORE_SCREEN = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.UiScreenType.
 */
export const UiScreenTypeSchema: GenEnum<UiScreenType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 5);

/**
 * @generated from enum com.stablemoney.api.identity.InvestabilityStatus
 */
export enum InvestabilityStatus {
  /**
   * @generated from enum value: UNKNOWN_INVESTABILITY_STATUS = 0;
   */
  UNKNOWN_INVESTABILITY_STATUS = 0,

  /**
   * @generated from enum value: ACTIVE_INVESTABILITY_STATUS = 1;
   */
  ACTIVE_INVESTABILITY_STATUS = 1,

  /**
   * @generated from enum value: INACTIVE_INVESTABILITY_STATUS = 2;
   */
  INACTIVE_INVESTABILITY_STATUS = 2,

  /**
   * @generated from enum value: COMING_SOON_INVESTABILITY_STATUS = 3;
   */
  COMING_SOON_INVESTABILITY_STATUS = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.InvestabilityStatus.
 */
export const InvestabilityStatusSchema: GenEnum<InvestabilityStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 6);

/**
 * @generated from enum com.stablemoney.api.identity.InvestabilityRolloutStatus
 */
export enum InvestabilityRolloutStatus {
  /**
   * @generated from enum value: UNKNOWN_INVESTABILITY_ROLLOUT_STATUS = 0;
   */
  UNKNOWN_INVESTABILITY_ROLLOUT_STATUS = 0,

  /**
   * @generated from enum value: COMPLETE_ROLLOUT_STATUS = 1;
   */
  COMPLETE_ROLLOUT_STATUS = 1,

  /**
   * @generated from enum value: PARTIALLY_ROLLOUT_STATUS = 2;
   */
  PARTIALLY_ROLLOUT_STATUS = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.InvestabilityRolloutStatus.
 */
export const InvestabilityRolloutStatusSchema: GenEnum<InvestabilityRolloutStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 7);

/**
 * @generated from enum com.stablemoney.api.identity.TenureFormatType
 */
export enum TenureFormatType {
  /**
   * @generated from enum value: UNKNOWN_TENURE_FORMAT_TYPE = 0;
   */
  UNKNOWN_TENURE_FORMAT_TYPE = 0,

  /**
   * @generated from enum value: DAYS_TENURE_FORMAT_TYPE = 1;
   */
  DAYS_TENURE_FORMAT_TYPE = 1,

  /**
   * @generated from enum value: YEAR_MONTH_DAY_TENURE_FORMAT_TYPE = 2;
   */
  YEAR_MONTH_DAY_TENURE_FORMAT_TYPE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.TenureFormatType.
 */
export const TenureFormatTypeSchema: GenEnum<TenureFormatType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 8);

/**
 * @generated from enum com.stablemoney.api.identity.BusinessProvider
 */
export enum BusinessProvider {
  /**
   * @generated from enum value: UNKNOWN_BUSINESS_PROVIDER = 0;
   */
  UNKNOWN_BUSINESS_PROVIDER = 0,

  /**
   * @generated from enum value: TARRAKKI_BUSINESS_PROVIDER = 1;
   */
  TARRAKKI_BUSINESS_PROVIDER = 1,

  /**
   * @generated from enum value: UPSWING_BUSINESS_PROVIDER = 2;
   */
  UPSWING_BUSINESS_PROVIDER = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.BusinessProvider.
 */
export const BusinessProviderSchema: GenEnum<BusinessProvider> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BusinessCommon, 9);
