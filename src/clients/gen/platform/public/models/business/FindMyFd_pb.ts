// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/FindMyFd.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { FixedDepositResponse } from "./Collection_pb.js";
import { file_public_models_business_Collection } from "./Collection_pb.js";
import type { RedirectDeeplink } from "./BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "./BusinessCommon_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/FindMyFd.proto.
 */
export const file_public_models_business_FindMyFd: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_Collection,
      file_public_models_business_BusinessCommon,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.FindMyFdResponse
 */
export type FindMyFdResponse =
  Message<"com.stablemoney.api.identity.FindMyFdResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.Question questionnaire = 1;
     */
    questionnaire: Question[];
  };

/**
 * Describes the message com.stablemoney.api.identity.FindMyFdResponse.
 * Use `create(FindMyFdResponseSchema)` to create a new message.
 */
export const FindMyFdResponseSchema: GenMessage<FindMyFdResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 0);

/**
 * @generated from message com.stablemoney.api.identity.Question
 */
export type Question = Message<"com.stablemoney.api.identity.Question"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: string question = 2;
   */
  question: string;

  /**
   * @generated from field: string description = 3;
   */
  description: string;

  /**
   * @generated from field: string submit_button_text = 4;
   */
  submitButtonText: string;

  /**
   * @generated from field: bool is_skippable = 5;
   */
  isSkippable: boolean;

  /**
   * @generated from field: com.stablemoney.api.identity.QuestionResponseType question_response_type = 6;
   */
  questionResponseType: QuestionResponseType;

  /**
   * @generated from field: repeated com.stablemoney.api.identity.AnswerResponse answer = 7;
   */
  answer: AnswerResponse[];

  /**
   * @generated from field: string parameter_asked = 9;
   */
  parameterAsked: string;
};

/**
 * Describes the message com.stablemoney.api.identity.Question.
 * Use `create(QuestionSchema)` to create a new message.
 */
export const QuestionSchema: GenMessage<Question> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 1);

/**
 * @generated from message com.stablemoney.api.identity.AnswerResponse
 */
export type AnswerResponse =
  Message<"com.stablemoney.api.identity.AnswerResponse"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string answer = 2;
     */
    answer: string;

    /**
     * @generated from field: string description = 3;
     */
    description: string;

    /**
     * @generated from field: string parameter_value = 5;
     */
    parameterValue: string;

    /**
     * @generated from field: string short_description = 6;
     */
    shortDescription: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.AnswerResponse.
 * Use `create(AnswerResponseSchema)` to create a new message.
 */
export const AnswerResponseSchema: GenMessage<AnswerResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 2);

/**
 * @generated from message com.stablemoney.api.identity.FindMyFdSubmitRequest
 */
export type FindMyFdSubmitRequest =
  Message<"com.stablemoney.api.identity.FindMyFdSubmitRequest"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.Response responses = 1;
     */
    responses: Response[];

    /**
     * @generated from field: bool is_skipped = 2;
     */
    isSkipped: boolean;

    /**
     * @generated from field: double investment_amount = 3;
     */
    investmentAmount: number;

    /**
     * @generated from field: optional int32 number_of_nib_recommendations = 4;
     */
    numberOfNibRecommendations?: number;

    /**
     * @generated from field: optional int32 number_of_ib_recommendations = 5;
     */
    numberOfIbRecommendations?: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.FindMyFdSubmitRequest.
 * Use `create(FindMyFdSubmitRequestSchema)` to create a new message.
 */
export const FindMyFdSubmitRequestSchema: GenMessage<FindMyFdSubmitRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 3);

/**
 * @generated from message com.stablemoney.api.identity.Response
 */
export type Response = Message<"com.stablemoney.api.identity.Response"> & {
  /**
   * @generated from field: string question_id = 1;
   */
  questionId: string;

  /**
   * @generated from field: repeated string answer_id = 2;
   */
  answerId: string[];
};

/**
 * Describes the message com.stablemoney.api.identity.Response.
 * Use `create(ResponseSchema)` to create a new message.
 */
export const ResponseSchema: GenMessage<Response> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 4);

/**
 * @generated from message com.stablemoney.api.identity.FindMyFdSubmitResponse
 */
export type FindMyFdSubmitResponse =
  Message<"com.stablemoney.api.identity.FindMyFdSubmitResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.FindMyFdRecommendation recommendations = 1;
     */
    recommendations: FindMyFdRecommendation[];

    /**
     * @generated from field: string recommendation_id = 2;
     */
    recommendationId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.FindMyFdSubmitResponse.
 * Use `create(FindMyFdSubmitResponseSchema)` to create a new message.
 */
export const FindMyFdSubmitResponseSchema: GenMessage<FindMyFdSubmitResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 5);

/**
 * @generated from message com.stablemoney.api.identity.RecommendationReviewRequest
 */
export type RecommendationReviewRequest =
  Message<"com.stablemoney.api.identity.RecommendationReviewRequest"> & {
    /**
     * @generated from field: bool did_recommendation_helped_you = 1;
     */
    didRecommendationHelpedYou: boolean;

    /**
     * @generated from field: string recommendation_id = 2;
     */
    recommendationId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendationReviewRequest.
 * Use `create(RecommendationReviewRequestSchema)` to create a new message.
 */
export const RecommendationReviewRequestSchema: GenMessage<RecommendationReviewRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 6);

/**
 * @generated from message com.stablemoney.api.identity.RecommendationReviewResponse
 */
export type RecommendationReviewResponse =
  Message<"com.stablemoney.api.identity.RecommendationReviewResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.RecommendationReviewResponse.
 * Use `create(RecommendationReviewResponseSchema)` to create a new message.
 */
export const RecommendationReviewResponseSchema: GenMessage<RecommendationReviewResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 7);

/**
 * @generated from message com.stablemoney.api.identity.FindMyFdRecommendation
 */
export type FindMyFdRecommendation =
  Message<"com.stablemoney.api.identity.FindMyFdRecommendation"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse fixed_deposit = 1;
     */
    fixedDeposit?: FixedDepositResponse;

    /**
     * @generated from field: double investment_amount = 2;
     */
    investmentAmount: number;

    /**
     * @generated from field: double returns = 3;
     */
    returns: number;

    /**
     * @generated from field: double maturity_amount = 4;
     */
    maturityAmount: number;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 5;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BenefitsDetails benefitsDetails = 6;
     */
    benefitsDetails: BenefitsDetails[];
  };

/**
 * Describes the message com.stablemoney.api.identity.FindMyFdRecommendation.
 * Use `create(FindMyFdRecommendationSchema)` to create a new message.
 */
export const FindMyFdRecommendationSchema: GenMessage<FindMyFdRecommendation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 8);

/**
 * @generated from message com.stablemoney.api.identity.BenefitsDetails
 */
export type BenefitsDetails =
  Message<"com.stablemoney.api.identity.BenefitsDetails"> & {
    /**
     * @generated from field: string benefit = 1;
     */
    benefit: string;

    /**
     * @generated from field: string icon_url = 2;
     */
    iconUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BenefitsDetails.
 * Use `create(BenefitsDetailsSchema)` to create a new message.
 */
export const BenefitsDetailsSchema: GenMessage<BenefitsDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 9);

/**
 * @generated from message com.stablemoney.api.identity.RecommendationReviewSubmitRequest
 */
export type RecommendationReviewSubmitRequest =
  Message<"com.stablemoney.api.identity.RecommendationReviewSubmitRequest"> & {
    /**
     * @generated from field: string recommendation_id = 1;
     */
    recommendationId: string;

    /**
     * @generated from field: bool has_helped_you = 2;
     */
    hasHelpedYou: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendationReviewSubmitRequest.
 * Use `create(RecommendationReviewSubmitRequestSchema)` to create a new message.
 */
export const RecommendationReviewSubmitRequestSchema: GenMessage<RecommendationReviewSubmitRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 10);

/**
 * @generated from message com.stablemoney.api.identity.RecommendationReviewSubmitResponse
 */
export type RecommendationReviewSubmitResponse =
  Message<"com.stablemoney.api.identity.RecommendationReviewSubmitResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.RecommendationReviewSubmitResponse.
 * Use `create(RecommendationReviewSubmitResponseSchema)` to create a new message.
 */
export const RecommendationReviewSubmitResponseSchema: GenMessage<RecommendationReviewSubmitResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 11);

/**
 * @generated from message com.stablemoney.api.identity.InitiateFindMyFdResponse
 */
export type InitiateFindMyFdResponse =
  Message<"com.stablemoney.api.identity.InitiateFindMyFdResponse"> & {
    /**
     * @generated from field: bool is_find_my_fd_initiated = 1;
     */
    isFindMyFdInitiated: boolean;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FindMyFdRecommendation recommendations = 2;
     */
    recommendations: FindMyFdRecommendation[];

    /**
     * @generated from field: bool is_recommendation_feedback_submitted = 3;
     */
    isRecommendationFeedbackSubmitted: boolean;

    /**
     * @generated from field: double investment_amount = 4;
     */
    investmentAmount: number;

    /**
     * @generated from field: string recommendation_id = 5;
     */
    recommendationId: string;

    /**
     * @generated from field: com.stablemoney.api.identity.FindMyFdSubmitRequest find_my_fd_submit_request = 6;
     */
    findMyFdSubmitRequest?: FindMyFdSubmitRequest;
  };

/**
 * Describes the message com.stablemoney.api.identity.InitiateFindMyFdResponse.
 * Use `create(InitiateFindMyFdResponseSchema)` to create a new message.
 */
export const InitiateFindMyFdResponseSchema: GenMessage<InitiateFindMyFdResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FindMyFd, 12);

/**
 * @generated from enum com.stablemoney.api.identity.QuestionResponseType
 */
export enum QuestionResponseType {
  /**
   * @generated from enum value: UNKNOWN_QUESTION_RESPONSE_TYPE = 0;
   */
  UNKNOWN_QUESTION_RESPONSE_TYPE = 0,

  /**
   * @generated from enum value: SINGLE_RESPONSE_QUESTION = 1;
   */
  SINGLE_RESPONSE_QUESTION = 1,

  /**
   * @generated from enum value: MULTIPLE_RESPONSE_QUESTION = 2;
   */
  MULTIPLE_RESPONSE_QUESTION = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.QuestionResponseType.
 */
export const QuestionResponseTypeSchema: GenEnum<QuestionResponseType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FindMyFd, 0);

/**
 * @generated from enum com.stablemoney.api.identity.QuestionnaireType
 */
export enum QuestionnaireType {
  /**
   * @generated from enum value: QUESTIONNAIRE_TYPE_UNKNOWN = 0;
   */
  QUESTIONNAIRE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: FIND_MY_FD = 1;
   */
  FIND_MY_FD = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.QuestionnaireType.
 */
export const QuestionnaireTypeSchema: GenEnum<QuestionnaireType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FindMyFd, 1);
