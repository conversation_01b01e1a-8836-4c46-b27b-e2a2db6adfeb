// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/Goal.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { BankResponse, FixedDepositResponse } from "./Collection_pb.js";
import { file_public_models_business_Collection } from "./Collection_pb.js";
import type { RedirectDeeplink } from "./BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "./BusinessCommon_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/Goal.proto.
 */
export const file_public_models_business_Goal: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_public_models_business_Collection,
      file_public_models_business_BusinessCommon,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.GoalDetails
 */
export type GoalDetails =
  Message<"com.stablemoney.api.identity.GoalDetails"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: double target_amount = 2;
     */
    targetAmount: number;

    /**
     * @generated from field: double current_amount = 3;
     */
    currentAmount: number;

    /**
     * @generated from field: double progress_percentage = 4;
     */
    progressPercentage: number;

    /**
     * @generated from field: string progress_description = 5;
     */
    progressDescription: string;

    /**
     * @generated from field: string progress_description_dashboard = 6;
     */
    progressDescriptionDashboard: string;

    /**
     * @generated from field: bool is_reminder_date_selected = 7;
     */
    isReminderDateSelected: boolean;

    /**
     * @generated from field: int32 reminder_date_of_month = 8;
     */
    reminderDateOfMonth: number;

    /**
     * @generated from field: double gains = 9;
     */
    gains: number;

    /**
     * @generated from field: int32 target_duration_months = 10;
     */
    targetDurationMonths: number;

    /**
     * @generated from field: string achievement_badge = 11;
     */
    achievementBadge: string;

    /**
     * @generated from field: string share_text = 12;
     */
    shareText: string;

    /**
     * @generated from field: string progress_sub_description_dashboard = 13;
     */
    progressSubDescriptionDashboard: string;

    /**
     * @generated from field: double fd_goal = 14;
     */
    fdGoal: number;

    /**
     * @generated from field: double savings_account_goal = 15;
     */
    savingsAccountGoal: number;

    /**
     * @generated from field: double savings_min_limit = 16;
     */
    savingsMinLimit: number;

    /**
     * @generated from field: double savings_max_limit = 17;
     */
    savingsMaxLimit: number;

    /**
     * @generated from field: double savings_account_amount = 18;
     */
    savingsAccountAmount: number;

    /**
     * @generated from field: double fd_amount = 19;
     */
    fdAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalDetails.
 * Use `create(GoalDetailsSchema)` to create a new message.
 */
export const GoalDetailsSchema: GenMessage<GoalDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 0);

/**
 * @generated from message com.stablemoney.api.identity.GetGoalDetailsResponse
 */
export type GetGoalDetailsResponse =
  Message<"com.stablemoney.api.identity.GetGoalDetailsResponse"> & {
    /**
     * @generated from field: bool is_active = 1;
     */
    isActive: boolean;

    /**
     * @generated from field: bool is_goal_created = 2;
     */
    isGoalCreated: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.GoalDetails goal_details = 3;
     */
    goalDetails?: GoalDetails;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetGoalDetailsResponse.
 * Use `create(GetGoalDetailsResponseSchema)` to create a new message.
 */
export const GetGoalDetailsResponseSchema: GenMessage<GetGoalDetailsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 1);

/**
 * @generated from message com.stablemoney.api.identity.GoalCreateRequest
 */
export type GoalCreateRequest =
  Message<"com.stablemoney.api.identity.GoalCreateRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GoalType goal_type = 1;
     */
    goalType: GoalType;

    /**
     * @generated from field: int32 target_duration_months = 2;
     */
    targetDurationMonths: number;

    /**
     * @generated from field: double income = 3;
     */
    income: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalCreateRequest.
 * Use `create(GoalCreateRequestSchema)` to create a new message.
 */
export const GoalCreateRequestSchema: GenMessage<GoalCreateRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 2);

/**
 * @generated from message com.stablemoney.api.identity.GoalCreateResponse
 */
export type GoalCreateResponse =
  Message<"com.stablemoney.api.identity.GoalCreateResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GoalDetails goal_details = 1;
     */
    goalDetails?: GoalDetails;

    /**
     * @generated from field: com.stablemoney.api.identity.RecommendedBankForGoalResponse recommended_bank = 2;
     */
    recommendedBank?: RecommendedBankForGoalResponse;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalCreateResponse.
 * Use `create(GoalCreateResponseSchema)` to create a new message.
 */
export const GoalCreateResponseSchema: GenMessage<GoalCreateResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 3);

/**
 * @generated from message com.stablemoney.api.identity.RecommendedBankForGoalResponse
 */
export type RecommendedBankForGoalResponse =
  Message<"com.stablemoney.api.identity.RecommendedBankForGoalResponse"> & {
    /**
     * @generated from field: bool has_dicgc_limit_reached = 1;
     */
    hasDicgcLimitReached: boolean;

    /**
     * @generated from field: bool is_active = 2;
     */
    isActive: boolean;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankSellingPointsResponse selling_points = 3;
     */
    sellingPoints: RecommendedBankSellingPointsResponse[];

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 4;
     */
    bank?: BankResponse;

    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse highest_interest_rate_fd = 5;
     */
    highestInterestRateFd?: FixedDepositResponse;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 6;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: double recommended_amount = 7;
     */
    recommendedAmount: number;

    /**
     * @generated from field: double current_invested_amount = 8;
     */
    currentInvestedAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendedBankForGoalResponse.
 * Use `create(RecommendedBankForGoalResponseSchema)` to create a new message.
 */
export const RecommendedBankForGoalResponseSchema: GenMessage<RecommendedBankForGoalResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 4);

/**
 * @generated from message com.stablemoney.api.identity.RecommendedBankSellingPointsResponse
 */
export type RecommendedBankSellingPointsResponse =
  Message<"com.stablemoney.api.identity.RecommendedBankSellingPointsResponse"> & {
    /**
     * @generated from field: string icon_url = 1;
     */
    iconUrl: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendedBankSellingPointsResponse.
 * Use `create(RecommendedBankSellingPointsResponseSchema)` to create a new message.
 */
export const RecommendedBankSellingPointsResponseSchema: GenMessage<RecommendedBankSellingPointsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 5);

/**
 * @generated from message com.stablemoney.api.identity.GoalNotificationRequest
 */
export type GoalNotificationRequest =
  Message<"com.stablemoney.api.identity.GoalNotificationRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GoalType goal_type = 1;
     */
    goalType: GoalType;

    /**
     * @generated from field: int32 date_of_month = 2;
     */
    dateOfMonth: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalNotificationRequest.
 * Use `create(GoalNotificationRequestSchema)` to create a new message.
 */
export const GoalNotificationRequestSchema: GenMessage<GoalNotificationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 6);

/**
 * @generated from message com.stablemoney.api.identity.GoalNotificationResponse
 */
export type GoalNotificationResponse =
  Message<"com.stablemoney.api.identity.GoalNotificationResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GoalDetails goal_details = 1;
     */
    goalDetails?: GoalDetails;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalNotificationResponse.
 * Use `create(GoalNotificationResponseSchema)` to create a new message.
 */
export const GoalNotificationResponseSchema: GenMessage<GoalNotificationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 7);

/**
 * @generated from message com.stablemoney.api.identity.RecommendedBankListResponse
 */
export type RecommendedBankListResponse =
  Message<"com.stablemoney.api.identity.RecommendedBankListResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankForGoalResponse recommended_banks = 1;
     */
    recommendedBanks: RecommendedBankForGoalResponse[];
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendedBankListResponse.
 * Use `create(RecommendedBankListResponseSchema)` to create a new message.
 */
export const RecommendedBankListResponseSchema: GenMessage<RecommendedBankListResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 8);

/**
 * @generated from message com.stablemoney.api.identity.GoalInvestmentListResponse
 */
export type GoalInvestmentListResponse =
  Message<"com.stablemoney.api.identity.GoalInvestmentListResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.GoalInvestmentInfo investments = 1;
     */
    investments: GoalInvestmentInfo[];
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalInvestmentListResponse.
 * Use `create(GoalInvestmentListResponseSchema)` to create a new message.
 */
export const GoalInvestmentListResponseSchema: GenMessage<GoalInvestmentListResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 9);

/**
 * @generated from message com.stablemoney.api.identity.GoalInvestmentInfo
 */
export type GoalInvestmentInfo =
  Message<"com.stablemoney.api.identity.GoalInvestmentInfo"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank = 1;
     */
    bank?: BankResponse;

    /**
     * @generated from field: string maturity_date = 2;
     */
    maturityDate: string;

    /**
     * @generated from field: string booking_date = 3;
     */
    bookingDate: string;

    /**
     * @generated from field: double amount = 4;
     */
    amount: number;

    /**
     * @generated from field: double interest_rate = 5;
     */
    interestRate: number;

    /**
     * @generated from field: string id = 6;
     */
    id: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalInvestmentInfo.
 * Use `create(GoalInvestmentInfoSchema)` to create a new message.
 */
export const GoalInvestmentInfoSchema: GenMessage<GoalInvestmentInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 10);

/**
 * @generated from message com.stablemoney.api.identity.GoalSetInvestmentListRequest
 */
export type GoalSetInvestmentListRequest =
  Message<"com.stablemoney.api.identity.GoalSetInvestmentListRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GoalType goal_type = 1;
     */
    goalType: GoalType;

    /**
     * @generated from field: repeated string investment_ids = 2;
     */
    investmentIds: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalSetInvestmentListRequest.
 * Use `create(GoalSetInvestmentListRequestSchema)` to create a new message.
 */
export const GoalSetInvestmentListRequestSchema: GenMessage<GoalSetInvestmentListRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 11);

/**
 * @generated from message com.stablemoney.api.identity.GoalSetInvestmentListResponse
 */
export type GoalSetInvestmentListResponse =
  Message<"com.stablemoney.api.identity.GoalSetInvestmentListResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GoalDetails goal_details = 1;
     */
    goalDetails?: GoalDetails;
  };

/**
 * Describes the message com.stablemoney.api.identity.GoalSetInvestmentListResponse.
 * Use `create(GoalSetInvestmentListResponseSchema)` to create a new message.
 */
export const GoalSetInvestmentListResponseSchema: GenMessage<GoalSetInvestmentListResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 12);

/**
 * @generated from message com.stablemoney.api.identity.SelectBankRequest
 */
export type SelectBankRequest =
  Message<"com.stablemoney.api.identity.SelectBankRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.GoalType goal_type = 1;
     */
    goalType: GoalType;

    /**
     * @generated from field: string bank_id = 2;
     */
    bankId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SelectBankRequest.
 * Use `create(SelectBankRequestSchema)` to create a new message.
 */
export const SelectBankRequestSchema: GenMessage<SelectBankRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 13);

/**
 * @generated from message com.stablemoney.api.identity.SelectBankResponse
 */
export type SelectBankResponse =
  Message<"com.stablemoney.api.identity.SelectBankResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.SelectBankResponse.
 * Use `create(SelectBankResponseSchema)` to create a new message.
 */
export const SelectBankResponseSchema: GenMessage<SelectBankResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 14);

/**
 * @generated from message com.stablemoney.api.identity.UpdateSavingsAmountResponse
 */
export type UpdateSavingsAmountResponse =
  Message<"com.stablemoney.api.identity.UpdateSavingsAmountResponse"> & {};

/**
 * Describes the message com.stablemoney.api.identity.UpdateSavingsAmountResponse.
 * Use `create(UpdateSavingsAmountResponseSchema)` to create a new message.
 */
export const UpdateSavingsAmountResponseSchema: GenMessage<UpdateSavingsAmountResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 15);

/**
 * @generated from message com.stablemoney.api.identity.UpdateSavingsAmountRequest
 */
export type UpdateSavingsAmountRequest =
  Message<"com.stablemoney.api.identity.UpdateSavingsAmountRequest"> & {
    /**
     * @generated from field: double savings_account_amount = 1;
     */
    savingsAccountAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.UpdateSavingsAmountRequest.
 * Use `create(UpdateSavingsAmountRequestSchema)` to create a new message.
 */
export const UpdateSavingsAmountRequestSchema: GenMessage<UpdateSavingsAmountRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 16);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundStepsResponse
 */
export type EmergencyFundStepsResponse =
  Message<"com.stablemoney.api.identity.EmergencyFundStepsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.EmergencyFundSteps steps = 1;
     */
    steps: EmergencyFundSteps[];

    /**
     * @generated from field: string next_step = 2;
     */
    nextStep: string;

    /**
     * @generated from field: bool is_completed = 3;
     */
    isCompleted: boolean;

    /**
     * @generated from field: double completion_percentage = 4;
     */
    completionPercentage: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundStepsResponse.
 * Use `create(EmergencyFundStepsResponseSchema)` to create a new message.
 */
export const EmergencyFundStepsResponseSchema: GenMessage<EmergencyFundStepsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 17);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundSteps
 */
export type EmergencyFundSteps =
  Message<"com.stablemoney.api.identity.EmergencyFundSteps"> & {
    /**
     * @generated from field: string step_name = 1;
     */
    stepName: string;

    /**
     * @generated from field: bool is_completed = 2;
     */
    isCompleted: boolean;

    /**
     * @generated from field: int32 priority = 3;
     */
    priority: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundSteps.
 * Use `create(EmergencyFundStepsSchema)` to create a new message.
 */
export const EmergencyFundStepsSchema: GenMessage<EmergencyFundSteps> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 18);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundQuestionnaireRequest
 */
export type EmergencyFundQuestionnaireRequest =
  Message<"com.stablemoney.api.identity.EmergencyFundQuestionnaireRequest"> & {
    /**
     * @generated from field: string step = 1;
     */
    step: string;

    /**
     * @generated from field: optional com.stablemoney.api.identity.CityStep city_step = 2;
     */
    cityStep?: CityStep;

    /**
     * @generated from field: optional com.stablemoney.api.identity.FamilyDetailsStep family_details_step = 3;
     */
    familyDetailsStep?: FamilyDetailsStep;

    /**
     * @generated from field: optional com.stablemoney.api.identity.MonthlyExpenseStep monthly_expense_step = 4;
     */
    monthlyExpenseStep?: MonthlyExpenseStep;

    /**
     * @generated from field: optional com.stablemoney.api.identity.InsuranceStep insurance_step = 5;
     */
    insuranceStep?: InsuranceStep;

    /**
     * @generated from field: optional com.stablemoney.api.identity.ModifyPlanStep modify_plan_step = 6;
     */
    modifyPlanStep?: ModifyPlanStep;

    /**
     * @generated from field: optional com.stablemoney.api.identity.AcceptPlanStep accept_plan_step = 7;
     */
    acceptPlanStep?: AcceptPlanStep;

    /**
     * @generated from field: optional com.stablemoney.api.identity.MonthlyPledgeStep monthly_pledge = 8;
     */
    monthlyPledge?: MonthlyPledgeStep;

    /**
     * @generated from field: optional com.stablemoney.api.identity.GetPlanRecommendation plan_recommendation = 9;
     */
    planRecommendation?: GetPlanRecommendation;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundQuestionnaireRequest.
 * Use `create(EmergencyFundQuestionnaireRequestSchema)` to create a new message.
 */
export const EmergencyFundQuestionnaireRequestSchema: GenMessage<EmergencyFundQuestionnaireRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 19);

/**
 * @generated from message com.stablemoney.api.identity.GetEmergencyFundResponse
 */
export type GetEmergencyFundResponse =
  Message<"com.stablemoney.api.identity.GetEmergencyFundResponse"> & {
    /**
     * @generated from field: string city_id = 1;
     */
    cityId: string;

    /**
     * @generated from field: bool is_paying_emi = 2;
     */
    isPayingEmi: boolean;

    /**
     * @generated from field: bool is_paying_rent = 3;
     */
    isPayingRent: boolean;

    /**
     * @generated from field: bool is_married = 4;
     */
    isMarried: boolean;

    /**
     * @generated from field: bool has_kids = 5;
     */
    hasKids: boolean;

    /**
     * @generated from field: bool do_parents_depend_on_you = 6;
     */
    doParentsDependOnYou: boolean;

    /**
     * @generated from field: double monthly_expense = 7;
     */
    monthlyExpense: number;

    /**
     * @generated from field: bool has_health_insurance = 8;
     */
    hasHealthInsurance: boolean;

    /**
     * @generated from field: bool has_life_insurance = 9;
     */
    hasLifeInsurance: boolean;

    /**
     * @generated from field: double goal_amount = 10;
     */
    goalAmount: number;

    /**
     * @generated from field: double monthly_pledge = 12;
     */
    monthlyPledge: number;

    /**
     * @generated from field: double recommended_amount = 13;
     */
    recommendedAmount: number;

    /**
     * @generated from field: bool is_active = 14;
     */
    isActive: boolean;

    /**
     * @generated from field: string city = 15;
     */
    city: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetEmergencyFundResponse.
 * Use `create(GetEmergencyFundResponseSchema)` to create a new message.
 */
export const GetEmergencyFundResponseSchema: GenMessage<GetEmergencyFundResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 20);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundQuestionnaireResponse
 */
export type EmergencyFundQuestionnaireResponse =
  Message<"com.stablemoney.api.identity.EmergencyFundQuestionnaireResponse"> & {
    /**
     * @generated from field: double goal_amount = 1;
     */
    goalAmount: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankForGoalResponse recommended_banks = 2;
     */
    recommendedBanks: RecommendedBankForGoalResponse[];

    /**
     * @generated from field: double fd_goal = 3;
     */
    fdGoal: number;

    /**
     * @generated from field: double savings_account_goal = 4;
     */
    savingsAccountGoal: number;

    /**
     * @generated from field: double mapped_investments_amount = 5;
     */
    mappedInvestmentsAmount: number;

    /**
     * @generated from field: double fd_contribution_amount = 6;
     */
    fdContributionAmount: number;

    /**
     * @generated from field: string savings_recommended_prompt = 7;
     */
    savingsRecommendedPrompt: string;

    /**
     * @generated from field: double savings_min_percentage = 8;
     */
    savingsMinPercentage: number;

    /**
     * @generated from field: double savings_max_percentage = 9;
     */
    savingsMaxPercentage: number;

    /**
     * @generated from field: bool has_mappable_investments = 10;
     */
    hasMappableInvestments: boolean;

    /**
     * @generated from field: double highest_fd_return = 12;
     */
    highestFdReturn: number;

    /**
     * @generated from field: double savings_account_amount = 13;
     */
    savingsAccountAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundQuestionnaireResponse.
 * Use `create(EmergencyFundQuestionnaireResponseSchema)` to create a new message.
 */
export const EmergencyFundQuestionnaireResponseSchema: GenMessage<EmergencyFundQuestionnaireResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 21);

/**
 * @generated from message com.stablemoney.api.identity.GetPlanRecommendation
 */
export type GetPlanRecommendation =
  Message<"com.stablemoney.api.identity.GetPlanRecommendation"> & {
    /**
     * @generated from field: double goal_amount = 1;
     */
    goalAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetPlanRecommendation.
 * Use `create(GetPlanRecommendationSchema)` to create a new message.
 */
export const GetPlanRecommendationSchema: GenMessage<GetPlanRecommendation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 22);

/**
 * @generated from message com.stablemoney.api.identity.CityStep
 */
export type CityStep = Message<"com.stablemoney.api.identity.CityStep"> & {
  /**
   * @generated from field: string city_id = 1;
   */
  cityId: string;

  /**
   * @generated from field: bool is_paying_emi = 2;
   */
  isPayingEmi: boolean;

  /**
   * @generated from field: bool is_paying_rent = 3;
   */
  isPayingRent: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.CityStep.
 * Use `create(CityStepSchema)` to create a new message.
 */
export const CityStepSchema: GenMessage<CityStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 23);

/**
 * @generated from message com.stablemoney.api.identity.FamilyDetailsStep
 */
export type FamilyDetailsStep =
  Message<"com.stablemoney.api.identity.FamilyDetailsStep"> & {
    /**
     * @generated from field: bool is_married = 1;
     */
    isMarried: boolean;

    /**
     * @generated from field: bool has_kids = 2;
     */
    hasKids: boolean;

    /**
     * @generated from field: bool do_parents_depend_on_you = 3;
     */
    doParentsDependOnYou: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.FamilyDetailsStep.
 * Use `create(FamilyDetailsStepSchema)` to create a new message.
 */
export const FamilyDetailsStepSchema: GenMessage<FamilyDetailsStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 24);

/**
 * @generated from message com.stablemoney.api.identity.MonthlyExpenseStep
 */
export type MonthlyExpenseStep =
  Message<"com.stablemoney.api.identity.MonthlyExpenseStep"> & {
    /**
     * @generated from field: double monthly_expense = 1;
     */
    monthlyExpense: number;

    /**
     * @generated from field: double emi_amount = 2;
     */
    emiAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.MonthlyExpenseStep.
 * Use `create(MonthlyExpenseStepSchema)` to create a new message.
 */
export const MonthlyExpenseStepSchema: GenMessage<MonthlyExpenseStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 25);

/**
 * @generated from message com.stablemoney.api.identity.InsuranceStep
 */
export type InsuranceStep =
  Message<"com.stablemoney.api.identity.InsuranceStep"> & {
    /**
     * @generated from field: bool has_health_insurance = 1;
     */
    hasHealthInsurance: boolean;

    /**
     * @generated from field: bool has_life_insurance = 2;
     */
    hasLifeInsurance: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.InsuranceStep.
 * Use `create(InsuranceStepSchema)` to create a new message.
 */
export const InsuranceStepSchema: GenMessage<InsuranceStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 26);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundGoal
 */
export type EmergencyFundGoal =
  Message<"com.stablemoney.api.identity.EmergencyFundGoal"> & {
    /**
     * @generated from field: optional double goal_amount = 1;
     */
    goalAmount?: number;

    /**
     * @generated from field: double emi_amount = 2;
     */
    emiAmount: number;

    /**
     * @generated from field: double monthly_expense = 3;
     */
    monthlyExpense: number;

    /**
     * @generated from field: bool has_health_insurance = 4;
     */
    hasHealthInsurance: boolean;

    /**
     * @generated from field: bool has_kids = 5;
     */
    hasKids: boolean;

    /**
     * @generated from field: bool is_married = 6;
     */
    isMarried: boolean;

    /**
     * @generated from field: bool has_dependent_parents = 7;
     */
    hasDependentParents: boolean;

    /**
     * @generated from field: string city_id = 8;
     */
    cityId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundGoal.
 * Use `create(EmergencyFundGoalSchema)` to create a new message.
 */
export const EmergencyFundGoalSchema: GenMessage<EmergencyFundGoal> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 27);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundMilestone
 */
export type EmergencyFundMilestone =
  Message<"com.stablemoney.api.identity.EmergencyFundMilestone"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @generated from field: optional double amount = 2;
     */
    amount?: number;

    /**
     * @generated from field: bool is_completed = 3;
     */
    isCompleted: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundMilestone.
 * Use `create(EmergencyFundMilestoneSchema)` to create a new message.
 */
export const EmergencyFundMilestoneSchema: GenMessage<EmergencyFundMilestone> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 28);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundResponse
 */
export type EmergencyFundResponse =
  Message<"com.stablemoney.api.identity.EmergencyFundResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.EmergencyFundGoal goal = 1;
     */
    goal?: EmergencyFundGoal;

    /**
     * @generated from field: double invested_amount = 2;
     */
    investedAmount: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.EmergencyFundMilestone milestones = 3;
     */
    milestones: EmergencyFundMilestone[];
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundResponse.
 * Use `create(EmergencyFundResponseSchema)` to create a new message.
 */
export const EmergencyFundResponseSchema: GenMessage<EmergencyFundResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 29);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundReferralData
 */
export type EmergencyFundReferralData =
  Message<"com.stablemoney.api.identity.EmergencyFundReferralData"> & {
    /**
     * @generated from field: string referral_link = 1;
     */
    referralLink: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.EmergencyFundReferralReward rewards = 2;
     */
    rewards: EmergencyFundReferralReward[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.EmergencyFundReferralProgramMember referees = 3;
     */
    referees: EmergencyFundReferralProgramMember[];

    /**
     * @generated from field: com.stablemoney.api.identity.EmergencyFundReferralProgramMember referrer = 4;
     */
    referrer?: EmergencyFundReferralProgramMember;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundReferralData.
 * Use `create(EmergencyFundReferralDataSchema)` to create a new message.
 */
export const EmergencyFundReferralDataSchema: GenMessage<EmergencyFundReferralData> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 30);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundReferralReward
 */
export type EmergencyFundReferralReward =
  Message<"com.stablemoney.api.identity.EmergencyFundReferralReward"> & {
    /**
     * @generated from field: int64 referral_count = 1;
     */
    referralCount: bigint;

    /**
     * @generated from field: double reward_amount = 2;
     */
    rewardAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundReferralReward.
 * Use `create(EmergencyFundReferralRewardSchema)` to create a new message.
 */
export const EmergencyFundReferralRewardSchema: GenMessage<EmergencyFundReferralReward> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 31);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundReferralProgramMember
 */
export type EmergencyFundReferralProgramMember =
  Message<"com.stablemoney.api.identity.EmergencyFundReferralProgramMember"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string first_name = 2;
     */
    firstName: string;

    /**
     * @generated from field: string last_name = 3;
     */
    lastName: string;

    /**
     * @generated from field: float goal_progress = 4;
     */
    goalProgress: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.EmergencyFundMilestone milestones = 5;
     */
    milestones: EmergencyFundMilestone[];
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundReferralProgramMember.
 * Use `create(EmergencyFundReferralProgramMemberSchema)` to create a new message.
 */
export const EmergencyFundReferralProgramMemberSchema: GenMessage<EmergencyFundReferralProgramMember> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 32);

/**
 * @generated from message com.stablemoney.api.identity.GetRecommendedGoalAmountResponse
 */
export type GetRecommendedGoalAmountResponse =
  Message<"com.stablemoney.api.identity.GetRecommendedGoalAmountResponse"> & {
    /**
     * @generated from field: double recommended_amount = 1;
     */
    recommendedAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.GetRecommendedGoalAmountResponse.
 * Use `create(GetRecommendedGoalAmountResponseSchema)` to create a new message.
 */
export const GetRecommendedGoalAmountResponseSchema: GenMessage<GetRecommendedGoalAmountResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 33);

/**
 * @generated from message com.stablemoney.api.identity.ModifyPlanStep
 */
export type ModifyPlanStep =
  Message<"com.stablemoney.api.identity.ModifyPlanStep"> & {
    /**
     * @generated from field: double goal_amount = 1;
     */
    goalAmount: number;

    /**
     * @generated from field: double fd_goal = 2;
     */
    fdGoal: number;

    /**
     * @generated from field: double savings_account_goal = 3;
     */
    savingsAccountGoal: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.ModifyPlanStep.
 * Use `create(ModifyPlanStepSchema)` to create a new message.
 */
export const ModifyPlanStepSchema: GenMessage<ModifyPlanStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 34);

/**
 * @generated from message com.stablemoney.api.identity.AcceptPlanStep
 */
export type AcceptPlanStep =
  Message<"com.stablemoney.api.identity.AcceptPlanStep"> & {
    /**
     * @generated from field: double fd_goal = 1;
     */
    fdGoal: number;

    /**
     * @generated from field: double savings_account_goal = 2;
     */
    savingsAccountGoal: number;

    /**
     * @generated from field: bool map_existing_investments = 3;
     */
    mapExistingInvestments: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.AcceptPlanStep.
 * Use `create(AcceptPlanStepSchema)` to create a new message.
 */
export const AcceptPlanStepSchema: GenMessage<AcceptPlanStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 35);

/**
 * @generated from message com.stablemoney.api.identity.MonthlyPledgeStep
 */
export type MonthlyPledgeStep =
  Message<"com.stablemoney.api.identity.MonthlyPledgeStep"> & {
    /**
     * @generated from field: double monthly_pledge_amount = 1;
     */
    monthlyPledgeAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.MonthlyPledgeStep.
 * Use `create(MonthlyPledgeStepSchema)` to create a new message.
 */
export const MonthlyPledgeStepSchema: GenMessage<MonthlyPledgeStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 36);

/**
 * @generated from message com.stablemoney.api.identity.EmergencyFundDashboardResponse
 */
export type EmergencyFundDashboardResponse =
  Message<"com.stablemoney.api.identity.EmergencyFundDashboardResponse"> & {
    /**
     * @generated from field: bool has_done_first_investment = 1;
     */
    hasDoneFirstInvestment: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.GetGoalDetailsResponse goal_details = 2;
     */
    goalDetails?: GetGoalDetailsResponse;

    /**
     * @generated from field: com.stablemoney.api.identity.PledgeDetail pledge = 3;
     */
    pledge?: PledgeDetail;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankForGoalResponse recommended_banks = 4;
     */
    recommendedBanks: RecommendedBankForGoalResponse[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankForGoalResponse chosen_banks = 5;
     */
    chosenBanks: RecommendedBankForGoalResponse[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedBankForGoalResponse other_banks = 6;
     */
    otherBanks: RecommendedBankForGoalResponse[];

    /**
     * @generated from field: bool is_new_ef_user = 7;
     */
    isNewEfUser: boolean;

    /**
     * @generated from field: string recommended_savings_prompt = 8;
     */
    recommendedSavingsPrompt: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.EmergencyFundDashboardResponse.
 * Use `create(EmergencyFundDashboardResponseSchema)` to create a new message.
 */
export const EmergencyFundDashboardResponseSchema: GenMessage<EmergencyFundDashboardResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 37);

/**
 * @generated from message com.stablemoney.api.identity.PledgeDetail
 */
export type PledgeDetail =
  Message<"com.stablemoney.api.identity.PledgeDetail"> & {
    /**
     * @generated from field: double monthly_pledge = 1;
     */
    monthlyPledge: number;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.Contribution contributions = 2;
     */
    contributions: Contribution[];

    /**
     * @generated from field: int64 next_contribution_days = 3;
     */
    nextContributionDays: bigint;
  };

/**
 * Describes the message com.stablemoney.api.identity.PledgeDetail.
 * Use `create(PledgeDetailSchema)` to create a new message.
 */
export const PledgeDetailSchema: GenMessage<PledgeDetail> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 38);

/**
 * @generated from message com.stablemoney.api.identity.Contribution
 */
export type Contribution =
  Message<"com.stablemoney.api.identity.Contribution"> & {
    /**
     * @generated from field: string month = 1;
     */
    month: string;

    /**
     * @generated from field: string year = 2;
     */
    year: string;

    /**
     * @generated from field: com.stablemoney.api.identity.ContributionStatus status = 3;
     */
    status: ContributionStatus;
  };

/**
 * Describes the message com.stablemoney.api.identity.Contribution.
 * Use `create(ContributionSchema)` to create a new message.
 */
export const ContributionSchema: GenMessage<Contribution> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_Goal, 39);

/**
 * @generated from enum com.stablemoney.api.identity.GoalType
 */
export enum GoalType {
  /**
   * @generated from enum value: UNKNOWN_GOAL_TYPE = 0;
   */
  UNKNOWN_GOAL_TYPE = 0,

  /**
   * @generated from enum value: EMERGENCY_FUND = 1;
   */
  EMERGENCY_FUND = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.GoalType.
 */
export const GoalTypeSchema: GenEnum<GoalType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Goal, 0);

/**
 * @generated from enum com.stablemoney.api.identity.EmergencyFundExpenseCategory
 */
export enum EmergencyFundExpenseCategory {
  /**
   * @generated from enum value: HOUSEHOLD_EXPENSES = 0;
   */
  HOUSEHOLD_EXPENSES = 0,

  /**
   * @generated from enum value: KIDS_EXPENSES = 1;
   */
  KIDS_EXPENSES = 1,

  /**
   * @generated from enum value: TRAVEL_EXPENSES = 2;
   */
  TRAVEL_EXPENSES = 2,

  /**
   * @generated from enum value: PARENTS_HEALTH_EXPENSES = 3;
   */
  PARENTS_HEALTH_EXPENSES = 3,

  /**
   * @generated from enum value: KIDS_EDUCATION_EXPENSES = 4;
   */
  KIDS_EDUCATION_EXPENSES = 4,

  /**
   * @generated from enum value: JOB_LOSS_EXPENSES = 5;
   */
  JOB_LOSS_EXPENSES = 5,

  /**
   * @generated from enum value: MEDICAL_EXPENSES = 6;
   */
  MEDICAL_EXPENSES = 6,

  /**
   * @generated from enum value: REPAIRS_EXPENSES = 7;
   */
  REPAIRS_EXPENSES = 7,

  /**
   * @generated from enum value: OTHER_EXPENSES = 8;
   */
  OTHER_EXPENSES = 8,
}

/**
 * Describes the enum com.stablemoney.api.identity.EmergencyFundExpenseCategory.
 */
export const EmergencyFundExpenseCategorySchema: GenEnum<EmergencyFundExpenseCategory> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Goal, 1);

/**
 * @generated from enum com.stablemoney.api.identity.EmergencyFundUserCohort
 */
export enum EmergencyFundUserCohort {
  /**
   * @generated from enum value: HAS_KIDS_AND_PARENTS_COHORT = 0;
   */
  HAS_KIDS_AND_PARENTS_COHORT = 0,

  /**
   * @generated from enum value: HAS_KIDS_COHORT = 1;
   */
  HAS_KIDS_COHORT = 1,

  /**
   * @generated from enum value: HAS_PARENTS_COHORT = 2;
   */
  HAS_PARENTS_COHORT = 2,

  /**
   * @generated from enum value: MARRIED_COHORT = 3;
   */
  MARRIED_COHORT = 3,

  /**
   * @generated from enum value: SINGLE_COHORT = 4;
   */
  SINGLE_COHORT = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.EmergencyFundUserCohort.
 */
export const EmergencyFundUserCohortSchema: GenEnum<EmergencyFundUserCohort> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Goal, 2);

/**
 * @generated from enum com.stablemoney.api.identity.ContributionStatus
 */
export enum ContributionStatus {
  /**
   * @generated from enum value: UNKNOWN_CONTRIBUTION_STATUS = 0;
   */
  UNKNOWN_CONTRIBUTION_STATUS = 0,

  /**
   * @generated from enum value: PENDING_CONTRIBUTION_STATUS = 1;
   */
  PENDING_CONTRIBUTION_STATUS = 1,

  /**
   * @generated from enum value: COMPLETED_CONTRIBUTION_STATUS = 2;
   */
  COMPLETED_CONTRIBUTION_STATUS = 2,

  /**
   * @generated from enum value: MISSED_CONTRIBUTION_STATUS = 3;
   */
  MISSED_CONTRIBUTION_STATUS = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.ContributionStatus.
 */
export const ContributionStatusSchema: GenEnum<ContributionStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_Goal, 3);
