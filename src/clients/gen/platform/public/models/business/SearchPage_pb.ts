// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/SearchPage.proto (package com.stablemoney.api.identity, syntax proto3)

import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv2";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { BankResponseWithFdData } from "./Collection_pb.js";
import { file_public_models_business_Collection } from "./Collection_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/SearchPage.proto.
 */
export const file_public_models_business_SearchPage: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CidwdWJsaWMvbW9kZWxzL2J1c2luZXNzL1NlYXJjaFBhZ2UucHJvdG8SHGNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkimwEKDlNlYXJjaFBhZ2VJdGVtElgKGmJhbmtfcmVzcG9uc2Vfd2l0aF9mZF9kYXRhGAEgASgLMjQuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5CYW5rUmVzcG9uc2VXaXRoRmREYXRhEhQKDGRpc3BsYXlfbmFtZRgCIAEoCRIZChFvcGVyYXRpbmdfYmFua19pZBgDIAEoCSJxChJTZWFyY2hQYWdlUmVzcG9uc2USRgoQc2VhcmNoX3BhZ2VfaXRlbRgBIAMoCzIsLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuU2VhcmNoUGFnZUl0ZW0SEwoLc2VhcmNoX2hpdHMYAiABKAVCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzc1ABYgZwcm90bzM",
    [file_public_models_business_Collection],
  );

/**
 * @generated from message com.stablemoney.api.identity.SearchPageItem
 */
export type SearchPageItem =
  Message<"com.stablemoney.api.identity.SearchPageItem"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponseWithFdData bank_response_with_fd_data = 1;
     */
    bankResponseWithFdData?: BankResponseWithFdData;

    /**
     * @generated from field: string display_name = 2;
     */
    displayName: string;

    /**
     * @generated from field: string operating_bank_id = 3;
     */
    operatingBankId: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.SearchPageItem.
 * Use `create(SearchPageItemSchema)` to create a new message.
 */
export const SearchPageItemSchema: GenMessage<SearchPageItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_SearchPage, 0);

/**
 * @generated from message com.stablemoney.api.identity.SearchPageResponse
 */
export type SearchPageResponse =
  Message<"com.stablemoney.api.identity.SearchPageResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.SearchPageItem search_page_item = 1;
     */
    searchPageItem: SearchPageItem[];

    /**
     * @generated from field: int32 search_hits = 2;
     */
    searchHits: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.SearchPageResponse.
 * Use `create(SearchPageResponseSchema)` to create a new message.
 */
export const SearchPageResponseSchema: GenMessage<SearchPageResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_SearchPage, 1);
