// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/FixedDepositRecommendation.proto (package com.stablemoney.api.business.recommendation, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Bank } from "./Bank_pb.js";
import { file_public_models_business_Bank } from "./Bank_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/FixedDepositRecommendation.proto.
 */
export const file_public_models_business_FixedDepositRecommendation: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CjdwdWJsaWMvbW9kZWxzL2J1c2luZXNzL0ZpeGVkRGVwb3NpdFJlY29tbWVuZGF0aW9uLnByb3RvEitjb20uc3RhYmxlbW9uZXkuYXBpLmJ1c2luZXNzLnJlY29tbWVuZGF0aW9uIoQCCiVHZXRJbnN1cmVkRml4ZWREZXBvc2l0c1JlY29tbWVuZGF0aW9uEnIKC2ludmVzdG1lbnRzGAEgAygLMl0uY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5yZWNvbW1lbmRhdGlvbi5HZXRJbnN1cmVkRml4ZWREZXBvc2l0c1JlY29tbWVuZGF0aW9uLkludmVzdG1lbnQaZwoKSW52ZXN0bWVudBIsCgRiYW5rGAEgASgLMh4uY29tLnN0YWJsZW1vbmV5LmFwaS5iYW5rLkJhbmsSFwoPaW52ZXN0ZWRfYW1vdW50GAIgASgBEhIKCm1heF9hbW91bnQYAyABKAEilQUKJkdldE5leHRCZXN0Rml4ZWREZXBvc2l0c1JlY29tbWVuZGF0aW9uEngKDmZpeGVkX2RlcG9zaXRzGAEgAygLMmAuY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5yZWNvbW1lbmRhdGlvbi5HZXROZXh0QmVzdEZpeGVkRGVwb3NpdHNSZWNvbW1lbmRhdGlvbi5GaXhlZERlcG9zaXQa8AMKDEZpeGVkRGVwb3NpdBIsCgRiYW5rGAEgASgLMh4uY29tLnN0YWJsZW1vbmV5LmFwaS5iYW5rLkJhbmsSdwoGdGVudXJlGAIgASgLMmcuY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5yZWNvbW1lbmRhdGlvbi5HZXROZXh0QmVzdEZpeGVkRGVwb3NpdHNSZWNvbW1lbmRhdGlvbi5GaXhlZERlcG9zaXQuVGVudXJlEhUKDWludGVyZXN0X3JhdGUYAyABKAESEAoIYmVuZWZpdHMYBSADKAkSFwoPaW5zdGFudF9ib29raW5nGAYgASgIEh0KFXJlY29tbWVuZGF0aW9uX3JlYXNvbhgIIAEoCRIaChJtaW5fdGVudXJlX2luX2RheXMYCSABKAUSGgoSbWF4X3RlbnVyZV9pbl9kYXlzGAogASgFEgwKBHhpcnIYCyABKAEaRQoGVGVudXJlEg4KBnRlbnVyZRgBIAEoCRIMCgRkYXlzGAIgASgFEg4KBm1vbnRocxgDIAEoBRINCgV5ZWFycxgEIAEoBSJLCgxEdXJhdGlvblR5cGUSCwoHVU5LTk9XThAAEg4KClNIT1JUX1RFUk0QARIPCgtNRURJVU1fVEVSTRACEg0KCUxPTkdfVEVSTRADIo0CCiNPbmVDbGlja0ZpeGVkRGVwb3NpdHNSZWNvbW1lbmRhdGlvbhJ2CgViYW5rcxgBIAMoCzJnLmNvbS5zdGFibGVtb25leS5hcGkuYnVzaW5lc3MucmVjb21tZW5kYXRpb24uT25lQ2xpY2tGaXhlZERlcG9zaXRzUmVjb21tZW5kYXRpb24uT25lQ2xpY2tJbnZlc3RtZW50QmFuaxpuChZPbmVDbGlja0ludmVzdG1lbnRCYW5rEiwKBGJhbmsYASABKAsyHi5jb20uc3RhYmxlbW9uZXkuYXBpLmJhbmsuQmFuaxIYChBsYXN0X2ludmVzdGVkX29uGAIgASgJEgwKBHRhZ3MYAyADKAlCLworY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5yZWNvbW1lbmRhdGlvblABYgZwcm90bzM",
    [file_public_models_business_Bank],
  );

/**
 * @generated from message com.stablemoney.api.business.recommendation.GetInsuredFixedDepositsRecommendation
 */
export type GetInsuredFixedDepositsRecommendation =
  Message<"com.stablemoney.api.business.recommendation.GetInsuredFixedDepositsRecommendation"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.recommendation.GetInsuredFixedDepositsRecommendation.Investment investments = 1;
     */
    investments: GetInsuredFixedDepositsRecommendation_Investment[];
  };

/**
 * Describes the message com.stablemoney.api.business.recommendation.GetInsuredFixedDepositsRecommendation.
 * Use `create(GetInsuredFixedDepositsRecommendationSchema)` to create a new message.
 */
export const GetInsuredFixedDepositsRecommendationSchema: GenMessage<GetInsuredFixedDepositsRecommendation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDepositRecommendation, 0);

/**
 * @generated from message com.stablemoney.api.business.recommendation.GetInsuredFixedDepositsRecommendation.Investment
 */
export type GetInsuredFixedDepositsRecommendation_Investment =
  Message<"com.stablemoney.api.business.recommendation.GetInsuredFixedDepositsRecommendation.Investment"> & {
    /**
     * @generated from field: com.stablemoney.api.bank.Bank bank = 1;
     */
    bank?: Bank;

    /**
     * @generated from field: double invested_amount = 2;
     */
    investedAmount: number;

    /**
     * @generated from field: double max_amount = 3;
     */
    maxAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.business.recommendation.GetInsuredFixedDepositsRecommendation.Investment.
 * Use `create(GetInsuredFixedDepositsRecommendation_InvestmentSchema)` to create a new message.
 */
export const GetInsuredFixedDepositsRecommendation_InvestmentSchema: GenMessage<GetInsuredFixedDepositsRecommendation_Investment> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDepositRecommendation, 0, 0);

/**
 * @generated from message com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation
 */
export type GetNextBestFixedDepositsRecommendation =
  Message<"com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit fixed_deposits = 1;
     */
    fixedDeposits: GetNextBestFixedDepositsRecommendation_FixedDeposit[];
  };

/**
 * Describes the message com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.
 * Use `create(GetNextBestFixedDepositsRecommendationSchema)` to create a new message.
 */
export const GetNextBestFixedDepositsRecommendationSchema: GenMessage<GetNextBestFixedDepositsRecommendation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDepositRecommendation, 1);

/**
 * @generated from message com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit
 */
export type GetNextBestFixedDepositsRecommendation_FixedDeposit =
  Message<"com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit"> & {
    /**
     * @generated from field: com.stablemoney.api.bank.Bank bank = 1;
     */
    bank?: Bank;

    /**
     * @generated from field: com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit.Tenure tenure = 2;
     */
    tenure?: GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure;

    /**
     * @generated from field: double interest_rate = 3;
     */
    interestRate: number;

    /**
     * @generated from field: repeated string benefits = 5;
     */
    benefits: string[];

    /**
     * @generated from field: bool instant_booking = 6;
     */
    instantBooking: boolean;

    /**
     * @generated from field: string recommendation_reason = 8;
     */
    recommendationReason: string;

    /**
     * @generated from field: int32 min_tenure_in_days = 9;
     */
    minTenureInDays: number;

    /**
     * @generated from field: int32 max_tenure_in_days = 10;
     */
    maxTenureInDays: number;

    /**
     * @generated from field: double xirr = 11;
     */
    xirr: number;
  };

/**
 * Describes the message com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit.
 * Use `create(GetNextBestFixedDepositsRecommendation_FixedDepositSchema)` to create a new message.
 */
export const GetNextBestFixedDepositsRecommendation_FixedDepositSchema: GenMessage<GetNextBestFixedDepositsRecommendation_FixedDeposit> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDepositRecommendation, 1, 0);

/**
 * @generated from message com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit.Tenure
 */
export type GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure =
  Message<"com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit.Tenure"> & {
    /**
     * @generated from field: string tenure = 1;
     */
    tenure: string;

    /**
     * @generated from field: int32 days = 2;
     */
    days: number;

    /**
     * @generated from field: int32 months = 3;
     */
    months: number;

    /**
     * @generated from field: int32 years = 4;
     */
    years: number;
  };

/**
 * Describes the message com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit.Tenure.
 * Use `create(GetNextBestFixedDepositsRecommendation_FixedDeposit_TenureSchema)` to create a new message.
 */
export const GetNextBestFixedDepositsRecommendation_FixedDeposit_TenureSchema: GenMessage<GetNextBestFixedDepositsRecommendation_FixedDeposit_Tenure> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDepositRecommendation, 1, 0, 0);

/**
 * @generated from enum com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit.DurationType
 */
export enum GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: SHORT_TERM = 1;
   */
  SHORT_TERM = 1,

  /**
   * @generated from enum value: MEDIUM_TERM = 2;
   */
  MEDIUM_TERM = 2,

  /**
   * @generated from enum value: LONG_TERM = 3;
   */
  LONG_TERM = 3,
}

/**
 * Describes the enum com.stablemoney.api.business.recommendation.GetNextBestFixedDepositsRecommendation.FixedDeposit.DurationType.
 */
export const GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationTypeSchema: GenEnum<GetNextBestFixedDepositsRecommendation_FixedDeposit_DurationType> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_FixedDepositRecommendation, 1, 0, 0);

/**
 * @generated from message com.stablemoney.api.business.recommendation.OneClickFixedDepositsRecommendation
 */
export type OneClickFixedDepositsRecommendation =
  Message<"com.stablemoney.api.business.recommendation.OneClickFixedDepositsRecommendation"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.recommendation.OneClickFixedDepositsRecommendation.OneClickInvestmentBank banks = 1;
     */
    banks: OneClickFixedDepositsRecommendation_OneClickInvestmentBank[];
  };

/**
 * Describes the message com.stablemoney.api.business.recommendation.OneClickFixedDepositsRecommendation.
 * Use `create(OneClickFixedDepositsRecommendationSchema)` to create a new message.
 */
export const OneClickFixedDepositsRecommendationSchema: GenMessage<OneClickFixedDepositsRecommendation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDepositRecommendation, 2);

/**
 * @generated from message com.stablemoney.api.business.recommendation.OneClickFixedDepositsRecommendation.OneClickInvestmentBank
 */
export type OneClickFixedDepositsRecommendation_OneClickInvestmentBank =
  Message<"com.stablemoney.api.business.recommendation.OneClickFixedDepositsRecommendation.OneClickInvestmentBank"> & {
    /**
     * @generated from field: com.stablemoney.api.bank.Bank bank = 1;
     */
    bank?: Bank;

    /**
     * @generated from field: string last_invested_on = 2;
     */
    lastInvestedOn: string;

    /**
     * @generated from field: repeated string tags = 3;
     */
    tags: string[];
  };

/**
 * Describes the message com.stablemoney.api.business.recommendation.OneClickFixedDepositsRecommendation.OneClickInvestmentBank.
 * Use `create(OneClickFixedDepositsRecommendation_OneClickInvestmentBankSchema)` to create a new message.
 */
export const OneClickFixedDepositsRecommendation_OneClickInvestmentBankSchema: GenMessage<OneClickFixedDepositsRecommendation_OneClickInvestmentBank> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_FixedDepositRecommendation, 2, 0);
