// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/business/BankListing.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type {
  BankType,
  CompoundingFrequencyType,
  InterestPayoutType,
  InvestabilityStatus,
  InvestorType,
  RedirectDeeplink,
} from "./BusinessCommon_pb.js";
import { file_public_models_business_BusinessCommon } from "./BusinessCommon_pb.js";
import type { BankResponse, FixedDepositResponse } from "./Collection_pb.js";
import { file_public_models_business_Collection } from "./Collection_pb.js";
import type { Faq } from "./Faq_pb.js";
import { file_public_models_business_Faq } from "./Faq_pb.js";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/business/BankListing.proto.
 */
export const file_public_models_business_BankListing: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CihwdWJsaWMvbW9kZWxzL2J1c2luZXNzL0JhbmtMaXN0aW5nLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IkoKGkZEUmV0dXJuQ2FsY3VsYXRvclJlc3BvbnNlEhQKDGV4dHJhX2luY29tZRgBIAEoARIWCg5tYXR1cmVkX2Ftb3VudBgCIAEoASLfCAobQmFua0xpc3RpbmdQYWdlRGF0YVJlc3BvbnNlEkEKDWJhbmtfcmVzcG9uc2UYASABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkJhbmtSZXNwb25zZRIYChBmZF9ib29raW5nX3N0YXRzGAIgASgJEhYKDmFzc3VyYW5jZV9saW5lGAMgASgJEhoKEmFzc3VyYW5jZV9sb2dvX3VybBgEIAEoCRJQChVzdGFibGVfbW9uZXlfYW5hbHlzaXMYBSADKAsyMS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlN0YWJsZU1vbmV5QW5hbHlzaXMSYAodZ2VuZXJhbF9jaXRpemVuX2ludGVyZXN0X2RhdGEYBiABKAsyOS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkJhbmtMaXN0aW5nUGFnZUludGVyZXN0RGF0YRJfChxzZW5pb3JfY2l0aXplbl9pbnRlcmVzdF9kYXRhGAcgASgLMjkuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5CYW5rTGlzdGluZ1BhZ2VJbnRlcmVzdERhdGESKQohc2VuaW9yX2NpdGl6ZW5fcm9pX21heF9kaWZmZXJlbmNlGAogASgBEhwKFHJhdGVfbGFzdF91cGRhdGVkX2F0GAsgASgJEhUKDXJhdGVzX3BkZl91cmwYDCABKAkSFwoPYWJvdXRfYmFua19pbmZvGA0gASgJElYKGGJhbmtfcHJvbWluZW50X3BlcnNvbm5lbBgOIAMoCzI0LmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQmFua1Byb21pbmVudFBlcnNvbm5lbBI7CgpiYW5rX2F3YXJkGA8gAygLMicuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5CYW5rQXdhcmQSOgoKc2FmZV9iYW5rcxgQIAMoCzImLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuU2FmZUJhbmsSWAoZYmFua19jdXN0b21lcl90ZXN0aW1vbmlhbBgRIAMoCzI1LmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQmFua0N1c3RvbWVyVGVzdGltb25pYWwSSgoSaW52ZXN0X25vd19tZXNzYWdlGBIgAygLMi4uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5JbnZlc3ROb3dNZXNzYWdlEjcKCGJhbmtfZmFxGBMgAygLMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5mYXEuRmFxEkkKEXJlZGlyZWN0X2RlZXBsaW5rGBQgASgLMi4uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5SZWRpcmVjdERlZXBsaW5rEiYKHmlzX2ZpcnN0X3RpbWVfb2ZmZXJfYXBwbGljYWJsZRgVIAEoCCKAAwobQmFua0xpc3RpbmdQYWdlSW50ZXJlc3REYXRhEk8KE2hpZ2hlc3RfaW50ZXJlc3RfZmQYASABKAsyMi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkZpeGVkRGVwb3NpdFJlc3BvbnNlEkoKDmZpeGVkX2RlcG9zaXRzGAIgAygLMjIuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5GaXhlZERlcG9zaXRSZXNwb25zZRJECg9yZWNvbW1lbmRlZF9mZHMYAyADKAsyKy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlJlY29tbWVuZGVkRmQSEgoKZGlzY2xhaW1lchgEIAEoCRJBCg1pbnZlc3Rvcl90eXBlGAUgASgOMiouY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5JbnZlc3RvclR5cGUSJwofcm9pX21heF9kaWZmZXJlbmNlX2Zyb21fZ2VuZXJhbBgGIAEoASIjChBJbnZlc3ROb3dNZXNzYWdlEg8KB21lc3NhZ2UYASABKAkiKgoTU3RhYmxlTW9uZXlBbmFseXNpcxITCgtkZXNjcmlwdGlvbhgBIAEoCSItCghTYWZlQmFuaxIPCgdiYW5rX2lkGAEgASgJEhAKCGxvZ29fdXJsGAIgASgJImEKFkJhbmtQcm9taW5lbnRQZXJzb25uZWwSDwoHaGVhZGluZxgBIAEoCRIMCgRuYW1lGAIgASgJEhMKC2Rlc2NyaXB0aW9uGAMgASgJEhMKC3BpY3R1cmVfdXJsGAQgASgJIkEKCUJhbmtBd2FyZBINCgV0aXRsZRgBIAEoCRIQCghpY29uX3VybBgCIAEoCRITCgtyZWNlaXZlZF9ieRgDIAEoCSJiChdCYW5rQ3VzdG9tZXJUZXN0aW1vbmlhbBIPCgdjb21tZW50GAEgASgJEgwKBG5hbWUYAiABKAkSEwoLZGVzaWduYXRpb24YAyABKAkSEwoLcGljdHVyZV91cmwYBCABKAkiigEKDVJlY29tbWVuZGVkRmQSEwoLZGVzY3JpcHRpb24YASABKAkSUgoWZml4ZWRfZGVwb3NpdF9yZXNwb25zZRgCIAEoCzIyLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuRml4ZWREZXBvc2l0UmVzcG9uc2USEAoIaWNvbl91cmwYAyABKAki3wUKDUJhbmtMaXN0aW5nVjISQQoNYmFua19yZXNwb25zZRgBIAEoCzIqLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQmFua1Jlc3BvbnNlEkAKDXRvcF9pbmZvX2NhcmQYAiADKAsyKS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlRvcEluZm9DYXJkEjgKCWJhbmtfdGFncxgDIAMoCzIlLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQmFua1RhZxJQCg1pbnRlcmVzdF9kYXRhGAQgAygLMjkuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5CYW5rTGlzdGluZ1BhZ2VJbnRlcmVzdERhdGESSwoTYmFua19zZWxsaW5nX3BvaW50cxgFIAMoCzIuLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQmFua1NlbGxpbmdQb2ludBJJChFyZWRpcmVjdF9kZWVwbGluaxgGIAEoCzIuLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUmVkaXJlY3REZWVwbGluaxIYChBmZF9ib29raW5nX3N0YXRzGAcgASgJEjcKCGJhbmtfZmFxGAggAygLMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5mYXEuRmFxEhwKFHJhdGVfbGFzdF91cGRhdGVkX2F0GAogASgJEhUKDXJhdGVzX3BkZl91cmwYCyABKAkSFgoOYXNzdXJhbmNlX2xpbmUYDCABKAkSGgoSYXNzdXJhbmNlX2xvZ29fdXJsGA0gASgJEiYKHmlzX2ZpcnN0X3RpbWVfb2ZmZXJfYXBwbGljYWJsZRgOIAEoCBIYChBpc192a3ljX3JlcXVpcmVkGA8gASgIEicKH2JhbmtfZmFxX2NhdGVnb3J5X3ByaW9yaXR5X2xpc3QYHCADKAkiowEKE0hpZ2hlc3RSYXRlS2V5VmFsdWUSQQoNaW52ZXN0b3JfdHlwZRgBIAEoDjIqLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuSW52ZXN0b3JUeXBlEkkKDWZpeGVkX2RlcG9zaXQYAiABKAsyMi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkZpeGVkRGVwb3NpdFJlc3BvbnNlInEKFUludmVzdGFibGVCYW5rQ29tcGFyZRIVCg1pbnRlcmVzdF9yYXRlGAEgASgBEkEKDWJhbmtfcmVzcG9uc2UYAiABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkJhbmtSZXNwb25zZSK8AwoYTm9uSW52ZXN0YWJsZUJhbmtDb21wYXJlEhIKBnRpdGxlcxgBIAMoCUICGAESIAoUaGlnaGVzdF9iYW5rX2RldGFpbHMYAiADKAlCAhgBEhgKDGJhbmtfZGV0YWlscxgDIAMoCUICGAESTgoaaGlnaGVzdF9yYXRlX2JhbmtfcmVzcG9uc2UYBCABKAsyKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkJhbmtSZXNwb25zZRJaCgpsaW5lX2l0ZW1zGAYgAygLMkYuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Ob25JbnZlc3RhYmxlQmFua0NvbXBhcmUuQ29tcGFyZUxpbmVJdGVtGqMBCg9Db21wYXJlTGluZUl0ZW0SDQoFdGl0bGUYASABKAkSGgoSaGlnaGVzdF9iYW5rX3BvaW50GAIgASgJEhoKEmN1cnJlbnRfYmFua19wb2ludBgDIAEoCRJJChFtb3JlX2RldGFpbHNfbGluaxgEIAEoCzIuLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUmVkaXJlY3REZWVwbGluayJpChdGZFdpdGhkcmF3YWxDYWxjdWxhdGlvbhIOCgZ0ZW51cmUYASABKAkSGQoRd2l0aGRyYXdhbF9hbW91bnQYAiABKAESDAoEcmF0ZRgDIAEoARIVCg1vcmlnaW5hbF9yYXRlGAQgASgBImIKHkZkV2l0aGRyYXdhbENhbGN1bGF0aW9uRGV0YWlscxIVCg1pbnRlcmVzdF9yYXRlGAEgASgBEhkKEWludmVzdG1lbnRfYW1vdW50GAIgASgBEg4KBnRlbnVyZRgDIAEoCSKuCQoNQmFua0xpc3RpbmdWMxJBCg1iYW5rX3Jlc3BvbnNlGAEgASgLMiouY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5CYW5rUmVzcG9uc2USUAoNaW50ZXJlc3RfZGF0YRgEIAMoCzI5LmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQmFua0xpc3RpbmdQYWdlSW50ZXJlc3REYXRhEkkKEXJlZGlyZWN0X2RlZXBsaW5rGAYgASgLMi4uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5SZWRpcmVjdERlZXBsaW5rEjcKCGJhbmtfZmFxGAggAygLMiUuY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzcy5mYXEuRmFxEhgKEGlzX3ZreWNfcmVxdWlyZWQYDyABKAgSSwoQaGlnaGVzdF9yYXRlX21hcBgRIAMoCzIxLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuSGlnaGVzdFJhdGVLZXlWYWx1ZRIpCiFpbnZlc3RhYmxlX2JhbmtfZG93bl90aW1lX21lc3NhZ2UYEiABKAkSVAoXaW52ZXN0YWJsZV9iYW5rX2NvbXBhcmUYEyADKAsyMy5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkludmVzdGFibGVCYW5rQ29tcGFyZRJbChtub25faW52ZXN0YWJsZV9iYW5rX2NvbXBhcmUYFCABKAsyNi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5Lk5vbkludmVzdGFibGVCYW5rQ29tcGFyZRJYChlmZF93aXRoZHJhd2FsX2NhbGN1bGF0aW9uGBUgAygLMjUuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5GZFdpdGhkcmF3YWxDYWxjdWxhdGlvbhIjChtub25faW52ZXN0YWJsZV9iYW5rX21lc3NhZ2UYFiABKAkSFgoOYmFua19pbWFnZV91cmwYFyABKAkSZwohZmRfd2l0aGRyYXdhbF9jYWxjdWxhdGlvbl9kZXRhaWxzGBggASgLMjwuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5GZFdpdGhkcmF3YWxDYWxjdWxhdGlvbkRldGFpbHMSIgoaaXNfcmVwZWF0X3VzZXJfdG9fcGxhdGZvcm0YGSABKAgSHgoWaXNfcmVwZWF0X3VzZXJfdG9fYmFuaxgaIAEoCBInCh9iYW5rX2ZhcV9jYXRlZ29yeV9wcmlvcml0eV9saXN0GBwgAygJEmoKG2RldmljZV9pbnZlc3RhYmlsaXR5X3N0YXR1cxgeIAEoDjJFLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQmFua0xpc3RpbmdWMy5EZXZpY2VJbnZlc3RhYmlsaXR5U3RhdHVzImYKGURldmljZUludmVzdGFiaWxpdHlTdGF0dXMSCwoHVU5LTk9XThAAEgoKBkFDVElWRRABEgwKCElOQUNUSVZFEAISDwoLQ09NSU5HX1NPT04QAxIRCg1BQ1RJVkVfT05fQVBQEAQinAUKC0JhbmtMaXN0aW5nEgoKAmlkGAEgASgJEgwKBHNsdWcYAiABKAkSEAoIbG9nb191cmwYAyABKAkSDAoEbmFtZRgEIAEoCRIQCghrZXl3b3JkcxgFIAEoCRIiChphY2NvdW50X3JlcXVpcmVkX3RvX2ludmVzdBgGIAEoCBI5CgliYW5rX3R5cGUYByABKA4yJi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkJhbmtUeXBlEk8KFGludmVzdGFiaWxpdHlfc3RhdHVzGAggASgOMjEuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5JbnZlc3RhYmlsaXR5U3RhdHVzEjMKBGZhcXMYCSADKAsyJS5jb20uc3RhYmxlbW9uZXkuYXBpLmJ1c2luZXNzLmZhcS5GYXESSgoSd2l0aGRyYXdhbF9vcHRpb25zGAogAygLMi4uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5XaXRoZHJhd2FsT3B0aW9uEkYKDmZpeGVkX2RlcG9zaXRzGAsgAygLMi4uY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5GaXhlZERlcG9zaXRJbmZvEjkKCWRvd250aW1lcxgMIAMoCzImLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuRG93bnRpbWUSQwoLaW52ZXN0X2xpbmsYDSABKAsyLi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlJlZGlyZWN0RGVlcGxpbmsSHwoXd2l0aGRyYXdhbF9wZW5hbHR5X3JhdGUYDiABKAESJwofYmFua19mYXFfY2F0ZWdvcnlfcHJpb3JpdHlfbGlzdBgPIAMoCSKuAwoQRml4ZWREZXBvc2l0SW5mbxIKCgJpZBgBIAEoCRIMCgRyYXRlGAIgASgBEhMKC21pbl9kZXBvc2l0GAMgASgBEhMKC21heF9kZXBvc2l0GAQgASgBEkEKDWludmVzdG9yX3R5cGUYBSABKA4yKi5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkludmVzdG9yVHlwZRIoCiBpc19wcmVfbWF0dXJlX3dpdGhkcmF3YWxfYWxsb3dlZBgGIAEoCBJOChRpbnRlcmVzdF9wYXlvdXRfdHlwZRgHIAEoDjIwLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuSW50ZXJlc3RQYXlvdXRUeXBlEiIKGmlzX2xvYW5fYWdhaW5zdF9mZF9hbGxvd2VkGAggASgIEiUKHWlzX3BhcnRpYWxfd2l0aGRyYXdhbF9hbGxvd2VkGAkgASgIEhYKDmRpc3BsYXlfdGVudXJlGAogASgJEhoKEm1pbl90ZW51cmVfaW5fZGF5cxgLIAEoDRIaChJtYXhfdGVudXJlX2luX2RheXMYDCABKA0iLAoIRG93bnRpbWUSEAoIc3RhcnRfb24YASABKAQSDgoGZW5kX29uGAIgASgEIpwCChBXaXRoZHJhd2FsT3B0aW9uEgwKBHJhdGUYASABKAESGgoSbWluX3RlbnVyZV9pbl9kYXlzGAIgASgNEhoKEm1heF90ZW51cmVfaW5fZGF5cxgDIAEoDRITCgttaW5fZGVwb3NpdBgEIAEoARITCgttYXhfZGVwb3NpdBgFIAEoARJBCg1pbnZlc3Rvcl90eXBlGAYgASgOMiouY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5JbnZlc3RvclR5cGUSVQoVY29tcG91bmRpbmdfZnJlcXVlbmN5GAcgASgOMjYuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5Db21wb3VuZGluZ0ZyZXF1ZW5jeVR5cGUiiwEKC1RvcEluZm9DYXJkEg0KBXRpdGxlGAEgASgJEhMKC2Rlc2NyaXB0aW9uGAIgASgJEhAKCGljb25fdXJsGAMgASgJEh8KF2luZm9fYm90dG9tX3NoZWV0X3RpdGxlGAQgASgJEiUKHWluZm9fYm90dG9tX3NoZWV0X2Rlc2NyaXB0aW9uGAUgASgJIj0KB0JhbmtUYWcSDgoGdGFnX2lkGAEgASgJEhAKCHRhZ19uYW1lGAIgASgJEhAKCGljb25fdXJsGAMgASgJIkgKEEJhbmtTZWxsaW5nUG9pbnQSDQoFdGl0bGUYASABKAkSEwoLZGVzY3JpcHRpb24YAiABKAkSEAoIaWNvbl91cmwYAyABKAkiTQoUQmFua0ZpeGVkRGVwb3NpdFN0ZXASDQoFdGl0bGUYASABKAkSEwoLZGVzY3JpcHRpb24YAiABKAkSEQoJaW1hZ2VfdXJsGAMgASgJInQKHUJhbmtGaXhlZERlcG9zaXRTdGVwc1Jlc3BvbnNlElMKF2JhbmtfZml4ZWRfZGVwb3NpdF9zdGVwGAEgAygLMjIuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5CYW5rRml4ZWREZXBvc2l0U3RlcCIuChxCYW5rUmF0ZU5vdGlmaWNhdGlvblJlc3BvbnNlEg4KBnN0YXR1cxgBIAEoCCJUChtCYW5rUmF0ZU5vdGlmaWNhdGlvblJlcXVlc3QSDwoHYmFua19pZBgBIAEoCRIWCgl0b19ub3RpZnkYAiABKAhIAIgBAUIMCgpfdG9fbm90aWZ5IkwKEEFsbEJhbmtzUmVzcG9uc2USOAoFYmFua3MYASADKAsyKS5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LkJhbmtMaXN0aW5nIqEBCg9CcmFuY2hMb2NhdGlvbnMSRgoQYnJhbmNoX2xvY2F0aW9ucxgBIAMoCzIsLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQnJhbmNoTG9jYXRpb24SEQoJY2l0eV9uYW1lGAMgASgJEhYKDnRvdGFsX2JyYW5jaGVzGAUgASgFEhsKE3RvdGFsX2NpdHlfYnJhbmNoZXMYByABKAUi6gEKDkJyYW5jaExvY2F0aW9uEg8KB2FkZHJlc3MYASABKAkSDAoEYXJlYRgCIAEoCRIMCgRjaXR5GAMgASgJEg0KBXN0YXRlGAQgASgJEg8KB3BpbmNvZGUYBSABKAkSRQoHbGF0TG9uZxgGIAEoCzI0LmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuQnJhbmNoTG9jYXRpb24uTGF0TG9uZxIUCgxwaG9uZV9udW1iZXIYByABKAkaLgoHTGF0TG9uZxIQCghsYXRpdHVkZRgBIAEoARIRCglsb25naXR1ZGUYAiABKAFCIAocY29tLnN0YWJsZW1vbmV5LmFwaS5idXNpbmVzc1ABYgZwcm90bzM",
    [
      file_public_models_business_BusinessCommon,
      file_public_models_business_Collection,
      file_public_models_business_Faq,
    ],
  );

/**
 * @generated from message com.stablemoney.api.identity.FDReturnCalculatorResponse
 */
export type FDReturnCalculatorResponse =
  Message<"com.stablemoney.api.identity.FDReturnCalculatorResponse"> & {
    /**
     * @generated from field: double extra_income = 1;
     */
    extraIncome: number;

    /**
     * @generated from field: double matured_amount = 2;
     */
    maturedAmount: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.FDReturnCalculatorResponse.
 * Use `create(FDReturnCalculatorResponseSchema)` to create a new message.
 */
export const FDReturnCalculatorResponseSchema: GenMessage<FDReturnCalculatorResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 0);

/**
 * @generated from message com.stablemoney.api.identity.BankListingPageDataResponse
 */
export type BankListingPageDataResponse =
  Message<"com.stablemoney.api.identity.BankListingPageDataResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank_response = 1;
     */
    bankResponse?: BankResponse;

    /**
     * @generated from field: string fd_booking_stats = 2;
     */
    fdBookingStats: string;

    /**
     * @generated from field: string assurance_line = 3;
     */
    assuranceLine: string;

    /**
     * @generated from field: string assurance_logo_url = 4;
     */
    assuranceLogoUrl: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.StableMoneyAnalysis stable_money_analysis = 5;
     */
    stableMoneyAnalysis: StableMoneyAnalysis[];

    /**
     * @generated from field: com.stablemoney.api.identity.BankListingPageInterestData general_citizen_interest_data = 6;
     */
    generalCitizenInterestData?: BankListingPageInterestData;

    /**
     * @generated from field: com.stablemoney.api.identity.BankListingPageInterestData senior_citizen_interest_data = 7;
     */
    seniorCitizenInterestData?: BankListingPageInterestData;

    /**
     * @generated from field: double senior_citizen_roi_max_difference = 10;
     */
    seniorCitizenRoiMaxDifference: number;

    /**
     * @generated from field: string rate_last_updated_at = 11;
     */
    rateLastUpdatedAt: string;

    /**
     * @generated from field: string rates_pdf_url = 12;
     */
    ratesPdfUrl: string;

    /**
     * @generated from field: string about_bank_info = 13;
     */
    aboutBankInfo: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankProminentPersonnel bank_prominent_personnel = 14;
     */
    bankProminentPersonnel: BankProminentPersonnel[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankAward bank_award = 15;
     */
    bankAward: BankAward[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.SafeBank safe_banks = 16;
     */
    safeBanks: SafeBank[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankCustomerTestimonial bank_customer_testimonial = 17;
     */
    bankCustomerTestimonial: BankCustomerTestimonial[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InvestNowMessage invest_now_message = 18;
     */
    investNowMessage: InvestNowMessage[];

    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 19;
     */
    bankFaq: Faq[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 20;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: bool is_first_time_offer_applicable = 21;
     */
    isFirstTimeOfferApplicable: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankListingPageDataResponse.
 * Use `create(BankListingPageDataResponseSchema)` to create a new message.
 */
export const BankListingPageDataResponseSchema: GenMessage<BankListingPageDataResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 1);

/**
 * @generated from message com.stablemoney.api.identity.BankListingPageInterestData
 */
export type BankListingPageInterestData =
  Message<"com.stablemoney.api.identity.BankListingPageInterestData"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse highest_interest_fd = 1;
     */
    highestInterestFd?: FixedDepositResponse;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FixedDepositResponse fixed_deposits = 2;
     */
    fixedDeposits: FixedDepositResponse[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.RecommendedFd recommended_fds = 3;
     */
    recommendedFds: RecommendedFd[];

    /**
     * @generated from field: string disclaimer = 4;
     */
    disclaimer: string;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestorType investor_type = 5;
     */
    investorType: InvestorType;

    /**
     * @generated from field: double roi_max_difference_from_general = 6;
     */
    roiMaxDifferenceFromGeneral: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankListingPageInterestData.
 * Use `create(BankListingPageInterestDataSchema)` to create a new message.
 */
export const BankListingPageInterestDataSchema: GenMessage<BankListingPageInterestData> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 2);

/**
 * @generated from message com.stablemoney.api.identity.InvestNowMessage
 */
export type InvestNowMessage =
  Message<"com.stablemoney.api.identity.InvestNowMessage"> & {
    /**
     * @generated from field: string message = 1;
     */
    message: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.InvestNowMessage.
 * Use `create(InvestNowMessageSchema)` to create a new message.
 */
export const InvestNowMessageSchema: GenMessage<InvestNowMessage> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 3);

/**
 * @generated from message com.stablemoney.api.identity.StableMoneyAnalysis
 */
export type StableMoneyAnalysis =
  Message<"com.stablemoney.api.identity.StableMoneyAnalysis"> & {
    /**
     * @generated from field: string description = 1;
     */
    description: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.StableMoneyAnalysis.
 * Use `create(StableMoneyAnalysisSchema)` to create a new message.
 */
export const StableMoneyAnalysisSchema: GenMessage<StableMoneyAnalysis> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 4);

/**
 * @generated from message com.stablemoney.api.identity.SafeBank
 */
export type SafeBank = Message<"com.stablemoney.api.identity.SafeBank"> & {
  /**
   * @generated from field: string bank_id = 1;
   */
  bankId: string;

  /**
   * @generated from field: string logo_url = 2;
   */
  logoUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.SafeBank.
 * Use `create(SafeBankSchema)` to create a new message.
 */
export const SafeBankSchema: GenMessage<SafeBank> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 5);

/**
 * @generated from message com.stablemoney.api.identity.BankProminentPersonnel
 */
export type BankProminentPersonnel =
  Message<"com.stablemoney.api.identity.BankProminentPersonnel"> & {
    /**
     * @generated from field: string heading = 1;
     */
    heading: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string description = 3;
     */
    description: string;

    /**
     * @generated from field: string picture_url = 4;
     */
    pictureUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankProminentPersonnel.
 * Use `create(BankProminentPersonnelSchema)` to create a new message.
 */
export const BankProminentPersonnelSchema: GenMessage<BankProminentPersonnel> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 6);

/**
 * @generated from message com.stablemoney.api.identity.BankAward
 */
export type BankAward = Message<"com.stablemoney.api.identity.BankAward"> & {
  /**
   * @generated from field: string title = 1;
   */
  title: string;

  /**
   * @generated from field: string icon_url = 2;
   */
  iconUrl: string;

  /**
   * @generated from field: string received_by = 3;
   */
  receivedBy: string;
};

/**
 * Describes the message com.stablemoney.api.identity.BankAward.
 * Use `create(BankAwardSchema)` to create a new message.
 */
export const BankAwardSchema: GenMessage<BankAward> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 7);

/**
 * @generated from message com.stablemoney.api.identity.BankCustomerTestimonial
 */
export type BankCustomerTestimonial =
  Message<"com.stablemoney.api.identity.BankCustomerTestimonial"> & {
    /**
     * @generated from field: string comment = 1;
     */
    comment: string;

    /**
     * @generated from field: string name = 2;
     */
    name: string;

    /**
     * @generated from field: string designation = 3;
     */
    designation: string;

    /**
     * @generated from field: string picture_url = 4;
     */
    pictureUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankCustomerTestimonial.
 * Use `create(BankCustomerTestimonialSchema)` to create a new message.
 */
export const BankCustomerTestimonialSchema: GenMessage<BankCustomerTestimonial> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 8);

/**
 * @generated from message com.stablemoney.api.identity.RecommendedFd
 */
export type RecommendedFd =
  Message<"com.stablemoney.api.identity.RecommendedFd"> & {
    /**
     * @generated from field: string description = 1;
     */
    description: string;

    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse fixed_deposit_response = 2;
     */
    fixedDepositResponse?: FixedDepositResponse;

    /**
     * @generated from field: string icon_url = 3;
     */
    iconUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.RecommendedFd.
 * Use `create(RecommendedFdSchema)` to create a new message.
 */
export const RecommendedFdSchema: GenMessage<RecommendedFd> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 9);

/**
 * @generated from message com.stablemoney.api.identity.BankListingV2
 */
export type BankListingV2 =
  Message<"com.stablemoney.api.identity.BankListingV2"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank_response = 1;
     */
    bankResponse?: BankResponse;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.TopInfoCard top_info_card = 2;
     */
    topInfoCard: TopInfoCard[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankTag bank_tags = 3;
     */
    bankTags: BankTag[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankListingPageInterestData interest_data = 4;
     */
    interestData: BankListingPageInterestData[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankSellingPoint bank_selling_points = 5;
     */
    bankSellingPoints: BankSellingPoint[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 6;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: string fd_booking_stats = 7;
     */
    fdBookingStats: string;

    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 8;
     */
    bankFaq: Faq[];

    /**
     * @generated from field: string rate_last_updated_at = 10;
     */
    rateLastUpdatedAt: string;

    /**
     * @generated from field: string rates_pdf_url = 11;
     */
    ratesPdfUrl: string;

    /**
     * @generated from field: string assurance_line = 12;
     */
    assuranceLine: string;

    /**
     * @generated from field: string assurance_logo_url = 13;
     */
    assuranceLogoUrl: string;

    /**
     * @generated from field: bool is_first_time_offer_applicable = 14;
     */
    isFirstTimeOfferApplicable: boolean;

    /**
     * @generated from field: bool is_vkyc_required = 15;
     */
    isVkycRequired: boolean;

    /**
     * @generated from field: repeated string bank_faq_category_priority_list = 28;
     */
    bankFaqCategoryPriorityList: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankListingV2.
 * Use `create(BankListingV2Schema)` to create a new message.
 */
export const BankListingV2Schema: GenMessage<BankListingV2> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 10);

/**
 * @generated from message com.stablemoney.api.identity.HighestRateKeyValue
 */
export type HighestRateKeyValue =
  Message<"com.stablemoney.api.identity.HighestRateKeyValue"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.InvestorType investor_type = 1;
     */
    investorType: InvestorType;

    /**
     * @generated from field: com.stablemoney.api.identity.FixedDepositResponse fixed_deposit = 2;
     */
    fixedDeposit?: FixedDepositResponse;
  };

/**
 * Describes the message com.stablemoney.api.identity.HighestRateKeyValue.
 * Use `create(HighestRateKeyValueSchema)` to create a new message.
 */
export const HighestRateKeyValueSchema: GenMessage<HighestRateKeyValue> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 11);

/**
 * @generated from message com.stablemoney.api.identity.InvestableBankCompare
 */
export type InvestableBankCompare =
  Message<"com.stablemoney.api.identity.InvestableBankCompare"> & {
    /**
     * @generated from field: double interest_rate = 1;
     */
    interestRate: number;

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank_response = 2;
     */
    bankResponse?: BankResponse;
  };

/**
 * Describes the message com.stablemoney.api.identity.InvestableBankCompare.
 * Use `create(InvestableBankCompareSchema)` to create a new message.
 */
export const InvestableBankCompareSchema: GenMessage<InvestableBankCompare> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 12);

/**
 * @generated from message com.stablemoney.api.identity.NonInvestableBankCompare
 */
export type NonInvestableBankCompare =
  Message<"com.stablemoney.api.identity.NonInvestableBankCompare"> & {
    /**
     * @generated from field: repeated string titles = 1 [deprecated = true];
     * @deprecated
     */
    titles: string[];

    /**
     * @generated from field: repeated string highest_bank_details = 2 [deprecated = true];
     * @deprecated
     */
    highestBankDetails: string[];

    /**
     * @generated from field: repeated string bank_details = 3 [deprecated = true];
     * @deprecated
     */
    bankDetails: string[];

    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse highest_rate_bank_response = 4;
     */
    highestRateBankResponse?: BankResponse;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.NonInvestableBankCompare.CompareLineItem line_items = 6;
     */
    lineItems: NonInvestableBankCompare_CompareLineItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.NonInvestableBankCompare.
 * Use `create(NonInvestableBankCompareSchema)` to create a new message.
 */
export const NonInvestableBankCompareSchema: GenMessage<NonInvestableBankCompare> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 13);

/**
 * @generated from message com.stablemoney.api.identity.NonInvestableBankCompare.CompareLineItem
 */
export type NonInvestableBankCompare_CompareLineItem =
  Message<"com.stablemoney.api.identity.NonInvestableBankCompare.CompareLineItem"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string highest_bank_point = 2;
     */
    highestBankPoint: string;

    /**
     * @generated from field: string current_bank_point = 3;
     */
    currentBankPoint: string;

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink more_details_link = 4;
     */
    moreDetailsLink?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.identity.NonInvestableBankCompare.CompareLineItem.
 * Use `create(NonInvestableBankCompare_CompareLineItemSchema)` to create a new message.
 */
export const NonInvestableBankCompare_CompareLineItemSchema: GenMessage<NonInvestableBankCompare_CompareLineItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 13, 0);

/**
 * @generated from message com.stablemoney.api.identity.FdWithdrawalCalculation
 */
export type FdWithdrawalCalculation =
  Message<"com.stablemoney.api.identity.FdWithdrawalCalculation"> & {
    /**
     * @generated from field: string tenure = 1;
     */
    tenure: string;

    /**
     * @generated from field: double withdrawal_amount = 2;
     */
    withdrawalAmount: number;

    /**
     * @generated from field: double rate = 3;
     */
    rate: number;

    /**
     * @generated from field: double original_rate = 4;
     */
    originalRate: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.FdWithdrawalCalculation.
 * Use `create(FdWithdrawalCalculationSchema)` to create a new message.
 */
export const FdWithdrawalCalculationSchema: GenMessage<FdWithdrawalCalculation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 14);

/**
 * @generated from message com.stablemoney.api.identity.FdWithdrawalCalculationDetails
 */
export type FdWithdrawalCalculationDetails =
  Message<"com.stablemoney.api.identity.FdWithdrawalCalculationDetails"> & {
    /**
     * @generated from field: double interest_rate = 1;
     */
    interestRate: number;

    /**
     * @generated from field: double investment_amount = 2;
     */
    investmentAmount: number;

    /**
     * @generated from field: string tenure = 3;
     */
    tenure: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.FdWithdrawalCalculationDetails.
 * Use `create(FdWithdrawalCalculationDetailsSchema)` to create a new message.
 */
export const FdWithdrawalCalculationDetailsSchema: GenMessage<FdWithdrawalCalculationDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 15);

/**
 * @generated from message com.stablemoney.api.identity.BankListingV3
 */
export type BankListingV3 =
  Message<"com.stablemoney.api.identity.BankListingV3"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.BankResponse bank_response = 1;
     */
    bankResponse?: BankResponse;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankListingPageInterestData interest_data = 4;
     */
    interestData: BankListingPageInterestData[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink redirect_deeplink = 6;
     */
    redirectDeeplink?: RedirectDeeplink;

    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq bank_faq = 8;
     */
    bankFaq: Faq[];

    /**
     * @generated from field: bool is_vkyc_required = 15;
     */
    isVkycRequired: boolean;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.HighestRateKeyValue highest_rate_map = 17;
     */
    highestRateMap: HighestRateKeyValue[];

    /**
     * @generated from field: string investable_bank_down_time_message = 18;
     */
    investableBankDownTimeMessage: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.InvestableBankCompare investable_bank_compare = 19;
     */
    investableBankCompare: InvestableBankCompare[];

    /**
     * @generated from field: com.stablemoney.api.identity.NonInvestableBankCompare non_investable_bank_compare = 20;
     */
    nonInvestableBankCompare?: NonInvestableBankCompare;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FdWithdrawalCalculation fd_withdrawal_calculation = 21;
     */
    fdWithdrawalCalculation: FdWithdrawalCalculation[];

    /**
     * @generated from field: string non_investable_bank_message = 22;
     */
    nonInvestableBankMessage: string;

    /**
     * @generated from field: string bank_image_url = 23;
     */
    bankImageUrl: string;

    /**
     * @generated from field: com.stablemoney.api.identity.FdWithdrawalCalculationDetails fd_withdrawal_calculation_details = 24;
     */
    fdWithdrawalCalculationDetails?: FdWithdrawalCalculationDetails;

    /**
     * @generated from field: bool is_repeat_user_to_platform = 25;
     */
    isRepeatUserToPlatform: boolean;

    /**
     * @generated from field: bool is_repeat_user_to_bank = 26;
     */
    isRepeatUserToBank: boolean;

    /**
     * @generated from field: repeated string bank_faq_category_priority_list = 28;
     */
    bankFaqCategoryPriorityList: string[];

    /**
     * @generated from field: com.stablemoney.api.identity.BankListingV3.DeviceInvestabilityStatus device_investability_status = 30;
     */
    deviceInvestabilityStatus: BankListingV3_DeviceInvestabilityStatus;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankListingV3.
 * Use `create(BankListingV3Schema)` to create a new message.
 */
export const BankListingV3Schema: GenMessage<BankListingV3> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 16);

/**
 * @generated from enum com.stablemoney.api.identity.BankListingV3.DeviceInvestabilityStatus
 */
export enum BankListingV3_DeviceInvestabilityStatus {
  /**
   * @generated from enum value: UNKNOWN = 0;
   */
  UNKNOWN = 0,

  /**
   * @generated from enum value: ACTIVE = 1;
   */
  ACTIVE = 1,

  /**
   * @generated from enum value: INACTIVE = 2;
   */
  INACTIVE = 2,

  /**
   * @generated from enum value: COMING_SOON = 3;
   */
  COMING_SOON = 3,

  /**
   * @generated from enum value: ACTIVE_ON_APP = 4;
   */
  ACTIVE_ON_APP = 4,
}

/**
 * Describes the enum com.stablemoney.api.identity.BankListingV3.DeviceInvestabilityStatus.
 */
export const BankListingV3_DeviceInvestabilityStatusSchema: GenEnum<BankListingV3_DeviceInvestabilityStatus> =
  /*@__PURE__*/
  enumDesc(file_public_models_business_BankListing, 16, 0);

/**
 * @generated from message com.stablemoney.api.identity.BankListing
 */
export type BankListing =
  Message<"com.stablemoney.api.identity.BankListing"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: string slug = 2;
     */
    slug: string;

    /**
     * @generated from field: string logo_url = 3;
     */
    logoUrl: string;

    /**
     * @generated from field: string name = 4;
     */
    name: string;

    /**
     * @generated from field: string keywords = 5;
     */
    keywords: string;

    /**
     * @generated from field: bool account_required_to_invest = 6;
     */
    accountRequiredToInvest: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.BankType bank_type = 7;
     */
    bankType: BankType;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestabilityStatus investability_status = 8;
     */
    investabilityStatus: InvestabilityStatus;

    /**
     * @generated from field: repeated com.stablemoney.api.business.faq.Faq faqs = 9;
     */
    faqs: Faq[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.WithdrawalOption withdrawal_options = 10;
     */
    withdrawalOptions: WithdrawalOption[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.FixedDepositInfo fixed_deposits = 11;
     */
    fixedDeposits: FixedDepositInfo[];

    /**
     * @generated from field: repeated com.stablemoney.api.identity.Downtime downtimes = 12;
     */
    downtimes: Downtime[];

    /**
     * @generated from field: com.stablemoney.api.identity.RedirectDeeplink invest_link = 13;
     */
    investLink?: RedirectDeeplink;

    /**
     * @generated from field: double withdrawal_penalty_rate = 14;
     */
    withdrawalPenaltyRate: number;

    /**
     * @generated from field: repeated string bank_faq_category_priority_list = 15;
     */
    bankFaqCategoryPriorityList: string[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankListing.
 * Use `create(BankListingSchema)` to create a new message.
 */
export const BankListingSchema: GenMessage<BankListing> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 17);

/**
 * @generated from message com.stablemoney.api.identity.FixedDepositInfo
 */
export type FixedDepositInfo =
  Message<"com.stablemoney.api.identity.FixedDepositInfo"> & {
    /**
     * @generated from field: string id = 1;
     */
    id: string;

    /**
     * @generated from field: double rate = 2;
     */
    rate: number;

    /**
     * @generated from field: double min_deposit = 3;
     */
    minDeposit: number;

    /**
     * @generated from field: double max_deposit = 4;
     */
    maxDeposit: number;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestorType investor_type = 5;
     */
    investorType: InvestorType;

    /**
     * @generated from field: bool is_pre_mature_withdrawal_allowed = 6;
     */
    isPreMatureWithdrawalAllowed: boolean;

    /**
     * @generated from field: com.stablemoney.api.identity.InterestPayoutType interest_payout_type = 7;
     */
    interestPayoutType: InterestPayoutType;

    /**
     * @generated from field: bool is_loan_against_fd_allowed = 8;
     */
    isLoanAgainstFdAllowed: boolean;

    /**
     * @generated from field: bool is_partial_withdrawal_allowed = 9;
     */
    isPartialWithdrawalAllowed: boolean;

    /**
     * @generated from field: string display_tenure = 10;
     */
    displayTenure: string;

    /**
     * @generated from field: uint32 min_tenure_in_days = 11;
     */
    minTenureInDays: number;

    /**
     * @generated from field: uint32 max_tenure_in_days = 12;
     */
    maxTenureInDays: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.FixedDepositInfo.
 * Use `create(FixedDepositInfoSchema)` to create a new message.
 */
export const FixedDepositInfoSchema: GenMessage<FixedDepositInfo> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 18);

/**
 * @generated from message com.stablemoney.api.identity.Downtime
 */
export type Downtime = Message<"com.stablemoney.api.identity.Downtime"> & {
  /**
   * @generated from field: uint64 start_on = 1;
   */
  startOn: bigint;

  /**
   * @generated from field: uint64 end_on = 2;
   */
  endOn: bigint;
};

/**
 * Describes the message com.stablemoney.api.identity.Downtime.
 * Use `create(DowntimeSchema)` to create a new message.
 */
export const DowntimeSchema: GenMessage<Downtime> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 19);

/**
 * @generated from message com.stablemoney.api.identity.WithdrawalOption
 */
export type WithdrawalOption =
  Message<"com.stablemoney.api.identity.WithdrawalOption"> & {
    /**
     * @generated from field: double rate = 1;
     */
    rate: number;

    /**
     * @generated from field: uint32 min_tenure_in_days = 2;
     */
    minTenureInDays: number;

    /**
     * @generated from field: uint32 max_tenure_in_days = 3;
     */
    maxTenureInDays: number;

    /**
     * @generated from field: double min_deposit = 4;
     */
    minDeposit: number;

    /**
     * @generated from field: double max_deposit = 5;
     */
    maxDeposit: number;

    /**
     * @generated from field: com.stablemoney.api.identity.InvestorType investor_type = 6;
     */
    investorType: InvestorType;

    /**
     * @generated from field: com.stablemoney.api.identity.CompoundingFrequencyType compounding_frequency = 7;
     */
    compoundingFrequency: CompoundingFrequencyType;
  };

/**
 * Describes the message com.stablemoney.api.identity.WithdrawalOption.
 * Use `create(WithdrawalOptionSchema)` to create a new message.
 */
export const WithdrawalOptionSchema: GenMessage<WithdrawalOption> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 20);

/**
 * @generated from message com.stablemoney.api.identity.TopInfoCard
 */
export type TopInfoCard =
  Message<"com.stablemoney.api.identity.TopInfoCard"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: string icon_url = 3;
     */
    iconUrl: string;

    /**
     * @generated from field: string info_bottom_sheet_title = 4;
     */
    infoBottomSheetTitle: string;

    /**
     * @generated from field: string info_bottom_sheet_description = 5;
     */
    infoBottomSheetDescription: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.TopInfoCard.
 * Use `create(TopInfoCardSchema)` to create a new message.
 */
export const TopInfoCardSchema: GenMessage<TopInfoCard> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 21);

/**
 * @generated from message com.stablemoney.api.identity.BankTag
 */
export type BankTag = Message<"com.stablemoney.api.identity.BankTag"> & {
  /**
   * @generated from field: string tag_id = 1;
   */
  tagId: string;

  /**
   * @generated from field: string tag_name = 2;
   */
  tagName: string;

  /**
   * @generated from field: string icon_url = 3;
   */
  iconUrl: string;
};

/**
 * Describes the message com.stablemoney.api.identity.BankTag.
 * Use `create(BankTagSchema)` to create a new message.
 */
export const BankTagSchema: GenMessage<BankTag> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 22);

/**
 * @generated from message com.stablemoney.api.identity.BankSellingPoint
 */
export type BankSellingPoint =
  Message<"com.stablemoney.api.identity.BankSellingPoint"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: string icon_url = 3;
     */
    iconUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankSellingPoint.
 * Use `create(BankSellingPointSchema)` to create a new message.
 */
export const BankSellingPointSchema: GenMessage<BankSellingPoint> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 23);

/**
 * @generated from message com.stablemoney.api.identity.BankFixedDepositStep
 */
export type BankFixedDepositStep =
  Message<"com.stablemoney.api.identity.BankFixedDepositStep"> & {
    /**
     * @generated from field: string title = 1;
     */
    title: string;

    /**
     * @generated from field: string description = 2;
     */
    description: string;

    /**
     * @generated from field: string image_url = 3;
     */
    imageUrl: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankFixedDepositStep.
 * Use `create(BankFixedDepositStepSchema)` to create a new message.
 */
export const BankFixedDepositStepSchema: GenMessage<BankFixedDepositStep> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 24);

/**
 * @generated from message com.stablemoney.api.identity.BankFixedDepositStepsResponse
 */
export type BankFixedDepositStepsResponse =
  Message<"com.stablemoney.api.identity.BankFixedDepositStepsResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankFixedDepositStep bank_fixed_deposit_step = 1;
     */
    bankFixedDepositStep: BankFixedDepositStep[];
  };

/**
 * Describes the message com.stablemoney.api.identity.BankFixedDepositStepsResponse.
 * Use `create(BankFixedDepositStepsResponseSchema)` to create a new message.
 */
export const BankFixedDepositStepsResponseSchema: GenMessage<BankFixedDepositStepsResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 25);

/**
 * @generated from message com.stablemoney.api.identity.BankRateNotificationResponse
 */
export type BankRateNotificationResponse =
  Message<"com.stablemoney.api.identity.BankRateNotificationResponse"> & {
    /**
     * @generated from field: bool status = 1;
     */
    status: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankRateNotificationResponse.
 * Use `create(BankRateNotificationResponseSchema)` to create a new message.
 */
export const BankRateNotificationResponseSchema: GenMessage<BankRateNotificationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 26);

/**
 * @generated from message com.stablemoney.api.identity.BankRateNotificationRequest
 */
export type BankRateNotificationRequest =
  Message<"com.stablemoney.api.identity.BankRateNotificationRequest"> & {
    /**
     * @generated from field: string bank_id = 1;
     */
    bankId: string;

    /**
     * @generated from field: optional bool to_notify = 2;
     */
    toNotify?: boolean;
  };

/**
 * Describes the message com.stablemoney.api.identity.BankRateNotificationRequest.
 * Use `create(BankRateNotificationRequestSchema)` to create a new message.
 */
export const BankRateNotificationRequestSchema: GenMessage<BankRateNotificationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 27);

/**
 * @generated from message com.stablemoney.api.identity.AllBanksResponse
 */
export type AllBanksResponse =
  Message<"com.stablemoney.api.identity.AllBanksResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.BankListing banks = 1;
     */
    banks: BankListing[];
  };

/**
 * Describes the message com.stablemoney.api.identity.AllBanksResponse.
 * Use `create(AllBanksResponseSchema)` to create a new message.
 */
export const AllBanksResponseSchema: GenMessage<AllBanksResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 28);

/**
 * @generated from message com.stablemoney.api.identity.BranchLocations
 */
export type BranchLocations =
  Message<"com.stablemoney.api.identity.BranchLocations"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.BranchLocation branch_locations = 1;
     */
    branchLocations: BranchLocation[];

    /**
     * @generated from field: string city_name = 3;
     */
    cityName: string;

    /**
     * @generated from field: int32 total_branches = 5;
     */
    totalBranches: number;

    /**
     * @generated from field: int32 total_city_branches = 7;
     */
    totalCityBranches: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.BranchLocations.
 * Use `create(BranchLocationsSchema)` to create a new message.
 */
export const BranchLocationsSchema: GenMessage<BranchLocations> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 29);

/**
 * @generated from message com.stablemoney.api.identity.BranchLocation
 */
export type BranchLocation =
  Message<"com.stablemoney.api.identity.BranchLocation"> & {
    /**
     * @generated from field: string address = 1;
     */
    address: string;

    /**
     * @generated from field: string area = 2;
     */
    area: string;

    /**
     * @generated from field: string city = 3;
     */
    city: string;

    /**
     * @generated from field: string state = 4;
     */
    state: string;

    /**
     * @generated from field: string pincode = 5;
     */
    pincode: string;

    /**
     * @generated from field: com.stablemoney.api.identity.BranchLocation.LatLong latLong = 6;
     */
    latLong?: BranchLocation_LatLong;

    /**
     * @generated from field: string phone_number = 7;
     */
    phoneNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.BranchLocation.
 * Use `create(BranchLocationSchema)` to create a new message.
 */
export const BranchLocationSchema: GenMessage<BranchLocation> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 30);

/**
 * @generated from message com.stablemoney.api.identity.BranchLocation.LatLong
 */
export type BranchLocation_LatLong =
  Message<"com.stablemoney.api.identity.BranchLocation.LatLong"> & {
    /**
     * @generated from field: double latitude = 1;
     */
    latitude: number;

    /**
     * @generated from field: double longitude = 2;
     */
    longitude: number;
  };

/**
 * Describes the message com.stablemoney.api.identity.BranchLocation.LatLong.
 * Use `create(BranchLocation_LatLongSchema)` to create a new message.
 */
export const BranchLocation_LatLongSchema: GenMessage<BranchLocation_LatLong> =
  /*@__PURE__*/
  messageDesc(file_public_models_business_BankListing, 30, 0);
