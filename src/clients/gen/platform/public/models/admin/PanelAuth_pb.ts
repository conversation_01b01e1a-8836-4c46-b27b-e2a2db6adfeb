// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/admin/PanelAuth.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/admin/PanelAuth.proto.
 */
export const file_public_models_admin_PanelAuth: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.identity.PanelUserCredentialRequest
 */
export type PanelUserCredentialRequest =
  Message<"com.stablemoney.api.identity.PanelUserCredentialRequest"> & {
    /**
     * @generated from field: string email = 1;
     */
    email: string;

    /**
     * @generated from field: string password = 2;
     */
    password: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.PanelUserCredentialRequest.
 * Use `create(PanelUserCredentialRequestSchema)` to create a new message.
 */
export const PanelUserCredentialRequestSchema: GenMessage<PanelUserCredentialRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_admin_PanelAuth, 0);

/**
 * @generated from message com.stablemoney.api.identity.PanelUserCreateRequest
 */
export type PanelUserCreateRequest =
  Message<"com.stablemoney.api.identity.PanelUserCreateRequest"> & {
    /**
     * @generated from field: string email = 1;
     */
    email: string;

    /**
     * @generated from field: string password = 2;
     */
    password: string;

    /**
     * @generated from field: optional string created_by = 4;
     */
    createdBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.PanelUserCreateRequest.
 * Use `create(PanelUserCreateRequestSchema)` to create a new message.
 */
export const PanelUserCreateRequestSchema: GenMessage<PanelUserCreateRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_admin_PanelAuth, 1);

/**
 * @generated from message com.stablemoney.api.identity.PanelUserUpdateRequest
 */
export type PanelUserUpdateRequest =
  Message<"com.stablemoney.api.identity.PanelUserUpdateRequest"> & {
    /**
     * @generated from field: string email = 1;
     */
    email: string;

    /**
     * @generated from field: optional string password = 2;
     */
    password?: string;

    /**
     * @generated from field: optional string updated_by = 4;
     */
    updatedBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.PanelUserUpdateRequest.
 * Use `create(PanelUserUpdateRequestSchema)` to create a new message.
 */
export const PanelUserUpdateRequestSchema: GenMessage<PanelUserUpdateRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_admin_PanelAuth, 2);

/**
 * @generated from message com.stablemoney.api.identity.FeaturePrivilegeCreateRequest
 */
export type FeaturePrivilegeCreateRequest =
  Message<"com.stablemoney.api.identity.FeaturePrivilegeCreateRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.FeatureType feature_name = 1;
     */
    featureName: FeatureType;

    /**
     * @generated from field: com.stablemoney.api.identity.PrivilegeType privilege_type = 2;
     */
    privilegeType: PrivilegeType;

    /**
     * @generated from field: optional string created_by = 3;
     */
    createdBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.FeaturePrivilegeCreateRequest.
 * Use `create(FeaturePrivilegeCreateRequestSchema)` to create a new message.
 */
export const FeaturePrivilegeCreateRequestSchema: GenMessage<FeaturePrivilegeCreateRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_admin_PanelAuth, 3);

/**
 * @generated from message com.stablemoney.api.identity.FeaturePrivilegeUpdateRequest
 */
export type FeaturePrivilegeUpdateRequest =
  Message<"com.stablemoney.api.identity.FeaturePrivilegeUpdateRequest"> & {
    /**
     * @generated from field: com.stablemoney.api.identity.FeatureType feature_name = 1;
     */
    featureName: FeatureType;

    /**
     * @generated from field: com.stablemoney.api.identity.PrivilegeType privilege_type = 2;
     */
    privilegeType: PrivilegeType;

    /**
     * @generated from field: optional bool status = 3;
     */
    status?: boolean;

    /**
     * @generated from field: optional string updated_by = 4;
     */
    updatedBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.FeaturePrivilegeUpdateRequest.
 * Use `create(FeaturePrivilegeUpdateRequestSchema)` to create a new message.
 */
export const FeaturePrivilegeUpdateRequestSchema: GenMessage<FeaturePrivilegeUpdateRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_admin_PanelAuth, 4);

/**
 * @generated from message com.stablemoney.api.identity.UserPrivilegeMapCreateRequest
 */
export type UserPrivilegeMapCreateRequest =
  Message<"com.stablemoney.api.identity.UserPrivilegeMapCreateRequest"> & {
    /**
     * @generated from field: string user_email = 1;
     */
    userEmail: string;

    /**
     * @generated from field: com.stablemoney.api.identity.FeatureType feature_name = 2;
     */
    featureName: FeatureType;

    /**
     * @generated from field: com.stablemoney.api.identity.PrivilegeType privilege_type = 3;
     */
    privilegeType: PrivilegeType;

    /**
     * @generated from field: optional string created_by = 5;
     */
    createdBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserPrivilegeMapCreateRequest.
 * Use `create(UserPrivilegeMapCreateRequestSchema)` to create a new message.
 */
export const UserPrivilegeMapCreateRequestSchema: GenMessage<UserPrivilegeMapCreateRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_admin_PanelAuth, 5);

/**
 * @generated from message com.stablemoney.api.identity.UserPrivilegeMapUpdateRequest
 */
export type UserPrivilegeMapUpdateRequest =
  Message<"com.stablemoney.api.identity.UserPrivilegeMapUpdateRequest"> & {
    /**
     * @generated from field: string user_email = 1;
     */
    userEmail: string;

    /**
     * @generated from field: com.stablemoney.api.identity.FeatureType feature_name = 2;
     */
    featureName: FeatureType;

    /**
     * @generated from field: com.stablemoney.api.identity.PrivilegeType privilege_type = 3;
     */
    privilegeType: PrivilegeType;

    /**
     * @generated from field: bool status = 4;
     */
    status: boolean;

    /**
     * @generated from field: optional string updated_by = 5;
     */
    updatedBy?: string;
  };

/**
 * Describes the message com.stablemoney.api.identity.UserPrivilegeMapUpdateRequest.
 * Use `create(UserPrivilegeMapUpdateRequestSchema)` to create a new message.
 */
export const UserPrivilegeMapUpdateRequestSchema: GenMessage<UserPrivilegeMapUpdateRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_admin_PanelAuth, 6);

/**
 * @generated from enum com.stablemoney.api.identity.PanelUserRoleType
 */
export enum PanelUserRoleType {
  /**
   * @generated from enum value: ADMIN = 0;
   */
  ADMIN = 0,

  /**
   * @generated from enum value: GENERAL = 1;
   */
  GENERAL = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.PanelUserRoleType.
 */
export const PanelUserRoleTypeSchema: GenEnum<PanelUserRoleType> =
  /*@__PURE__*/
  enumDesc(file_public_models_admin_PanelAuth, 0);

/**
 * @generated from enum com.stablemoney.api.identity.PrivilegeType
 */
export enum PrivilegeType {
  /**
   * @generated from enum value: PRIVILEGE_TYPE_UNKNOWN = 0;
   */
  PRIVILEGE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: VIEW = 1;
   */
  VIEW = 1,

  /**
   * @generated from enum value: CREATE = 2;
   */
  CREATE = 2,

  /**
   * @generated from enum value: MODIFY = 3;
   */
  MODIFY = 3,

  /**
   * @generated from enum value: DELETE = 4;
   */
  DELETE = 4,

  /**
   * @generated from enum value: FORCE_DELETE = 5;
   */
  FORCE_DELETE = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.PrivilegeType.
 */
export const PrivilegeTypeSchema: GenEnum<PrivilegeType> =
  /*@__PURE__*/
  enumDesc(file_public_models_admin_PanelAuth, 1);

/**
 * @generated from enum com.stablemoney.api.identity.FeatureType
 */
export enum FeatureType {
  /**
   * @generated from enum value: FEATURE_TYPE_UNKNOWN = 0;
   */
  FEATURE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: BANK_INFO = 1;
   */
  BANK_INFO = 1,

  /**
   * @generated from enum value: BANK_LISTING = 2;
   */
  BANK_LISTING = 2,

  /**
   * @generated from enum value: FIREBASE_APP_CONFIG = 3;
   */
  FIREBASE_APP_CONFIG = 3,

  /**
   * @generated from enum value: COLLECTIONS = 4;
   */
  COLLECTIONS = 4,

  /**
   * @generated from enum value: USER_DATA = 5;
   */
  USER_DATA = 5,
}

/**
 * Describes the enum com.stablemoney.api.identity.FeatureType.
 */
export const FeatureTypeSchema: GenEnum<FeatureType> =
  /*@__PURE__*/
  enumDesc(file_public_models_admin_PanelAuth, 2);
