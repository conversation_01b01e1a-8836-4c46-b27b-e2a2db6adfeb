// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/notification/Notifications.proto (package com.stablemoney.api.identity, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/notification/Notifications.proto.
 */
export const file_public_models_notification_Notifications: GenFile =
  /*@__PURE__*/
  fileDesc(
    "Ci5wdWJsaWMvbW9kZWxzL25vdGlmaWNhdGlvbi9Ob3RpZmljYXRpb25zLnByb3RvEhxjb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5IrgBCgdQYXlsb2FkEhIKCnJlcXVlc3RfaWQYASABKAkSDAoEdHlwZRgCIAEoCRIUCgxkZXZpY2VfdG9rZW4YAyABKAkSDQoFdGl0bGUYBCABKAkSDAoEYm9keRgFIAEoCRIMCgRsaW5rGAYgASgJEhMKBnRiaV9pZBgHIAEoCUgAiAEBEhkKDGlzX3RheF9zYXZlchgIIAEoCEgBiAEBQgkKB190YmlfaWRCDwoNX2lzX3RheF9zYXZlciKKAQoaVXBzd2luZ05vdGlmaWNhdGlvblJlcXVlc3QSEgoKZXZlbnRfdHlwZRgBIAEoCRIbChNwYXJ0bmVyX2N1c3RvbWVyX2lkGAIgASgJEjsKDGNoYW5uZWxfZGF0YRgDIAMoCzIlLmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuUGF5bG9hZCJgCgpTdGF0dXNJdGVtEgoKAmlkGAEgASgJEkYKBnJlc3VsdBgCIAEoDjI2LmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuTm90aWZpY2F0aW9uUmVzcG9uc2VUeXBlIlcKG1Vwc3dpbmdOb3RpZmljYXRpb25SZXNwb25zZRI4CgZzdGF0dXMYASADKAsyKC5jb20uc3RhYmxlbW9uZXkuYXBpLmlkZW50aXR5LlN0YXR1c0l0ZW0iigEKHE5vdGlmaWNhdGlvblByb3ZpZGVyUmVzcG9uc2USYAobdXBzd2luZ05vdGlmaWNhdGlvblJlc3BvbnNlGAEgASgLMjkuY29tLnN0YWJsZW1vbmV5LmFwaS5pZGVudGl0eS5VcHN3aW5nTm90aWZpY2F0aW9uUmVzcG9uc2VIAEIICgZyZXN1bHQihwEKG05vdGlmaWNhdGlvblByb3ZpZGVyUmVxdWVzdBJeChp1cHN3aW5nTm90aWZpY2F0aW9uUmVxdWVzdBgBIAEoCzI4LmNvbS5zdGFibGVtb25leS5hcGkuaWRlbnRpdHkuVXBzd2luZ05vdGlmaWNhdGlvblJlcXVlc3RIAEIICgZyZXN1bHQqYAoUTm90aWZpY2F0aW9uUHJvdmlkZXISIQodTk9USUZJQ0FUSU9OX1BST1ZJREVSX1VOS05PV04QABILCgdVUFNXSU5HEAESCwoHRklOQ0FSRRACEgsKB1VKSklWQU4QAyo7ChBOb3RpZmljYXRpb25UeXBlEh0KGU5PVElGSUNBVElPTl9UWVBFX1VOS05PV04QABIICgRQVVNIEAEqTwoYTm90aWZpY2F0aW9uUmVzcG9uc2VUeXBlEhkKFVJFU1BPTlNFX1RZUEVfVU5LTk9XThAAEgsKB1NVQ0NFU1MQARILCgdGQUlMVVJFEAJCJAogY29tLnN0YWJsZW1vbmV5LmFwaS5ub3RpZmljYXRpb25QAWIGcHJvdG8z",
  );

/**
 * @generated from message com.stablemoney.api.identity.Payload
 */
export type Payload = Message<"com.stablemoney.api.identity.Payload"> & {
  /**
   * @generated from field: string request_id = 1;
   */
  requestId: string;

  /**
   * @generated from field: string type = 2;
   */
  type: string;

  /**
   * @generated from field: string device_token = 3;
   */
  deviceToken: string;

  /**
   * @generated from field: string title = 4;
   */
  title: string;

  /**
   * @generated from field: string body = 5;
   */
  body: string;

  /**
   * @generated from field: string link = 6;
   */
  link: string;

  /**
   * @generated from field: optional string tbi_id = 7;
   */
  tbiId?: string;

  /**
   * @generated from field: optional bool is_tax_saver = 8;
   */
  isTaxSaver?: boolean;
};

/**
 * Describes the message com.stablemoney.api.identity.Payload.
 * Use `create(PayloadSchema)` to create a new message.
 */
export const PayloadSchema: GenMessage<Payload> =
  /*@__PURE__*/
  messageDesc(file_public_models_notification_Notifications, 0);

/**
 * @generated from message com.stablemoney.api.identity.UpswingNotificationRequest
 */
export type UpswingNotificationRequest =
  Message<"com.stablemoney.api.identity.UpswingNotificationRequest"> & {
    /**
     * @generated from field: string event_type = 1;
     */
    eventType: string;

    /**
     * @generated from field: string partner_customer_id = 2;
     */
    partnerCustomerId: string;

    /**
     * @generated from field: repeated com.stablemoney.api.identity.Payload channel_data = 3;
     */
    channelData: Payload[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UpswingNotificationRequest.
 * Use `create(UpswingNotificationRequestSchema)` to create a new message.
 */
export const UpswingNotificationRequestSchema: GenMessage<UpswingNotificationRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_notification_Notifications, 1);

/**
 * @generated from message com.stablemoney.api.identity.StatusItem
 */
export type StatusItem = Message<"com.stablemoney.api.identity.StatusItem"> & {
  /**
   * @generated from field: string id = 1;
   */
  id: string;

  /**
   * @generated from field: com.stablemoney.api.identity.NotificationResponseType result = 2;
   */
  result: NotificationResponseType;
};

/**
 * Describes the message com.stablemoney.api.identity.StatusItem.
 * Use `create(StatusItemSchema)` to create a new message.
 */
export const StatusItemSchema: GenMessage<StatusItem> =
  /*@__PURE__*/
  messageDesc(file_public_models_notification_Notifications, 2);

/**
 * @generated from message com.stablemoney.api.identity.UpswingNotificationResponse
 */
export type UpswingNotificationResponse =
  Message<"com.stablemoney.api.identity.UpswingNotificationResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.identity.StatusItem status = 1;
     */
    status: StatusItem[];
  };

/**
 * Describes the message com.stablemoney.api.identity.UpswingNotificationResponse.
 * Use `create(UpswingNotificationResponseSchema)` to create a new message.
 */
export const UpswingNotificationResponseSchema: GenMessage<UpswingNotificationResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_notification_Notifications, 3);

/**
 * @generated from message com.stablemoney.api.identity.NotificationProviderResponse
 */
export type NotificationProviderResponse =
  Message<"com.stablemoney.api.identity.NotificationProviderResponse"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.NotificationProviderResponse.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.UpswingNotificationResponse upswingNotificationResponse = 1;
           */
          value: UpswingNotificationResponse;
          case: "upswingNotificationResponse";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.NotificationProviderResponse.
 * Use `create(NotificationProviderResponseSchema)` to create a new message.
 */
export const NotificationProviderResponseSchema: GenMessage<NotificationProviderResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_notification_Notifications, 4);

/**
 * @generated from message com.stablemoney.api.identity.NotificationProviderRequest
 */
export type NotificationProviderRequest =
  Message<"com.stablemoney.api.identity.NotificationProviderRequest"> & {
    /**
     * @generated from oneof com.stablemoney.api.identity.NotificationProviderRequest.result
     */
    result:
      | {
          /**
           * @generated from field: com.stablemoney.api.identity.UpswingNotificationRequest upswingNotificationRequest = 1;
           */
          value: UpswingNotificationRequest;
          case: "upswingNotificationRequest";
        }
      | { case: undefined; value?: undefined };
  };

/**
 * Describes the message com.stablemoney.api.identity.NotificationProviderRequest.
 * Use `create(NotificationProviderRequestSchema)` to create a new message.
 */
export const NotificationProviderRequestSchema: GenMessage<NotificationProviderRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_notification_Notifications, 5);

/**
 * @generated from enum com.stablemoney.api.identity.NotificationProvider
 */
export enum NotificationProvider {
  /**
   * @generated from enum value: NOTIFICATION_PROVIDER_UNKNOWN = 0;
   */
  NOTIFICATION_PROVIDER_UNKNOWN = 0,

  /**
   * @generated from enum value: UPSWING = 1;
   */
  UPSWING = 1,

  /**
   * @generated from enum value: FINCARE = 2;
   */
  FINCARE = 2,

  /**
   * @generated from enum value: UJJIVAN = 3;
   */
  UJJIVAN = 3,
}

/**
 * Describes the enum com.stablemoney.api.identity.NotificationProvider.
 */
export const NotificationProviderSchema: GenEnum<NotificationProvider> =
  /*@__PURE__*/
  enumDesc(file_public_models_notification_Notifications, 0);

/**
 * @generated from enum com.stablemoney.api.identity.NotificationType
 */
export enum NotificationType {
  /**
   * @generated from enum value: NOTIFICATION_TYPE_UNKNOWN = 0;
   */
  NOTIFICATION_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: PUSH = 1;
   */
  PUSH = 1,
}

/**
 * Describes the enum com.stablemoney.api.identity.NotificationType.
 */
export const NotificationTypeSchema: GenEnum<NotificationType> =
  /*@__PURE__*/
  enumDesc(file_public_models_notification_Notifications, 1);

/**
 * @generated from enum com.stablemoney.api.identity.NotificationResponseType
 */
export enum NotificationResponseType {
  /**
   * @generated from enum value: RESPONSE_TYPE_UNKNOWN = 0;
   */
  RESPONSE_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: SUCCESS = 1;
   */
  SUCCESS = 1,

  /**
   * @generated from enum value: FAILURE = 2;
   */
  FAILURE = 2,
}

/**
 * Describes the enum com.stablemoney.api.identity.NotificationResponseType.
 */
export const NotificationResponseTypeSchema: GenEnum<NotificationResponseType> =
  /*@__PURE__*/
  enumDesc(file_public_models_notification_Notifications, 2);
