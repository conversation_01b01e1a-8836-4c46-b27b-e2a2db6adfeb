// @generated by protoc-gen-es v2.5.1 with parameter "target=ts,import_extension=js"
// @generated from file public/models/scc/scc.proto (package com.stablemoney.api.business.scc, syntax proto3)

import type {
  GenEnum,
  GenFile,
  GenMessage,
} from "@bufbuild/protobuf/codegenv2";
import { enumDesc, fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv2";
import type { Message } from "@bufbuild/protobuf";

/**
 * Describes the file public/models/scc/scc.proto.
 */
export const file_public_models_scc_scc: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
  );

/**
 * @generated from message com.stablemoney.api.business.scc.DedupeRequest
 */
export type DedupeRequest =
  Message<"com.stablemoney.api.business.scc.DedupeRequest"> & {
    /**
     * @generated from field: string pan = 1;
     */
    pan: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.DedupeRequest.
 * Use `create(DedupeRequestSchema)` to create a new message.
 */
export const DedupeRequestSchema: GenMessage<DedupeRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 0);

/**
 * @generated from message com.stablemoney.api.business.scc.DedupeResponse
 */
export type DedupeResponse =
  Message<"com.stablemoney.api.business.scc.DedupeResponse"> & {
    /**
     * @generated from field: string customer_id = 1;
     */
    customerId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.DedupeResponse.
 * Use `create(DedupeResponseSchema)` to create a new message.
 */
export const DedupeResponseSchema: GenMessage<DedupeResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 1);

/**
 * @generated from message com.stablemoney.api.business.scc.SdkUrlResponse
 */
export type SdkUrlResponse =
  Message<"com.stablemoney.api.business.scc.SdkUrlResponse"> & {
    /**
     * @generated from field: string url = 1;
     */
    url: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.SdkUrlResponse.
 * Use `create(SdkUrlResponseSchema)` to create a new message.
 */
export const SdkUrlResponseSchema: GenMessage<SdkUrlResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 2);

/**
 * @generated from message com.stablemoney.api.business.scc.CustomerDetail
 */
export type CustomerDetail =
  Message<"com.stablemoney.api.business.scc.CustomerDetail"> & {
    /**
     * Corresponds to cifNo
     *
     * @generated from field: string cif_no = 1;
     */
    cifNo: string;

    /**
     * Corresponds to custType
     *
     * @generated from field: string cust_type = 2;
     */
    custType: string;

    /**
     * Corresponds to title
     *
     * @generated from field: string title = 3;
     */
    title: string;

    /**
     * Corresponds to firstName
     *
     * @generated from field: string first_name = 4;
     */
    firstName: string;

    /**
     * Corresponds to middleName
     *
     * @generated from field: string middle_name = 5;
     */
    middleName: string;

    /**
     * Corresponds to lastName
     *
     * @generated from field: string last_name = 6;
     */
    lastName: string;

    /**
     * Corresponds to gender
     *
     * @generated from field: string gender = 7;
     */
    gender: string;

    /**
     * Corresponds to dateOfBirth
     *
     * @generated from field: string date_of_birth = 8;
     */
    dateOfBirth: string;

    /**
     * Corresponds to mobileNumber
     *
     * @generated from field: string mobile_number = 9;
     */
    mobileNumber: string;

    /**
     * Corresponds to emailId
     *
     * @generated from field: string email_id = 10;
     */
    emailId: string;

    /**
     * Corresponds to aadharRefNo
     *
     * @generated from field: string aadhar_ref_no = 11;
     */
    aadharRefNo: string;

    /**
     * Corresponds to pan
     *
     * @generated from field: string pan = 12;
     */
    pan: string;

    /**
     * Corresponds to kycType
     *
     * @generated from field: string kyc_type = 13;
     */
    kycType: string;

    /**
     * Corresponds to ckycNumber
     *
     * @generated from field: string ckyc_number = 14;
     */
    ckycNumber: string;

    /**
     * Corresponds to nreFlag
     *
     * @generated from field: string nre_flag = 15;
     */
    nreFlag: string;

    /**
     * Corresponds to addressDetails
     *
     * @generated from field: com.stablemoney.api.business.scc.AddressDetails address_details = 16;
     */
    addressDetails?: AddressDetails;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.CustomerDetail.
 * Use `create(CustomerDetailSchema)` to create a new message.
 */
export const CustomerDetailSchema: GenMessage<CustomerDetail> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 3);

/**
 * @generated from message com.stablemoney.api.business.scc.AddressDetails
 */
export type AddressDetails =
  Message<"com.stablemoney.api.business.scc.AddressDetails"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.scc.Address address = 1;
     */
    address: Address[];
  };

/**
 * Describes the message com.stablemoney.api.business.scc.AddressDetails.
 * Use `create(AddressDetailsSchema)` to create a new message.
 */
export const AddressDetailsSchema: GenMessage<AddressDetails> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 4);

/**
 * @generated from message com.stablemoney.api.business.scc.Address
 */
export type Address = Message<"com.stablemoney.api.business.scc.Address"> & {
  /**
   * Corresponds to addressType
   *
   * @generated from field: string address_type = 1;
   */
  addressType: string;

  /**
   * Corresponds to addressLine1
   *
   * @generated from field: string address_line1 = 2;
   */
  addressLine1: string;

  /**
   * Corresponds to addressLine2
   *
   * @generated from field: string address_line2 = 3;
   */
  addressLine2: string;

  /**
   * Corresponds to addressLine3
   *
   * @generated from field: string address_line3 = 4;
   */
  addressLine3: string;

  /**
   * Corresponds to pincode
   *
   * @generated from field: string pincode = 5;
   */
  pincode: string;

  /**
   * Corresponds to city
   *
   * @generated from field: string city = 6;
   */
  city: string;

  /**
   * Corresponds to state
   *
   * @generated from field: string state = 7;
   */
  state: string;

  /**
   * Corresponds to country
   *
   * @generated from field: string country = 8;
   */
  country: string;
};

/**
 * Describes the message com.stablemoney.api.business.scc.Address.
 * Use `create(AddressSchema)` to create a new message.
 */
export const AddressSchema: GenMessage<Address> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 5);

/**
 * @generated from message com.stablemoney.api.business.scc.AccountDetail
 */
export type AccountDetail =
  Message<"com.stablemoney.api.business.scc.AccountDetail"> & {
    /**
     * @generated from field: string account_id = 1;
     */
    accountId: string;

    /**
     * @generated from field: string account_open_date = 2;
     */
    accountOpenDate: string;

    /**
     * @generated from field: string account_status = 3;
     */
    accountStatus: string;

    /**
     * @generated from field: string disbursement_option = 4;
     */
    disbursementOption: string;

    /**
     * @generated from field: double deposit_amount = 5;
     */
    depositAmount: number;

    /**
     * @generated from field: string is_cumulative = 6;
     */
    isCumulative: string;

    /**
     * @generated from field: string tenure_in_months = 7;
     */
    tenureInMonths: string;

    /**
     * @generated from field: string tenure_in_days = 8;
     */
    tenureInDays: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.AccountDetail.
 * Use `create(AccountDetailSchema)` to create a new message.
 */
export const AccountDetailSchema: GenMessage<AccountDetail> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 6);

/**
 * @generated from message com.stablemoney.api.business.scc.CustomerDetailResponse
 */
export type CustomerDetailResponse =
  Message<"com.stablemoney.api.business.scc.CustomerDetailResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.business.scc.CustomerDetail customer_details = 1;
     */
    customerDetails?: CustomerDetail;

    /**
     * @generated from field: repeated com.stablemoney.api.business.scc.AccountDetail account_details = 2;
     */
    accountDetails: AccountDetail[];

    /**
     * @generated from field: double eligible_limit = 3;
     */
    eligibleLimit: number;

    /**
     * @generated from field: double max_allowed_limit = 4;
     */
    maxAllowedLimit: number;

    /**
     * @generated from field: double min_allowed_limit = 5;
     */
    minAllowedLimit: number;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.CustomerDetailResponse.
 * Use `create(CustomerDetailResponseSchema)` to create a new message.
 */
export const CustomerDetailResponseSchema: GenMessage<CustomerDetailResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 7);

/**
 * @generated from message com.stablemoney.api.business.scc.CardDetailResponse
 */
export type CardDetailResponse =
  Message<"com.stablemoney.api.business.scc.CardDetailResponse"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @generated from field: string program_id = 2;
     */
    programId: string;

    /**
     * @generated from field: string program_name = 3;
     */
    programName: string;

    /**
     * @generated from field: string due_date = 4;
     */
    dueDate: string;

    /**
     * @generated from field: string type = 5;
     */
    type: string;

    /**
     * @generated from field: string last_4_digits = 6;
     */
    last4Digits: string;

    /**
     * @generated from field: double total_due_amount = 7;
     */
    totalDueAmount: number;

    /**
     * @generated from field: string minimum_payment = 8;
     */
    minimumPayment: string;

    /**
     * @generated from field: string customer_name = 9;
     */
    customerName: string;

    /**
     * @generated from field: string status = 10;
     */
    status: string;

    /**
     * @generated from field: string bin_ = 11;
     */
    bin: string;

    /**
     * @generated from field: double credit_limit = 13;
     */
    creditLimit: number;

    /**
     * @generated from field: double max_allowed_limit = 14;
     */
    maxAllowedLimit: number;

    /**
     * @generated from field: double current_limit = 15;
     */
    currentLimit: number;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.CardDetailResponse.
 * Use `create(CardDetailResponseSchema)` to create a new message.
 */
export const CardDetailResponseSchema: GenMessage<CardDetailResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 8);

/**
 * @generated from message com.stablemoney.api.business.scc.BreCheckRequest
 */
export type BreCheckRequest =
  Message<"com.stablemoney.api.business.scc.BreCheckRequest"> & {
    /**
     * @generated from field: string name_on_card = 1;
     */
    nameOnCard: string;

    /**
     * @generated from field: double requested_limit = 2;
     */
    requestedLimit: number;

    /**
     * @generated from field: repeated com.stablemoney.api.business.scc.AccountDetail account_details = 3;
     */
    accountDetails: AccountDetail[];

    /**
     * @generated from field: bool best_rate_auto_ren = 4;
     */
    bestRateAutoRen: boolean;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.BreCheckRequest.
 * Use `create(BreCheckRequestSchema)` to create a new message.
 */
export const BreCheckRequestSchema: GenMessage<BreCheckRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 9);

/**
 * @generated from message com.stablemoney.api.business.scc.BreCheckResponse
 */
export type BreCheckResponse =
  Message<"com.stablemoney.api.business.scc.BreCheckResponse"> & {};

/**
 * Describes the message com.stablemoney.api.business.scc.BreCheckResponse.
 * Use `create(BreCheckResponseSchema)` to create a new message.
 */
export const BreCheckResponseSchema: GenMessage<BreCheckResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 10);

/**
 * @generated from message com.stablemoney.api.business.scc.CardStatusResponse
 */
export type CardStatusResponse =
  Message<"com.stablemoney.api.business.scc.CardStatusResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.business.scc.CreditCardStatueType step = 1;
     */
    step: CreditCardStatueType;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.CardStatusResponse.
 * Use `create(CardStatusResponseSchema)` to create a new message.
 */
export const CardStatusResponseSchema: GenMessage<CardStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 11);

/**
 * @generated from message com.stablemoney.api.business.scc.OnboardingStatusResponse
 */
export type OnboardingStatusResponse =
  Message<"com.stablemoney.api.business.scc.OnboardingStatusResponse"> & {
    /**
     * @generated from field: com.stablemoney.api.business.scc.OnboardingStepType step = 1;
     */
    step: OnboardingStepType;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.OnboardingStatusResponse.
 * Use `create(OnboardingStatusResponseSchema)` to create a new message.
 */
export const OnboardingStatusResponseSchema: GenMessage<OnboardingStatusResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 12);

/**
 * @generated from message com.stablemoney.api.business.scc.UpdateMaturityInstructionRequest
 */
export type UpdateMaturityInstructionRequest =
  Message<"com.stablemoney.api.business.scc.UpdateMaturityInstructionRequest"> & {
    /**
     * @generated from field: string is_cumulative_scheme = 1;
     */
    isCumulativeScheme: string;

    /**
     * @generated from field: string best_rate_auto_ren = 2;
     */
    bestRateAutoRen: string;

    /**
     * @generated from field: string account_number = 3;
     */
    accountNumber: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.UpdateMaturityInstructionRequest.
 * Use `create(UpdateMaturityInstructionRequestSchema)` to create a new message.
 */
export const UpdateMaturityInstructionRequestSchema: GenMessage<UpdateMaturityInstructionRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 13);

/**
 * @generated from message com.stablemoney.api.business.scc.UpdateMaturityInstructionResponse
 */
export type UpdateMaturityInstructionResponse =
  Message<"com.stablemoney.api.business.scc.UpdateMaturityInstructionResponse"> & {};

/**
 * Describes the message com.stablemoney.api.business.scc.UpdateMaturityInstructionResponse.
 * Use `create(UpdateMaturityInstructionResponseSchema)` to create a new message.
 */
export const UpdateMaturityInstructionResponseSchema: GenMessage<UpdateMaturityInstructionResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 14);

/**
 * @generated from message com.stablemoney.api.business.scc.InitiateOTPResponse
 */
export type InitiateOTPResponse =
  Message<"com.stablemoney.api.business.scc.InitiateOTPResponse"> & {
    /**
     * @generated from field: string reference_id = 1;
     */
    referenceId: string;

    /**
     * @generated from field: string mobile_number = 2;
     */
    mobileNumber: string;

    /**
     * @generated from field: string name = 3;
     */
    name: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.InitiateOTPResponse.
 * Use `create(InitiateOTPResponseSchema)` to create a new message.
 */
export const InitiateOTPResponseSchema: GenMessage<InitiateOTPResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 15);

/**
 * @generated from message com.stablemoney.api.business.scc.ValidateOTPRequest
 */
export type ValidateOTPRequest =
  Message<"com.stablemoney.api.business.scc.ValidateOTPRequest"> & {
    /**
     * @generated from field: string otp = 1;
     */
    otp: string;

    /**
     * @generated from field: string reference_id = 2;
     */
    referenceId: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.ValidateOTPRequest.
 * Use `create(ValidateOTPRequestSchema)` to create a new message.
 */
export const ValidateOTPRequestSchema: GenMessage<ValidateOTPRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 16);

/**
 * @generated from message com.stablemoney.api.business.scc.ValidateOTPResponse
 */
export type ValidateOTPResponse =
  Message<"com.stablemoney.api.business.scc.ValidateOTPResponse"> & {};

/**
 * Describes the message com.stablemoney.api.business.scc.ValidateOTPResponse.
 * Use `create(ValidateOTPResponseSchema)` to create a new message.
 */
export const ValidateOTPResponseSchema: GenMessage<ValidateOTPResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 17);

/**
 * @generated from message com.stablemoney.api.business.scc.KeyFactStatementResponse
 */
export type KeyFactStatementResponse =
  Message<"com.stablemoney.api.business.scc.KeyFactStatementResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.scc.Section sections = 1;
     */
    sections: Section[];

    /**
     * @generated from field: int64 last_updated_at = 2;
     */
    lastUpdatedAt: bigint;

    /**
     * @generated from field: com.stablemoney.api.business.scc.RedirectDeeplink mitc_redirect_url = 4;
     */
    mitcRedirectUrl?: RedirectDeeplink;

    /**
     * @generated from field: com.stablemoney.api.business.scc.RedirectDeeplink tnc_redirect_url = 5;
     */
    tncRedirectUrl?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.KeyFactStatementResponse.
 * Use `create(KeyFactStatementResponseSchema)` to create a new message.
 */
export const KeyFactStatementResponseSchema: GenMessage<KeyFactStatementResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 18);

/**
 * @generated from message com.stablemoney.api.business.scc.KeyFactStatementRequest
 */
export type KeyFactStatementRequest =
  Message<"com.stablemoney.api.business.scc.KeyFactStatementRequest"> & {
    /**
     * @generated from field: double requested_limit = 1;
     */
    requestedLimit: number;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.KeyFactStatementRequest.
 * Use `create(KeyFactStatementRequestSchema)` to create a new message.
 */
export const KeyFactStatementRequestSchema: GenMessage<KeyFactStatementRequest> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 19);

/**
 * @generated from message com.stablemoney.api.business.scc.Section
 */
export type Section = Message<"com.stablemoney.api.business.scc.Section"> & {
  /**
   * @generated from field: string name = 1;
   */
  name: string;

  /**
   * @generated from field: string header = 2;
   */
  header: string;

  /**
   * @generated from field: repeated com.stablemoney.api.business.scc.KeyValuePair key_value_pair = 3;
   */
  keyValuePair: KeyValuePair[];

  /**
   * @generated from field: com.stablemoney.api.business.scc.RedirectDeeplink name_redirect_url = 4;
   */
  nameRedirectUrl?: RedirectDeeplink;
};

/**
 * Describes the message com.stablemoney.api.business.scc.Section.
 * Use `create(SectionSchema)` to create a new message.
 */
export const SectionSchema: GenMessage<Section> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 20);

/**
 * @generated from message com.stablemoney.api.business.scc.KeyValuePair
 */
export type KeyValuePair =
  Message<"com.stablemoney.api.business.scc.KeyValuePair"> & {
    /**
     * @generated from field: string name = 1;
     */
    name: string;

    /**
     * @generated from field: string value = 2;
     */
    value: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.KeyValuePair.
 * Use `create(KeyValuePairSchema)` to create a new message.
 */
export const KeyValuePairSchema: GenMessage<KeyValuePair> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 21);

/**
 * @generated from message com.stablemoney.api.business.scc.RedirectDeeplink
 */
export type RedirectDeeplink =
  Message<"com.stablemoney.api.business.scc.RedirectDeeplink"> & {
    /**
     * @generated from field: string path = 1;
     */
    path: string;

    /**
     * @generated from field: string path_type = 2;
     */
    pathType: string;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.RedirectDeeplink.
 * Use `create(RedirectDeeplinkSchema)` to create a new message.
 */
export const RedirectDeeplinkSchema: GenMessage<RedirectDeeplink> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 22);

/**
 * @generated from message com.stablemoney.api.business.scc.SanctionLetterResponse
 */
export type SanctionLetterResponse =
  Message<"com.stablemoney.api.business.scc.SanctionLetterResponse"> & {
    /**
     * @generated from field: repeated com.stablemoney.api.business.scc.Section sections = 1;
     */
    sections: Section[];

    /**
     * @generated from field: int64 last_updated_at = 2;
     */
    lastUpdatedAt: bigint;

    /**
     * @generated from field: com.stablemoney.api.business.scc.RedirectDeeplink mitc_redirect_url = 3;
     */
    mitcRedirectUrl?: RedirectDeeplink;

    /**
     * @generated from field: com.stablemoney.api.business.scc.RedirectDeeplink kfs_redirect_url = 4;
     */
    kfsRedirectUrl?: RedirectDeeplink;
  };

/**
 * Describes the message com.stablemoney.api.business.scc.SanctionLetterResponse.
 * Use `create(SanctionLetterResponseSchema)` to create a new message.
 */
export const SanctionLetterResponseSchema: GenMessage<SanctionLetterResponse> =
  /*@__PURE__*/
  messageDesc(file_public_models_scc_scc, 23);

/**
 * @generated from enum com.stablemoney.api.business.scc.CreditCardStatueType
 */
export enum CreditCardStatueType {
  /**
   * @generated from enum value: CREDIT_CARD_TYPE_UNKNOWN = 0;
   */
  CREDIT_CARD_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: ACTIVE = 1;
   */
  ACTIVE = 1,

  /**
   * @generated from enum value: BLOCKED = 2;
   */
  BLOCKED = 2,

  /**
   * @generated from enum value: INACTIVE = 5;
   */
  INACTIVE = 5,

  /**
   * @generated from enum value: NOT_ISSUED = 6;
   */
  NOT_ISSUED = 6,

  /**
   * @generated from enum value: IN_PROGRESS = 7;
   */
  IN_PROGRESS = 7,
}

/**
 * Describes the enum com.stablemoney.api.business.scc.CreditCardStatueType.
 */
export const CreditCardStatueTypeSchema: GenEnum<CreditCardStatueType> =
  /*@__PURE__*/
  enumDesc(file_public_models_scc_scc, 0);

/**
 * @generated from enum com.stablemoney.api.business.scc.OnboardingStepType
 */
export enum OnboardingStepType {
  /**
   * @generated from enum value: ONBOARDING_STEP_TYPE_UNKNOWN = 0;
   */
  ONBOARDING_STEP_TYPE_UNKNOWN = 0,

  /**
   * @generated from enum value: PAN = 1;
   */
  PAN = 1,

  /**
   * @generated from enum value: FD_LIST = 2;
   */
  FD_LIST = 2,

  /**
   * @generated from enum value: SANCTION_LETTER = 3;
   */
  SANCTION_LETTER = 3,

  /**
   * @generated from enum value: BRE_CHECK_IN_PROGRESS = 4;
   */
  BRE_CHECK_IN_PROGRESS = 4,

  /**
   * @generated from enum value: BRE_CHECK_REJECTED = 5;
   */
  BRE_CHECK_REJECTED = 5,
}

/**
 * Describes the enum com.stablemoney.api.business.scc.OnboardingStepType.
 */
export const OnboardingStepTypeSchema: GenEnum<OnboardingStepType> =
  /*@__PURE__*/
  enumDesc(file_public_models_scc_scc, 1);

/**
 * @generated from enum com.stablemoney.api.business.scc.BRECheckStatusType
 */
export enum BRECheckStatusType {
  /**
   * @generated from enum value: BRE_CHECK_STATUS_UNKNOWN = 0;
   */
  BRE_CHECK_STATUS_UNKNOWN = 0,

  /**
   * @generated from enum value: SUBMITTED = 1;
   */
  SUBMITTED = 1,

  /**
   * @generated from enum value: COMPLETED = 2;
   */
  COMPLETED = 2,

  /**
   * @generated from enum value: REJECTED = 3;
   */
  REJECTED = 3,
}

/**
 * Describes the enum com.stablemoney.api.business.scc.BRECheckStatusType.
 */
export const BRECheckStatusTypeSchema: GenEnum<BRECheckStatusType> =
  /*@__PURE__*/
  enumDesc(file_public_models_scc_scc, 2);
