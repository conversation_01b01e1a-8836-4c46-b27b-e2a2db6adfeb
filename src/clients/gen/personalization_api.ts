/**
 * Generated by orval v7.9.0 🍺
 * Do not edit manually.
 * Personalization API
 * API for personalization
 * OpenAPI spec version: 1.0.0
 */
import { request } from "../personalization-api";
export interface ParamsMap {
  [key: string]: string;
}

export interface ErrorResponse {
  statusCode: number;
  message: string;
}

export interface PageRequest {
  path: string;
  name: string;
}

export interface PageResponse {
  noCache?: boolean;
  path: string;
  name: string;
  id?: string;
  root: Widget;
}

export interface FrameResponse {
  id?: string;
  name: string;
  root: Widget;
}

export interface StoryResponse {
  page: string;
  children?: StoriesV2Item[];
}

export type UserContextResponseProps = { [key: string]: unknown };

export type UserContextResponseFeatureFlags = { [key: string]: unknown };

export interface UserContextResponse {
  appName?: string;
  environment?: string;
  userId?: string;
  sessionId?: string;
  remoteAddress?: string;
  currentTime?: number;
  props?: UserContextResponseProps;
  featureFlags?: UserContextResponseFeatureFlags;
}

export interface Nudges {
  nudges: Nudge[];
}

export interface Nudge {
  nudgeId: string;
  nudge: string;
  name?: string;
  category: NudgeCategory;
  type: NudgeType;
}

export interface NudgeResponse {
  nudgeId: string;
  nudge: string;
  name?: string;
}

export type NudgeType = (typeof NudgeType)[keyof typeof NudgeType];

export const NudgeType = {
  BOTTOM_SHEET: "BOTTOM_SHEET",
  FULL_PAGE: "FULL_PAGE",
  CARD: "CARD",
  STRIP: "STRIP",
} as const;

export type NudgeCategory = (typeof NudgeCategory)[keyof typeof NudgeCategory];

export const NudgeCategory = {
  GENERAL: "GENERAL",
  FD_JOURNEY: "FD_JOURNEY",
  BOND_JOURNEY: "BOND_JOURNEY",
} as const;

export interface NudgeAction {
  nudgeId: string;
  actionType: NudgeActionType;
  category?: NudgeCategory;
}

export type NudgeActionType =
  (typeof NudgeActionType)[keyof typeof NudgeActionType];

export const NudgeActionType = {
  ACCEPT: "ACCEPT",
  DISMISS: "DISMISS",
} as const;

export interface RatingNudgeResponse {
  showRatingNudge?: boolean;
  source?: string;
  tag?: string;
}

export interface PostRatingRequest {
  rating: number;
  review?: string;
  playStoreRatingShown?: boolean;
}

export interface PostRatingResponse {
  rating?: number;
  tag?: string;
}

export interface StoryReactionRequest {
  storyId: string;
  hasLiked: boolean;
}

export type PathType = (typeof PathType)[keyof typeof PathType];

export const PathType = {
  inapp: "inapp",
  upswing: "upswing",
  external: "external",
  bottomSheet: "bottomSheet",
} as const;

export type ImageType = (typeof ImageType)[keyof typeof ImageType];

export const ImageType = {
  raster: "raster",
  svg: "svg",
} as const;

export type CollectionWidgetType =
  (typeof CollectionWidgetType)[keyof typeof CollectionWidgetType];

export const CollectionWidgetType = {
  fdType: "fdType",
  bankType: "bankType",
} as const;

export type StaggeredGridType =
  (typeof StaggeredGridType)[keyof typeof StaggeredGridType];

export const StaggeredGridType = {
  aligned: "aligned",
  staggered: "staggered",
} as const;

export type TitleType = (typeof TitleType)[keyof typeof TitleType];

export const TitleType = {
  mainHeader: "mainHeader",
  subHeader: "subHeader",
  centeredWithDivider: "centeredWithDivider",
} as const;

export type AnimationType = (typeof AnimationType)[keyof typeof AnimationType];

export const AnimationType = {
  lottie: "lottie",
  dotLottie: "dotLottie",
  gif: "gif",
  video: "video",
} as const;

export type MediaFitType = (typeof MediaFitType)[keyof typeof MediaFitType];

export const MediaFitType = {
  fill: "fill",
  contain: "contain",
  cover: "cover",
  fitWidth: "fitWidth",
  fitHeight: "fitHeight",
  none: "none",
  scaleDown: "scaleDown",
} as const;

export type SpacingType = (typeof SpacingType)[keyof typeof SpacingType];

export const SpacingType = {
  small: "small",
  medium: "medium",
  large: "large",
  extraLarge: "extraLarge",
} as const;

export type StoryType = (typeof StoryType)[keyof typeof StoryType];

export const StoryType = {
  image: "image",
  video: "video",
} as const;

export type StoryCTAType = (typeof StoryCTAType)[keyof typeof StoryCTAType];

export const StoryCTAType = {
  image: "image",
  animation: "animation",
} as const;

export type DataKeyVariableType =
  (typeof DataKeyVariableType)[keyof typeof DataKeyVariableType];

export const DataKeyVariableType = {
  string: "string",
  currency: "currency",
  date: "date",
  date_time: "date_time",
  user_first_name: "user_first_name",
  "2f_percent": "2f_percent",
  percent: "percent",
  short_currency: "short_currency",
  number: "number",
  currency_2f: "currency_2f",
  countdown: "countdown",
} as const;

export type TextStyle = (typeof TextStyle)[keyof typeof TextStyle];

export const TextStyle = {
  heading1: "heading1",
  heading2: "heading2",
  heading3: "heading3",
  heading4: "heading4",
  body1: "body1",
  body2: "body2",
  caption: "caption",
} as const;

export type PageIndicatorType =
  (typeof PageIndicatorType)[keyof typeof PageIndicatorType];

export const PageIndicatorType = {
  dotWrapped: "dotWrapped",
  dot: "dot",
} as const;

export type BondsCollectionCardType =
  (typeof BondsCollectionCardType)[keyof typeof BondsCollectionCardType];

export const BondsCollectionCardType = {
  card: "card",
  indexedCard: "indexedCard",
} as const;

export type BondsMediaType =
  (typeof BondsMediaType)[keyof typeof BondsMediaType];

export const BondsMediaType = {
  image: "image",
  video: "video",
} as const;

export type RedirectPresentation =
  (typeof RedirectPresentation)[keyof typeof RedirectPresentation];

export const RedirectPresentation = {
  push: "push",
  pop: "pop",
  replace: "replace",
  reset: "reset",
  popAndPush: "popAndPush",
} as const;

export type CardContentSpacingType =
  (typeof CardContentSpacingType)[keyof typeof CardContentSpacingType];

export const CardContentSpacingType = {
  noSpacing: "noSpacing",
  smallSpacing: "smallSpacing",
  mediumSpacing: "mediumSpacing",
  largeSpacing: "largeSpacing",
} as const;

export type CardContentRadiusType =
  (typeof CardContentRadiusType)[keyof typeof CardContentRadiusType];

export const CardContentRadiusType = {
  noRadius: "noRadius",
  smallRadius: "smallRadius",
  mediumRadius: "mediumRadius",
  largeRadius: "largeRadius",
} as const;

export type VideoControlsPosition =
  (typeof VideoControlsPosition)[keyof typeof VideoControlsPosition];

export const VideoControlsPosition = {
  topRight: "topRight",
  bottomRight: "bottomRight",
  topLeft: "topLeft",
  bottomLeft: "bottomLeft",
  center: "center",
} as const;

export type BankInvestabilityStatus =
  (typeof BankInvestabilityStatus)[keyof typeof BankInvestabilityStatus];

export const BankInvestabilityStatus = {
  active: "active",
  inactive: "inactive",
  rollout: "rollout",
  comingSoon: "comingSoon",
} as const;

export type RedirectLinkAnalyticsEventProps = { [key: string]: unknown };

export interface RedirectLink {
  path: string;
  presentation?: RedirectPresentation;
  pathType: PathType;
  eventName?: string;
  analyticsEventProps?: RedirectLinkAnalyticsEventProps;
}

export interface DataKey {
  key: string;
  contextVariables?: DataKeyVariable[];
}

export interface DataKeyVariable {
  name: string;
  value: string;
  type: DataKeyVariableType;
}

export interface TableDataResponse {
  data?: TableDataItem[];
}

export interface TableDataItem {
  labelDataKey?: DataKey;
  valueDataKey?: DataKey;
  subValueDataKey?: DataKey;
}

export type TabItem = RegularTabItem | TaggedTabItem | DefaultTabItem;

export type Widget =
  | Home
  | BasePage
  | AppBar
  | Image
  | Animation
  | Carousel
  | HorizontalListCollection
  | GridCollection
  | BondsGridCollection
  | HorizontalListSection
  | SingleCategoryFAQ
  | FindMyFD
  | InvestmentSummary
  | BondInvestmentsSummary
  | BondsGrid
  | AppBarProfileAction
  | ClippedTransitionCarousel
  | ClippedTransitionCarouselItem
  | MarketingListWithCover
  | NextBestFD
  | OneClickFD
  | MaturingFdsCarousel
  | Stories
  | StoriesV2
  | BottomSheet
  | StaggeredGrid
  | FdsInsuranceProgressTracker
  | InlineFaqs
  | Faqs
  | NavigationBar
  | NavigationBarItem
  | BondDetails
  | DataTable
  | BondsDataTable
  | BondsReturnsCalculator
  | BondsInvestButton
  | BondsMarketingHighlights
  | BondsKeyHighlights
  | BondsDisclosureButton
  | BondsDisclosureDetails
  | BondsDisclosureIssuerDetails
  | BondsSupportedDocuments
  | BondsSellerEntityDisclosure
  | BondsFaqs
  | Card
  | BondsCollectionTable
  | BondsCollection
  | InvestmentCards
  | OrgInvestmentSummary
  | OrgInvestmentCards
  | Column
  | BondsTnCAndKYCFooter
  | BondsHeroHeader
  | BondsSingleMedia
  | CarouselItem
  | KebabMenu
  | KeyValueTile
  | InAppRatingInvisibleWidget
  | SuryodayCreditCardFooter
  | InvestmentBasicDetails
  | InsuranceAmountProgress
  | KebabMenuV2
  | KebabMenuV3
  | FDReceiptKebabMenuItem
  | MenuItemV2
  | RedirectWidget
  | BulletPoints
  | FDCollectionTable
  | BankTenureCard
  | BankDetails
  | BanksHeroHeader
  | BankTableCard
  | BankTableCardItem
  | BankTenureCard
  | BankFdNibCompareCard
  | BankMessageWidget
  | BankNibInvestableOptions
  | BankShareWidget
  | BankInvestButton
  | BanksSingleMedia
  | BankWithdrawalCalculatorCard
  | BankHighlightPointersCard
  | BankIbCompareCard
  | SuryodayCreditCardWaitListFooter
  | DynamicBankFaqs
  | BaseBottomSheet
  | Text
  | Counter
  | PollCard
  | HorizontalCollectionItem
  | HorizontalCollectionSection
  | ProfileHeader
  | ProfileMenuItem
  | ProfileFooter
  | TabularPage
  | TranslucentTag
  | SolidTag
  | DefaultWidget
  | BankDICGCCard
  | TrustMarkersFooter
  | SuryodayCreditCardAddressChangeFooter;

export type HomeWidget = (typeof HomeWidget)[keyof typeof HomeWidget];

export const HomeWidget = {
  Home: "Home",
} as const;

export interface Home {
  widget: HomeWidget;
  id: string;
  props: HomeProps;
  slots: HomeSlots;
}

export type HomePropsViewEventProps = { [key: string]: unknown };

export interface HomeProps {
  extendBodyBehindAppBar: boolean;
  viewEventProps?: HomePropsViewEventProps;
}

export interface HomeSlots {
  header?: Widget;
  body: Widget[];
}

export type BaseBottomSheetWidget =
  (typeof BaseBottomSheetWidget)[keyof typeof BaseBottomSheetWidget];

export const BaseBottomSheetWidget = {
  BaseBottomSheet: "BaseBottomSheet",
} as const;

export interface BaseBottomSheet {
  widget: BaseBottomSheetWidget;
  id: string;
  slots: BaseBottomSheetSlots;
  props?: BaseBottomSheetProps;
}

export interface BaseBottomSheetSlots {
  body: Widget[];
  ctaWidget?: Widget;
}

export interface BaseBottomSheetProps {
  backgroundColor?: string;
}

export type BasePageWidget =
  (typeof BasePageWidget)[keyof typeof BasePageWidget];

export const BasePageWidget = {
  BasePage: "BasePage",
} as const;

export interface BasePage {
  widget: BasePageWidget;
  id: string;
  props: BasePageProps;
  slots: BasePageSlots;
}

export type BasePagePropsViewEventProps = { [key: string]: unknown };

export interface BasePageProps {
  extendBodyBehindAppBar?: boolean;
  backgroundColor?: string;
  isEmptyState?: boolean;
  viewEventProps?: BasePagePropsViewEventProps;
}

export interface BasePageSlots {
  header?: Widget;
  body: Widget[];
  emptyState?: Widget;
  footer?: Widget;
}

export type AppBarWidget = (typeof AppBarWidget)[keyof typeof AppBarWidget];

export const AppBarWidget = {
  AppBar: "AppBar",
} as const;

export interface AppBar {
  widget: AppBarWidget;
  id: string;
  props: AppBarProps;
  slots: AppBarSlots;
}

export interface AppBarProps {
  title?: string;
  backgroundColor?: string;
  isPinned?: boolean;
}

export interface AppBarSlots {
  left?: Widget[];
  right?: Widget[];
}

export type ImageWidget = (typeof ImageWidget)[keyof typeof ImageWidget];

export const ImageWidget = {
  Image: "Image",
} as const;

export interface Image {
  widget: ImageWidget;
  id: string;
  props: ImageProps;
}

export interface ImageSource {
  url: string;
  screenType: ScreenType;
}

export type ScreenType = (typeof ScreenType)[keyof typeof ScreenType];

export const ScreenType = {
  mobile: "mobile",
  tablet: "tablet",
  desktop: "desktop",
} as const;

export type ImagePropsAnalyticsEventProps = { [key: string]: unknown };

export interface ImageProps {
  srcSet?: ImageSource[];
  url: string;
  redirectLink?: RedirectLink;
  width?: number;
  height?: number;
  widthPercentage?: number;
  aspectRatio?: number;
  useLocalAssets?: boolean;
  hideLoadingShimmer?: boolean;
  type: ImageType;
  fit?: MediaFitType;
  analyticsEventProps?: ImagePropsAnalyticsEventProps;
}

export type AnimationWidget =
  (typeof AnimationWidget)[keyof typeof AnimationWidget];

export const AnimationWidget = {
  Animation: "Animation",
} as const;

export interface Animation {
  widget: AnimationWidget;
  id: string;
  props: AnimationProps;
}

export interface AnimationProps {
  url: string;
  redirectLink?: RedirectLink;
  width?: number;
  height?: number;
  widthPercentage?: number;
  aspectRatio?: number;
  loop: boolean;
  playOnVisibility: boolean;
  videoControlsPosition?: VideoControlsPosition;
  type: AnimationType;
  fit?: MediaFitType;
  useLocalAssets?: boolean;
}

export type CarouselWidget =
  (typeof CarouselWidget)[keyof typeof CarouselWidget];

export const CarouselWidget = {
  Carousel: "Carousel",
} as const;

export interface Carousel {
  widget: CarouselWidget;
  id: string;
  slots: CarouselSlots;
  props?: CarouselProps;
}

export interface CarouselSlots {
  children: CarouselItem[];
}

export interface CarouselProps {
  height: number;
  loop?: boolean;
  autoSwipe?: boolean;
  heightPercentage?: number;
  pageIndicatorType?: PageIndicatorType;
}

export type CarouselItemWidget =
  (typeof CarouselItemWidget)[keyof typeof CarouselItemWidget];

export const CarouselItemWidget = {
  CarouselItem: "CarouselItem",
} as const;

export interface CarouselItem {
  widget: CarouselItemWidget;
  id: string;
  slots: CarouselItemSlots;
  props?: CarouselItemProps;
}

export interface CarouselItemSlots {
  content: Widget;
}

export interface CarouselItemProps {
  autoPlayDurationInMillis?: number;
}

export type ClippedTransitionCarouselWidget =
  (typeof ClippedTransitionCarouselWidget)[keyof typeof ClippedTransitionCarouselWidget];

export const ClippedTransitionCarouselWidget = {
  ClippedTransitionCarousel: "ClippedTransitionCarousel",
} as const;

export interface ClippedTransitionCarousel {
  widget: ClippedTransitionCarouselWidget;
  id: string;
  slots: ClippedTransitionCarouselSlots;
  props?: ClippedTransitionCarouselProps;
}

export interface ClippedTransitionCarouselSlots {
  children?: ClippedTransitionCarouselItem[];
}

export type ClippedTransitionCarouselItemWidget =
  (typeof ClippedTransitionCarouselItemWidget)[keyof typeof ClippedTransitionCarouselItemWidget];

export const ClippedTransitionCarouselItemWidget = {
  ClippedTransitionCarouselItem: "ClippedTransitionCarouselItem",
} as const;

export interface ClippedTransitionCarouselItem {
  id: string;
  widget: ClippedTransitionCarouselItemWidget;
  slots: ClippedTransitionCarouselItemSlots;
  props?: ClippedTransitionCarouselItemProps;
}

export interface ClippedTransitionCarouselItemSlots {
  stacked: Widget;
  clipped: Widget;
}

export interface ClippedTransitionCarouselItemProps {
  backgroundColor?: string;
  stackedWidgetPercentage?: number;
  clippedWidgetPercentage?: number;
  redirectLink?: RedirectLink;
}

export interface ClippedTransitionCarouselProps {
  height: number;
  heightPercentage?: number;
  loop?: boolean;
  autoPlayDurationInMillis?: number;
  disableAppBarPadding?: boolean;
  bannerPath?: string;
  bannerCount?: number;
}

export type HorizontalListCollectionWidget =
  (typeof HorizontalListCollectionWidget)[keyof typeof HorizontalListCollectionWidget];

export const HorizontalListCollectionWidget = {
  HorizontalListCollection: "HorizontalListCollection",
} as const;

export interface HorizontalListCollection {
  widget: HorizontalListCollectionWidget;
  id: string;
  props: HorizontalListCollectionProps;
}

export interface HorizontalListCollectionProps {
  /** @deprecated */
  collectionId?: string;
  collectionName?: string;
  maxCount?: number;
  viewAllMinLimit?: number;
  collectionWidgetType: CollectionWidgetType;
}

export type BulletPointsWidget =
  (typeof BulletPointsWidget)[keyof typeof BulletPointsWidget];

export const BulletPointsWidget = {
  BulletPoints: "BulletPoints",
} as const;

export interface BulletPoints {
  widget: BulletPointsWidget;
  id: string;
  props: BulletPointsWidgetProps;
}

export interface BulletPointsWidgetProps {
  pointsList?: DataKey[];
}

export type GridCollectionWidget =
  (typeof GridCollectionWidget)[keyof typeof GridCollectionWidget];

export const GridCollectionWidget = {
  GridCollection: "GridCollection",
} as const;

export interface GridCollection {
  widget: GridCollectionWidget;
  id: string;
  props: GridCollectionProps;
}

export interface GridCollectionProps {
  /** @deprecated */
  collectionId?: string;
  collectionName?: string;
  backgroundColor?: string;
  collectionWidgetType: CollectionWidgetType;
}

export type HorizontalListSectionWidget =
  (typeof HorizontalListSectionWidget)[keyof typeof HorizontalListSectionWidget];

export const HorizontalListSectionWidget = {
  HorizontalListSection: "HorizontalListSection",
} as const;

export interface HorizontalListSection {
  widget: HorizontalListSectionWidget;
  id: string;
  props: HorizontalListSectionProps;
  slots: WidgetListSlots;
}

export interface HorizontalListSectionProps {
  height?: number;
  title?: string;
  titleType?: TitleType;
  backgroundColor?: string;
  addItemShadow?: boolean;
  aspectRatio?: number;
  viewPort?: number;
  widthPercentage?: number;
  snap?: boolean;
}

export interface WidgetListSlots {
  children: Widget[];
}

export type SingleCategoryFAQWidget =
  (typeof SingleCategoryFAQWidget)[keyof typeof SingleCategoryFAQWidget];

export const SingleCategoryFAQWidget = {
  SingleCategoryFAQ: "SingleCategoryFAQ",
} as const;

export interface SingleCategoryFAQ {
  widget: SingleCategoryFAQWidget;
  id: string;
  props: SingleCategoryFAQProps;
}

export interface SingleCategoryFAQProps {
  faqBusinessUnit?: string;
  faqCategory: string;
  faqNamespaceIdentifier?: string;
  title?: string;
  titleType?: TitleType;
  maxCollapsedCount?: number;
  maxViewAllFAQLabel?: string;
  expandFAQByDefault?: boolean;
  redirectLink?: RedirectLink;
  isDarkMode?: boolean;
}

export type InvestmentSummaryWidget =
  (typeof InvestmentSummaryWidget)[keyof typeof InvestmentSummaryWidget];

export const InvestmentSummaryWidget = {
  InvestmentSummary: "InvestmentSummary",
} as const;

export interface InvestmentSummary {
  widget: InvestmentSummaryWidget;
  id: string;
  props?: InvestmentSummaryProps;
}

export interface InvestmentSummaryProps {
  isTappable?: boolean;
  showWhenZero?: boolean;
  allowMasking?: boolean;
}

export type FindMyFDWidget =
  (typeof FindMyFDWidget)[keyof typeof FindMyFDWidget];

export const FindMyFDWidget = {
  FindMyFD: "FindMyFD",
} as const;

export interface FindMyFD {
  widget: FindMyFDWidget;
  id: string;
  props?: FindMyFDProps;
  slots?: FindMyFDSlots;
}

export interface FindMyFDSlots {
  zeroState: Widget;
}

export interface FindMyFDProps {
  nibCount?: number;
  ibCount?: number;
  totalBankCount?: number;
  title?: string;
  titleType?: TitleType;
}

export type AppBarProfileActionWidget =
  (typeof AppBarProfileActionWidget)[keyof typeof AppBarProfileActionWidget];

export const AppBarProfileActionWidget = {
  AppBarProfileAction: "AppBarProfileAction",
} as const;

export interface AppBarProfileAction {
  widget: AppBarProfileActionWidget;
  id: string;
  props: AppBarProfileActionProps;
}

export interface AppBarProfileActionProps {
  width: number;
  height: number;
}

export type MarketingListWithCoverWidget =
  (typeof MarketingListWithCoverWidget)[keyof typeof MarketingListWithCoverWidget];

export const MarketingListWithCoverWidget = {
  MarketingListWithCover: "MarketingListWithCover",
} as const;

export interface MarketingListWithCover {
  id: string;
  props: MarketingListWithCoverProps;
  slots: WidgetListSlots;
  widget: MarketingListWithCoverWidget;
}

export interface MarketingListWithCoverProps {
  aspectRatio?: number;
  height?: number;
  redirectLink?: RedirectLink;
  sliderHeight?: number;
  url: string;
  width?: number;
}

export type NextBestFDWidget =
  (typeof NextBestFDWidget)[keyof typeof NextBestFDWidget];

export const NextBestFDWidget = {
  NextBestFD: "NextBestFD",
} as const;

export interface NextBestFD {
  widget: NextBestFDWidget;
  id: string;
  props?: NextBestFDProps;
}

export type OneClickFDWidget =
  (typeof OneClickFDWidget)[keyof typeof OneClickFDWidget];

export const OneClickFDWidget = {
  OneClickFD: "OneClickFD",
} as const;

export interface OneClickFD {
  widget: OneClickFDWidget;
  id: string;
  props?: OneClickFDProps;
}

export type MaturingFdsCarouselWidget =
  (typeof MaturingFdsCarouselWidget)[keyof typeof MaturingFdsCarouselWidget];

export const MaturingFdsCarouselWidget = {
  MaturingFdsCarousel: "MaturingFdsCarousel",
} as const;

export interface MaturingFdsCarousel {
  id: string;
  widget: MaturingFdsCarouselWidget;
  props?: MaturingFdsCarouselProps;
}

export interface NextBestFDProps {
  title?: string;
  titleType?: TitleType;
}

export interface OneClickFDProps {
  title?: string;
  titleType?: TitleType;
}

export interface MaturingFdsCarouselProps {
  title?: string;
  titleType?: TitleType;
}

export type StoriesWidget = (typeof StoriesWidget)[keyof typeof StoriesWidget];

export const StoriesWidget = {
  Stories: "Stories",
} as const;

export interface Stories {
  id: string;
  props: StoriesProps;
  widget: StoriesWidget;
}

export interface StoriesProps {
  storiesItemList?: StoriesItemList;
  title?: string;
  titleType?: TitleType;
  unseenStoriesWidget?: Widget;
  seenStoriesWidget?: Widget;
  toolTipDurationInSeconds?: number;
  toolTipContent?: string;
}

export interface StoriesItemList {
  children: StoriesItem[];
}

export interface StoriesItem {
  id?: string;
  backgroundColor?: string;
  caption?: string;
  ctaWidget?: Widget;
  fit?: MediaFitType;
  label?: string;
  durationInSeconds?: number;
  type?: StoryType;
  url?: string;
}

export type InlineFaqsWidget =
  (typeof InlineFaqsWidget)[keyof typeof InlineFaqsWidget];

export const InlineFaqsWidget = {
  InlineFaqs: "InlineFaqs",
} as const;

export interface InlineFaqs {
  widget: InlineFaqsWidget;
  id: string;
  props: FaqProps;
}

export type FaqsWidget = (typeof FaqsWidget)[keyof typeof FaqsWidget];

export const FaqsWidget = {
  Faqs: "Faqs",
} as const;

export interface Faqs {
  widget: FaqsWidget;
  id: string;
  props: FaqProps;
}

export interface FaqProps {
  faqs: Faq[];
  faqCount?: number;
  searchFieldPrefix?: string;
  searchFieldPrompt?: string[];
  categoryList?: string[];
  faqNamespace?: string;
  faqBusinessUnit?: string;
  faqNamespaceIdentifier?: string;
  path?: string;
  title?: string;
  titleType?: TitleType;
}

export interface Faq {
  faqId: string;
  question: string;
  answer: string;
  htmlAnswer: string;
  externalTags: string[];
  userImage?: string;
  userName: string;
  location?: string;
  upVotes: number;
  downVotes: number;
}

export type BottomSheetWidget =
  (typeof BottomSheetWidget)[keyof typeof BottomSheetWidget];

export const BottomSheetWidget = {
  BottomSheet: "BottomSheet",
} as const;

export interface BottomSheet {
  widget: BottomSheetWidget;
  id: string;
  slots: BottomSheetSlots;
}

export interface BottomSheetSlots {
  body: Widget[];
}

export type StaggeredGridWidget =
  (typeof StaggeredGridWidget)[keyof typeof StaggeredGridWidget];

export const StaggeredGridWidget = {
  StaggeredGrid: "StaggeredGrid",
} as const;

export interface StaggeredGrid {
  widget: StaggeredGridWidget;
  id: string;
  props: StaggeredGridProps;
  slots: StaggeredGridSlots;
}

export interface StaggeredGridProps {
  title?: string;
  titleType?: TitleType;
  type?: StaggeredGridType;
  crossAxisCount?: number;
  hasCrossAxisSpacing?: boolean;
  hasMainAxisSpacing?: boolean;
}

export interface StaggeredGridSlots {
  children: StaggeredGridItem[];
}

export interface StaggeredGridItem {
  id: string;
  widget: string;
  slots: StaggeredGridItemSlots;
  props: StaggeredGridItemProps;
}

export interface StaggeredGridItemSlots {
  child: Widget;
}

export interface StaggeredGridItemProps {
  crossAxisCellCount?: number;
  title?: string;
  titleType?: TitleType;
  mainAxisCellCount?: number;
}

export type FdsInsuranceProgressTrackerWidget =
  (typeof FdsInsuranceProgressTrackerWidget)[keyof typeof FdsInsuranceProgressTrackerWidget];

export const FdsInsuranceProgressTrackerWidget = {
  FdsInsuranceProgressTracker: "FdsInsuranceProgressTracker",
} as const;

export interface FdsInsuranceProgressTracker {
  widget: FdsInsuranceProgressTrackerWidget;
  id: string;
  props?: FdsInsuranceProgressTrackerProps;
}

export interface FdsInsuranceProgressTrackerProps {
  title?: string;
  titleType?: TitleType;
  heading?: string;
  subTitles?: string[];
  footerText?: string;
}

export type StoriesV2Widget =
  (typeof StoriesV2Widget)[keyof typeof StoriesV2Widget];

export const StoriesV2Widget = {
  StoriesV2: "StoriesV2",
} as const;

export interface StoriesV2 {
  id: string;
  props: StoriesV2Props;
  slots?: StoriesV2Slots;
  widget: StoriesV2Widget;
}

export interface StoriesV2Slots {
  children?: StoriesV2Item[];
  unseenStories?: Widget;
  seenStories?: Widget;
}

export interface StoriesV2Props {
  title?: string;
  toolTipDurationInSeconds?: number;
  toolTipContent?: string;
  page?: string;
}

export interface StoriesV2Item {
  id?: string;
  props?: StoriesV2ItemProps;
  slots?: StoriesV2ItemSlots;
  widget?: string;
}

export interface StoriesV2ItemProps {
  backgroundColor?: string;
  caption?: string;
  fit?: MediaFitType;
  label?: string;
  durationInSeconds?: number;
  type?: StoryType;
  url?: string;
  likeEnabled?: boolean;
  shareEnabled?: boolean;
  storyId?: string;
}

export type StoriesContentPropsCta = {
  type?: StoryCTAType;
  loopAnimation?: boolean;
  url?: string;
  redirectLink?: RedirectLink;
};

export interface StoriesContentProps {
  backgroundColor?: string;
  caption?: string;
  fit?: MediaFitType;
  label?: string;
  durationInSeconds?: number;
  type?: StoryType;
  url?: string;
  cta?: StoriesContentPropsCta;
}

export interface StoriesV2ItemSlots {
  ctaWidget?: Widget;
}

export type BondInvestmentsSummaryWidget =
  (typeof BondInvestmentsSummaryWidget)[keyof typeof BondInvestmentsSummaryWidget];

export const BondInvestmentsSummaryWidget = {
  BondInvestmentsSummary: "BondInvestmentsSummary",
} as const;

export interface BondInvestmentsSummary {
  widget: BondInvestmentsSummaryWidget;
  id: string;
  props?: BondInvestmentsSummaryProps;
}

export interface BondInvestmentsSummaryProps {
  isTappable?: boolean;
  showWhenZero?: boolean;
}

export type BondsGridWidget =
  (typeof BondsGridWidget)[keyof typeof BondsGridWidget];

export const BondsGridWidget = {
  BondsGrid: "BondsGrid",
} as const;

export interface BondsGrid {
  widget: BondsGridWidget;
  id: string;
  props?: BondsGridProps;
}

export interface BondsGridProps {
  title?: string;
  titleType?: TitleType;
}

export type KeyValueTileWidget =
  (typeof KeyValueTileWidget)[keyof typeof KeyValueTileWidget];

export const KeyValueTileWidget = {
  KeyValueTile: "KeyValueTile",
} as const;

export interface KeyValueTile {
  widget: KeyValueTileWidget;
  id: string;
  props?: KeyValueTileProps;
}

export type KeyValueTilePropsAnalyticsEventProps = { [key: string]: unknown };

export interface KeyValueTileProps {
  keyText?: DataKey;
  valueText?: DataKey;
  copyContent?: string;
  hintText?: string;
  redirectLink?: RedirectLink;
  analyticsEventProps?: KeyValueTilePropsAnalyticsEventProps;
}

export type ColumnWidget = (typeof ColumnWidget)[keyof typeof ColumnWidget];

export const ColumnWidget = {
  Column: "Column",
} as const;

export interface Column {
  widget: ColumnWidget;
  id: string;
  props?: ColumnProps;
  slots?: ColumnSlots;
}

export interface ColumnSlots {
  items?: Widget[];
}

export interface ColumnProps {
  spacingType?: SpacingType;
  toShowDivider?: boolean;
}

export type NavigationBarWidget =
  (typeof NavigationBarWidget)[keyof typeof NavigationBarWidget];

export const NavigationBarWidget = {
  NavigationBar: "NavigationBar",
} as const;

export interface NavigationBar {
  widget: NavigationBarWidget;
  id: string;
  slots?: NavigationBarSlots;
}

export interface NavigationBarSlots {
  items?: NavigationBarItem[];
}

export type NavigationBarItemWidget =
  (typeof NavigationBarItemWidget)[keyof typeof NavigationBarItemWidget];

export const NavigationBarItemWidget = {
  NavigationBarItem: "NavigationBarItem",
} as const;

export interface NavigationBarItem {
  widget: NavigationBarItemWidget;
  id: string;
  props?: NavigationBarItemProps;
}

export interface NavigationBarItemProps {
  selectedIcon?: Widget;
  unSelectedIcon?: Widget;
  label?: string;
  link?: RedirectLink;
  tag?: string;
}

export type BondDetailsWidget =
  (typeof BondDetailsWidget)[keyof typeof BondDetailsWidget];

export const BondDetailsWidget = {
  BondDetails: "BondDetails",
} as const;

export interface BondDetails {
  widget: BondDetailsWidget;
  id: string;
  props?: BondDetailsProps;
  slots?: BondDetailsSlots;
}

export type BondDetailsPropsViewEventProps = { [key: string]: unknown };

export interface BondDetailsProps {
  title?: string;
  titleType?: TitleType;
  extendBodyBehindAppBar?: boolean;
  backgroundColor?: string;
  tags?: string[];
  name?: string;
  logo?: string;
  logoBg?: string;
  viewEventProps?: BondDetailsPropsViewEventProps;
}

export interface BondDetailsSlots {
  header?: Widget;
  children?: Widget[];
  heroHeader?: BondsHeroHeader;
  bottomButton?: Widget;
}

export type BondsHeroHeaderWidget =
  (typeof BondsHeroHeaderWidget)[keyof typeof BondsHeroHeaderWidget];

export const BondsHeroHeaderWidget = {
  BondsHeroHeader: "BondsHeroHeader",
} as const;

export interface BondsHeroHeader {
  widget: BondsHeroHeaderWidget;
  id: string;
  props?: BondsHeroHeaderProps;
  slots?: BondsHeroHeaderSlots;
}

export interface BondsHeroHeaderProps {
  isContentExpandable: boolean;
  section?: string;
  bondId?: string;
  backgroundColor?: string;
}

export interface BondsHeroHeaderSlots {
  content?: Widget;
}

export type BondsKeyHighlightsWidget =
  (typeof BondsKeyHighlightsWidget)[keyof typeof BondsKeyHighlightsWidget];

export const BondsKeyHighlightsWidget = {
  BondsKeyHighlights: "BondsKeyHighlights",
} as const;

export interface BondsKeyHighlights {
  widget: BondsKeyHighlightsWidget;
  id: string;
  props?: BondsKeyHighlightProps;
}

export interface BondsKeyHighlightProps {
  availableUnitsPercent?: number;
  returns: TableDataItem;
  tenure: TableDataItem;
  soldUnits: TableDataItem;
}

export type BondsMarketingHighlightsWidget =
  (typeof BondsMarketingHighlightsWidget)[keyof typeof BondsMarketingHighlightsWidget];

export const BondsMarketingHighlightsWidget = {
  BondsMarketingHighlights: "BondsMarketingHighlights",
} as const;

export interface BondsMarketingHighlights {
  widget: BondsMarketingHighlightsWidget;
  id: string;
  props?: BondsMarketingHighlightsProps;
}

export interface BondsMarketingHighlightsProps {
  marketingHighlightsMap?: DataKey[];
}

export type BondsInvestButtonWidget =
  (typeof BondsInvestButtonWidget)[keyof typeof BondsInvestButtonWidget];

export const BondsInvestButtonWidget = {
  BondsInvestButton: "BondsInvestButton",
} as const;

export interface BondsInvestButton {
  widget: BondsInvestButtonWidget;
  id: string;
  props?: BondsInvestButtonProps;
}

export type BondsInvestButtonPropsAnalyticsEventProps = {
  [key: string]: unknown;
};

export interface BondsInvestButtonProps {
  label?: string;
  completeKYCLabel?: string;
  soldOutLabel?: string;
  isFloatingButton?: boolean;
  isKYCCompleted?: boolean;
  isBondSoldOut?: boolean;
  bondId?: string;
  analyticsEventProps?: BondsInvestButtonPropsAnalyticsEventProps;
}

export type BondsReturnsCalculatorWidget =
  (typeof BondsReturnsCalculatorWidget)[keyof typeof BondsReturnsCalculatorWidget];

export const BondsReturnsCalculatorWidget = {
  BondsReturnsCalculator: "BondsReturnsCalculator",
} as const;

export interface BondsReturnsCalculator {
  widget: BondsReturnsCalculatorWidget;
  id: string;
  props?: BondsReturnsCalculatorProps;
}

export type BondsReturnsCalculatorPropsRepaymentScheduleItem = {
  type?: string;
  interestAmount?: number;
  principalAmount?: number;
  date?: string;
  dateTime?: string;
};

export type BondsReturnsCalculatorPropsAnalyticsEventProps = {
  [key: string]: unknown;
};

export interface BondsReturnsCalculatorProps {
  title?: string;
  titleType?: TitleType;
  label?: string;
  initialUnits?: number;
  pricePerBond?: number;
  tenure?: string;
  rate?: number;
  bondId?: string;
  payoutType?: string;
  issueDate?: string;
  maturityDate?: string;
  repaymentSchedule?: BondsReturnsCalculatorPropsRepaymentScheduleItem[];
  analyticsEventProps?: BondsReturnsCalculatorPropsAnalyticsEventProps;
}

export type DataTableWidget =
  (typeof DataTableWidget)[keyof typeof DataTableWidget];

export const DataTableWidget = {
  DataTable: "DataTable",
} as const;

export interface DataTable {
  widget: DataTableWidget;
  id: string;
  props?: DataTableProps;
}

export interface DataTableProps {
  title?: string;
  titleType?: TitleType;
  tableData?: TableDataResponse;
}

export type BondsDataTableWidget =
  (typeof BondsDataTableWidget)[keyof typeof BondsDataTableWidget];

export const BondsDataTableWidget = {
  BondsDataTable: "BondsDataTable",
} as const;

export interface BondsDataTable {
  widget: BondsDataTableWidget;
  id: string;
  props?: DataTableProps;
}

export type BondsDisclosureButtonWidget =
  (typeof BondsDisclosureButtonWidget)[keyof typeof BondsDisclosureButtonWidget];

export const BondsDisclosureButtonWidget = {
  BondsDisclosureButton: "BondsDisclosureButton",
} as const;

export interface BondsDisclosureButton {
  widget: BondsDisclosureButtonWidget;
  id: string;
  props?: BondsDisclosureButtonProps;
}

export type BondsDisclosureButtonPropsAnalyticsEventProps = {
  [key: string]: unknown;
};

export interface BondsDisclosureButtonProps {
  title?: string;
  titleType?: TitleType;
  label?: string;
  bondId?: string;
  analyticsEventProps?: BondsDisclosureButtonPropsAnalyticsEventProps;
}

export type BondsDisclosureIssuerDetailsWidget =
  (typeof BondsDisclosureIssuerDetailsWidget)[keyof typeof BondsDisclosureIssuerDetailsWidget];

export const BondsDisclosureIssuerDetailsWidget = {
  BondsDisclosureIssuerDetails: "BondsDisclosureIssuerDetails",
} as const;

export interface BondsDisclosureIssuerDetails {
  widget: BondsDisclosureIssuerDetailsWidget;
  id: string;
  props?: BondsDisclosureIssuerDetailsProps;
}

export interface BondsDisclosureIssuerDetailsProps {
  title?: string;
  titleType?: TitleType;
  bondName?: string;
  bondIssuerDescription?: string;
  bondLogo?: string;
  bondLogoBgColor?: string;
  tags?: string[];
  bondId?: string;
}

export type BondsSupportedDocumentsWidget =
  (typeof BondsSupportedDocumentsWidget)[keyof typeof BondsSupportedDocumentsWidget];

export const BondsSupportedDocumentsWidget = {
  BondsSupportedDocuments: "BondsSupportedDocuments",
} as const;

export interface BondsSupportedDocuments {
  widget: BondsSupportedDocumentsWidget;
  id: string;
  props?: BondsSupportedDocumentsProps;
}

export type BondsSupportedDocumentsPropsSupportedDocumentsItem = {
  url?: string;
  name?: string;
};

export interface BondsSupportedDocumentsProps {
  title?: string;
  titleType?: TitleType;
  bondId?: string;
  supportedDocuments?: BondsSupportedDocumentsPropsSupportedDocumentsItem[];
}

export type BondsDisclosureDetailsWidget =
  (typeof BondsDisclosureDetailsWidget)[keyof typeof BondsDisclosureDetailsWidget];

export const BondsDisclosureDetailsWidget = {
  BondsDisclosureDetails: "BondsDisclosureDetails",
} as const;

export interface BondsDisclosureDetails {
  widget: BondsDisclosureDetailsWidget;
  id: string;
  props?: BondsDisclosureDetailsProps;
}

export type BondsDisclosureDetailsPropsAnalyticsEventProps = {
  [key: string]: unknown;
};

export interface BondsDisclosureDetailsProps {
  title?: string;
  titleType?: TitleType;
  bondId?: string;
  disclosures?: TableDataResponse;
  analyticsEventProps?: BondsDisclosureDetailsPropsAnalyticsEventProps;
}

export type BondsSellerEntityDisclosureWidget =
  (typeof BondsSellerEntityDisclosureWidget)[keyof typeof BondsSellerEntityDisclosureWidget];

export const BondsSellerEntityDisclosureWidget = {
  BondsSellerEntityDisclosure: "BondsSellerEntityDisclosure",
} as const;

export interface BondsSellerEntityDisclosure {
  widget: BondsSellerEntityDisclosureWidget;
  id: string;
  props?: BondsSellerEntityDisclosureProps;
}

export interface BondsSellerEntityDisclosureProps {
  title?: string;
  titleType?: TitleType;
  bondId?: string;
  sellerEntityDisclosure?: string;
}

export type BondsFaqsWidget =
  (typeof BondsFaqsWidget)[keyof typeof BondsFaqsWidget];

export const BondsFaqsWidget = {
  BondsFaqs: "BondsFaqs",
} as const;

export interface BondsFaqs {
  widget: BondsFaqsWidget;
  id: string;
  props?: BondsFaqsProps;
}

export interface BondsFaqsProps {
  title?: string;
  titleType?: TitleType;
  label?: string;
}

export type CardWidget = (typeof CardWidget)[keyof typeof CardWidget];

export const CardWidget = {
  Card: "Card",
} as const;

export interface Card {
  widget: CardWidget;
  id: string;
  props?: CardProps;
  slots?: CardSlots;
}

export interface CardSlots {
  content?: Widget;
}

export interface CardProps {
  borderRadius?: number;
  isAttachedWidget?: boolean;
  title?: string;
  isInnerPadding?: boolean;
  titleType?: TitleType;
  contentSpacingType?: CardContentSpacingType;
  contentRadiusType?: CardContentRadiusType;
}

export type BondsCollectionWidget =
  (typeof BondsCollectionWidget)[keyof typeof BondsCollectionWidget];

export const BondsCollectionWidget = {
  BondsCollection: "BondsCollection",
} as const;

export interface BondsCollection {
  widget: BondsCollectionWidget;
  id: string;
  props: BondsCollectionProps;
}

export interface BondsCollectionProps {
  collectionName: string;
  cardType: BondsCollectionCardType;
  viewAllMinLimit?: number;
}

export type BondsCollectionTableWidget =
  (typeof BondsCollectionTableWidget)[keyof typeof BondsCollectionTableWidget];

export const BondsCollectionTableWidget = {
  BondsCollectionTable: "BondsCollectionTable",
} as const;

export interface BondsCollectionTable {
  widget: BondsCollectionTableWidget;
  id: string;
  props: BondsCollectionTableProps;
}

export type InsuranceAmountProgressWidget =
  (typeof InsuranceAmountProgressWidget)[keyof typeof InsuranceAmountProgressWidget];

export const InsuranceAmountProgressWidget = {
  InsuranceAmountProgress: "InsuranceAmountProgress",
} as const;

export interface InsuranceAmountProgress {
  widget: InsuranceAmountProgressWidget;
  id: string;
  props: InsuranceAmountProgressProps;
}

export type InvestmentBasicDetailsWidget =
  (typeof InvestmentBasicDetailsWidget)[keyof typeof InvestmentBasicDetailsWidget];

export const InvestmentBasicDetailsWidget = {
  InvestmentBasicDetails: "InvestmentBasicDetails",
} as const;

export interface InvestmentBasicDetails {
  widget: InvestmentBasicDetailsWidget;
  id: string;
  props: InvestmentCardProps;
}

export interface BondsCollectionTableProps {
  titleType?: TitleType;
  collectionName: string;
  columnHeadings: string[];
}

export interface Cta {
  text: string;
  redirectLink: RedirectLink;
}

export interface InvestmentAmount {
  investedAmountTitleKey: string;
  interestEarnedTitleKey: string;
  currentAmountTitleKey: string;
  investedAmountValue: number;
  interestEarnedValue: number;
  currentAmountValue: number;
}

export interface InsuranceAmountProgressProps {
  totalInsuredAmount: number;
  currentAmount: number;
  subTitle: string;
  footerText?: string;
  cta?: Cta;
}

export interface OrgInvestmentSummaryProps {
  investmentAmountSummary?: InvestmentAmount;
  insuranceAmountProgress?: InsuranceAmountProgressProps;
  footerText?: string;
  nudgeId?: string;
  isRootWidget?: boolean;
}

export type OrgInvestmentSummaryWidget =
  (typeof OrgInvestmentSummaryWidget)[keyof typeof OrgInvestmentSummaryWidget];

export const OrgInvestmentSummaryWidget = {
  OrgInvestmentSummary: "OrgInvestmentSummary",
} as const;

export interface OrgInvestmentSummary {
  id: string;
  props: OrgInvestmentSummaryProps;
  widget: OrgInvestmentSummaryWidget;
}

export type OrgInvestmentCardsWidget =
  (typeof OrgInvestmentCardsWidget)[keyof typeof OrgInvestmentCardsWidget];

export const OrgInvestmentCardsWidget = {
  OrgInvestmentCards: "OrgInvestmentCards",
} as const;

export interface OrgInvestmentCards {
  id: string;
  props: OrgInvestmentCardsProps;
  widget: OrgInvestmentCardsWidget;
}

export interface OrgInvestmentCardsProps {
  headerText?: string;
  cards?: OrgInvestmentCard[];
}

export interface OrgInvestmentCard {
  orgInvestmentStatus: InvestmentStatus;
  isCollapsed: boolean;
  title: string;
  orgIconUrls?: string[];
  orgColor: string;
  subTitle: string;
  animateOnRedirect?: boolean;
  investmentSummary?: InvestmentAmount;
  redirectLink?: RedirectLink;
  productType?: ProductType;
}

export type InvestmentCardsWidget =
  (typeof InvestmentCardsWidget)[keyof typeof InvestmentCardsWidget];

export const InvestmentCardsWidget = {
  InvestmentCards: "InvestmentCards",
} as const;

export interface InvestmentCards {
  id: string;
  props: InvestmentCardsProps;
  widget: InvestmentCardsWidget;
}

export interface InvestmentCardsProps {
  cards?: InvestmentCardProps[];
}

export interface InvestmentCardProps {
  investmentStatus?: InvestmentStatus;
  title?: string;
  bankIconUrls?: string[];
  bankColor?: string;
  footerText?: string;
  insuranceAmountProgress?: InsuranceAmountProgressProps;
  subTitle?: string;
  investmentSummary?: InvestmentAmount;
  redirectLink?: RedirectLink;
  productType?: ProductType;
}

export type InvestmentStatus =
  (typeof InvestmentStatus)[keyof typeof InvestmentStatus];

export const InvestmentStatus = {
  ACTIVE: "ACTIVE",
  PROCESSING: "PROCESSING",
  MATURED: "MATURED",
  WITHDRAWN: "WITHDRAWN",
} as const;

export type ProductType = (typeof ProductType)[keyof typeof ProductType];

export const ProductType = {
  FD: "FD",
  BOND: "BOND",
} as const;

export type BondsGridCollectionWidget =
  (typeof BondsGridCollectionWidget)[keyof typeof BondsGridCollectionWidget];

export const BondsGridCollectionWidget = {
  BondsGridCollection: "BondsGridCollection",
} as const;

export interface BondsGridCollection {
  widget: BondsGridCollectionWidget;
  id: string;
  props: BondsGridCollectionProps;
}

export interface BondsGridCollectionProps {
  collectionName: string;
  backgroundColor?: string;
}

export type KebabMenuV2Widget =
  (typeof KebabMenuV2Widget)[keyof typeof KebabMenuV2Widget];

export const KebabMenuV2Widget = {
  KebabMenuV2: "KebabMenuV2",
} as const;

export interface KebabMenuV2 {
  id?: string;
  slots?: KebabMenuV2Slots;
  props?: KebabMenuV2Props;
  widget: KebabMenuV2Widget;
}

export interface KebabMenuV2Slots {
  iconWidget?: Widget;
  menuItems?: MenuItemV2[];
}

export type KebabMenuV3Widget =
  (typeof KebabMenuV3Widget)[keyof typeof KebabMenuV3Widget];

export const KebabMenuV3Widget = {
  KebabMenuV3: "KebabMenuV3",
} as const;

export interface KebabMenuV3 {
  id?: string;
  slots?: KebabMenuV3Slots;
  props?: KebabMenuV3Props;
  widget: KebabMenuV3Widget;
}

export interface KebabMenuV3Slots {
  iconWidget?: Widget;
  menuItems?: Widget[];
}

export interface KebabMenuV3Props {
  label?: string;
}

export type FDReceiptKebabMenuItemWidget =
  (typeof FDReceiptKebabMenuItemWidget)[keyof typeof FDReceiptKebabMenuItemWidget];

export const FDReceiptKebabMenuItemWidget = {
  FDReceiptKebabMenuItem: "FDReceiptKebabMenuItem",
} as const;

export interface FDReceiptKebabMenuItem {
  id?: string;
  props?: FDReceiptKebabMenuItemProps;
  slots?: FDReceiptKebabMenuItemSlots;
  widget: FDReceiptKebabMenuItemWidget;
}

export type FDReceiptKebabMenuItemPropsData = { [key: string]: unknown };

export interface FDReceiptKebabMenuItemProps {
  label?: DataKey;
  disabledToastText?: string;
  redirectLink?: RedirectLink;
  data?: FDReceiptKebabMenuItemPropsData;
}

export interface FDReceiptKebabMenuItemSlots {
  leadingWidget?: Widget;
}

export type MenuItemV2Widget =
  (typeof MenuItemV2Widget)[keyof typeof MenuItemV2Widget];

export const MenuItemV2Widget = {
  MenuItemV2: "MenuItemV2",
} as const;

export interface MenuItemV2 {
  id?: string;
  props?: MenuItemV2Props;
  slots?: MenuItemV2Slots;
  widget: MenuItemV2Widget;
}

export interface KebabMenuV2Props {
  label?: string;
}

export interface MenuItemV2Slots {
  leadingWidget?: Widget;
}

export interface MenuItemV2Props {
  label?: DataKey;
  disabledToastText?: string;
  redirectLink?: RedirectLink;
}

export type KebabMenuWidget =
  (typeof KebabMenuWidget)[keyof typeof KebabMenuWidget];

export const KebabMenuWidget = {
  KebabMenu: "KebabMenu",
} as const;

export interface KebabMenu {
  id: string;
  props: KebabMenuProps;
  widget: KebabMenuWidget;
}

export type BondsTnCAndKYCFooterWidget =
  (typeof BondsTnCAndKYCFooterWidget)[keyof typeof BondsTnCAndKYCFooterWidget];

export const BondsTnCAndKYCFooterWidget = {
  BondsTnCAndKYCFooter: "BondsTnCAndKYCFooter",
} as const;

export interface BondsTnCAndKYCFooter {
  id: string;
  props: BondsTnCAndKYCFooterProps;
  widget: BondsTnCAndKYCFooterWidget;
}

export interface BondsTnCAndKYCFooterProps {
  isKYCCompleted?: boolean;
}

export type BondsSingleMediaWidget =
  (typeof BondsSingleMediaWidget)[keyof typeof BondsSingleMediaWidget];

export const BondsSingleMediaWidget = {
  BondsSingleMedia: "BondsSingleMedia",
} as const;

export interface BondsSingleMedia {
  widget: BondsSingleMediaWidget;
  id: string;
  props?: BondsSingleMediaProps;
  slots?: BondsSingleMediaSlots;
}

export interface BondsSingleMediaProps {
  title?: string;
  titleType?: TitleType;
  bondId?: string;
  section: string;
}

export interface BondsSingleMediaSlots {
  content?: Widget;
}

export interface KebabMenuProps {
  iconUrl?: string;
  menuItems?: MenuItem[];
}

export interface MenuItem {
  iconUrl?: string;
  label: string;
  redirectLink: RedirectLink;
}

export type InAppRatingInvisibleWidgetWidget =
  (typeof InAppRatingInvisibleWidgetWidget)[keyof typeof InAppRatingInvisibleWidgetWidget];

export const InAppRatingInvisibleWidgetWidget = {
  InAppRatingInvisibleWidget: "InAppRatingInvisibleWidget",
} as const;

export interface InAppRatingInvisibleWidget {
  widget: InAppRatingInvisibleWidgetWidget;
  id?: string;
}

export type SuryodayCreditCardFooterWidget =
  (typeof SuryodayCreditCardFooterWidget)[keyof typeof SuryodayCreditCardFooterWidget];

export const SuryodayCreditCardFooterWidget = {
  SuryodayCreditCardFooter: "SuryodayCreditCardFooter",
} as const;

export interface SuryodayCreditCardFooter {
  widget: SuryodayCreditCardFooterWidget;
  id: string;
  props: SuryodayCreditCardFooterProps;
}

export interface SuryodayCreditCardFooterProps {
  ctaWidget: Widget;
}

export type RedirectWidgetWidget =
  (typeof RedirectWidgetWidget)[keyof typeof RedirectWidgetWidget];

export const RedirectWidgetWidget = {
  RedirectWidget: "RedirectWidget",
} as const;

export interface RedirectWidget {
  widget: RedirectWidgetWidget;
  id: string;
  props: RedirectWidgetProps;
}

export interface RedirectWidgetProps {
  link: RedirectLink;
}

export type FDCollectionTableWidget =
  (typeof FDCollectionTableWidget)[keyof typeof FDCollectionTableWidget];

export const FDCollectionTableWidget = {
  FDCollectionTable: "FDCollectionTable",
} as const;

export interface FDCollectionTable {
  widget: FDCollectionTableWidget;
  id: string;
  props: FDCollectionTableProps;
}

export interface FDCollectionTableProps {
  titleType?: TitleType;
  /** @deprecated */
  collectionId?: string;
  collectionName?: string;
  columnHeadings: string[];
}

export type BankDetailsWidget =
  (typeof BankDetailsWidget)[keyof typeof BankDetailsWidget];

export const BankDetailsWidget = {
  BankDetails: "BankDetails",
} as const;

export interface BankDetails {
  widget: BankDetailsWidget;
  id: string;
  props?: BankDetailsProps;
  slots?: BankDetailsSlots;
}

export type BankDetailsPropsViewEventProps = { [key: string]: unknown };

export interface BankDetailsProps {
  title?: string;
  titleType?: TitleType;
  extendBodyBehindAppBar?: boolean;
  backgroundColor?: string;
  tags?: string[];
  name?: string;
  logo?: string;
  logoBg?: string;
  branchLocatorImageUrl?: string;
  viewEventProps?: BankDetailsPropsViewEventProps;
  logoFrameRedirectLink?: RedirectLink;
}

export interface BankDetailsSlots {
  header?: Widget;
  children?: Widget[];
  heroHeader?: BanksHeroHeader;
  bottomButton?: Widget;
  logoFrame?: Widget;
}

export type BanksHeroHeaderWidget =
  (typeof BanksHeroHeaderWidget)[keyof typeof BanksHeroHeaderWidget];

export const BanksHeroHeaderWidget = {
  BanksHeroHeader: "BanksHeroHeader",
} as const;

export interface BanksHeroHeader {
  widget: BanksHeroHeaderWidget;
  id: string;
  props?: BanksHeroHeaderProps;
  slots?: BanksHeroHeaderSlots;
}

export interface BanksHeroHeaderProps {
  isContentExpandable: boolean;
  section?: string;
  bankId?: string;
  backgroundColor?: string;
}

export interface BanksHeroHeaderSlots {
  content?: Widget;
}

export type BankTableCardWidget =
  (typeof BankTableCardWidget)[keyof typeof BankTableCardWidget];

export const BankTableCardWidget = {
  BankTableCard: "BankTableCard",
} as const;

export interface BankTableCard {
  widget: BankTableCardWidget;
  id: string;
  props?: BankTableCardProps;
  slots?: BankTableCardSlots;
}

export interface BankTableCardProps {
  section?: string;
  tags?: string[];
  title?: DataKey;
  bankResponse?: BankResponseProps;
  footerText?: DataKey;
}

export interface BankTableCardSlots {
  children?: BankTableCardItem[];
}

export type BankTableCardItemWidget =
  (typeof BankTableCardItemWidget)[keyof typeof BankTableCardItemWidget];

export const BankTableCardItemWidget = {
  BankTableCardItem: "BankTableCardItem",
} as const;

export interface BankTableCardItem {
  widget: BankTableCardItemWidget;
  id: string;
  props?: BankTableCardItemProps;
}

export interface BankTableCardItemProps {
  label?: string;
  labelValue?: string;
  subLabel?: string;
}

export type BankIbCompareCardWidget =
  (typeof BankIbCompareCardWidget)[keyof typeof BankIbCompareCardWidget];

export const BankIbCompareCardWidget = {
  BankIbCompareCard: "BankIbCompareCard",
} as const;

export interface BankIbCompareCard {
  widget: BankIbCompareCardWidget;
  id: string;
  props?: BankIbCompareCardProps;
}

export interface BankIbCompareCardProps {
  title?: DataKey;
  note?: DataKey;
  children?: BankIbCompareCardItem[];
}

export interface BankIbCompareCardItem {
  interestRate?: number;
  bankLogoUrl?: string;
  bankName?: string;
}

export type BankTenureCardWidget =
  (typeof BankTenureCardWidget)[keyof typeof BankTenureCardWidget];

export const BankTenureCardWidget = {
  BankTenureCard: "BankTenureCard",
} as const;

export interface BankTenureCard {
  widget: BankTenureCardWidget;
  id: string;
  props?: BankTenureCardProps;
  slots?: BanksTenureCardSlots;
}

export type BankDICGCCardWidget =
  (typeof BankDICGCCardWidget)[keyof typeof BankDICGCCardWidget];

export const BankDICGCCardWidget = {
  BankDICGCCard: "BankDICGCCard",
} as const;

export interface BankDICGCCard {
  widget: BankDICGCCardWidget;
  id: string;
  props?: BankDICGCCardProps;
}

export interface BankDICGCCardProps {
  backgroundImageUrl?: string;
  bankId?: string;
  bankName?: string;
  heading?: string;
  profileImageUrls?: string[];
}

export interface BanksTenureCardSlots {
  content?: Widget;
}

export interface BankTenureCardProps {
  children?: BankTenureCardItems[];
  womenRoiDiff?: number;
  childrenWomenRates?: BankTenureCardItems[];
  tenureBts?: BankTenureCardBottomsheetProps;
  bankResponse?: BankResponseProps;
  childrenV2?: BankTenureCardItems[];
  childrenWomenRatesV2?: BankTenureCardItems[];
  xirrText?: DataKey;
  lineItemIconUrl?: string;
  bottomsheetLineItemIconUrl?: string;
}

export interface BankTenureCardItems {
  tenure?: string;
  investorTypeLabel?: string;
  tag?: string;
  interestRate?: number;
  xirr?: number;
}

export interface BankTenureCardBottomsheetProps {
  bankResponse?: BankResponseProps;
  description?: string;
  womenRoiDiff?: number;
  isRateChangeNotificationEnabled?: boolean;
  tenureLineItems?: BankTenureCardBottomsheetItems[];
  womenTenureLineItems?: BankTenureCardBottomsheetItems[];
  seniorCitizenRoiDiff?: number;
  tenureLineItemsGeneral?: BankTenureCardBottomsheetItemsV2[];
  tenureLineItemsSenior?: BankTenureCardBottomsheetItemsV2[];
  tenureLineItemsWomen?: BankTenureCardBottomsheetItemsV2[];
  tenureLineItemsWomenSenior?: BankTenureCardBottomsheetItemsV2[];
}

export interface BankTenureCardBottomsheetItems {
  tenure?: string;
  tag?: string;
  generalInterestRate?: number;
  seniorInterestRate?: number;
}

export interface BankTenureCardBottomsheetItemsV2 {
  tenure?: string;
  tag?: string;
  rate?: number;
  xirr?: number;
}

export type BankFdNibCompareCardWidget =
  (typeof BankFdNibCompareCardWidget)[keyof typeof BankFdNibCompareCardWidget];

export const BankFdNibCompareCardWidget = {
  BankFdNibCompareCard: "BankFdNibCompareCard",
} as const;

export interface BankFdNibCompareCard {
  widget: BankFdNibCompareCardWidget;
  id: string;
  props?: BankFdNibCompareCardProps;
}

export interface BankFdNibCompareCardProps {
  title?: string;
  primaryCtaText?: string;
  highestRateBankResponse: BankResponseProps;
  bankResponse: BankResponseProps;
  compareLineItems: BankFdNibCompareCardLineItemsProps[];
}

export interface BankResponseProps {
  bankName: string;
  bankLogoUrl: string;
  bankId: string;
  bankColor?: string;
  investabilityStatus: BankInvestabilityStatus;
}

export interface BankFdNibCompareCardLineItemsProps {
  title?: string;
  highestBankPoint?: string;
  currentBankPoint?: string;
  moreDetailsLink?: RedirectLink;
}

export type BankMessageWidgetWidget =
  (typeof BankMessageWidgetWidget)[keyof typeof BankMessageWidgetWidget];

export const BankMessageWidgetWidget = {
  BankMessageWidget: "BankMessageWidget",
} as const;

export interface BankMessageWidget {
  widget: BankMessageWidgetWidget;
  id: string;
  props?: BankMessageWidgetProps;
}

export interface BankMessageWidgetProps {
  message?: DataKey;
}

export type BankNibInvestableOptionsWidget =
  (typeof BankNibInvestableOptionsWidget)[keyof typeof BankNibInvestableOptionsWidget];

export const BankNibInvestableOptionsWidget = {
  BankNibInvestableOptions: "BankNibInvestableOptions",
} as const;

export interface BankNibInvestableOptions {
  widget: BankNibInvestableOptionsWidget;
  id: string;
  props?: BankNibInvestableOptionsProps;
}

export interface BankNibInvestableOptionsProps {
  title?: DataKey;
  description?: DataKey;
  /** @deprecated */
  collectionId?: string;
  collectionName?: string;
  itemCtaText?: string;
  itemComingSoonText?: string;
}

export type BankShareWidgetWidget =
  (typeof BankShareWidgetWidget)[keyof typeof BankShareWidgetWidget];

export const BankShareWidgetWidget = {
  BankShareWidget: "BankShareWidget",
} as const;

export interface BankShareWidget {
  widget: BankShareWidgetWidget;
  id: string;
  props?: BankShareWidgetProps;
}

export interface BankShareWidgetProps {
  shareText?: DataKey;
  shareImageUrl?: string;
  isShareEnabled: boolean;
  bankResponse?: BankResponseProps;
}

export type BankInvestButtonWidget =
  (typeof BankInvestButtonWidget)[keyof typeof BankInvestButtonWidget];

export const BankInvestButtonWidget = {
  BankInvestButton: "BankInvestButton",
} as const;

export interface BankInvestButton {
  widget: BankInvestButtonWidget;
  id: string;
  props?: BankInvestButtonProps;
}

export interface BankInvestButtonProps {
  isFloating?: boolean;
  bankId: string;
  primaryButton: BankButtonProps;
  secondaryButton?: BankButtonProps;
  bankResponse?: BankResponseProps;
}

export interface BankButtonProps {
  ctaText: string;
  showSecondaryButtonUi?: boolean;
  mapFdWithEf?: boolean;
  redirectLink?: RedirectLink;
  tag?: BankButtonTagProps;
}

export interface BankButtonTagProps {
  text?: string;
  textColor?: string;
  backgroundColor?: string;
  shimmerColor?: string;
  borderColor?: string;
  disableShimmer?: boolean;
  child?: Widget;
  prefixIcon?: Widget;
}

export type BanksSingleMediaWidget =
  (typeof BanksSingleMediaWidget)[keyof typeof BanksSingleMediaWidget];

export const BanksSingleMediaWidget = {
  BanksSingleMedia: "BanksSingleMedia",
} as const;

export interface BanksSingleMedia {
  widget: BanksSingleMediaWidget;
  id: string;
  props?: BanksSingleMediaProps;
  slots?: BanksSingleMediaSlots;
}

export type BanksSingleMediaPropsAnalyticsEventProps = {
  [key: string]: unknown;
};

export interface BanksSingleMediaProps {
  title?: string;
  titleType?: TitleType;
  section: string;
  analyticsEventProps?: BanksSingleMediaPropsAnalyticsEventProps;
}

export interface BanksSingleMediaSlots {
  content?: Widget;
}

export type BankHighlightPointersCardWidget =
  (typeof BankHighlightPointersCardWidget)[keyof typeof BankHighlightPointersCardWidget];

export const BankHighlightPointersCardWidget = {
  BankHighlightPointersCard: "BankHighlightPointersCard",
} as const;

export interface BankHighlightPointersCard {
  widget: BankHighlightPointersCardWidget;
  id: string;
  props?: BankHighlightPointersCardProps;
}

export interface BankHighlightPointersCardProps {
  section?: string;
  tags?: string[];
  title?: DataKey;
  children?: BankHighlightPointersCardItem[];
}

export interface BankHighlightPointersCardItem {
  text?: DataKey;
}

export type BankWithdrawalCalculatorCardWidget =
  (typeof BankWithdrawalCalculatorCardWidget)[keyof typeof BankWithdrawalCalculatorCardWidget];

export const BankWithdrawalCalculatorCardWidget = {
  BankWithdrawalCalculatorCard: "BankWithdrawalCalculatorCard",
} as const;

export interface BankWithdrawalCalculatorCard {
  widget: BankWithdrawalCalculatorCardWidget;
  id: string;
  props?: BankWithdrawalCalculatorCardProps;
}

export type SuryodayCreditCardAddressChangeFooterWidget =
  (typeof SuryodayCreditCardAddressChangeFooterWidget)[keyof typeof SuryodayCreditCardAddressChangeFooterWidget];

export const SuryodayCreditCardAddressChangeFooterWidget = {
  SuryodayCreditCardAddressChangeFooter:
    "SuryodayCreditCardAddressChangeFooter",
} as const;

export interface SuryodayCreditCardAddressChangeFooter {
  widget: SuryodayCreditCardAddressChangeFooterWidget;
  id: string;
  props: SuryodayCreditCardAddressChangeFooterProps;
}

export type SuryodayCreditCardAddressChangeFooterPropsCtaType =
  (typeof SuryodayCreditCardAddressChangeFooterPropsCtaType)[keyof typeof SuryodayCreditCardAddressChangeFooterPropsCtaType];

export const SuryodayCreditCardAddressChangeFooterPropsCtaType = {
  SUCCESS: "SUCCESS",
  FAILED: "FAILED",
  IN_PROGRESS: "IN_PROGRESS",
} as const;

export interface SuryodayCreditCardAddressChangeFooterProps {
  ctaType: SuryodayCreditCardAddressChangeFooterPropsCtaType;
  ctaText?: string;
}

export interface BankWithdrawalCalculatorCardProps {
  title?: DataKey;
  cardTitle?: DataKey;
  returnsDescription?: DataKey;
  zeroReturnsDescription?: DataKey;
  intermediateZeroReturnsDescription?: DataKey;
  footerText?: DataKey;
  fdWithdrawalCalculationDetails?: FdWithdrawalCalculationDetailsProps;
  bankResponse?: BankResponseProps;
}

export interface FdWithdrawalCalculationDetailsProps {
  interestRate?: number;
  investmentAmount?: number;
  maturityAmount?: number;
  tenure?: string;
  sliderPoints?: FdWithdrawalCalculationItem[];
  bottomsheetData?: FdWithdrawalCalculationBtsData;
}

export interface FdWithdrawalCalculationItem {
  tenure?: string;
  withdrawalAmount?: number;
  rate?: number;
}

export interface FdWithdrawalCalculationBtsData {
  title?: DataKey;
  description?: DataKey;
  children?: FdWithdrawalCalculationBtsDataItems[];
}

export interface FdWithdrawalCalculationBtsDataItems {
  tenure?: string;
  rate?: number;
  originalRate?: number;
}

export type DynamicBankFaqsWidget =
  (typeof DynamicBankFaqsWidget)[keyof typeof DynamicBankFaqsWidget];

export const DynamicBankFaqsWidget = {
  DynamicBankFaqs: "DynamicBankFaqs",
} as const;

export interface DynamicBankFaqs {
  widget: DynamicBankFaqsWidget;
  id: string;
  props: DynamicBankFaqsProps;
}

export interface DynamicBankFaqsProps {
  faqBusinessUnit?: string;
  faqCategory: string;
  faqNamespaceIdentifier?: string;
  title?: string;
  titleType?: TitleType;
  maxCollapsedCount?: number;
  maxViewAllFAQLabel?: string;
  expandFAQByDefault?: boolean;
  redirectLink?: RedirectLink;
  bankResponse?: BankResponseProps;
}

export type SuryodayCreditCardWaitListFooterWidget =
  (typeof SuryodayCreditCardWaitListFooterWidget)[keyof typeof SuryodayCreditCardWaitListFooterWidget];

export const SuryodayCreditCardWaitListFooterWidget = {
  SuryodayCreditCardWaitListFooter: "SuryodayCreditCardWaitListFooter",
} as const;

export interface SuryodayCreditCardWaitListFooter {
  widget: SuryodayCreditCardWaitListFooterWidget;
  id: string;
  props: SuryodayCreditCardWaitListProps;
}

export interface SuryodayCreditCardWaitListProps {
  notifyCTAWidget: Widget;
  inviteCTAWidget: Widget;
  notifyMeText: string;
  shareText: string;
}

export interface WidgetInteractionResponse {
  totalInteractionCount?: string;
  totalInteractionCountShort?: string;
  lastUserName?: string;
  lastInteractionTimeAgo?: string;
}

export interface PollResponse {
  pollId?: string;
  title?: string;
  isDismissible?: boolean;
  hideWhenCompleted?: boolean;
  autoRedirect?: boolean;
  redirectLink?: RedirectLink;
  questions?: PollQuestion[];
}

export type PollQuestionQuestionType =
  (typeof PollQuestionQuestionType)[keyof typeof PollQuestionQuestionType];

export const PollQuestionQuestionType = {
  SINGLE_SELECTION: "SINGLE_SELECTION",
  MULTIPLE_SELECTION: "MULTIPLE_SELECTION",
  CORRECT_ANSWER_SELECTION: "CORRECT_ANSWER_SELECTION",
  RANGE_SELECTION: "RANGE_SELECTION",
} as const;

export interface PollQuestion {
  questionId?: string;
  question?: string;
  description?: string;
  questionType?: PollQuestionQuestionType;
  options?: PollQuestionOption[];
  shuffleOptions?: boolean;
  isQuestionAnswered?: boolean;
  config?: PollConfig;
}

export interface PollConfig {
  lowerRangeLabel?: string;
  higherRangeLabel?: string;
  rangeFeedbackTitle?: string;
  rangeFeedbackDescription?: string;
  rangeFeedbackInputHint?: string;
}

export interface PollQuestionOption {
  optionId?: string;
  text?: string;
  percentage?: number;
  isCorrectAnswer?: boolean;
  isSelected?: boolean;
}

export interface PollQuestionSubmitRequest {
  selectedOptions?: string[];
}

export interface PollCommentSubmitRequest {
  comment: string;
}

export interface PollOptionsJsonParser {
  options?: PollQuestionOption[];
}

export interface PollResponseContentJsonParser {
  redirectLink?: RedirectLink;
}

export type TextWidget = (typeof TextWidget)[keyof typeof TextWidget];

export const TextWidget = {
  Text: "Text",
} as const;

export interface Text {
  widget: TextWidget;
  id: string;
  props: TextProps;
}

export interface TextProps {
  text: DataKey;
  textStyle?: TextStyle;
  isCentreAligned?: boolean;
}

export type CounterWidget = (typeof CounterWidget)[keyof typeof CounterWidget];

export const CounterWidget = {
  Counter: "Counter",
} as const;

export interface Counter {
  widget: CounterWidget;
  id: string;
  props: CounterProps;
  slots: CounterSlots;
}

export interface CounterProps {
  endTime: string;
}

export interface CounterSlots {
  content: Widget;
}

export type PollCardWidget =
  (typeof PollCardWidget)[keyof typeof PollCardWidget];

export const PollCardWidget = {
  PollCard: "PollCard",
} as const;

export interface PollCard {
  widget: PollCardWidget;
  id: string;
  props: PollCardProps;
}

export interface PollCardProps {
  pollId: string;
}

export interface PollQuestionWithUserSelections {
  questionId?: string;
  question?: string;
  description?: string;
  questionType?: string;
  optionsJson?: string;
  shuffleOptions?: boolean;
  selectedOptions?: string[];
  configJson?: string;
}

export type HorizontalCollectionSectionWidget =
  (typeof HorizontalCollectionSectionWidget)[keyof typeof HorizontalCollectionSectionWidget];

export const HorizontalCollectionSectionWidget = {
  HorizontalCollectionSection: "HorizontalCollectionSection",
} as const;

export interface HorizontalCollectionSection {
  widget: HorizontalCollectionSectionWidget;
  id: string;
  props: HorizontalCollectionSectionProps;
  slots: HorizontalCollectionSectionSlots;
}

export interface HorizontalCollectionSectionProps {
  title?: string;
  titleType?: TitleType;
}

export interface HorizontalCollectionSectionSlots {
  items?: HorizontalCollectionItem[];
}

export type HorizontalCollectionItemWidget =
  (typeof HorizontalCollectionItemWidget)[keyof typeof HorizontalCollectionItemWidget];

export const HorizontalCollectionItemWidget = {
  HorizontalCollectionItem: "HorizontalCollectionItem",
} as const;

export interface HorizontalCollectionItem {
  widget: HorizontalCollectionItemWidget;
  id: string;
  props: HorizontalCollectionItemProps;
}

export interface HorizontalCollectionItemProps {
  icon?: Widget;
  collectionName?: string;
  title?: string;
}

export type ProfileHeaderWidget =
  (typeof ProfileHeaderWidget)[keyof typeof ProfileHeaderWidget];

export const ProfileHeaderWidget = {
  ProfileHeader: "ProfileHeader",
} as const;

export interface ProfileHeader {
  widget: ProfileHeaderWidget;
  id: string;
}

export type ProfileMenuItemWidget =
  (typeof ProfileMenuItemWidget)[keyof typeof ProfileMenuItemWidget];

export const ProfileMenuItemWidget = {
  ProfileMenuItem: "ProfileMenuItem",
} as const;

export interface ProfileMenuItem {
  widget: ProfileMenuItemWidget;
  id: string;
  props: ProfileMenuItemProps;
  slots: ProfileMenuItemSlots;
}

export interface ProfileMenuItemSlots {
  icon: Widget;
  tag?: Widget;
}

export interface ProfileMenuItemProps {
  title: string;
  redirectLink?: RedirectLink;
}

export type ProfileFooterWidget =
  (typeof ProfileFooterWidget)[keyof typeof ProfileFooterWidget];

export const ProfileFooterWidget = {
  ProfileFooter: "ProfileFooter",
} as const;

export interface ProfileFooter {
  widget: ProfileFooterWidget;
  id: string;
  props: ProfileFooterProps;
  slots: ProfileFooterSlots;
}

export interface ProfileFooterSlots {
  logo: Widget;
}

export interface ProfileFooterProps {
  links: FooterLink[];
  disclaimer?: DataKey;
}

export interface FooterLink {
  text: string;
  redirectLink: RedirectLink;
}

export type TabularPageWidget =
  (typeof TabularPageWidget)[keyof typeof TabularPageWidget];

export const TabularPageWidget = {
  TabularPage: "TabularPage",
} as const;

export interface TabularPage {
  widget: TabularPageWidget;
  id: string;
  props: TabularPageProps;
  slots: TabularPageSlots;
}

export interface TabularPageSlots {
  header?: Widget;
}

export type TabularPagePropsViewEventProps = { [key: string]: unknown };

export interface TabularPageProps {
  selectedTabId?: string;
  tabs: TabItem[];
  viewEventProps?: TabularPagePropsViewEventProps;
}

export type RegularTabItemTabItem =
  (typeof RegularTabItemTabItem)[keyof typeof RegularTabItemTabItem];

export const RegularTabItemTabItem = {
  RegularTabItem: "RegularTabItem",
} as const;

export interface RegularTabItem {
  id: string;
  label: string;
  path: string;
  tabItem: RegularTabItemTabItem;
}

export type TaggedTabItemTabItem =
  (typeof TaggedTabItemTabItem)[keyof typeof TaggedTabItemTabItem];

export const TaggedTabItemTabItem = {
  TaggedTabItem: "TaggedTabItem",
} as const;

export type TaggedTabItemAnalyticsEventProps = { [key: string]: unknown };

export interface TaggedTabItem {
  id: string;
  label: string;
  tag: string;
  path: string;
  tabItem: TaggedTabItemTabItem;
  analyticsEventProps?: TaggedTabItemAnalyticsEventProps;
}

export type TranslucentTagWidget =
  (typeof TranslucentTagWidget)[keyof typeof TranslucentTagWidget];

export const TranslucentTagWidget = {
  TranslucentTag: "TranslucentTag",
} as const;

export interface TranslucentTag {
  widget: TranslucentTagWidget;
  id: string;
  props?: TranslucentTagProps;
  slots?: TranslucentTagSlots;
}

export interface TranslucentTagSlots {
  prefixIcon?: Widget;
}

export interface TranslucentTagProps {
  label?: DataKey;
  color?: string;
  hasShimmer?: boolean;
}

export type SolidTagWidget =
  (typeof SolidTagWidget)[keyof typeof SolidTagWidget];

export const SolidTagWidget = {
  SolidTag: "SolidTag",
} as const;

export interface SolidTag {
  widget: SolidTagWidget;
  id: string;
  props?: SolidTagProps;
  slots?: SolidTagSlots;
}

export interface SolidTagSlots {
  prefixIcon?: Widget;
}

export interface SolidTagProps {
  label?: DataKey;
  color?: string;
}

export type DefaultWidgetWidget =
  (typeof DefaultWidgetWidget)[keyof typeof DefaultWidgetWidget];

export const DefaultWidgetWidget = {
  DefaultWidget: "DefaultWidget",
} as const;

export interface DefaultWidget {
  widget: DefaultWidgetWidget;
  id: string;
}

export type DefaultTabItemTabItem =
  (typeof DefaultTabItemTabItem)[keyof typeof DefaultTabItemTabItem];

export const DefaultTabItemTabItem = {
  DefaultTabItem: "DefaultTabItem",
} as const;

export interface DefaultTabItem {
  id: string;
  tabItem: DefaultTabItemTabItem;
}

export type TrustMarkersFooterWidget =
  (typeof TrustMarkersFooterWidget)[keyof typeof TrustMarkersFooterWidget];

export const TrustMarkersFooterWidget = {
  TrustMarkersFooter: "TrustMarkersFooter",
} as const;

export interface TrustMarkersFooter {
  widget: TrustMarkersFooterWidget;
  id: string;
  props?: TrustMarkersFooterProps;
}

export interface TrustMarkersFooterProps {
  title?: string;
  subTitle?: string;
  subHeader?: string;
  items?: TrustMarkersFooterBankItem[];
  subFooter?: string;
  footerText?: string;
  footerTextPrefixIcon?: string;
  footerRedirect?: RedirectLink;
}

export interface TrustMarkersFooterBankItem {
  iconUrl?: string;
  redirect?: RedirectLink;
}

export type GetPageParams = {
  path: string;
  params?: string;
};

export type GetFlags200 = { [key: string]: boolean };

export type GetStoriesParams = {
  page: string;
};

export type GetFrameParams = {
  name: string;
};

export type GetNudgeParams = {
  pageUri: string;
};

export type GetAllNudgesParams = {
  pageUri: string;
};

export type GetRatingNudgeParams = {
  type?: string;
};

/**
 * @summary Get a page's widget tree
 */
export const getPage = (params: GetPageParams) => {
  return request<PageResponse>({ url: `/page`, method: "GET", params });
};

/**
 * @summary Get eligible feature flags for a user by on his allocated group
 */
export const getFlags = () => {
  return request<GetFlags200>({ url: `/feature-flags`, method: "GET" });
};

/**
 * @summary Get eligible stories by page
 */
export const getStories = (params: GetStoriesParams) => {
  return request<StoryResponse>({ url: `/stories`, method: "GET", params });
};

export const setStoryReaction = (
  storyReactionRequest: StoryReactionRequest,
) => {
  return request<void>({
    url: `/story-reaction`,
    method: "POST",
    headers: { "Content-Type": "application/json" },
    data: storyReactionRequest,
  });
};

/**
 * @summary Get the latest view information for a widget interaction
 */
export const getWidgetInteractionSummary = (widgetId: string) => {
  return request<WidgetInteractionResponse>({
    url: `/widgets/${widgetId}/interaction`,
    method: "GET",
  });
};

/**
 * @summary Record a new interaction of a widget
 */
export const recordWidgetInteraction = (widgetId: string) => {
  return request<void>({
    url: `/widgets/${widgetId}/interaction`,
    method: "POST",
  });
};

/**
 * @summary Get poll by ID
 */
export const getPoll = (pollId: string) => {
  return request<PollResponse>({ url: `/polls/${pollId}`, method: "GET" });
};

/**
 * @summary Submit an answer to a poll question
 */
export const submitPollQuestion = (
  pollId: string,
  questionId: string,
  pollQuestionSubmitRequest: PollQuestionSubmitRequest,
) => {
  return request<void>({
    url: `/polls/${pollId}/questions/${questionId}/answer`,
    method: "POST",
    headers: { "Content-Type": "application/json" },
    data: pollQuestionSubmitRequest,
  });
};

/**
 * @summary Add comment to a submitted an answer
 */
export const addPollComment = (
  pollId: string,
  questionId: string,
  pollCommentSubmitRequest: PollCommentSubmitRequest,
) => {
  return request<void>({
    url: `/polls/${pollId}/questions/${questionId}/comment`,
    method: "POST",
    headers: { "Content-Type": "application/json" },
    data: pollCommentSubmitRequest,
  });
};

/**
 * @summary Dismiss a poll
 */
export const dismissPoll = (pollId: string) => {
  return request<void>({ url: `/polls/${pollId}/dismiss`, method: "POST" });
};

/**
 * @summary Get unleash context for a user
 */
export const getUserContext = () => {
  return request<UserContextResponse>({ url: `/user-context`, method: "GET" });
};

/**
 * @summary Get eligible frame (widget) for user
 */
export const getFrame = (params: GetFrameParams) => {
  return request<FrameResponse>({ url: `/frame`, method: "GET", params });
};

/**
 * @summary Get nudges for a user
 */
export const getNudge = (params: GetNudgeParams) => {
  return request<NudgeResponse>({ url: `/nudges`, method: "GET", params });
};

/**
 * @summary Get all nudges for a user
 */
export const getAllNudges = (params: GetAllNudgesParams) => {
  return request<Nudges>({ url: `/nudges/all`, method: "GET", params });
};

/**
 * @summary Post action on a nudge
 */
export const updateNudgeAction = (nudgeAction: NudgeAction) => {
  return request<void>({
    url: `/nudges/action`,
    method: "POST",
    headers: { "Content-Type": "application/json" },
    data: nudgeAction,
  });
};

/**
 * @summary Get rating nudge for a user
 */
export const getRatingNudge = (params?: GetRatingNudgeParams) => {
  return request<RatingNudgeResponse>({
    url: `/rating-nudge`,
    method: "GET",
    params,
  });
};

/**
 * @summary Dismiss rating nudge
 */
export const dismissRatingNudge = () => {
  return request<void>({ url: `/rating-nudge`, method: "DELETE" });
};

/**
 * @summary Post app rating
 */
export const postRating = (postRatingRequest: PostRatingRequest) => {
  return request<PostRatingResponse>({
    url: `/rating-nudge`,
    method: "POST",
    headers: { "Content-Type": "application/json" },
    data: postRatingRequest,
  });
};

export type GetPageResult = NonNullable<Awaited<ReturnType<typeof getPage>>>;
export type GetFlagsResult = NonNullable<Awaited<ReturnType<typeof getFlags>>>;
export type GetStoriesResult = NonNullable<
  Awaited<ReturnType<typeof getStories>>
>;
export type SetStoryReactionResult = NonNullable<
  Awaited<ReturnType<typeof setStoryReaction>>
>;
export type GetWidgetInteractionSummaryResult = NonNullable<
  Awaited<ReturnType<typeof getWidgetInteractionSummary>>
>;
export type RecordWidgetInteractionResult = NonNullable<
  Awaited<ReturnType<typeof recordWidgetInteraction>>
>;
export type GetPollResult = NonNullable<Awaited<ReturnType<typeof getPoll>>>;
export type SubmitPollQuestionResult = NonNullable<
  Awaited<ReturnType<typeof submitPollQuestion>>
>;
export type AddPollCommentResult = NonNullable<
  Awaited<ReturnType<typeof addPollComment>>
>;
export type DismissPollResult = NonNullable<
  Awaited<ReturnType<typeof dismissPoll>>
>;
export type GetUserContextResult = NonNullable<
  Awaited<ReturnType<typeof getUserContext>>
>;
export type GetFrameResult = NonNullable<Awaited<ReturnType<typeof getFrame>>>;
export type GetNudgeResult = NonNullable<Awaited<ReturnType<typeof getNudge>>>;
export type GetAllNudgesResult = NonNullable<
  Awaited<ReturnType<typeof getAllNudges>>
>;
export type UpdateNudgeActionResult = NonNullable<
  Awaited<ReturnType<typeof updateNudgeAction>>
>;
export type GetRatingNudgeResult = NonNullable<
  Awaited<ReturnType<typeof getRatingNudge>>
>;
export type DismissRatingNudgeResult = NonNullable<
  Awaited<ReturnType<typeof dismissRatingNudge>>
>;
export type PostRatingResult = NonNullable<
  Awaited<ReturnType<typeof postRating>>
>;
