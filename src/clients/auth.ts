import { create, fromBinary, toBinary } from "@bufbuild/protobuf";
import <PERSON><PERSON> from "js-cookie";
import {
  RefreshTokenRequestSchema,
  RefreshTokenResponseSchema,
} from "./gen/platform/public/models/identity/Auth_pb";
import { queryClient } from "@/queries/client";
import * as native from "@/utils/native-integration";

export const AUTH_COOKIE_NAME = "sm_access_token";
export const AUTH_REFRESH_COOKIE_NAME = "sm_refresh_token";

/**
 * Get the authentication token
 */
export async function getAuthToken() {
  let token: string | null | undefined = Cookie.get(AUTH_COOKIE_NAME);
  if (!token) {
    token = await native.authentication.getToken("access_token");
    if (token) {
      await setAuthToken(token);
    }
  }
  return token;
}

/**
 * Get the refresh token
 */
export async function getRefreshToken() {
  let token: string | null | undefined = Cookie.get(AUTH_REFRESH_COOKIE_NAME);
  if (!token) {
    token = await native.authentication.getToken("refresh_token");
    if (token) {
      await setRefreshToken(token);
    }
  }
  return token;
}

/**
 * Update the authentication token
 * @param token The new authentication token
 */
export async function setAuthToken(token: string): Promise<void> {
  Cookie.set(AUTH_COOKIE_NAME, token, {
    expires: 30 * 9,
    httpOnly: false,
  });
}

/**
 * Update the refresh token
 * @param token The new refresh token
 */
export async function setRefreshToken(token: string): Promise<void> {
  Cookie.set(AUTH_REFRESH_COOKIE_NAME, token, {
    expires: 30 * 9,
    httpOnly: false,
  });
}

export async function logout() {
  Cookie.remove(AUTH_COOKIE_NAME);
  Cookie.remove(AUTH_REFRESH_COOKIE_NAME);
  queryClient.clear();
  localStorage.clear();
  window.location.href = "/";
}

export async function refreshAuthToken() {
  const token = await getRefreshToken();
  if (!token) {
    return false;
  }
  const headers: Record<string, string> = {
    Accept: "application/x-protobuf",
    "Content-Type": "application/x-protobuf",
    "X-App-Version-Code": "855",
    "X-Client-Type": "WEB",
  };
  const url = `${import.meta.env.VITE_IDENTITY_BASE_URL}/v1/auth/refresh-token`;
  const fetchResponse = await fetch(url, {
    method: "POST",
    headers: headers,
    body: toBinary(
      RefreshTokenRequestSchema,
      create(RefreshTokenRequestSchema, {
        token,
      })
    ),
  });
  if (fetchResponse.ok) {
    const arrayBuffer = await fetchResponse.arrayBuffer();
    const refreshResponse = fromBinary(
      RefreshTokenResponseSchema,
      new Uint8Array(arrayBuffer)
    );
    const authToken = refreshResponse.authenticationResult?.token;
    if (authToken) {
      await setAuthToken(authToken);
      return true;
    }
  }
  return false;
}
