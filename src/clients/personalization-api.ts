import { HttpCallException } from "@/exceptions/http-call-exception";
import { getAuthToken, refreshAuthToken } from "./auth";
import { UnauthenticatedException } from "@/exceptions/unauthenticated";
import { getClientOS, getClientPlatform } from "./http-context";

export const request = async <T>({
  url,
  method,
  params,
  data,
  headers = {},
}: {
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  params?: Record<string, string>;
  data?: unknown;
  headers?: Record<string, string>;
  responseType?: string;
}): Promise<T> => {
  headers["X-Client-Type"] = "WEB";
  headers["X-Platform-Type"] = getClientPlatform();
  headers["X-App-Version-Code"] = "855";
  headers["X-Client-OS"] = getClientOS();
  const nativeAuthToken = await getAuthToken();
  if (nativeAuthToken) {
    headers["Authorization"] = `Bearer ${nativeAuthToken}`;
  }
  const fetchOptions = {
    method,
    headers,
    ...(data ? { body: JSON.stringify(data) } : {}),
  };
  const searchParams = new URLSearchParams(params);
  const queryString = searchParams.toString();
  const fetchUrl = `${import.meta.env.VITE_PERSONALIZATION_BASE_URL}${url}${queryString ? `?${queryString}` : ""}`;
  const fetchRequest = new Request(fetchUrl, fetchOptions);
  const response = await fetch(fetchRequest);
  if (response.status === 401) {
    if (await refreshAuthToken()) {
      return request({ url, method, params, data, headers });
    }
    throw new UnauthenticatedException();
  }
  if (!response.ok) {
    throw new HttpCallException(response, fetchRequest);
  }
  return response.json() as T;
};
