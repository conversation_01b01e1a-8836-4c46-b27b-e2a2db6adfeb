import parser from "my-ua-parser";

export function fromTurboNative() {
  return navigator.userAgent.includes("Turbo Native");
}

export function getClientPlatform() {
  return fromTurboNative() ? "PLATFORM_FLUTTER" : "PLATFORM_WEB";
}

export function getDevice() {
  return parser(navigator.userAgent);
}

export function getClientOS() {
  const userAgent = navigator.userAgent;
  const device = parser(userAgent);
  return device.os.name || "unknown";
}
