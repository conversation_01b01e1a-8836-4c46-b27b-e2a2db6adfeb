import { HttpCallException } from "@/exceptions/http-call-exception";
import {
  type DescMessage,
  fromBinary,
  type MessageShape,
} from "@bufbuild/protobuf";
import { getAuthToken, refreshAuthToken } from "./auth";
import { UnauthenticatedException } from "@/exceptions/unauthenticated";
import { getClientOS, getClientPlatform } from "./http-context";

type RequestOptions<T extends DescMessage> = {
  url: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  params?: Record<string, string>;
  data?: Uint8Array;
  headers?: Record<string, string>;
  responseSchema?: T;
};
export function request<T extends DescMessage>(
  options: RequestOptions<T> & {
    responseSchema: NonNullable<RequestOptions<T>["responseSchema"]>;
  }
): Promise<MessageShape<T>>;
export function request(
  options: Omit<RequestOptions<never>, "responseSchema">
): Promise<void>;
export async function request<T extends DescMessage>({
  url,
  method,
  params,
  data,
  headers = {},
  responseSchema,
}: RequestOptions<T>): Promise<MessageShape<T> | void> {
  headers.Accept = "application/x-protobuf";
  headers["Content-Type"] = "application/x-protobuf";
  headers["X-Platform-Type"] = getClientPlatform();
  headers["X-Client-Type"] = "WEB";
  headers["X-App-Version-Code"] = "855";
  headers["X-Client-OS"] = getClientOS();
  const nativeAuthToken = await getAuthToken();
  if (nativeAuthToken) {
    headers["Authorization"] = `Bearer ${nativeAuthToken}`;
  }
  const searchParams = new URLSearchParams(params);
  const queryString = searchParams.toString();
  const fetchUrl = `${import.meta.env.VITE_BROKING_BASE_URL}${url}${queryString ? `?${queryString}` : ""}`;
  const fetchOptions = {
    method,
    headers: headers,
    ...(data ? { body: data } : {}),
  };
  const fetchRequest = new Request(fetchUrl, fetchOptions);
  const response = await fetch(fetchRequest);
  if (!responseSchema) {
    return;
  }
  if (response.status === 401) {
    if (await refreshAuthToken()) {
      return request({ url, method, params, data, headers, responseSchema });
    }
    throw new UnauthenticatedException();
  }
  if (!response.ok) {
    throw new HttpCallException(response, fetchRequest);
  }
  const arrayBuffer = await response.arrayBuffer();
  return fromBinary(responseSchema, new Uint8Array(arrayBuffer));
}
