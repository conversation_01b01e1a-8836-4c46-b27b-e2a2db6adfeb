import * as Sentry from "@sentry/react";
import { RouterProvider } from "react-router";
import { router } from "./router";
import { PersistQueryClientProvider } from "@tanstack/react-query-persist-client";
import { persister, queryClient } from "./queries/client";
import { HelmetProvider } from "@dr.pogodin/react-helmet";

const App = Sentry.withProfiler(function AppWithProvider() {
  return (
    <HelmetProvider>
      <PersistQueryClientProvider
        client={queryClient}
        persistOptions={{ persister }}
      >
        <RouterProvider router={router} />
      </PersistQueryClientProvider>
    </HelmetProvider>
  );
});

export default App;
