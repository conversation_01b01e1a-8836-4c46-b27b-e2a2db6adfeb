declare global {
  // Digio SDK types
  interface DigioConfig {
    environment: "production" | "sandbox";
    callback: (response: DigioResponse) => void;
    logo?: string;
    theme?: {
      primaryColor?: string;
      secondaryColor?: string;
    };
    is_iframe?: boolean;
    method?: "otp" | "biometric";
  }

  interface DigioInstance {
    init(): void;
    submit(
      documentId: string,
      customerIdentifier: string,
      accessToken: string
    ): void;
  }

  type DigioResponse =
    | { error_code: string }
    | { error_code?: string; digio_doc_id: string };

  // HyperVerge SDK types
  interface HyperKycConfigInstance {
    inputs: {
      input_img: string;
    };
  }

  interface HyperKycResult {
    status:
      | "auto_approved"
      | "needs_review"
      | "pending"
      | "user_cancelled"
      | "error"
      | "auto_declined";
    transactionId: string;
    [key: string]: string | number | boolean | undefined;
  }

  // Google Analytics gtag types
  type GtagCommand = "config" | "event" | "js" | "set" | "get";

  type GtagConfigParams = {
    page_title?: string;
    page_location?: string;
    page_path?: string;
    send_page_view?: boolean;
    custom_map?: Record<string, string>;
    [key: string]: unknown;
  };

  type GtagEventParams = {
    event_category?: string;
    event_label?: string;
    value?: number;
    currency?: string;
    transaction_id?: string;
    [key: string]: unknown;
  };

  interface Window {
    prerenderReady: boolean;
    Digio?: new (config: DigioConfig) => DigioInstance;
    HyperKYCModule?: {
      launch: (
        config: HyperKycConfigInstance,
        handler: (result: HyperKycResult) => void
      ) => void;
    };
    HyperKycConfig?: new (
      accessToken: string,
      workflowId: string,
      transactionId: string
    ) => HyperKycConfigInstance;

    // Google Analytics gtag function
    gtag?: {
      (command: "config", targetId: string, config?: GtagConfigParams): void;
      (
        command: "event",
        eventName: string,
        eventParams?: GtagEventParams
      ): void;
      (command: "js", date: Date): void;
      (command: "set", config: Record<string, UnknownAction>): void;
      (
        command: "get",
        targetId: string,
        fieldName: string,
        callback?: (value: unknown) => void
      ): void;
    };

    // Google Analytics dataLayer
    dataLayer?: unknwon[];

    // Flutter WebView InAppWebView types
    flutter_inappwebview?: FlutterInAppWebView;
  }

  interface FlutterInAppWebView {
    callHandler(
      handlerName: "share",
      text: string,
      imageUrl?: string
    ): Promise<void>;
    callHandler(
      handlerName: "navigate",
      presentation: FlutterPresentationType,
      path?: string,
      pathType?: FlutterPathType
    ): Promise<void>;
    callHandler(
      handlerName: "trackAnalyticsEvent",
      eventName: string,
      eventProperties?: Record<string, unknown>
    ): Promise<void>;
    callHandler(handlerName: "getAccessToken"): Promise<string>;
    callHandler(handlerName: "getRefreshToken"): Promise<string>;
    callHandler(
      handlerName: "initiateDigio",
      documentId: string,
      customerIdentifier: string,
      accessToken: string
    ): Promise<FlutterDigioResponse>;
    callHandler(
      handlerName: "initiateHyperVerge",
      workflowId: string,
      transactionId: string,
      accessToken: string,
      inputImg: string
    ): Promise<FlutterHyperVergeResponse>;
    callHandler(
      handlerName: "initiateCashfree",
      orderId: string,
      paymentSessionId: string
    ): Promise<FlutterCashfreeResponse>;
  }

  type FlutterHandlerName =
    | "share"
    | "navigate"
    | "trackAnalyticsEvent"
    | "getAccessToken"
    | "getRefreshToken"
    | "initiateDigio"
    | "initiateHyperVerge"
    | "initiateCashfree";

  type FlutterPresentationType = "push" | "pop" | "replace" | "reset";

  type FlutterPathType =
    | "INAPP"
    | "INAPP_DASHBOARD"
    | "BOTTOMSHEET"
    | "UPSWING"
    | "EXTERNAL"
    | "DISMISS"
    | "HOTWIRE"
    | "UNKNOWN";

  type FlutterShareResponse = void;
  type FlutterNavigateResponse = void;
  type FlutterTrackAnalyticsResponse = void;
  type FlutterTokenResponse = string;

  type FlutterDigioResponse = {
    documentId: string;
    screen: string;
    message: string;
    errorCode: number;
    code: number;
    step: string;
    permissions: string;
  };

  type FlutterHyperVergeResponse = {
    errorCode: number;
    details: string;
    errorMessage: string;
    latestModule: string;
    name: string;
    transactionId: string;
    rawResponse: string;
  };

  type FlutterCashfreeResponse = {
    orderId: string;
  };

  type FlutterCashfreeError = {
    code: string;
    message: string;
    status: string;
    type: string;
  };
}

export {};
