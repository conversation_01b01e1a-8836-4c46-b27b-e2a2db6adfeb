import * as Sentry from "@sentry/react";
import { createBrowserRouter } from "react-router";
import Home, { loader as homeLoader } from "./pages/home";
import AppHomePage, { loader as appHomepageLoader } from "./pages/app";
import ErrorBoundary from "./components/functional/error-boundary";
import WorkflowLayout from "./components/layouts/workflow";
import MobileNumberPage from "./pages/auth/mobile-number";
import MobileOtpPage from "./pages/auth/mobile-otp";
import {
  BondDetailsPage,
  loader as bondDetailsLoader,
} from "./pages/bonds/details";
import Root from "./components/layouts/root";
import DynamicPage, { loader as dynamicPageLoader } from "./pages/dynamic";
import DynamicReferralPage, {
  loader as dynamicReferralLoader,
} from "./pages/dynamic/referral";
import CollectionPage, {
  loader as collectionPageLoader,
} from "./pages/collections/show";
import BondCalculatorPage, {
  loader as bondCalculatorLoader,
} from "./pages/bonds/calculator";
import AboutUsPage from "./pages/about-us";
import ContactUsPage from "./pages/contact-us";
import InvestmentsPage, {
  loader as investmentsPageLoader,
} from "./pages/investments";
import Form15GPage from "./pages/form15g";
import CheckoutPage, {
  loader as checkoutPageLoader,
} from "./pages/checkout/[id]";
import CheckoutPayPage from "./pages/checkout/[id]/pay";
import ProfilePage, {
  loader as profilePageLoader,
} from "./pages/profile/index";
import ProfileBankPage, {
  loader as profileBankPageLoader,
} from "./pages/profile/bank";
import ProfileDematPage, {
  loader as profileDematPageLoader,
} from "./pages/profile/demat";
import ProfileKycPage, {
  loader as profileKycPageLoader,
} from "./pages/profile/kyc";
import ProfileReportsPage, {
  loader as profileReportsPageLoader,
} from "./pages/profile/reports";
import FaqsPage from "./pages/faqs";
import DownloadPage from "./pages/download";
import BondsReturnSchedulePage from "./pages/bonds/returns-schedule";
import ContactPage from "./pages/compliance/contact";
import ReferralConditionsPage from "./pages/compliance/referral-tnc";
import FormsPage from "./pages/compliance/forms";
import InvestorCharterForDepositoryParticipantPage from "./pages/compliance/investor-charter-for-depository-participant";
import PrivacyPolicyPage from "./pages/compliance/privacy-policy";
import TermsAndConditionsPage from "./pages/compliance/term-and-condition";
import GovernancePage from "./pages/compliance/governance";
import BottomSheetPage from "./pages/sheet";
import CkycInstruction from "./pages/compliance/ckyc-instructions";
import CompliancePage from "./pages/compliance";
import WorkflowInitiatePage, {
  loader as workflowInitiateLoader,
} from "./pages/workflow";
import WorkflowKycPage from "./pages/workflow/kyc";
import WorkflowSelfiePage from "./pages/workflow/selfie";
import WorkflowBasicDetailsPage from "./pages/workflow/basic-details";
import WorkflowEsignPage from "./pages/workflow/esign";
import WorkflowWetsignPage from "./pages/workflow/wetsign";
import WorkflowStatusPage from "./pages/workflow/status";
import WorkflowParentDetailsPage from "./pages/workflow/parent-details";
import WorkflowDematPage from "./pages/workflow/demat";
import WorkflowAadharPanMatchPage, {
  loader as workflowAadharPanMatchLoader,
} from "./pages/workflow/aadhar-pan-match";
import WorkflowBankPdPage from "./pages/workflow/bank-pd";
import WorkflowBankRpdPage from "./pages/workflow/bank-rpd";
import WorkflowNomineePage from "./pages/workflow/nominee";
import WorkflowPanPage from "./pages/workflow/pan";
import CatchallPage from "./pages/catch-all";
import FindByBrokerPage from "./pages/workflow/demat/find-by-broker";
import RpdStatusPage from "./pages/workflow/bank-rpd/status";
import EmailPage from "./pages/onboarding/email";
import EmailOtpPage from "./pages/onboarding/email-otp";
import NamePage from "./pages/onboarding/name";
import FilterSortPage from "./pages/sort-filter";

const sentryCreateBrowserRouter =
  Sentry.wrapCreateBrowserRouterV7(createBrowserRouter);

export const router = sentryCreateBrowserRouter([
  {
    path: "/",
    ErrorBoundary,
    Component: Root,
    children: [
      {
        index: true,
        Component: Home,
        loader: homeLoader,
      },
      {
        path: "app",
        Component: AppHomePage,
        loader: appHomepageLoader,
      },
      {
        path: "bonds/:issuer/:bond_id",
        Component: BondDetailsPage,
        loader: bondDetailsLoader,
      },
      {
        path: "bonds/:issuer/:bond_id/calculator",
        Component: BondCalculatorPage,
        loader: bondCalculatorLoader,
      },
      {
        path: "bonds/:issuer/:bond_id/returns-schedule",
        Component: BondsReturnSchedulePage,
      },
      {
        path: "collections/:collection",
        Component: CollectionPage,
        loader: collectionPageLoader,
      },
      {
        path: "dynamic/referral",
        Component: DynamicReferralPage,
        loader: dynamicReferralLoader,
      },
      {
        path: "dynamic/:path",
        Component: DynamicPage,
        loader: dynamicPageLoader,
      },
      {
        path: "about-us",
        Component: AboutUsPage,
      },
      {
        path: "contact-us",
        Component: ContactUsPage,
      },
      {
        path: "faqs",
        Component: FaqsPage,
      },
      {
        path: "download",
        Component: DownloadPage,
      },
      {
        path: "faqs",
        Component: FaqsPage,
      },
      {
        path: "download",
        Component: DownloadPage,
      },
      {
        path: "investments",
        Component: InvestmentsPage,
        loader: investmentsPageLoader,
      },
      {
        path: "how-to-fill-form15g",
        Component: Form15GPage,
      },
      {
        path: "profile",
        children: [
          {
            index: true,
            Component: ProfilePage,
            loader: profilePageLoader,
          },
          {
            path: "bank",
            Component: ProfileBankPage,
            loader: profileBankPageLoader,
          },
          {
            path: "demat",
            Component: ProfileDematPage,
            loader: profileDematPageLoader,
          },
          {
            path: "kyc",
            Component: ProfileKycPage,
            loader: profileKycPageLoader,
          },
          {
            path: "reports",
            Component: ProfileReportsPage,
            loader: profileReportsPageLoader,
          },
        ],
      },
      {
        path: "checkout",
        Component: WorkflowLayout,
        children: [
          {
            path: ":id",
            Component: CheckoutPage,
            loader: checkoutPageLoader,
          },
          {
            path: ":id/pay",
            Component: CheckoutPayPage,
          },
        ],
      },
      {
        path: "authentication",
        Component: WorkflowLayout,
        children: [
          {
            path: "mobile-number",
            Component: MobileNumberPage,
          },
          {
            path: "mobile-otp",
            Component: MobileOtpPage,
          },
          {
            path: "email",
            Component: EmailPage,
          },
          {
            path: "email-otp",
            Component: EmailOtpPage,
          },
          {
            path: "name",
            Component: NamePage,
          },
        ],
      },
      {
        path: "workflow/:slug",
        Component: WorkflowLayout,
        children: [
          {
            index: true,
            Component: WorkflowInitiatePage,
            loader: workflowInitiateLoader,
          },
          {
            path: "kyc",
            Component: WorkflowKycPage,
          },
          {
            path: "selfie",
            Component: WorkflowSelfiePage,
          },
          {
            path: "basic-details",
            Component: WorkflowBasicDetailsPage,
          },
          {
            path: "parent-details",
            Component: WorkflowParentDetailsPage,
          },
          {
            path: "esign",
            Component: WorkflowEsignPage,
          },
          {
            path: "wetsign",
            Component: WorkflowWetsignPage,
          },
          {
            path: "status",
            Component: WorkflowStatusPage,
          },
          {
            path: "demat",
            Component: WorkflowDematPage,
          },
          {
            path: "aadhar-pan-match",
            Component: WorkflowAadharPanMatchPage,
            loader: workflowAadharPanMatchLoader,
          },
          {
            path: "bank-pd",
            Component: WorkflowBankPdPage,
          },
          {
            path: "bank-rpd",
            Component: WorkflowBankRpdPage,
          },
          {
            path: "bank-rpd/:refId",
            Component: RpdStatusPage,
          },
          {
            path: "nominee",
            Component: WorkflowNomineePage,
          },
          {
            path: "pan",
            Component: WorkflowPanPage,
          },
          {
            path: "demat/find-by-broker",
            Component: FindByBrokerPage,
          },
        ],
      },
      {
        path: "compliance",
        Component: CompliancePage,
        children: [
          {
            path: "contact",
            Component: ContactPage,
          },
          {
            path: "referral-tnc",
            Component: ReferralConditionsPage,
          },
          {
            path: "forms",
            Component: FormsPage,
          },
          {
            path: "investor-charter-for-depositroy-participant",
            Component: InvestorCharterForDepositoryParticipantPage,
          },
          {
            path: "privacy-policy",
            Component: PrivacyPolicyPage,
          },
          {
            path: "term-and-condition",
            Component: TermsAndConditionsPage,
          },
          {
            path: "governance",
            Component: GovernancePage,
          },
          {
            path: "ckyc-instruction",
            Component: CkycInstruction,
          },
        ],
      },
      {
        path: "sheets/:id",
        Component: BottomSheetPage,
      },
      {
        path: "filter-sort",
        Component: FilterSortPage,
      },
      {
        path: "*",
        Component: CatchallPage,
      },
    ],
  },
]);
