# Testing Guide

This document provides guidelines for writing tests in this project, specifically focusing on Playwright E2E tests and API mocking patterns.

## Project Structure

```
tests/
├── e2e/                          # End-to-end tests
│   ├── auth/                     # Authentication-related tests
│   │   ├── login.spec.ts         # Login test
│   │   └── login.mocks.ts        # Login-specific mocks
│   ├── bonds/                    # Bond-related tests
│   │   ├── calculator.spec.ts    # Calculator test
│   │   └── calculator.mocks.ts   # Calculator-specific mocks
│   ├── onboarding/               # Onboarding tests
│   │   ├── *.spec.ts             # Individual onboarding tests
│   │   └── *.mocks.ts            # Test-specific mock files
│   ├── fixtures/                 # Test fixtures and utilities
│   ├── storage-states/           # StorageState files for SDK testing
│   └── ...                       # Other test categories
├── stubs/                        # SDK stubs for testing
│   └── sdk-loader.ts             # SDK loader stub
├── utils/                        # Shared test utilities
│   └── index.ts                  # DeepPartial type and mergeDeep function
└── README.md                     # This file
```

## API Mocking Patterns

### Core Principles

1. **One mock file per spec file**: Each test file has its own dedicated mock file (e.g., `login.spec.ts` uses `login.mocks.ts`)
2. **One mock function per endpoint per test**: Create specific mock functions for each endpoint needed by each test
3. **Page-only parameter**: Mock functions must only accept the `page` argument - no overrides or callback parameters
4. **Internal request validation**: Validate requests inside the mock function using expect assertions
5. **Return response data**: Mock functions should return the expected response data so tests can assert what's displayed on the UI
6. **Use faker for realistic data**: Generate fake data using faker instead of hardcoded values
7. **Find schemas in src/queries**: Examine the API call functions in `src/queries` to find the correct request and response schemas
8. **Protobuf communication**: We use protobuf for API communication, so use `fromBinary`, `toBinary`, and `create` for request/response handling
9. **NEVER mock authentication**: Authentication APIs (`/auth/*` endpoints) should never be mocked. Use real login flows or proper storage state with valid tokens for authenticated tests

### Creating API Mocks

#### Basic Mock Pattern

```typescript
import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  YourRequestSchema,
  YourResponseSchema,
} from "@/clients/gen/platform/public/models/your/Schema_pb";
import { expect } from "@playwright/test";

// Test: should load page and display user data
export function mockLoadUserProfile(page: Page) {
  const responseData: MessageInitShape<typeof YourResponseSchema> = {
    data: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      mobile: faker.string.numeric(10),
    },
    profileData: {
      dob: faker.date.past().toISOString().split("T")[0],
    },
  };

  page.route("*/**/v1/user/profile", (route) => {
    // Validate the request if needed
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(YourRequestSchema, requestBuffer);
      const requestJson = toJson(YourRequestSchema, requestData) as Record<
        string,
        unknown
      >;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        userId: expect.any(String),
      });
    }

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(YourResponseSchema, create(YourResponseSchema, responseData))
      ),
    });
  });

  return responseData;
}
```

#### Mock with Polling Support

For features that use polling, mock functions may accept a `successAfter` option to control when the polling succeeds:

```typescript
// Test: should complete flow successfully after polling
export function mockEsignPollingFlow(page: Page, successAfter = 3) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  const statusResponse = {
    status: "completed",
    documentId: faker.string.uuid(),
    signedUrl: faker.internet.url(),
  };

  let statusCallCount = 0;

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();

    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (requestData.stepName === StepName.ESIGN) {
          const step = requestData.result.value.step;

          if (step === EsignStep.GENERATE_TOKEN) {
            // Validate token generation request
            expect(requestData.result.value).toMatchObject({
              step: EsignStep.GENERATE_TOKEN,
            });

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, {
                    result: {
                      case: "esignResponse",
                      value: { credential: credentialResponse },
                    },
                  })
                )
              ),
            });
            return;
          } else if (step === EsignStep.STATUS) {
            statusCallCount++;

            // Validate status request
            expect(requestData.result.value).toMatchObject({
              step: EsignStep.STATUS,
              credentialId: credentialResponse.id,
            });

            // Return pending response until successAfter calls, then success
            const shouldSucceed = statusCallCount >= successAfter;
            const response = shouldSucceed
              ? statusResponse
              : { status: "in_progress" };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, {
                    result: {
                      case: "esignResponse",
                      value: { status: response },
                    },
                  })
                )
              ),
            });
            return;
          }
        }
      }
    }

    route.continue();
  });

  return {
    credentialResponse,
    statusResponse,
    getStatusCallCount: () => statusCallCount,
  };
}
```

**Usage in tests**:

```typescript
test("should complete flow after 3 polling attempts", async ({ page }) => {
  const mockData = mockEsignPollingFlow(page, 3); // Success after 3 calls

  await page.goto("/esign");
  await page.getByRole("button", { name: "Start eSign" }).click();

  // Should eventually succeed
  await expect(page.getByText("Document signed successfully")).toBeVisible({
    timeout: 10000,
  });
  expect(mockData.getStatusCallCount()).toBe(3);
});

test("should timeout when polling limit exceeded", async ({ page }) => {
  const mockData = mockEsignPollingFlow(page, 15); // Success after 15 calls (higher than limit)

  await page.goto("/esign");
  await page.getByRole("button", { name: "Start eSign" }).click();

  // Should show timeout error
  await expect(page.getByText("Process failed")).toBeVisible({
    timeout: 15000,
  });
});
```

````

#### Multiple Different Endpoints

When a test needs to mock multiple different endpoints, create separate mock functions for each endpoint:

```typescript
// Test: should load user profile successfully
export function mockUserProfile(page: Page) {
  const responseData = {
    data: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      mobile: faker.string.numeric(10),
    },
    profileData: {
      dob: faker.date.past().toISOString().split("T")[0],
    },
  };

  page.route("*/**/v1/user/profile", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

// Test: should load onboarding state successfully
export function mockOnboardingState(page: Page) {
  const responseData = {
    next: 4,
    currentStep: "BANK_PD",
    isCompleted: false,
  };

  page.route("*/**/v1/onboarding/state", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateResponseSchema,
          create(OnboardingStateResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

// Test: should load bond details successfully
export function mockBondDetails(page: Page) {
  const responseData = {
    bondDetails: {
      id: faker.string.uuid(),
      bondName: faker.company.name() + " Bond",
      xirr: faker.number.float({ min: 6, max: 12, fractionDigits: 2 }),
      pricePerBond: faker.number.float({ min: 1000, max: 10000, fractionDigits: 2 }),
    },
  };

  page.route("*/**/v1/bond-details/*", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          BondDetailsResponseSchema,
          create(BondDetailsResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}
````

**Usage in tests**: If a test needs multiple endpoints, call each mock function separately:

```typescript
test("should display page with user data and bond details", async ({
  page,
}) => {
  // Mock each endpoint separately
  const userData = mockUserProfile(page);
  const bondData = mockBondDetails(page);

  await page.goto("/bonds/123");

  // Assert using returned data
  await expect(page.getByText(userData.data.firstName)).toBeVisible();
  await expect(page.getByText(bondData.bondDetails.bondName)).toBeVisible();
});
```

### Key Patterns

1. **File Organization**: Each test file has its own dedicated mock file (e.g., `login.spec.ts` uses `login.mocks.ts`)
2. **Function Naming**: Name mock functions descriptively based on the test they support (e.g., `mockLoadUserProfile`, `mockPanCompleteFlow`)
3. **Request Validation**: Use `expect` assertions inside mock functions to validate request content
4. **Response Data Return**: Return the mock response data so tests can assert what's displayed on the UI
5. **Faker Usage**: Use faker to generate realistic fake data instead of hardcoded values
6. **Schema Discovery**: Find request and response schemas by examining the corresponding functions in `src/queries`
7. **Call Count Tracking**: Use closure variables to track polling calls and return getter functions for assertions
8. **Route Continuation**: Call `route.continue()` for unhandled requests to allow other mocks to handle them

### Finding Request and Response Schemas

To create proper mocks, examine the API call functions in `src/queries` to understand:

1. **Endpoint URL**: The URL pattern used in the `request()` call
2. **Request Schema**: The schema used with `toBinary()` for the request data
3. **Response Schema**: The schema specified in `responseSchema` parameter
4. **Request Structure**: The data structure passed to `create()` for the request

Example from `src/queries/auth.ts`:

```typescript
// This shows the endpoint is "/v3/auth/initiate"
// Request uses InitiateAuthRequestSchema
// Response uses InitiateAuthResponseSchema
export async function initiateAuth(mobileNumber: string, encryptedMobile: string, aesKey: string) {
  const response = await request({
    url: "/v3/auth/initiate",
    method: "POST",
    data: toBinary(InitiateAuthRequestSchema, create(InitiateAuthRequestSchema, { ... })),
    responseSchema: InitiateAuthResponseSchema,
  });
}
```

## Writing Tests

### Basic Test Structure

```typescript
import { test, expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { mockYourEndpoint } from "./your-test.mocks";

test.describe("Feature Name", () => {
  test("should do something", async ({ page }) => {
    // Setup API mocks
    mockYourEndpoint(page, { status: "success" });

    // Navigate and interact
    await page.goto("/your-page");
    await page.getByRole("button", { name: "Click me" }).click();

    // Assertions
    await expect(page).toHaveURL("/expected-url");
  });
});
```

### Best Practices

1. **Organize by feature**: Group tests by feature/page in separate directories
2. **Use descriptive names**: Test names should clearly describe what is being tested
3. **Mock at the right level**: Mock API responses, not internal functions
4. **Keep tests independent**: Each test should be able to run in isolation
5. **Use realistic data**: Prefer faker-generated data over hardcoded values
6. **Test user journeys**: Focus on complete user workflows rather than isolated interactions
7. **Separate concerns**: Have one describe block for happy flow, separate blocks for errors/edge cases

### Testing UI States

Always test loading and error states to ensure proper user feedback during async operations.

#### Component State Architecture

Many features use a three-component architecture:

1. **Initial Component**: Shows form/CTA before user action
2. **Loading Component**: Shows spinner and progress message
3. **Error Component**: Shows error message with retry option

#### Complete State Transition Testing

Test the full flow from initial → loading → success/error states:

```typescript
test("should complete full flow with UI state transitions and navigate on success", async ({
  page,
}) => {
  let statusCallCount = 0;

  // Mock API with polling that succeeds after specific number of calls
  // Use successOnCall to control when polling succeeds for testing timing
  mockFeatureWithPolling(
    page,
    {
      responseData: { status: "approved" },
      successOnCall: 3, // Will succeed on the 3rd polling call
    },
    (requestJson) => {
      // Track polling calls to verify expected behavior
      if (requestJson.isStatusRequest) {
        statusCallCount++;
      }
    }
  );

  await page.goto("/feature-page");

  // Verify initial state UI elements
  await expect(page.getByText("Initial State Title")).toBeVisible();
  await expect(
    page.getByRole("button", { name: "Start Process" })
  ).toBeVisible();

  // Verify loading/error states are not visible initially
  await expect(page.getByText("Processing...")).not.toBeVisible();
  await expect(page.getByText("Process failed")).not.toBeVisible();

  // Click button and verify UI transitions
  await page.getByRole("button", { name: "Start Process" }).click();

  // Verify loading state UI
  await expect(page.getByText("Processing...")).toBeVisible();
  await expect(
    page.getByText("Please wait while we process your request")
  ).toBeVisible();

  // Verify spinner is present during loading
  await expect(page.locator("[data-testid='loading-spinner']")).toBeVisible();

  // Verify initial state is no longer visible during loading
  await expect(page.getByText("Initial State Title")).not.toBeVisible();

  // Verify successful navigation after polling completes
  await expect(page).toHaveURL("/success-page", { timeout: 5000 });

  // Assert that polling happened the expected number of times
  expect(statusCallCount).toBe(3);
});
```

#### Testing Polling Timeout Errors

Test error states that occur when polling limits are reached:

```typescript
test("should show complete UI state transitions from initial to loading to error when polling limit reached", async ({
  page,
}) => {
  // Mock polling that never succeeds by setting successOnCall higher than maxCount
  // This simulates a scenario where the backend never returns success within the polling limit
  mockFeatureWithPolling(page, {
    responseData: { status: "pending" },
    successOnCall: 15, // Higher than polling maxCount (typically 10)
  });

  await page.goto("/feature-page");

  // Verify initial state
  const startButton = page.getByRole("button", { name: "Start Process" });
  await expect(startButton).toBeVisible();

  // Verify loading/error states are not visible initially
  await expect(page.getByText("Processing...")).not.toBeVisible();
  await expect(page.getByText("Process failed")).not.toBeVisible();

  // Click button and verify UI transitions
  await startButton.click();

  // Verify loading state UI
  await expect(page.getByText("Processing...")).toBeVisible();
  await expect(
    page.getByText("Please wait while we process your request")
  ).toBeVisible();

  // Wait for polling to complete and error state to show
  // Use generous timeout since polling will run maxCount times with intervals
  await expect(page.getByText("Process failed")).toBeVisible({
    timeout: 15000,
  });
  await expect(
    page.getByText("The process could not be completed")
  ).toBeVisible();
  await expect(page.getByRole("button", { name: "Try Again" })).toBeVisible();
  await expect(
    page.getByRole("link", { name: "Need help? Call us" })
  ).toBeVisible();

  // Verify loading state is no longer visible in error state
  await expect(page.getByText("Processing...")).not.toBeVisible();

  // Verify URL remains the same (no navigation on error)
  await expect(page).toHaveURL("/feature-page");
});
```

#### Testing SDK Errors vs Polling Errors

Distinguish between immediate SDK failures and polling timeout failures:

```typescript
test("should show error toast and remain on initial screen when SDK fails", async ({
  browser,
}) => {
  // Use storageState to simulate SDK error before any polling begins
  const context = await browser.newContext({
    storageState: "tests/e2e/storage-states/sdk-error.json",
  });
  const page = await context.newPage();

  mockFeatureAPI(page, {});

  await page.goto("/feature-page");

  // Verify initial state
  const startButton = page.getByRole("button", { name: "Start Process" });
  await expect(startButton).toBeVisible();

  // Click button and verify SDK failure handling
  await startButton.click();

  // Verify error toast is shown after SDK failure (global error handling)
  await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });

  // Verify we remain on initial screen for SDK failures (no state transition)
  await expect(page.getByText("Initial State Title")).toBeVisible();

  // Verify error screen components are NOT shown for SDK failures
  await expect(page.getByText("Process failed")).not.toBeVisible();
  await expect(
    page.getByText("The process could not be completed")
  ).not.toBeVisible();

  // Verify loading screen is NOT shown for SDK failures
  await expect(page.getByText("Processing...")).not.toBeVisible();

  await context.close();
});
```

#### Testing Loading States with Delays

Use slow mock responses to capture loading states:

```typescript
test("should show loading state during slow API response", async ({ page }) => {
  // Mock slow API response to capture loading state
  mockSlowAPICall(page, 2000); // 2 second delay

  await page.goto("/feature-page");
  await page.getByRole("textbox", { name: "Input Field" }).fill("test-value");
  await page.getByRole("button", { name: "Submit" }).click();

  // Verify loading state appears
  await expect(page.getByTestId("loading-spinner")).toBeVisible();
  await expect(page.getByText("Processing...")).toBeVisible();

  // Verify loading state eventually disappears
  await expect(page.getByTestId("loading-spinner")).not.toBeVisible({
    timeout: 5000,
  });
  await expect(page.getByText("Processing...")).not.toBeVisible();
});
```

#### Key Patterns for UI Testing

1. **Polling Control**: Use `successOnCall` to control when polling succeeds, then assert exact call counts
2. **State Isolation**: Verify other states are not visible when testing a specific state
3. **Generous Timeouts**: Use 15000ms timeouts for polling scenarios with multiple API calls
4. **Error Type Distinction**: SDK errors show toasts; polling errors show dedicated error components
5. **Loading State Verification**: Test both appearance and disappearance of loading indicators

## External SDK Stubbing with Vite Resolve

For testing components that integrate with external SDKs (like payment gateways, KYC providers, etc.), we use Vite's resolve alias feature to replace real SDK modules with test stubs during testing.

### How It Works

1. **Vite Configuration**: The `vite.config.ts` includes resolve aliases that map SDK modules to stub implementations when `VITE_USE_STUB_SDK=true`
2. **Stub Modules**: Test stubs are located in `tests/stubs/` and provide the same API as real SDKs
3. **StorageState Control**: Stubs read configuration from `localStorage` using predefined storageState files
4. **Pre-configured Scenarios**: Common SDK scenarios are available as storageState files in `tests/e2e/storage-states/`

### Setting Up SDK Stubs

#### 1. Create Stub Module

Create a stub that matches the real SDK's API and reads mock responses from localStorage:

```typescript
// tests/stubs/external-sdk.ts
import sinon from "sinon";

// Helper function to get mock response from localStorage
function getExternalSDKMockResponse(): ExternalSDKResponse {
  let mockResponse: ExternalSDKResponse = {
    success: true,
    transactionId: "mock_txn_123",
  };
  try {
    const localStorageResponse = localStorage.getItem(
      "test_external_sdk_mock_response"
    );
    if (localStorageResponse) {
      mockResponse = JSON.parse(localStorageResponse);
    }
  } catch {
    // Use default response
  }
  return mockResponse;
}

// Create Sinon stubs for tracking calls
const externalSDKStubs = {
  constructor: sinon.spy(),
  initialize: sinon.stub(),
  processPayment: sinon.stub(),
};

class MockExternalSDK {
  constructor(config: any) {
    this.config = config;
    externalSDKStubs.constructor(config);
  }

  async initialize(): Promise<void> {
    externalSDKStubs.initialize();

    const mockResponse = getExternalSDKMockResponse();
    if (mockResponse.error) {
      throw new Error(mockResponse.error);
    }
  }

  async processPayment(amount: number): Promise<ExternalSDKResponse> {
    externalSDKStubs.processPayment(amount);

    // Simulate async delay
    await new Promise((resolve) => setTimeout(resolve, 100));

    return getExternalSDKMockResponse();
  }
}

export function loadExternalSDK(): Promise<void> {
  window.ExternalSDK = MockExternalSDK;
  window.testUtils = {
    reset: () => {
      localStorage.removeItem("test_external_sdk_mock_response");
      externalSDKStubs.constructor.resetHistory();
      externalSDKStubs.initialize.resetHistory();
      externalSDKStubs.processPayment.resetHistory();
    },
    getStubs: () => externalSDKStubs,
  };
  return Promise.resolve();
}
```

#### 2. Configure Vite Resolve Alias

In `vite.config.ts`, add resolve aliases controlled by environment variable:

```typescript
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const useStub = env.VITE_USE_STUB_SDK === "true";

  return {
    // ... other config
    resolve: {
      alias: {
        ...(useStub
          ? {
              "@/utils/external-sdk-loader": path.resolve(
                __dirname,
                "tests/stubs/external-sdk.ts"
              ),
            }
          : {}),
      },
    },
  };
});
```

#### 3. Create StorageState Files

Create predefined storageState files for common scenarios:

```json
// tests/e2e/storage-states/external-sdk-error.json
{
  "cookies": [],
  "origins": [
    {
      "origin": "http://localhost:3000",
      "localStorage": [
        {
          "name": "test_external_sdk_mock_response",
          "value": "{\"error\":\"MOCK_SDK_ERROR\",\"success\":false}"
        }
      ]
    }
  ]
}
```

### Using SDK Stubs in Tests

#### Basic Usage (Default Success)

```typescript
test("should handle SDK success", async ({ page }) => {
  // Default behavior is success - no storageState needed
  await page.goto("/payment-page");
  await page.getByRole("button", { name: "Pay Now" }).click();

  // Verify success handling
  await expect(page.getByText("Payment successful")).toBeVisible();
});
```

#### Testing Error Scenarios with StorageState

```typescript
test("should handle SDK error", async ({ browser }) => {
  // Use predefined storageState for SDK error scenario
  const context = await browser.newContext({
    storageState: "tests/e2e/storage-states/external-sdk-error.json",
  });
  const page = await context.newPage();

  await page.goto("/payment-page");
  await page.getByRole("button", { name: "Pay Now" }).click();

  // Verify error handling
  await expect(page.getByText("Payment system unavailable")).toBeVisible();

  await context.close();
});
```

#### Testing with Custom StorageState

```typescript
test("should handle custom SDK response", async ({ browser }) => {
  // Create custom storageState for specific test scenario
  const context = await browser.newContext({
    storageState: {
      cookies: [],
      origins: [
        {
          origin: "http://localhost:3000",
          localStorage: [
            {
              name: "test_external_sdk_mock_response",
              value: JSON.stringify({
                success: true,
                transactionId: "custom-txn-456",
                customField: "test-value",
              }),
            },
          ],
        },
      ],
    },
  });
  const page = await context.newPage();

  await page.goto("/payment-page");
  await page.getByRole("button", { name: "Pay Now" }).click();

  // Verify custom response handling
  await expect(page.getByText("Transaction: custom-txn-456")).toBeVisible();

  await context.close();
});
```

### Available StorageState Files

Pre-configured files for common SDK scenarios:

- `digio-error.json` - Digio SDK returns error
- `digio-esign-error.json` - Digio eSign specific error
- `hyperverge-error.json` - HyperVerge SDK returns error

## Utilities

### Request Validation in Mocks

Request validation is now done inside the mock function using expect assertions:

```typescript
// Test: should fetch PAN name successfully
export function mockPanNameFetch(page: Page) {
  const responseData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);
        const requestJson = toJson(
          OnboardingRequestSchema,
          requestData
        ) as Record<string, unknown>;

        // Validate the request structure
        expect(requestJson).toMatchObject({
          stepName: "PAN_KYC",
          panKycRequest: {
            step: "NAME_FETCH",
            kycFetchNameByPanRequest: {
              pan: expect.any(String),
            },
          },
        });

        route.fulfill({
          status: 200,
          body: Buffer.from(
            toBinary(
              OnboardingResponseSchema,
              create(OnboardingResponseSchema, {
                result: {
                  case: "panKycResponse",
                  value: {
                    step: "NAME_FETCH",
                    kycFetchNameByPanResponse: responseData,
                  },
                },
              })
            )
          ),
        });
        return;
      }
    }
    route.continue();
  });

  return responseData;
}
```

## Testing Server and Network Failures

**Critical Requirement**: All flows that make API calls must test both server errors and network failures to ensure proper user feedback and error handling.

### Error Testing Utilities

Use the established error utilities instead of manual route mocking:

```typescript
import { mockErrorResponse, mockNetworkError } from "../fixtures/api-response";

// For server errors (400, 500, etc.)
mockErrorResponse(page, "*/**/v1/your-endpoint", 500, {
  errorCode: "INTERNAL_ERROR",
  errorMessage: "Internal server error",
});

// For network connectivity issues
mockNetworkError(page, "*/**/v1/your-endpoint");
```

### Comprehensive Error Testing Pattern

Every feature that makes API calls should include these error tests:

```typescript
test.describe("Feature Name - Error Handling", () => {
  test.beforeEach(async ({ page }) => {
    // Setup common mocks (user profile, onboarding state, etc.)
    mockUserProfile(page);
    mockOnboardingState(page, { next: 4 });
  });

  test("should show error page when API fails with 500 server error", async ({
    page,
  }) => {
    mockErrorResponse(page, "*/**/v1/your-endpoint", 500, {
      errorMessage: "Internal server error",
    });

    await page.goto("/your-page");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify the correct error message is displayed
    await expect(page.getByText("Internal server error")).toBeVisible();

    // Verify user is not stuck on loading state
    await expect(page.getByText("Loading...")).not.toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should show error page when API fails with 400 client error", async ({
    page,
  }) => {
    mockErrorResponse(page, "*/**/v1/your-endpoint", 400, {
      errorMessage: "This step is already completed",
    });

    await page.goto("/your-page");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify the correct error message is displayed
    await expect(
      page.getByText("This step is already completed")
    ).toBeVisible();

    await expect(page.getByText("Loading...")).not.toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should show error page when network connection fails", async ({
    page,
  }) => {
    mockNetworkError(page, "*/**/v1/your-endpoint");

    await page.goto("/your-page");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify network error message is displayed
    await expect(
      page.getByText("Please check your internet connection and try again.")
    ).toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should reload page when retry button is clicked", async ({ page }) => {
    mockErrorResponse(page, "*/**/v1/your-endpoint", 500, {
      errorMessage: "Internal server error",
    });

    await page.goto("/your-page");

    // Should show error page first
    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify we're not on the initial state anymore
    await expect(page.getByText("Initial Page Content")).not.toBeVisible();

    // Click retry button should reload the page
    await page.getByRole("button", { name: "Retry" }).click();

    // After reload, should be back to the initial page state
    // (since the error will happen again with the same mock)
    await expect(page.getByText("Initial Page Content")).toBeVisible();
  });
});
```

### Testing Retry Functionality (Page Reload)

Error pages typically reload the entire window when users click retry. Test this behavior:

```typescript
test("should reload page when retry button is clicked on error page", async ({
  page,
}) => {
  // Mock API to always fail so we can test retry behavior
  mockErrorResponse(page, "*/**/v1/your-endpoint", 500, {
    errorMessage: "Internal server error",
  });

  await page.goto("/your-page");

  // Should show error page
  await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
    timeout: 10000,
  });

  // Verify we're on the error page (not initial state)
  await expect(page.getByText("Internal server error")).toBeVisible();
  await expect(page.getByText("Initial Page Content")).not.toBeVisible();

  // Test that retry button reloads the page
  const reloadPromise = page.waitForLoadState("load");
  await page.getByRole("button", { name: "Retry" }).click();
  await reloadPromise;
});

test("should successfully complete flow after retry when error is resolved", async ({
  page,
}) => {
  let requestCount = 0;

  // Mock first page load to fail, subsequent loads to succeed
  page.route("*/**/v1/your-endpoint", (route) => {
    requestCount++;
    if (requestCount === 1) {
      // First request fails
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: "Internal server error" }),
      });
    } else {
      // Subsequent requests succeed - continue to success mock
      route.continue();
    }
  });

  // Mock successful flow for after retry
  mockYourSuccessfulFlow(page);

  await page.goto("/your-page");

  // Should show error page first
  await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
    timeout: 10000,
  });

  // Click retry button (reloads page)
  await page.getByRole("button", { name: "Retry" }).click();

  // After reload, should complete successfully
  await expect(page.getByText("Success")).toBeVisible({ timeout: 10000 });

  // Verify that retry actually made a new request
  expect(requestCount).toBe(2);
});
```

### Error Message Verification

Always verify that the correct error messages are displayed to users:

#### Expected Error Messages by Type

- **500 Server Errors**: Display the `errorMessage` from the API response
- **400 Client Errors**: Display the `errorMessage` from the API response
- **Network Errors**: Display "Please check your internet connection and try again."
- **Timeout Errors**: Display "Please check your internet connection and try again."

#### Error Message Flow

1. **API Error** → Query/Mutation error state
2. **Error Component** → `<ErrorPage error={error} />` or custom error component
3. **Error Processing** → `getErrorMessage(error)` function formats the message
4. **Display** → Error message shown to user with retry option
5. **Retry Action** → Retry button calls `window.location.reload()` to reload the entire page

### Testing Different Error Scenarios

#### Credential/Token Generation Failures

```typescript
test("should handle credential generation failure", async ({ page }) => {
  mockErrorResponse(page, "*/**/v1/onboarding", 400, {
    errorCode: "INVALID_REQUEST",
    errorMessage: "Invalid request parameters",
  });

  await page.goto("/kyc");

  await expect(page.getByRole("button", { name: "Retry" })).toBeVisible();
  await expect(page.getByText("Invalid request parameters")).toBeVisible();
});
```

#### Polling API Failures

```typescript
test("should handle polling API failure", async ({ page }) => {
  // Mock initial request to succeed, polling to fail
  mockYourEndpointWithPolling(page, {
    initialResponse: { id: "test-id" },
    statusResponse: { error: "Polling failed" },
    successOnCall: 1, // Fail immediately on first status check
  });

  await page.goto("/your-page");
  await page.getByRole("button", { name: "Start" }).click();

  // Should show loading first
  await expect(page.getByText("Processing...")).toBeVisible();

  // Then show error when polling fails
  await expect(page.getByText("Process failed")).toBeVisible({
    timeout: 10000,
  });
});
```

#### Form Submission Failures

```typescript
test("should handle form submission server error", async ({ page }) => {
  mockErrorResponse(page, "*/**/v1/submit-form", 422, {
    errorMessage: "Validation failed: Email is required",
  });

  await page.goto("/form-page");
  await page.getByRole("textbox", { name: "Name" }).fill("John Doe");
  await page.getByRole("button", { name: "Submit" }).click();

  await expect(
    page.getByText("Validation failed: Email is required")
  ).toBeVisible();
  await expect(page.getByRole("button", { name: "Retry" })).toBeVisible();
});
```

### Why Error Testing is Critical

1. **User Experience**: Users need clear feedback when things go wrong
2. **Error Recovery**: Users should be able to retry failed operations by reloading the page
3. **Network Reliability**: Mobile users often have unstable connections
4. **Server Issues**: Backend services can fail or return errors
5. **Debugging**: Error messages help users and support teams diagnose issues
6. **Page Reload Behavior**: Retry buttons reload the entire page, giving users a fresh start

### Error Testing Checklist

For every feature that makes API calls, ensure you test:

- ✅ **500 Server Errors**: Internal server errors, database failures
- ✅ **400 Client Errors**: Validation errors, invalid requests
- ✅ **Network Errors**: Connection timeouts, DNS failures
- ✅ **Retry Functionality**: Users can reload the page to retry failed operations
- ✅ **Error Messages**: Correct messages are displayed to users
- ✅ **UI State**: Users aren't stuck in loading states during errors
- ✅ **Navigation**: Error states don't break navigation or routing
- ✅ **Page Reload**: Retry button actually reloads the page (test with `waitForLoadState`)

### Common Mistakes to Avoid

1. **❌ Manual Route Mocking**: Don't create custom route handlers for errors

   ```typescript
   // DON'T DO THIS
   page.route("*/**/api", (route) => {
     route.fulfill({ status: 500, body: "Error" });
   });
   ```

2. **✅ Use Error Utilities**: Use the established utilities instead

   ```typescript
   // DO THIS
   mockErrorResponse(page, "*/**/api", 500, {
     errorMessage: "Internal server error",
   });
   ```

3. **❌ Only Testing Happy Path**: Don't skip error scenarios
4. **❌ Generic Error Checking**: Don't just check for retry buttons
5. **❌ Expecting API Retry**: Don't expect retry to just re-call the API
   ```typescript
   // DON'T EXPECT THIS
   await page.getByRole("button", { name: "Retry" }).click();
   await expect(page.getByText("Success")).toBeVisible(); // Won't work if error persists
   ```
6. **✅ Test Page Reload**: Expect retry to reload the entire page
   ```typescript
   // DO THIS
   await page.getByRole("button", { name: "Retry" }).click();
   await expect(page.getByText("Initial Page Content")).toBeVisible(); // Back to start
   ```
7. **✅ Verify Specific Messages**: Always verify the exact error message shown to users
8. **✅ Test Page Reload**: Always verify that retry buttons actually reload the page

### Testing Page Reload Behavior

Every error test should verify that the retry button actually reloads the page:

```typescript
// Test that retry button reloads the page
const reloadPromise = page.waitForLoadState("load");
await page.getByRole("button", { name: "Retry" }).click();
await reloadPromise;
```

**Why this is important:**

- Ensures retry buttons work as expected
- Verifies users get a fresh page state
- Catches issues where retry buttons might be broken
- Confirms the reload mechanism is functioning

**Pattern to follow:**

1. Show error page and verify error message
2. Set up reload promise with `page.waitForLoadState('load')`
3. Click retry button
4. Wait for reload to complete

## Key Guidelines

- **Follow the new mocking pattern**: One mock file per spec file, one mock function per endpoint per test
- **Page-only parameters**: Mock functions should only accept the `page` argument
- **Internal validation**: Use expect assertions inside mock functions to validate requests
- **Return response data**: Mock functions should return the response data for test assertions
- **Use faker for data**: Generate realistic fake data using faker instead of hardcoded values
- **Find schemas in src/queries**: Examine API call functions in `src/queries` to find correct schemas
- **Test independence**: Each test should run in isolation
- **Organize tests**: Use describe blocks to group related tests
- **Error testing is mandatory**: Every API-calling feature must test server and network failures
- **Use error utilities**: Always use `mockErrorResponse` and `mockNetworkError` for error scenarios
- **Verify error messages**: Test that users see the correct error messages, not just error states
- **Test retry behavior**: Retry buttons reload the entire page, not just the failed API call
