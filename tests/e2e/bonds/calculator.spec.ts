import { test, expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  mockBondDetails,
  mockBondCalculation,
  mockSoldOutBond,
  mockErrorResponse,
  mockNetworkError,
  mockBrokingOnboardingStatus,
  mockOrderCreationFlow,
  mockOnboardingState,
  mockUserProfile,
} from "./calculator.mocks";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { StepName } from "@/clients/gen/broking/Kyc_pb";

test.describe("Bonds Calculator Page", () => {
  const bondId = faker.string.uuid();
  const issuer = "test-issuer";

  test.beforeEach(async ({ page }) => {
    // Mock bond details and calculation
    mockBondDetails(page, {
      bondDetails: {
        id: bondId,
        bondName: "Test Bond",
        xirr: 8.5,
        timeLeftToMaturity: "2 years 3 months",
        pricePerBond: 1000,
        defaultQuantity: 1,
        bondQuantity: {
          minCount: 1,
          maxCount: 100,
          availableCount: 50,
        },
        aboutTheInstitution: {
          title: "Test Institution",
          bondInstitutionName: "Test Institution Ltd",
          slug: issuer,
        },
      },
    });

    mockBondCalculation(page);
  });

  test.describe("Page Loading", () => {
    test("should load calculator page with bond details", async ({ page }) => {
      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      // Verify page title and bond information
      await expect(page.getByText("Test Institution")).toBeVisible();
      await expect(
        page.getByText("8.5% YTM for 2 years 3 months")
      ).toBeVisible();

      // Verify unit selector
      await expect(page.getByText("Units")).toBeVisible();
      await expect(page.locator('input[type="number"]')).toHaveValue("1");

      // Verify proceed button
      await expect(
        page.getByRole("button", { name: "⚡ Complete one-time KYC" })
      ).toBeVisible();
    });

    test("should show sold out state for unavailable bonds", async ({
      page,
    }) => {
      mockSoldOutBond(page);

      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      await expect(
        page.getByRole("button", { name: "Sold out" })
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Sold out" })
      ).toBeDisabled();
    });
  });

  test.describe("Unit Selection", () => {
    test.beforeEach(async ({ page }) => {
      // Mock calculation with predictable values for testing
      mockBondCalculation(page, {
        purchaseAmount: 1000, // Will be overridden by quantity-based calculation
        accruedInterest: 50, // Fixed value per unit for predictable testing
        stampDuty: 10, // Fixed value per unit for predictable testing
        maturityAmount: 1200, // Fixed value per unit for predictable testing
      });

      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      // Expand the "Investment amount" accordion to see the calculation details
      await page.getByRole("button", { name: "Investment amount" }).click();
    });

    test("should update calculation when quantity is incremented", async ({
      page,
    }) => {
      const quantityInput = page.getByTestId("quantity-input");
      const incrementButton = page.getByRole("button", { name: "plus button" });

      // Verify initial state (quantity = 1)
      await expect(quantityInput).toHaveValue("1");
      await expect(page.getByTestId("principal-amount")).toHaveText("₹1,000"); // Principal (1000 * 1)
      await expect(page.getByTestId("accrued-interest-amount")).toHaveText(
        "₹50"
      ); // Accrued interest (50 * 1)
      await expect(page.getByTestId("stamp-duty-amount")).toHaveText("₹10"); // Stamp duty (10 * 1)

      // Increment quantity to 2
      await incrementButton.click();

      // Verify calculation updates for quantity 2
      await expect(quantityInput).toHaveValue("2");
      await expect(page.getByTestId("principal-amount")).toHaveText("₹2,000"); // Principal (1000 * 2)
      await expect(page.getByTestId("accrued-interest-amount")).toHaveText(
        "₹100"
      ); // Accrued interest (50 * 2)
      await expect(page.getByTestId("stamp-duty-amount")).toHaveText("₹20"); // Stamp duty (10 * 2)

      // Increment quantity to 3
      await incrementButton.click();

      // Verify calculation updates for quantity 3
      await expect(quantityInput).toHaveValue("3");
      await expect(page.getByTestId("principal-amount")).toHaveText("₹3,000"); // Principal (1000 * 3)
      await expect(page.getByTestId("accrued-interest-amount")).toHaveText(
        "₹150"
      ); // Accrued interest (50 * 3)
      await expect(page.getByTestId("stamp-duty-amount")).toHaveText("₹30"); // Stamp duty (10 * 3)
    });

    test("should update calculation when quantity is decremented", async ({
      page,
    }) => {
      const quantityInput = page.getByTestId("quantity-input");
      const incrementButton = page.getByRole("button", { name: "plus button" });
      const decrementButton = page.getByRole("button", {
        name: "minus button",
      });

      // First increment to quantity 3 to have room to decrement
      await incrementButton.click();
      await incrementButton.click();

      // Verify we're at quantity 3
      await expect(quantityInput).toHaveValue("3");
      await expect(page.getByTestId("principal-amount")).toHaveText("₹3,000");

      // Decrement quantity to 2
      await decrementButton.click();

      // Verify calculation updates for quantity 2
      await expect(quantityInput).toHaveValue("2");
      await expect(page.getByTestId("principal-amount")).toHaveText("₹2,000"); // Principal (1000 * 2)
      await expect(page.getByTestId("accrued-interest-amount")).toHaveText(
        "₹100"
      ); // Accrued interest (50 * 2)
      await expect(page.getByTestId("stamp-duty-amount")).toHaveText("₹20"); // Stamp duty (10 * 2)

      // Decrement quantity to 1
      await decrementButton.click();

      // Verify calculation updates for quantity 1
      await expect(quantityInput).toHaveValue("1");
      await expect(page.getByTestId("principal-amount")).toHaveText("₹1,000"); // Principal (1000 * 1)
      await expect(page.getByTestId("accrued-interest-amount")).toHaveText(
        "₹50"
      ); // Accrued interest (50 * 1)
      await expect(page.getByTestId("stamp-duty-amount")).toHaveText("₹10"); // Stamp duty (10 * 1)
    });

    // TODO: Add direct input test once UnitSelector component properly handles input events
    // The component seems to have specific event handling that doesn't work well with direct input manipulation
  });

  test.describe("Onboarding Status CTA", () => {
    // Use storage state for authenticated user tests
    test.describe("Authenticated User Tests", () => {
      test.use({
        storageState: "tests/e2e/storage-states/authenticated-user.json",
      });

      test("should show 'Proceed' for completed KYC users", async ({
        page,
      }) => {
        // Mock APIs
        mockUserProfile(page);
        mockBrokingOnboardingStatus(page, {
          kycStatus: UserLifetimeStatusResponse_KycStatus.COMPLETED,
          lifetimeStatus: UserLifetimeStatus.KYC_COMPLETED,
        });

        await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

        // Should show normal "Proceed" button for completed users
        await expect(
          page.getByRole("button", { name: "Proceed" })
        ).toBeVisible();
      });

      test("should show 'Continue one-time KYC' for initiated KYC users", async ({
        page,
      }) => {
        // Mock APIs
        mockUserProfile(page);
        mockBrokingOnboardingStatus(page, {
          kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
          lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
        });

        await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

        // Should show continue KYC button for users with initiated KYC
        await expect(
          page.getByRole("button", { name: "⚡ Continue one-time KYC" })
        ).toBeVisible();
      });

      test("should show 'Complete one-time KYC' for new users", async ({
        page,
      }) => {
        // Mock APIs
        mockUserProfile(page);
        mockBrokingOnboardingStatus(page, {
          kycStatus: UserLifetimeStatusResponse_KycStatus.NOT_INITIATED,
          lifetimeStatus: UserLifetimeStatus.NEW_USER,
        });

        await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

        // Should show complete KYC button for new users
        await expect(
          page.getByRole("button", { name: "⚡ Complete one-time KYC" })
        ).toBeVisible();
      });

      test("should create order and navigate to payment for completed KYC users", async ({
        page,
      }) => {
        // Mock APIs
        mockUserProfile(page);
        mockBrokingOnboardingStatus(page, {
          kycStatus: UserLifetimeStatusResponse_KycStatus.COMPLETED,
          lifetimeStatus: UserLifetimeStatus.KYC_COMPLETED,
        });

        // Mock alpha onboarding as completed (no next step)
        mockOnboardingState(page, { next: undefined });

        // Mock successful order creation
        mockOrderCreationFlow(page);

        await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

        // Click the Proceed button
        await page.getByRole("button", { name: "Proceed" }).click();

        // Should navigate to payment page after order creation
        await expect(page).toHaveURL(/\/checkout\/.*\/pay/);
      });

      test("should navigate to onboarding for incomplete KYC users", async ({
        page,
      }) => {
        // Mock APIs
        mockUserProfile(page);
        mockBrokingOnboardingStatus(page, {
          kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
          lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
          nextStep: StepName.PAN_KYC, // Provide a valid next step
        });

        // Mock alpha onboarding as completed (no next step)
        mockOnboardingState(page, { next: undefined });

        // Mock cart operations
        mockOrderCreationFlow(page);

        await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

        // Click the Continue KYC button
        await page
          .getByRole("button", { name: "⚡ Continue one-time KYC" })
          .click();

        // Should navigate to PAN onboarding page specifically
        await expect(page).toHaveURL("/onboarding/pan");
      });
    });

    test("should show 'Complete KYC' for guest users", async ({ page }) => {
      // Don't mock user profile to simulate guest user

      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      // Should show normal "Proceed" button for guest users
      await expect(
        page.getByRole("button", { name: "⚡ Complete one-time KYC" })
      ).toBeVisible();
    });
  });

  test.describe("Guest User Flow", () => {
    test("should redirect to authentication for guest users", async ({
      page,
    }) => {
      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      const proceedButton = page.getByRole("button", {
        name: "⚡ Complete one-time KYC",
      });
      await expect(proceedButton).toBeVisible();
      await expect(proceedButton).not.toBeDisabled();

      // Click proceed button
      await proceedButton.click();

      // Should redirect to authentication
      await expect(page).toHaveURL("/authentication/mobile-number");
    });
  });

  test.describe("Sold Out Bond Handling", () => {
    test("should show label for sold out bonds", async ({ page }) => {
      mockSoldOutBond(page);

      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      // Button should be disabled and show "Sold out"
      const soldOutButton = page.getByRole("button", { name: "Sold out" });
      await expect(soldOutButton).toBeVisible();
      await expect(soldOutButton).toBeDisabled();
    });
  });

  test.describe("Error Handling", () => {
    test("should display error page when bond details fail to load", async ({
      page,
    }) => {
      mockErrorResponse(page, "*/**/v1/bond-details/*", 404, {
        errorMessage: "Bond not found",
      });

      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      // Should show default error page with proper error message
      await expect(page.getByText("Bond not found")).toBeVisible();
      await expect(page.getByText("Need help? Call us")).toBeVisible();
      await expect(page.getByText("Retry")).toBeVisible();

      // Verify the help button has correct phone link
      await expect(
        page.getByRole("link", { name: "Need help? Call us" })
      ).toHaveAttribute("href", "tel:+918045889087");
    });

    test("should display error page when network fails", async ({ page }) => {
      mockNetworkError(page, "*/**/v1/bond-details/*");

      await page.goto(`/bonds/${issuer}/${bondId}/calculator`);

      // Should show default error page for network failure
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
      await expect(page.getByText("Need help? Call us")).toBeVisible();
      await expect(page.getByText("Retry")).toBeVisible();
      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible();
    });
  });
});
