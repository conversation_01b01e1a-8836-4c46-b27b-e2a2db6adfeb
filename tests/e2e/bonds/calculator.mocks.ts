import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import {
  BondDetailsResponseSchema,
  BondAmountCalculatorResponseSchema,
  InvestabilityStatus,
} from "@/clients/gen/broking/BondDetails_pb";
import { UserProfileResponseSchema } from "@/clients/gen/broking/Profile_pb";
import {
  OnboardingResponseSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { OnboardingStateSchema } from "@/clients/gen/platform/public/models/identity/Onboarding_pb";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";
import { PlaceOrderResponseV2Schema } from "@/clients/gen/broking/Order_pb";
import {
  CartDetailsSchema,
  UpdateCartResponseSchema,
} from "@/clients/gen/broking/Cart_pb";
import { ErrorResponseSchema } from "@/clients/gen/platform/public/models/identity/Common_pb";
import { mergeDeep, type DeepPartial } from "../../utils";

// Test: should load calculator page with bond details
export function mockBondDetails(
  page: Page,
  overrides: DeepPartial<
    MessageInitShape<typeof BondDetailsResponseSchema>
  > = {}
) {
  page.route("*/**/v1/bond-details/*", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof BondDetailsResponseSchema
    > = {
      bondDetails: {
        id: faker.string.uuid(),
        bondName: faker.company.name() + " Bond",
        xirr: faker.number.float({ min: 6, max: 12, fractionDigits: 2 }),
        timeLeftToMaturity: "2 years 3 months",
        pricePerBond: faker.number.float({
          min: 1000,
          max: 10000,
          fractionDigits: 2,
        }),
        faceValue: "1000",
        maturityDate: "15-Mar-2027",
        defaultQuantity: 1,
        rating: "AAA",
        interestPayment: "Quarterly",
        investabilityStatus: InvestabilityStatus.LIVE,
        bondQuantity: {
          minCount: 1,
          maxCount: 100,
          availableCount: 50,
        },
        aboutTheInstitution: {
          title: faker.company.name(),
          bondInstitutionName: faker.company.name(),
          slug: faker.lorem.slug(),
        },
      },
    };

    const responseData = mergeDeep(defaultResponseData, overrides);
    const bondDetailsResponse = create(BondDetailsResponseSchema, responseData);

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(BondDetailsResponseSchema, bondDetailsResponse)
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });
}

// Test: should update calculation when quantity is incremented/decremented
export function mockBondCalculation(
  page: Page,
  overrides: DeepPartial<
    MessageInitShape<typeof BondAmountCalculatorResponseSchema>
  > = {}
) {
  page.route("*/**/v1/bond-amount-calculator*", (route) => {
    const url = new URL(route.request().url());
    const quantity = parseInt(url.searchParams.get("quantity") || "1");
    const baseAmount = 1000; // Base price per bond

    // Default per-unit values that can be overridden
    const defaultPerUnitValues = {
      purchaseAmount: baseAmount,
      accruedInterest: faker.number.float({
        min: 10,
        max: 100,
        fractionDigits: 2,
      }),
      stampDuty: faker.number.float({ min: 5, max: 50, fractionDigits: 2 }),
      maturityAmount: faker.number.float({
        min: 1200,
        max: 1500,
        fractionDigits: 2,
      }),
    };

    // Apply overrides to per-unit values if provided
    const perUnitValues = { ...defaultPerUnitValues };
    if (overrides.purchaseAmount !== undefined) {
      perUnitValues.purchaseAmount = overrides.purchaseAmount;
    }
    if (overrides.accruedInterest !== undefined) {
      perUnitValues.accruedInterest = overrides.accruedInterest;
    }
    if (overrides.stampDuty !== undefined) {
      perUnitValues.stampDuty = overrides.stampDuty;
    }
    if (overrides.maturityAmount !== undefined) {
      perUnitValues.maturityAmount = overrides.maturityAmount;
    }

    // Calculate final values based on quantity
    const defaultResponseData: MessageInitShape<
      typeof BondAmountCalculatorResponseSchema
    > = {
      purchaseAmount: perUnitValues.purchaseAmount * quantity,
      accruedInterest: perUnitValues.accruedInterest * quantity,
      stampDuty: perUnitValues.stampDuty * quantity,
      totalConsideration: 0, // Will be calculated below
      maturityAmount: perUnitValues.maturityAmount * quantity,
    };

    // Calculate total consideration
    defaultResponseData.totalConsideration =
      (defaultResponseData.purchaseAmount || 0) +
      (defaultResponseData.accruedInterest || 0) +
      (defaultResponseData.stampDuty || 0);

    // Apply any remaining overrides (like totalConsideration override)
    const responseData = mergeDeep(defaultResponseData, {
      ...overrides,
      // Don't override the calculated values we already handled above
      purchaseAmount: defaultResponseData.purchaseAmount,
      accruedInterest: defaultResponseData.accruedInterest,
      stampDuty: defaultResponseData.stampDuty,
      maturityAmount: defaultResponseData.maturityAmount,
    });

    const calculationResponse = create(
      BondAmountCalculatorResponseSchema,
      responseData
    );

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(BondAmountCalculatorResponseSchema, calculationResponse)
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });
}

// Test: should show sold out state for unavailable bonds
export function mockSoldOutBond(page: Page) {
  mockBondDetails(page, {
    bondDetails: {
      investabilityStatus: InvestabilityStatus.SOLD_OUT,
      bondQuantity: {
        availableCount: 0,
        minCount: 1,
        maxCount: 100,
      },
    },
  });
}

// Test: should show 'Proceed' for completed KYC users
export function mockUserProfile(
  page: Page,
  overrides: DeepPartial<
    MessageInitShape<typeof UserProfileResponseSchema>
  > = {}
) {
  page.route(`*/**/v1/user/profile`, (route) => {
    const userId = faker.string.uuid();
    const defaultResponseData: MessageInitShape<
      typeof UserProfileResponseSchema
    > = {
      data: {
        id: userId,
        name: faker.person.fullName(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
      },
      profileData: {
        id: userId,
        panNumber: faker.finance.accountNumber({ length: 10 }),
        aadharNumber: faker.finance.accountNumber({ length: 12 }),
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
      },
    };

    // Merge the override response with defaults using object spread
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> =
      mergeDeep(defaultResponseData, overrides);

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });
}

// Test: should show 'Continue one-time KYC' for initiated KYC users
export function mockBrokingOnboardingStatus(
  page: Page,
  options: {
    kycStatus: UserLifetimeStatusResponse_KycStatus;
    lifetimeStatus: UserLifetimeStatus;
    nextStep?: StepName;
  }
) {
  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "GET") {
      const responseData: MessageInitShape<typeof OnboardingResponseSchema> = {
        kycStatus: options.kycStatus,
        nextStep: options.nextStep ?? StepName.KYC_TYPE_UNKNOWN,
        lifetimeStatus: options.lifetimeStatus,
        result: {
          case: "emptyKycResponse" as const,
          value: {},
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            OnboardingResponseSchema,
            create(OnboardingResponseSchema, responseData)
          )
        ),
        headers: {
          "Content-Type": "application/x-protobuf",
        },
      });
      return;
    }
    route.continue();
  });
}

// Test: should navigate to onboarding for incomplete KYC users
export function mockOnboardingState(
  page: Page,
  options: { next?: KycType } = {}
) {
  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData: MessageInitShape<typeof OnboardingStateSchema> =
      options.next !== undefined ? { next: options.next } : {};

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });
}

// Test: should create order and navigate to payment for completed KYC users
export function mockOrderCreationFlow(
  page: Page,
  options: {
    orderResponse?: DeepPartial<
      MessageInitShape<typeof PlaceOrderResponseV2Schema>
    >;
    validateOrderRequest?: (requestJson: Record<string, unknown>) => void;
  } = {}
) {
  // Mock empty cart (will be reset before order creation)
  mockEmptyCart(page);

  // Mock cart update (for resetting cart)
  mockUpdateCart(page);

  // Mock order creation
  mockBuyNow(page, options.orderResponse, options.validateOrderRequest);
}

function mockBuyNow(
  page: Page,
  overrides: DeepPartial<
    MessageInitShape<typeof PlaceOrderResponseV2Schema>
  > = {},
  validateRequest?: (requestJson: Record<string, unknown>) => void
) {
  page.route("*/**/v1/cart/add-checkout", (route) => {
    if (validateRequest) {
      const requestBuffer = route.request().postDataBuffer();
      if (requestBuffer) {
        // Note: For protobuf validation, we'd need to decode the binary data
        // For now, we'll skip detailed request validation in this mock
        // but the validateRequest callback can be used for other checks
      }
    }

    const defaultResponseData: MessageInitShape<
      typeof PlaceOrderResponseV2Schema
    > = {
      orderId: faker.string.uuid(),
      paymentGateway: "CASHFREE",
      orderData: {
        id: faker.string.uuid(),
        totalConsideration: faker.number.float({
          min: 1000,
          max: 10000,
          fractionDigits: 2,
        }),
        status: "PENDING",
        orderType: "BUY",
        isOrderSettled: false,
        paymentGateway: "CASHFREE",
      },
    };

    const responseData = mergeDeep(defaultResponseData, overrides);
    const orderResponse = create(PlaceOrderResponseV2Schema, responseData);

    route.fulfill({
      status: 200,
      body: Buffer.from(toBinary(PlaceOrderResponseV2Schema, orderResponse)),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });
}

function mockUpdateCart(
  page: Page,
  overrides: DeepPartial<MessageInitShape<typeof UpdateCartResponseSchema>> = {}
) {
  page.route("*/**/v1/cart/update", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof UpdateCartResponseSchema
    > = {
      cartItemId: faker.string.uuid(),
    };

    const responseData = mergeDeep(defaultResponseData, overrides);
    const cartResponse = create(UpdateCartResponseSchema, responseData);

    route.fulfill({
      status: 200,
      body: Buffer.from(toBinary(UpdateCartResponseSchema, cartResponse)),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });
}

function mockEmptyCart(page: Page) {
  page.route("*/**/v1/cart/details", (route) => {
    const defaultResponseData: MessageInitShape<typeof CartDetailsSchema> = {
      cartItems: [],
    };

    const cartResponse = create(CartDetailsSchema, defaultResponseData);

    route.fulfill({
      status: 200,
      body: Buffer.from(toBinary(CartDetailsSchema, cartResponse)),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });
}

// Test: should display error page when bond details fail to load
export function mockErrorResponse(
  page: Page,
  route: string,
  status: number = 400,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  page.route(route, (routeHandler) => {
    const defaultResponseData = {
      errorCode: faker.string.alphanumeric(8).toUpperCase(),
      errorMessage: faker.lorem.sentence(),
    };

    const responseData = mergeDeep(defaultResponseData, overrides);

    routeHandler.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });
}

// Test: should display error page when network fails
export function mockNetworkError(page: Page, route: string) {
  page.route(route, (routeHandler) => {
    // Randomly choose between abort and timeout to simulate different network issues
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);

    if (errorType === "abort") {
      // Abort the request to simulate network disconnection
      routeHandler.abort("internetdisconnected");
    } else {
      // Simulate timeout by aborting with timeout error
      routeHandler.abort("timedout");
    }
  });
}
