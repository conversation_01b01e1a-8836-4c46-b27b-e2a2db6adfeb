# Storage States for SDK Testing

This directory contains Playwright storageState files that pre-configure localStorage values for SDK testing scenarios.

## Purpose

The SDK stubs read mock responses from localStorage before falling back to runtime configuration. This allows tests to set up SDK behavior before the page loads, which is crucial for testing error scenarios where the SDK is called immediately on page load.

## Available Storage States

### `digio-error.json`

Sets up Digio SDK to return an error response:

```json
{
  "test_digio_mock_response": "{\"error_code\":\"MOCK_ERROR\"}"
}
```

### `hyperverge-error.json`

Sets up HyperVerge SDK to return an error response:

```json
{
  "test_hyperverge_mock_response": "{\"status\":\"error\",\"transactionId\":\"mock_transaction_123\"}"
}
```

## Usage

Use these storage states in tests that need to simulate SDK errors:

```typescript
test("should handle SDK error", async ({ browser }) => {
  const context = await browser.newContext({
    storageState: "tests/e2e/storage-states/digio-error.json",
  });
  const page = await context.newPage();

  // Test error handling...

  await context.close();
});
```

## How It Works

1. **SDK Stub Priority**: The SDK stubs check localStorage first, then fall back to window state
2. **Pre-load Configuration**: StorageState files set localStorage before the page loads
3. **Immediate Availability**: Mock responses are available when the SDK is called on page load
4. **Backward Compatibility**: Runtime `testUtils.setDigioMockResponse()` still works and also sets localStorage

## Creating New Storage States

To create a new storage state:

1. Create a JSON file with the localStorage values you need
2. Use the same structure as existing files
3. Set the appropriate localStorage keys:
   - `test_digio_mock_response` for Digio SDK responses
   - `test_hyperverge_mock_response` for HyperVerge SDK responses
4. Document the purpose and usage in this README
