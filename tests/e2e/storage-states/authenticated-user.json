{"cookies": [{"name": "sm_access_token", "value": "mock_access_token_123", "domain": "localhost", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "sm_refresh_token", "value": "mock_refresh_token_123", "domain": "localhost", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}], "origins": [{"origin": "http://localhost:3000", "localStorage": [{"name": "user", "value": "{\"data\":{\"type\":\"user\",\"data\":{\"data\":{\"id\":\"mock_user_123\",\"name\":\"Test User\",\"firstName\":\"Test\",\"lastName\":\"User\"},\"profileData\":{\"id\":\"mock_profile_123\",\"panNumber\":\"**********\",\"aadharNumber\":\"123456789012\",\"dob\":\"1990-01-01T00:00:00.000Z\",\"gender\":1,\"incomeRange\":1,\"employmentType\":1,\"tradingExperience\":1,\"maritalStatus\":1,\"fatherName\":\"Test Father\",\"motherName\":\"Test Mother\"}}}}"}]}]}