import { test, expect } from "@playwright/test";
import { mockUserProfileSuccess } from "./user-hydration.mocks";

test.describe("User Hydration Behavior", () => {
  test("should hydrate user store with profile data when auth token is available", async ({
    browser,
  }) => {
    const context = await browser.newContext({
      storageState: "tests/e2e/storage-states/authenticated-user.json",
    });
    const page = await context.newPage();

    const profileData = mockUserProfileSuccess(page);

    await page.goto("/about-us");

    await expect(page.getByRole("button", { name: "Log out" })).toBeVisible();
    await page.goto("/profile");
    await page.waitForURL("/profile");
    await expect(page.getByText(profileData.firstName)).toBeVisible();
    await expect(page.getByText(profileData.lastName)).toBeVisible();

    await context.close();
  });

  test("should not make profile request when no auth token is available", async ({
    page,
  }) => {
    let profileRequestMade = false;

    page.route("*/**/v1/user/profile", (route) => {
      profileRequestMade = true;
      route.continue();
    });

    await page.goto("/about-us");

    await expect(page.getByText("About Us")).toBeVisible();
    expect(profileRequestMade).toBe(false);
  });
});
