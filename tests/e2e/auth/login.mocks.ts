import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  InitiateAuthResponseSchema,
  InitiateAuthRequestSchema,
  RespondToAuthChallengeResponseSchema,
  RespondToAuthChallengeRequestSchema,
} from "@/clients/gen/platform/public/models/identity/Auth_pb";
import { UserProfileResponseSchema } from "@/clients/gen/broking/Profile_pb";
import { OnboardingStateSchema } from "@/clients/gen/platform/public/models/identity/Onboarding_pb";
import {
  ErrorResponseSchema,
  ConfigDataResponseSchema,
  AppConfigType,
  AppConfigValueType,
} from "@/clients/gen/platform/public/models/identity/Common_pb";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";

import { expect } from "@playwright/test";

// Test: should navigate to email when next step is EMAIL
export function emailNavigationMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function emailNavigationMockAuthenticate(page: Page) {
  const mockData = {
    token: faker.string.uuid(),
    refreshToken: faker.string.uuid(),
    userId: faker.string.uuid(),
  };

  page.route(`*/**/v1/auth/authenticate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(
        RespondToAuthChallengeRequestSchema,
        requestBuffer
      );
      const requestJson = toJson(
        RespondToAuthChallengeRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson.answer).toBeDefined();
      expect(requestJson.userId).toBeDefined();
      expect(requestJson.challengeId).toBeDefined();
    }

    const responseData = {
      authenticationResult: {
        token: mockData.token,
        refreshToken: mockData.refreshToken,
        tokenType: "Bearer",
        expiresIn: "3600",
      },
      userId: mockData.userId,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          RespondToAuthChallengeResponseSchema,
          create(RespondToAuthChallengeResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function emailNavigationMockUserProfile(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    name: faker.person.fullName(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    panNumber: faker.finance.accountNumber({ length: 10 }),
    aadharNumber: faker.finance.accountNumber({ length: 12 }),
  };

  page.route(`*/**/v1/user/profile`, (route) => {
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        id: mockData.userId,
        name: mockData.name,
        firstName: mockData.firstName,
        lastName: mockData.lastName,
      },
      profileData: {
        id: mockData.userId,
        panNumber: mockData.panNumber,
        aadharNumber: mockData.aadharNumber,
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function emailNavigationMockOnboardingState(page: Page) {
  const mockData = {
    nextStep: KycType.EMAIL,
  };

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData = {
      next: mockData.nextStep,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should navigate to name when next step is NAME
export function nameNavigationMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function nameNavigationMockAuthenticate(page: Page) {
  const mockData = {
    token: faker.string.uuid(),
    refreshToken: faker.string.uuid(),
    userId: faker.string.uuid(),
  };

  page.route(`*/**/v1/auth/authenticate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(
        RespondToAuthChallengeRequestSchema,
        requestBuffer
      );
      const requestJson = toJson(
        RespondToAuthChallengeRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson.answer).toBeDefined();
      expect(requestJson.userId).toBeDefined();
      expect(requestJson.challengeId).toBeDefined();
    }

    const responseData = {
      authenticationResult: {
        token: mockData.token,
        refreshToken: mockData.refreshToken,
        tokenType: "Bearer",
        expiresIn: "3600",
      },
      userId: mockData.userId,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          RespondToAuthChallengeResponseSchema,
          create(RespondToAuthChallengeResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function nameNavigationMockUserProfile(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    name: faker.person.fullName(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    panNumber: faker.finance.accountNumber({ length: 10 }),
    aadharNumber: faker.finance.accountNumber({ length: 12 }),
  };

  page.route(`*/**/v1/user/profile`, (route) => {
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        id: mockData.userId,
        name: mockData.name,
        firstName: mockData.firstName,
        lastName: mockData.lastName,
      },
      profileData: {
        id: mockData.userId,
        panNumber: mockData.panNumber,
        aadharNumber: mockData.aadharNumber,
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function nameNavigationMockOnboardingState(page: Page) {
  const mockData = {
    nextStep: KycType.NAME,
  };

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData = {
      next: mockData.nextStep,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should navigate to home when next step is unknown
export function unknownStepMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function unknownStepMockAuthenticate(page: Page) {
  const mockData = {
    token: faker.string.uuid(),
    refreshToken: faker.string.uuid(),
    userId: faker.string.uuid(),
  };

  page.route(`*/**/v1/auth/authenticate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(
        RespondToAuthChallengeRequestSchema,
        requestBuffer
      );
      const requestJson = toJson(
        RespondToAuthChallengeRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson.answer).toBeDefined();
      expect(requestJson.userId).toBeDefined();
      expect(requestJson.challengeId).toBeDefined();
    }

    const responseData = {
      authenticationResult: {
        token: mockData.token,
        refreshToken: mockData.refreshToken,
        tokenType: "Bearer",
        expiresIn: "3600",
      },
      userId: mockData.userId,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          RespondToAuthChallengeResponseSchema,
          create(RespondToAuthChallengeResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function unknownStepMockUserProfile(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    name: faker.person.fullName(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    panNumber: faker.finance.accountNumber({ length: 10 }),
    aadharNumber: faker.finance.accountNumber({ length: 12 }),
  };

  page.route(`*/**/v1/user/profile`, (route) => {
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        id: mockData.userId,
        name: mockData.name,
        firstName: mockData.firstName,
        lastName: mockData.lastName,
      },
      profileData: {
        id: mockData.userId,
        panNumber: mockData.panNumber,
        aadharNumber: mockData.aadharNumber,
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function unknownStepMockOnboardingState(page: Page) {
  const mockData = {
    nextStep: KycType.KYC_TYPE_UNKNOWN,
  };

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData = {
      next: mockData.nextStep,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should navigate to home when no next step is provided
export function noNextStepMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function noNextStepMockAuthenticate(page: Page) {
  const mockData = {
    token: faker.string.uuid(),
    refreshToken: faker.string.uuid(),
    userId: faker.string.uuid(),
  };

  page.route(`*/**/v1/auth/authenticate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(
        RespondToAuthChallengeRequestSchema,
        requestBuffer
      );
      const requestJson = toJson(
        RespondToAuthChallengeRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson.answer).toBeDefined();
      expect(requestJson.userId).toBeDefined();
      expect(requestJson.challengeId).toBeDefined();
    }

    const responseData = {
      authenticationResult: {
        token: mockData.token,
        refreshToken: mockData.refreshToken,
        tokenType: "Bearer",
        expiresIn: "3600",
      },
      userId: mockData.userId,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          RespondToAuthChallengeResponseSchema,
          create(RespondToAuthChallengeResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function noNextStepMockUserProfile(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    name: faker.person.fullName(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    panNumber: faker.finance.accountNumber({ length: 10 }),
    aadharNumber: faker.finance.accountNumber({ length: 12 }),
  };

  page.route(`*/**/v1/user/profile`, (route) => {
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        id: mockData.userId,
        name: mockData.name,
        firstName: mockData.firstName,
        lastName: mockData.lastName,
      },
      profileData: {
        id: mockData.userId,
        panNumber: mockData.panNumber,
        aadharNumber: mockData.aadharNumber,
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function noNextStepMockOnboardingState(page: Page) {
  const mockData = {
    nextStep: undefined,
  };

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData = {};

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should display error toast for unexpected next step
export function unexpectedStepMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function unexpectedStepMockAuthenticate(page: Page) {
  const mockData = {
    token: faker.string.uuid(),
    refreshToken: faker.string.uuid(),
    userId: faker.string.uuid(),
  };

  page.route(`*/**/v1/auth/authenticate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(
        RespondToAuthChallengeRequestSchema,
        requestBuffer
      );
      const requestJson = toJson(
        RespondToAuthChallengeRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson.answer).toBeDefined();
      expect(requestJson.userId).toBeDefined();
      expect(requestJson.challengeId).toBeDefined();
    }

    const responseData = {
      authenticationResult: {
        token: mockData.token,
        refreshToken: mockData.refreshToken,
        tokenType: "Bearer",
        expiresIn: "3600",
      },
      userId: mockData.userId,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          RespondToAuthChallengeResponseSchema,
          create(RespondToAuthChallengeResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function unexpectedStepMockUserProfile(page: Page) {
  page.route(`*/**/v1/user/profile`, (route) => {
    const userId = faker.string.uuid();
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        id: userId,
        name: faker.person.fullName(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
      },
      profileData: {
        id: userId,
        panNumber: faker.finance.accountNumber({ length: 10 }),
        aadharNumber: faker.finance.accountNumber({ length: 12 }),
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });
}

export function unexpectedStepMockOnboardingState(page: Page) {
  const mockData = {
    nextStep: KycType.PAN_KYC,
  };

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData = {
      next: mockData.nextStep,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should display error message when API call fails due to network error
export function networkErrorMockInitiateAuthFailure(page: Page) {
  page.route("*/**/v3/auth/initiate*", (route) => {
    route.abort("failed");
  });
}

// Test: should display server error message when API returns 400 status code
export function serverErrorMockInitiateAuth(page: Page) {
  page.route("*/**/v3/auth/initiate*", (route) => {
    const responseData = {
      errorMessage: "Invalid mobile number format",
      errorCode: "INVALID_MOBILE",
    };

    route.fulfill({
      status: 400,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });
}

// Test: should display mobile number to which OTP was sent
export function otpDisplayMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should display validation message for empty OTP
export function emptyOtpValidationMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should display invalid OTP error when server returns 400
export function invalidOtpMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function invalidOtpMockAuthenticateError(page: Page) {
  page.route("*/**/v1/auth/authenticate*", (route) => {
    const responseData = {
      errorMessage: "Invalid OTP",
      errorCode: "INVALID_OTP",
    };

    route.fulfill({
      status: 400,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });
}

// Test: should allow entering OTP and clear validation errors
export function clearValidationMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function clearValidationMockAuthenticate(page: Page) {
  const mockData = {
    token: faker.string.uuid(),
    refreshToken: faker.string.uuid(),
    userId: faker.string.uuid(),
  };

  page.route(`*/**/v1/auth/authenticate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(
        RespondToAuthChallengeRequestSchema,
        requestBuffer
      );
      const requestJson = toJson(
        RespondToAuthChallengeRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson.answer).toBeDefined();
      expect(requestJson.userId).toBeDefined();
      expect(requestJson.challengeId).toBeDefined();
    }

    const responseData = {
      authenticationResult: {
        token: mockData.token,
        refreshToken: mockData.refreshToken,
        tokenType: "Bearer",
        expiresIn: "3600",
      },
      userId: mockData.userId,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          RespondToAuthChallengeResponseSchema,
          create(RespondToAuthChallengeResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function clearValidationMockUserProfile(page: Page) {
  page.route(`*/**/v1/user/profile`, (route) => {
    const userId = faker.string.uuid();
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        id: userId,
        name: faker.person.fullName(),
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
      },
      profileData: {
        id: userId,
        panNumber: faker.finance.accountNumber({ length: 10 }),
        aadharNumber: faker.finance.accountNumber({ length: 12 }),
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });
}

export function clearValidationMockOnboardingState(page: Page) {
  const mockData = {
    nextStep: undefined,
  };

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData = {};

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

// Test: should display network error message when OTP verification fails due to network error
export function otpNetworkErrorMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function otpNetworkErrorMockAuthenticateFailure(page: Page) {
  page.route("*/**/v1/auth/authenticate*", (route) => {
    route.abort("failed");
  });
}

// Test: should display server error message when OTP verification returns 500 status code
export function otpServerErrorMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function otpServerErrorMockAuthenticateFailure(page: Page) {
  page.route("*/**/v1/auth/authenticate*", (route) => {
    route.fulfill({
      status: 500,
      body: JSON.stringify({ error: "Internal server error" }),
    });
  });
}

// Test: should handle timeout errors gracefully
export function timeoutErrorMockInitiateAuth(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    challengeId: faker.string.uuid(),
  };

  page.route(`*/**/v3/auth/initiate`, async (route) => {
    // Validate the request
    const requestBuffer = route.request().postDataBuffer();
    if (requestBuffer) {
      const requestData = fromBinary(InitiateAuthRequestSchema, requestBuffer);
      const requestJson = toJson(
        InitiateAuthRequestSchema,
        requestData
      ) as Record<string, unknown>;

      // Perform assertions on the request
      expect(requestJson).toMatchObject({
        authType: "MOBILE_OTP",
      });
      expect(requestJson.mobileLoginRequest).toBeDefined();
      const request = requestJson as Record<string, unknown>;
      const mobileLoginRequest = request.mobileLoginRequest as Record<
        string,
        unknown
      >;
      expect(mobileLoginRequest.mobile).toBeDefined();
      expect(mobileLoginRequest.countryCode).toBe("91");
      expect(mobileLoginRequest.encryptedMobile).toBeDefined();
      expect(mobileLoginRequest.encryptionKey).toBeDefined();
      expect(request.userDevice).toBeDefined();
    }

    const responseData = {
      userId: mockData.userId,
      result: {
        case: "otpChallenge" as const,
        value: {
          challengeId: mockData.challengeId,
          expiry: BigInt(Date.now() + 300000),
        },
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateAuthResponseSchema,
          create(InitiateAuthResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

export function timeoutErrorMockAuthenticateTimeout(page: Page) {
  page.route("*/**/v1/auth/authenticate*", (route) => {
    route.abort("timedout");
  });
}

// Mock for identity service config API call
export function mockIdentityConfig(page: Page) {
  // This is a valid RSA public key for testing purposes only
  const mockPublicKey =
    "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuXIzqCPmtWM8Ez4StlwczIy55pFDNh0uPq4t6Fa8wt8HuiqeozRWdpQJkQqvhqEhazTLTZ2Opz/obn2ulIwz4Rtvbe7V5T7BEMG1cryFNK+eew6aHSjFTa7X8/vTyZbcffIPuWvMwkZjZgo/hljnEYXjBkLWHUVlh5r1gP7HRaZNPCgf+JTaEbp8N7sEVqniwLNITVRjSZ/iwFNN20CwoHngtWMcujIAwKLvOBHb+X/ALz7dtDF0dwnviwBqT5vryEwHOXYYPggz1rAXgxCoc9FJYIzWrzwQ+vu1r8zk4U/JnebvQ5KQakt7UT1oWmpS3IEl1sCXnczJ87Q52l2apwIDAQAB";

  page.route("*/**/v1/config/list", (route) => {
    const responseData = {
      data: [
        {
          configName: AppConfigType.ENCRYPTION_PUBLIC_KEY,
          configValue: `-----BEGIN PUBLIC KEY-----\n${mockPublicKey}\n-----END PUBLIC KEY-----`,
          configType: AppConfigValueType.TEXT_TYPE,
          minVersion: 1,
          maxVersion: 999,
          description: "RSA public key for encryption",
        },
      ],
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          ConfigDataResponseSchema,
          create(ConfigDataResponseSchema, responseData)
        )
      ),
    });
  });
}
