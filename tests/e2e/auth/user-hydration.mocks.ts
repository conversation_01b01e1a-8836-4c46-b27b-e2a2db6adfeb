import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import { UserProfileResponseSchema } from "@/clients/gen/broking/Profile_pb";
import { expect } from "@playwright/test";

// These functions are not needed when using storage states, but kept for consistency

// Test: should hydrate user store with profile data when auth token is available
export function mockUserProfileSuccess(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    mobile: faker.string.numeric(10),
    panNumber: faker.finance.accountNumber({ length: 10 }),
    aadharNumber: faker.finance.accountNumber({ length: 12 }),
  };

  page.route("*/**/v1/user/profile", (route) => {
    // Validate that the request has authorization header
    const headers = route.request().headers();
    expect(headers.authorization).toBeDefined();
    expect(headers.authorization).toMatch(/^Bearer /);

    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        id: mockData.userId,
        firstName: mockData.firstName,
        lastName: mockData.lastName,
        email: mockData.email,
        mobile: mockData.mobile,
        name: `${mockData.firstName} ${mockData.lastName}`,
      },
      profileData: {
        id: mockData.userId,
        panNumber: mockData.panNumber,
        aadharNumber: mockData.aadharNumber,
        dob: faker.date.past().toISOString(),
        gender: 1,
        incomeRange: 1,
        employmentType: 1,
        tradingExperience: 1,
        maritalStatus: 1,
        fatherName: faker.person.firstName(),
        motherName: faker.person.firstName(),
        incomeTaxDepartmentName: `${mockData.firstName} ${mockData.lastName}`,
      },
      lifeTimeStatus: "ONBOARDED",
      currentStatus: "ACTIVE",
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}
