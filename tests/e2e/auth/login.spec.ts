import { test, expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  emailNavigationMockInitiateAuth,
  emailNavigationMockAuthenticate,
  emailNavigationMockUserProfile,
  emailNavigationMockOnboardingState,
  nameNavigationMockInitiateAuth,
  nameNavigationMockAuthenticate,
  nameNavigationMockUserProfile,
  nameNavigationMockOnboardingState,
  unknownStepMockInitiateAuth,
  unknownStepMockAuthenticate,
  unknownStepMockUserProfile,
  unknownStepMockOnboardingState,
  noNextStepMockInitiateAuth,
  noNextStepMockAuthenticate,
  noNextStepMockUserProfile,
  noNextStepMockOnboardingState,
  unexpectedStepMockInitiateAuth,
  unexpectedStepMockAuthenticate,
  unexpectedStepMockUserProfile,
  unexpectedStepMockOnboardingState,
  networkErrorMockInitiateAuthFailure,
  serverErrorMockInitiateAuth,
  otpDisplayMockInitiateAuth,
  emptyOtpValidationMockInitiateAuth,
  invalidOtpMockInitiateAuth,
  invalidOtpMockAuthenticateError,
  clearValidationMockInitiateAuth,
  clearValidationMockAuthenticate,
  clearValidationMockUserProfile,
  clearValidationMockOnboardingState,
  otpNetworkErrorMockInitiateAuth,
  otpNetworkErrorMockAuthenticateFailure,
  otpServerErrorMockInitiateAuth,
  otpServerErrorMockAuthenticateFailure,
  timeoutErrorMockInitiateAuth,
  timeoutErrorMockAuthenticateTimeout,
  mockIdentityConfig,
} from "./login.mocks";

test.describe("Authentication - Login", () => {
  test.beforeEach(async ({ page }) => {
    // Mock identity service config API call for public key fetching
    mockIdentityConfig(page);
  });

  test.describe("Alpha Onboarding Flow Navigation", () => {
    test("should navigate to email when next step is EMAIL", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);
      const otpValue = faker.string.numeric(6);

      emailNavigationMockInitiateAuth(page);
      emailNavigationMockAuthenticate(page);
      emailNavigationMockUserProfile(page);
      emailNavigationMockOnboardingState(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();
      await page.getByLabel("OTP").fill(otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await page.waitForURL("/onboarding/email");
      const cookies = await page.context().cookies();
      expect(cookies.find((c) => c.name === "sm_access_token")).toBeTruthy();
      expect(cookies.find((c) => c.name === "sm_refresh_token")).toBeTruthy();
    });

    test("should navigate to name when next step is NAME", async ({ page }) => {
      const mobileNumber = faker.string.numeric(10);
      const otpValue = faker.string.numeric(6);

      nameNavigationMockInitiateAuth(page);
      nameNavigationMockAuthenticate(page);
      nameNavigationMockUserProfile(page);
      nameNavigationMockOnboardingState(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();
      await page.getByRole("textbox", { name: "OTP" }).fill(otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await page.waitForURL("/onboarding/name");
      const cookies = await page.context().cookies();
      expect(cookies.find((c) => c.name === "sm_access_token")).toBeTruthy();
      expect(cookies.find((c) => c.name === "sm_refresh_token")).toBeTruthy();
    });

    test("should navigate to home when next step is unknown", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);
      const otpValue = faker.string.numeric(6);

      unknownStepMockInitiateAuth(page);
      unknownStepMockAuthenticate(page);
      unknownStepMockUserProfile(page);
      unknownStepMockOnboardingState(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();
      await page.getByRole("textbox", { name: "OTP" }).fill(otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await page.waitForURL("/");
      const cookies = await page.context().cookies();
      expect(cookies.find((c) => c.name === "sm_access_token")).toBeTruthy();
      expect(cookies.find((c) => c.name === "sm_refresh_token")).toBeTruthy();
    });

    test("should navigate to home when no next step is provided", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);
      const otpValue = faker.string.numeric(6);

      noNextStepMockInitiateAuth(page);
      noNextStepMockAuthenticate(page);
      noNextStepMockUserProfile(page);
      noNextStepMockOnboardingState(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();
      await page.getByRole("textbox", { name: "OTP" }).fill(otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await page.waitForURL("/");
      const cookies = await page.context().cookies();
      expect(cookies.find((c) => c.name === "sm_access_token")).toBeTruthy();
      expect(cookies.find((c) => c.name === "sm_refresh_token")).toBeTruthy();
    });

    test("should display error toast for unexpected next step", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);
      const otpValue = faker.string.numeric(6);

      unexpectedStepMockInitiateAuth(page);
      unexpectedStepMockAuthenticate(page);
      unexpectedStepMockUserProfile(page);
      unexpectedStepMockOnboardingState(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();
      await page.getByRole("textbox", { name: "OTP" }).fill(otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      // Should display error toast since PAN_KYC is not handled in getPathForNextAlphaStep
      await expect(page.getByText("Cannot handle the step: 11")).toBeVisible();

      // Should stay on the OTP page
      expect(page.url()).toContain("/authentication/mobile-otp");
    });
  });

  test.describe("Mobile Number Validation", () => {
    test("should only allow numeric input and limit to 10 digits", async ({
      page,
    }) => {
      await page.goto("/authentication/mobile-number");
      const mobileInput = page.getByRole("textbox", { name: "Mobile number" });

      await mobileInput.fill("abc123def456");
      await expect(mobileInput).toHaveValue("123456");

      await mobileInput.fill("123-456-789");
      await expect(mobileInput).toHaveValue("123456789");

      await mobileInput.fill("12345678901234");
      await expect(mobileInput).toHaveValue("1234567890");
    });

    test("should display validation message for mobile number that is too short", async ({
      page,
    }) => {
      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill("123456789");
      await page.getByRole("button", { name: "Verify mobile number" }).click();
      await expect(
        page.getByText("Please enter a valid 10-digit mobile number")
      ).toBeVisible();
    });

    test("should display validation message for empty mobile number", async ({
      page,
    }) => {
      await page.goto("/authentication/mobile-number");
      await page.getByRole("button", { name: "Verify mobile number" }).click();
      await expect(page.getByText("Mobile number is required")).toBeVisible();
    });
  });

  test.describe("Mobile Number Error Handling", () => {
    test("should display error message when API call fails due to network error", async ({
      page,
    }) => {
      await page.goto("/authentication/mobile-number");

      networkErrorMockInitiateAuthFailure(page);

      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(faker.string.numeric(10));
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });

    test("should display server error message when API returns 400 status code", async ({
      page,
    }) => {
      await page.goto("/authentication/mobile-number");

      const errorMessage = "Invalid mobile number format";
      serverErrorMockInitiateAuth(page);

      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(faker.string.numeric(10));
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      await expect(page.getByText(errorMessage)).toBeVisible();
    });
  });

  test.describe("OTP Screen", () => {
    test("should display mobile number to which OTP was sent", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);

      otpDisplayMockInitiateAuth(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      // Wait for navigation to OTP page
      await page.waitForURL(/.*mobile-otp.*/);

      // Verify the mobile number is displayed in the description
      await expect(
        page.getByText(new RegExp(`.*sent.*\\+91.*${mobileNumber}.*`, "i"))
      ).toBeVisible();
    });

    test("should redirect to mobile number page if required parameters are missing", async ({
      page,
    }) => {
      // Test missing all parameters
      await page.goto("/authentication/mobile-otp");
      await page.waitForURL("/authentication/mobile-number");

      // Test missing userId
      await page.goto(
        "/authentication/mobile-otp?challenge=test&mobileNumber=1234567890"
      );
      await page.waitForURL("/authentication/mobile-number");

      // Test missing challenge
      await page.goto(
        "/authentication/mobile-otp?userId=test&mobileNumber=1234567890"
      );
      await page.waitForURL("/authentication/mobile-number");

      // Test missing mobileNumber
      await page.goto("/authentication/mobile-otp?userId=test&challenge=test");
      await page.waitForURL("/authentication/mobile-number");
    });
  });

  test.describe("OTP Validation", () => {
    test("should display validation message for empty OTP", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);

      emptyOtpValidationMockInitiateAuth(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      // Try to submit without entering OTP
      await page.getByRole("button", { name: "Submit OTP" }).click();
      await expect(page.getByText("OTP is required")).toBeVisible();
    });

    test("should display invalid OTP error when server returns 400", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);

      invalidOtpMockInitiateAuth(page);

      // Mock authentication to return 400 error
      invalidOtpMockAuthenticateError(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      await page
        .getByRole("textbox", { name: "OTP" })
        .fill(faker.string.numeric(6));
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await expect(page.getByText("Invalid OTP")).toBeVisible();
    });

    test("should allow entering OTP and clear validation errors", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);
      const otpValue = faker.string.numeric(6);

      clearValidationMockInitiateAuth(page);
      clearValidationMockAuthenticate(page);
      clearValidationMockUserProfile(page);
      clearValidationMockOnboardingState(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      // First submit without OTP to trigger validation
      await page.getByRole("button", { name: "Submit OTP" }).click();
      await expect(page.getByText("OTP is required")).toBeVisible();

      // Then enter OTP and verify validation error is cleared
      const otpInput = page.getByRole("textbox", { name: "OTP" });
      await otpInput.fill(otpValue);
      await expect(otpInput).toHaveValue(otpValue);
      await expect(page.getByText("OTP is required")).not.toBeVisible();
    });
  });

  test.describe("OTP Error Handling", () => {
    test("should display network error message when OTP verification fails due to network error", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);

      otpNetworkErrorMockInitiateAuth(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      // Mock network failure for authentication
      otpNetworkErrorMockAuthenticateFailure(page);

      await page
        .getByRole("textbox", { name: "OTP" })
        .fill(faker.string.numeric(6));
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });

    test("should display server error message when OTP verification returns 500 status code", async ({
      page,
    }) => {
      const mobileNumber = faker.string.numeric(10);

      otpServerErrorMockInitiateAuth(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      // Mock server error for authentication
      otpServerErrorMockAuthenticateFailure(page);

      await page
        .getByRole("textbox", { name: "OTP" })
        .fill(faker.string.numeric(6));
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await expect(page.getByText("Oops! Something went wrong")).toBeVisible();
    });

    test("should handle timeout errors gracefully", async ({ page }) => {
      const mobileNumber = faker.string.numeric(10);

      timeoutErrorMockInitiateAuth(page);

      await page.goto("/authentication/mobile-number");
      await page
        .getByRole("textbox", { name: "Mobile number" })
        .fill(mobileNumber);
      await page.getByRole("button", { name: "Verify mobile number" }).click();

      // Mock timeout for authentication
      timeoutErrorMockAuthenticateTimeout(page);

      await page
        .getByRole("textbox", { name: "OTP" })
        .fill(faker.string.numeric(6));
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });
  });
});
