import { test, expect } from "@playwright/test";
import { mockBondDetailsForReturnsSchedule } from "./page-titles.mocks";

test.describe("Sheet Pages - Page Titles", () => {
  test.describe("Empty Page Titles", () => {
    test("should have empty page title for mandatory-documents sheet", async ({
      page,
    }) => {
      await page.goto("/sheets/mandatory-documents");

      // Verify the page loads correctly
      await expect(page.getByText("Mandatory and")).toBeVisible();
      await expect(page.getByText("important documents")).toBeVisible();

      // Verify page title is empty
      await expect(page).toHaveTitle("");
    });

    test("should have empty page title for find-demat-by-broker sheet", async ({
      page,
    }) => {
      await page.goto("/sheets/find-demat-by-broker");

      // Verify the page loads correctly
      await expect(page.getByText("Choose your broker")).toBeVisible();

      // Verify page title is empty
      await expect(page).toHaveTitle("");
    });

    test("should have empty page title for dont-have-demat sheet", async ({
      page,
    }) => {
      await page.goto("/sheets/dont-have-demat");

      // Verify the page loads correctly
      await expect(
        page.getByText("A demat account is mandatory to purchase bonds")
      ).toBeVisible();

      // Verify page title is empty
      await expect(page).toHaveTitle("");
    });

    test("should have empty page title for info sheet with question and answer", async ({
      page,
    }) => {
      await page.goto("/sheets/info?question=test_question&answer=test_answer");

      // Verify the page loads correctly (content will depend on translation service)
      await expect(page.locator("body")).toBeVisible();

      // Verify page title is empty
      await expect(page).toHaveTitle("");
    });

    test("should have empty page title for first-time-investor sheet", async ({
      page,
    }) => {
      await page.goto("/sheets/first-time-investor");

      // Verify the page loads correctly
      await expect(
        page.getByText(
          "Before we continue, is this your first time investing in bonds?"
        )
      ).toBeVisible();

      // Verify page title is empty
      await expect(page).toHaveTitle("");
    });

    test("should have empty page title for bonds returns schedule page", async ({
      page,
    }) => {
      mockBondDetailsForReturnsSchedule(page);

      await page.goto(
        "/bonds/test-issuer/test-bond-id/returns-schedule?units=1"
      );

      // Verify the page loads correctly
      await expect(
        page.locator("h1.text-heading3").getByText("Returns schedule")
      ).toBeVisible();
      await expect(page.getByText("Total returns:")).toBeVisible();

      // Verify page title is empty
      await expect(page).toHaveTitle("");
    });
  });
});
