import { test, expect } from "@playwright/test";
import { mockBondDetailsForReturnsSchedule } from "./sheet-title.mocks";

test.describe("Sheet Pages - Title Verification", () => {
  test("should have empty title for mandatory-documents sheet", async ({
    page,
  }) => {
    await page.goto("/sheets/mandatory-documents");

    // Verify the page loads successfully
    await expect(page.getByText("Mandatory and")).toBeVisible();

    // Verify the title is empty
    await expect(page).toHaveTitle("");
  });

  test("should have empty title for find-demat-by-broker sheet", async ({
    page,
  }) => {
    await page.goto("/sheets/find-demat-by-broker");

    // Verify the page loads successfully
    await expect(page.getByText("Choose your broker")).toBeVisible();

    // Verify the title is empty
    await expect(page).toHaveTitle("");
  });

  test("should have empty title for dont-have-demat sheet", async ({
    page,
  }) => {
    await page.goto("/sheets/dont-have-demat");

    // Verify the page loads successfully
    await expect(page.getByText("A demat account is mandatory")).toBeVisible();

    // Verify the title is empty
    await expect(page).toHaveTitle("");
  });

  test("should have empty title for info sheet with query parameters", async ({
    page,
  }) => {
    await page.goto("/sheets/info?question=test-question&answer=test-answer");

    // Verify the page loads successfully by checking for the main content container
    await expect(page.locator(".flex.h-full.flex-col.gap-8.p-5")).toBeVisible();

    // Verify the title is empty
    await expect(page).toHaveTitle("");
  });

  test("should have empty title for first-time-investor sheet", async ({
    page,
  }) => {
    await page.goto("/sheets/first-time-investor");

    // Verify the page loads successfully
    await expect(
      page.getByText("Before we continue, is this your first time")
    ).toBeVisible();

    // Verify the title is empty
    await expect(page).toHaveTitle("");
  });

  test("should have empty title for returns schedule page", async ({
    page,
  }) => {
    mockBondDetailsForReturnsSchedule(page);

    await page.goto("/bonds/test-issuer/TEST123/returns-schedule");
    await expect(
      page.locator("h1.text-heading3").getByText("Returns schedule")
    ).toBeVisible();

    // Verify the title is empty
    await expect(page).toHaveTitle("");
  });
});
