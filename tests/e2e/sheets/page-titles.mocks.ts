import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import { BondDetailsResponseSchema } from "@/clients/gen/broking/BondDetails_pb";

// Mock for bonds returns schedule page
export function mockBondDetailsForReturnsSchedule(page: Page) {
  const responseData: MessageInitShape<typeof BondDetailsResponseSchema> = {
    bondDetails: {
      id: faker.string.uuid(),
      bondName: faker.company.name() + " Bond",
      xirr: faker.number.float({ min: 6, max: 12, fractionDigits: 2 }),
      timeLeftToMaturity: "2 years 3 months",
      maturityDate: "15-Mar-2027",
      issueDate: "15-Mar-2025",
      pricePerBond: faker.number.float({
        min: 1000,
        max: 10000,
        fractionDigits: 2,
      }),
      faceValue: "1000",
      rating: "AAA",
      interestPayment: "Quarterly",
      aboutTheInstitution: {
        title: faker.company.name(),
        bondInstitutionName: faker.company.name(),
        slug: faker.lorem.slug(),
      },
      interestPaymentAndPrincipalRepaymentSchedule: [
        {
          type: "INTEREST_PAYMENT",
          interestAmount: 25.0,
          principalAmount: 0.0,
          date: "15-06-2025",
          dateTime: "2025-06-15T00:00:00Z",
        },
        {
          type: "INTEREST_PAYMENT",
          interestAmount: 25.0,
          principalAmount: 0.0,
          date: "15-09-2025",
          dateTime: "2025-09-15T00:00:00Z",
        },
        {
          type: "PRINCIPAL_REPAYMENT",
          interestAmount: 25.0,
          principalAmount: 1000.0,
          date: "15-03-2027",
          dateTime: "2027-03-15T00:00:00Z",
        },
      ],
    },
  };

  page.route("*/**/v1/bond-details/*", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          BondDetailsResponseSchema,
          create(BondDetailsResponseSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return responseData;
}
