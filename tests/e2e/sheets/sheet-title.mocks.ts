import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import { BondDetailsResponseSchema } from "@/clients/gen/broking/BondDetails_pb";

// Test: should load returns schedule page and display bond data
export function mockBondDetailsForReturnsSchedule(page: Page) {
  // Generate realistic interest payment schedule
  const interestPaymentSchedule = [
    {
      type: "Interest",
      interestAmount: 50.0,
      date: "2024-06-15",
      principalAmount: 0.0,
      dateTime: "2024-06-15T00:00:00Z",
    },
    {
      type: "Interest",
      interestAmount: 50.0,
      date: "2024-12-15",
      principalAmount: 0.0,
      dateTime: "2024-12-15T00:00:00Z",
    },
    {
      type: "Principal + Interest",
      interestAmount: 50.0,
      date: "2025-06-15",
      principalAmount: 1000.0,
      dateTime: "2025-06-15T00:00:00Z",
    },
  ];

  const bondDetails = {
    isinCode: faker.string.alphanumeric(12).toUpperCase(),
    bondName: faker.company.name() + " Bond",
    issueDate: "2024-06-15",
    maturityDate: "2025-06-15",
    couponRate: 5.0,
    faceValue: "1000",
    interestPaymentAndPrincipalRepaymentSchedule: interestPaymentSchedule,
    aboutTheInstitution: {
      name: faker.company.name(),
      slug: faker.lorem.slug(),
    },
  };

  const responseData: MessageInitShape<typeof BondDetailsResponseSchema> = {
    bondDetails: bondDetails,
  };

  page.route("*/**/v1/bond-details/*", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          BondDetailsResponseSchema,
          create(BondDetailsResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}
