import { test, expect } from "@playwright/test";
import {
  mockParentDetailsSubmissionError,
  mockParentDetailsSubmissionNetworkError,
  mockParentDetailsSubmissionSlow,
} from "./parent-details.mocks";

test.describe("Workflow - Parent Details", () => {
  test.describe("Happy Path", () => {
    test("should load parent details page and show form fields", async ({
      page,
    }) => {
      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Verify all form fields are visible
      await expect(
        page.getByRole("textbox", { name: "Father's full name" })
      ).toBeVisible();
      await expect(
        page.getByRole("textbox", { name: "Mother's full name" })
      ).toBeVisible();

      // Verify submit button is present
      await expect(page.getByRole("button", { name: "Proceed" })).toBeVisible();
    });

    test("should pre-populate form with query parameters", async ({ page }) => {
      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Form should be pre-populated with the query parameters
      // Note: The actual implementation might store these values in form state
      // This test verifies the page loads correctly with query parameters
      await expect(
        page.getByRole("textbox", { name: "Father's full name" })
      ).toBeVisible();
    });
  });

  test.describe("Form Validation", () => {
    test("should show validation error when father's name is empty", async ({
      page,
    }) => {
      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Fill other fields but leave father's name empty
      await page
        .getByRole("textbox", { name: "Mother's full name" })
        .fill("Jane Doe");

      // Try to submit
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show validation error
      await expect(page.getByText("Father's name is required")).toBeVisible();
    });

    test("should show validation error when mother's name is empty", async ({
      page,
    }) => {
      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Fill other fields but leave mother's name empty
      await page
        .getByRole("textbox", { name: "Father's full name" })
        .fill("John Doe Sr");

      // Try to submit
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show validation error
      await expect(page.getByText("Mother's name is required")).toBeVisible();
    });

    test("should show validation error when father's name is too short", async ({
      page,
    }) => {
      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Fill fields with short father's name
      await page
        .getByRole("textbox", { name: "Father's full name" })
        .fill("Jo");
      await page
        .getByRole("textbox", { name: "Mother's full name" })
        .fill("Jane Doe");

      // Try to submit
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show validation error
      await expect(
        page.getByText("Father's name must be at least 4 characters")
      ).toBeVisible();
    });
  });

  test.describe("API Error Handling", () => {
    test("should show error toast when submission fails with server error", async ({
      page,
    }) => {
      mockParentDetailsSubmissionError(page, 500, {
        errorMessage: "Internal server error",
      });

      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Fill and submit form
      await page
        .getByRole("textbox", { name: "Father's full name" })
        .fill("John Doe Sr");
      await page
        .getByRole("textbox", { name: "Mother's full name" })
        .fill("Jane Doe");

      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show error toast
      await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });
      await expect(page.getByText("Internal server error")).toBeVisible();
    });

    test("should show error toast when submission fails with network error", async ({
      page,
    }) => {
      mockParentDetailsSubmissionNetworkError(page);

      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Fill and submit form
      await page
        .getByRole("textbox", { name: "Father's full name" })
        .fill("John Doe Sr");
      await page
        .getByRole("textbox", { name: "Mother's full name" })
        .fill("Jane Doe");

      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show error toast
      await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during form submission", async ({
      page,
    }) => {
      mockParentDetailsSubmissionSlow(page);

      await page.goto(
        "/workflow/demat-account-opening/parent-details?employmentType=1&incomeRange=3&tradingExperience=1&maritalStatus=1"
      );

      // Fill form
      await page
        .getByRole("textbox", { name: "Father's full name" })
        .fill("John Doe Sr");
      await page
        .getByRole("textbox", { name: "Mother's full name" })
        .fill("Jane Doe");

      const submitButton = page.getByRole("button", { name: "Proceed" });
      await submitButton.click();

      // Should show loading state
      await expect(submitButton.locator("svg")).toBeVisible();
    });
  });
});
