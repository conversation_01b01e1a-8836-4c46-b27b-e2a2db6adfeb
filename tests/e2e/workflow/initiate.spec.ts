import { test, expect } from "@playwright/test";
import {
  mockWorkflowInitiate,
  mockWorkflowInitiateError,
  mockWorkflowInitiateNetworkError,
} from "./initiate.mocks";

test.describe("Workflow - Initiate", () => {
  test.describe("Happy Path", () => {
    test("should initiate workflow and redirect to next step", async ({
      page,
    }) => {
      // Mock successful workflow initiation
      mockWorkflowInitiate(page, {
        nextStep: "USER_PROFILE",
        workflowStatus: "INITIATED",
      });

      await page.goto("/workflow/demat-account-opening");

      // Should redirect to the next step based on workflow response
      await page.waitForURL("/workflow/demat-account-opening/basic-details");
    });

    test("should redirect to status page when workflow is completed", async ({
      page,
    }) => {
      // Mock workflow that's already completed
      mockWorkflowInitiate(page, {
        nextStep: "UNKNOWN_STEP",
        workflowStatus: "COMPLETED",
      });

      await page.goto("/workflow/demat-account-opening");

      // Should redirect to status page for completed workflow
      await page.waitForURL("/workflow/demat-account-opening/status");
    });
  });

  test.describe("Error Handling", () => {
    test("should show error page when workflow initiation fails with server error", async ({
      page,
    }) => {
      mockWorkflowInitiateError(page, 500, {
        errorMessage: "Internal server error",
      });

      await page.goto("/workflow/demat-account-opening");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });
      await expect(page.getByText("Internal server error")).toBeVisible();
    });

    test("should show error page when workflow initiation fails with client error", async ({
      page,
    }) => {
      mockWorkflowInitiateError(page, 400, {
        errorMessage: "Invalid workflow type",
      });

      await page.goto("/workflow/invalid-workflow");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });
      await expect(page.getByText("Invalid workflow type")).toBeVisible();
    });

    test("should show error page when network connection fails", async ({
      page,
    }) => {
      mockWorkflowInitiateNetworkError(page);

      await page.goto("/workflow/demat-account-opening");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });

    test("should reload page when retry button is clicked", async ({
      page,
    }) => {
      mockWorkflowInitiateError(page, 500, {
        errorMessage: "Internal server error",
      });

      await page.goto("/workflow/demat-account-opening");

      // Should show error page first
      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Click retry button should reload the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during workflow initiation", async ({
      page,
    }) => {
      // Mock slow workflow initiation
      mockWorkflowInitiate(page, {
        nextStep: "USER_PROFILE",
        workflowStatus: "INITIATED",
        delay: 2000,
      });

      await page.goto("/workflow/demat-account-opening");

      // Should show page title during loading (includes site suffix)
      await expect(page).toHaveTitle("Initiating Workflow | Stable Bonds");
    });
  });
});
