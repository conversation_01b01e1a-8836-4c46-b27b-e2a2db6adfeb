import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import {
  WetSignatureResponseSchema,
  StepName,
  WorkflowStatus,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { ErrorResponseSchema } from "@/clients/gen/broking/Common_pb";
import { mergeDeep, type DeepPartial } from "../../utils";

type WetSignatureSubmissionOptions = {
  nextStep?: keyof typeof StepName;
  workflowStatus?: keyof typeof WorkflowStatus;
  delay?: number;
};

/**
 * Mock successful wet signature submission
 */
export function mockWetSignatureSubmission(
  page: Page,
  options: WetSignatureSubmissionOptions = {}
) {
  const {
    nextStep = "CVL_PULL",
    workflowStatus = "INITIATED",
    delay = 0,
  } = options;

  const documentUploadResponse = {
    document_id: faker.string.uuid(),
  };

  const wetSignatureResponse: MessageInitShape<
    typeof WetSignatureResponseSchema
  > = {
    nextStep: {
      nextStep: StepName[nextStep],
      workflowStatus: WorkflowStatus[workflowStatus],
    },
  };

  // Mock document upload endpoint (for signature file)
  page.route("*/**/v1/document/upload", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      if (delay > 0) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      route.fulfill({
        status: 200,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(documentUploadResponse),
      });
      return;
    }
    route.continue();
  });

  // Mock wet signature save endpoint
  page.route("*/**/v1/workflow-step/wet-signature", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      if (delay > 0) {
        await new Promise((resolve) => setTimeout(resolve, delay));
      }

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            WetSignatureResponseSchema,
            create(WetSignatureResponseSchema, wetSignatureResponse)
          )
        ),
      });
      return;
    }
    route.continue();
  });

  return {
    documentUploadResponse,
    wetSignatureResponse,
  };
}

/**
 * Mock wet signature submission with error response
 */
export function mockWetSignatureSubmissionError(
  page: Page,
  status: number = 500,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  const defaultResponseData = {
    errorCode: faker.string.alphanumeric(8).toUpperCase(),
    errorMessage: faker.lorem.sentence(),
  };

  const responseData = mergeDeep(defaultResponseData, overrides);

  // Mock document upload to succeed but wet signature save to fail
  page.route("*/**/v1/document/upload", (route) => {
    route.fulfill({
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        document_id: faker.string.uuid(),
      }),
    });
  });

  page.route("*/**/v1/workflow-step/wet-signature", (route) => {
    route.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });

  return responseData;
}

/**
 * Mock wet signature submission with network error
 */
export function mockWetSignatureSubmissionNetworkError(page: Page) {
  // Mock document upload to fail with network error
  page.route("*/**/v1/document/upload", (route) => {
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);
    if (errorType === "abort") {
      route.abort("internetdisconnected");
    } else {
      route.abort("timedout");
    }
  });

  page.route("*/**/v1/workflow-step/wet-signature", (route) => {
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);
    if (errorType === "abort") {
      route.abort("internetdisconnected");
    } else {
      route.abort("timedout");
    }
  });
}

/**
 * Mock wet signature submission with slow response for loading state testing
 */
export function mockWetSignatureSubmissionSlow(page: Page) {
  const documentUploadResponse = {
    document_id: faker.string.uuid(),
  };

  const wetSignatureResponse: MessageInitShape<
    typeof WetSignatureResponseSchema
  > = {
    nextStep: {
      nextStep: StepName.CVL_PULL,
      workflowStatus: WorkflowStatus.INITIATED,
    },
  };

  // Mock slow document upload
  page.route("*/**/v1/document/upload", async (route) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    route.fulfill({
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(documentUploadResponse),
    });
  });

  // Mock slow wet signature save
  page.route("*/**/v1/workflow-step/wet-signature", async (route) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          WetSignatureResponseSchema,
          create(WetSignatureResponseSchema, wetSignatureResponse)
        )
      ),
    });
  });

  return {
    documentUploadResponse,
    wetSignatureResponse,
  };
}
