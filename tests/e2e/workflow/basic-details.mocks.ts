import type { Page } from "@playwright/test";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import { ConfigDataResponseSchema } from "@/clients/gen/broking/Common_pb";

// Test: should load questionnaire data successfully
export function mockQuestionnaireList(page: Page) {
  const responseData: MessageInitShape<typeof ConfigDataResponseSchema> = {
    data: [
      {
        configValue: JSON.stringify({
          questionaires: [
            {
              id: 1,
              title: "Employment Type",
              subtitle: "Select your employment type",
              screenName: "basic-details",
              clickEvent: "employment_type_selected",
              options: [
                { label: "Salaried", value: 1 },
                { label: "Self Employed", value: 2 },
                { label: "Business", value: 3 },
                { label: "Student", value: 4 },
                { label: "Retired", value: 5 },
                { label: "Housewife", value: 6 },
              ],
            },
            {
              id: 2,
              title: "Income Range",
              subtitle: "Select your annual income range",
              screenName: "basic-details",
              clickEvent: "income_range_selected",
              options: [
                { label: "Below 2 Lakhs", value: 1 },
                { label: "2-5 Lakhs", value: 2 },
                { label: "5-10 Lakhs", value: 3 },
                { label: "10-25 Lakhs", value: 4 },
                { label: "25-50 Lakhs", value: 5 },
                { label: "Above 50 Lakhs", value: 6 },
              ],
            },
            {
              id: 3,
              title: "Trading Experience",
              subtitle: "Select your trading experience",
              screenName: "basic-details",
              clickEvent: "trading_experience_selected",
              options: [
                { label: "Beginner", value: 1 },
                { label: "Intermediate", value: 2 },
                { label: "Advanced", value: 3 },
              ],
            },
            {
              id: 4,
              title: "Marital Status",
              subtitle: "Select your marital status",
              screenName: "basic-details",
              clickEvent: "marital_status_selected",
              options: [
                { label: "Single", value: 1 },
                { label: "Married", value: 2 },
                { label: "Divorced", value: 3 },
                { label: "Widowed", value: 4 },
              ],
            },
          ],
        }),
      },
    ],
  };

  page.route("*/**/v1/config/list", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          ConfigDataResponseSchema,
          create(ConfigDataResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}
