import { test, expect } from "@playwright/test";
import {
  mockWetSignatureSubmission,
  mockWetSignatureSubmissionError,
  mockWetSignatureSubmissionNetworkError,
  mockWetSignatureSubmissionSlow,
} from "./wetsign.mocks";

test.describe("Workflow - Wet Signature", () => {
  test.describe("Happy Path", () => {
    test("should load wet signature page and show form fields", async ({
      page,
    }) => {
      await page.goto("/workflow/demat-account-opening/wetsign");

      // Verify page title and description
      await expect(page.getByText("Verify your signature")).toBeVisible();
      await expect(
        page.getByText("Please sign below to complete your account opening")
      ).toBeVisible();

      // Verify signature canvas is present
      await expect(page.locator("canvas")).toBeVisible();

      // Verify checkboxes are present
      await expect(
        page.getByText("I am not a PEP (Politically exposed person)")
      ).toBeVisible();
      await expect(
        page.getByText("I am an Indian citizen, born and residing in India")
      ).toBeVisible();
      await expect(
        page.getByText(
          "There has been no action initiated / taken against me for violation of applicable SEBI regulations"
        )
      ).toBeVisible();

      // Verify RAA duration dropdown
      await expect(page.locator("select[name='raaDuration']")).toBeVisible();

      // Verify submit button is present
      await expect(page.getByRole("button", { name: "Proceed" })).toBeVisible();
    });

    test("should handle different RAA duration selections", async ({
      page,
    }) => {
      mockWetSignatureSubmission(page);

      await page.goto("/workflow/demat-account-opening/wetsign");

      // Change RAA duration
      await page.locator("select[name='raaDuration']").selectOption("2"); // RAA_90_DAYS enum value

      // Draw signature
      const canvas = page.locator("canvas");
      await canvas.click({ position: { x: 50, y: 50 } });

      // Submit should work with different duration
      await page.getByRole("button", { name: "Proceed" }).click();

      await page.waitForURL("/workflow/demat-account-opening/status");
    });
  });

  test.describe("Form Validation", () => {
    test("should show validation error when signature is empty", async ({
      page,
    }) => {
      await page.goto("/workflow/demat-account-opening/wetsign");

      // Try to submit without drawing signature
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show validation error
      await expect(
        page.getByText("Signature is required to proceed")
      ).toBeVisible();
    });

    test("should show validation error when PEP checkbox is unchecked", async ({
      page,
    }) => {
      await page.goto("/workflow/demat-account-opening/wetsign");

      // Draw signature
      const canvas = page.locator("canvas");
      await canvas.click({ position: { x: 50, y: 50 } });

      // Uncheck PEP declaration by clicking the checkbox text
      await page
        .getByText("I am not a PEP (Politically exposed person)")
        .click();

      // Try to submit
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show validation error
      await expect(page.getByText("Please confirm to proceed")).toBeVisible();
    });

    test("should show validation error when Indian citizen checkbox is unchecked", async ({
      page,
    }) => {
      await page.goto("/workflow/demat-account-opening/wetsign");

      // Draw signature
      const canvas = page.locator("canvas");
      await canvas.click({ position: { x: 50, y: 50 } });

      // Uncheck Indian citizen by clicking the checkbox text
      await page
        .getByText("I am an Indian citizen, born and residing in India")
        .click();

      // Try to submit
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show validation error
      await expect(
        page.getByText("Indian citizenship is required to proceed")
      ).toBeVisible();
    });

    test("should show validation error when SEBI checkbox is unchecked", async ({
      page,
    }) => {
      await page.goto("/workflow/demat-account-opening/wetsign");

      // Draw signature
      const canvas = page.locator("canvas");
      await canvas.click({ position: { x: 50, y: 50 } });

      // Uncheck SEBI guidelines by clicking the checkbox text
      await page
        .getByText(
          "There has been no action initiated / taken against me for violation of applicable SEBI regulations"
        )
        .click();

      // Try to submit
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show validation error
      await expect(page.getByText("Please confirm to proceed")).toBeVisible();
    });
  });

  test.describe("API Error Handling", () => {
    test("should show field error when submission fails with server error", async ({
      page,
    }) => {
      mockWetSignatureSubmissionError(page, 500, {
        errorMessage: "Internal server error",
      });

      await page.goto("/workflow/demat-account-opening/wetsign");

      // Draw signature and submit
      const canvas = page.locator("canvas");
      await canvas.click({ position: { x: 50, y: 50 } });
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show field error (scoped to form to avoid toast conflicts)
      await expect(
        page.locator("#wetsign-form").getByText("Internal server error")
      ).toBeVisible();
    });

    test("should show field error when submission fails with network error", async ({
      page,
    }) => {
      mockWetSignatureSubmissionNetworkError(page);

      await page.goto("/workflow/demat-account-opening/wetsign");

      // Draw signature and submit
      const canvas = page.locator("canvas");
      await canvas.click({ position: { x: 50, y: 50 } });
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should show field error (scoped to form to avoid toast conflicts)
      await expect(
        page
          .locator("#wetsign-form")
          .getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during form submission", async ({
      page,
    }) => {
      mockWetSignatureSubmissionSlow(page);

      await page.goto("/workflow/demat-account-opening/wetsign");

      // Draw signature
      const canvas = page.locator("canvas");
      await canvas.click({ position: { x: 50, y: 50 } });

      const submitButton = page.getByRole("button", { name: "Proceed" });
      await submitButton.click();

      // Should show loading state
      await expect(submitButton.locator("svg")).toBeVisible();
    });
  });

  test.describe("Signature Canvas", () => {
    test("should allow drawing signature on canvas", async ({ page }) => {
      await page.goto("/workflow/demat-account-opening/wetsign");

      const canvas = page.locator("canvas");

      // Should be able to draw on canvas
      await canvas.click({ position: { x: 50, y: 50 } });
      await canvas.dragTo(canvas, {
        sourcePosition: { x: 50, y: 50 },
        targetPosition: { x: 100, y: 100 },
      });

      // Canvas should have content after drawing
      // Note: Actual signature validation would be done by the form validation
      await expect(canvas).toBeVisible();
    });

    test("should allow clearing signature", async ({ page }) => {
      await page.goto("/workflow/demat-account-opening/wetsign");

      const canvas = page.locator("canvas");

      // Draw signature
      await canvas.click({ position: { x: 50, y: 50 } });

      // Look for clear button (if available in the implementation)
      const clearButton = page.getByRole("button", { name: /clear/i });
      if (await clearButton.isVisible()) {
        await clearButton.click();
      }
    });
  });
});
