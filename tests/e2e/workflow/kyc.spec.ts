import { test, expect } from "@playwright/test";
import {
  mockUserProfile,
  mockOnboardingState,
  mockKycInitialState,
  mockKycFullFlowWithPolling,
  mockKycPollingLimit,
  mockKycSdkFailure,
  mockKycServerError,
  mockKycClientError,
  mockKycNetworkError,
} from "./kyc.mocks";

test.describe("KYC Page", () => {
  test.beforeEach(async ({ page }) => {
    mockUserProfile(page);
    mockOnboardingState(page);
  });

  test("should display KYC verification page and initialize Digio SDK", async ({
    page,
  }) => {
    const mockData = mockKycInitialState(page);

    await page.goto("/onboarding/kyc");

    await expect(page.getByText("Redirecting you to Digilocker")).toBeVisible();
    await expect(page.getByText("for seamless KYC verification")).toBeVisible();

    await page.waitForTimeout(3000);

    const digioCallInfo = await page.evaluate(() =>
      window.testUtils?.getDigioCallInfo()
    );
    expect(digioCallInfo?.constructorCalled).toBe(true);
    expect(digioCallInfo?.constructorCallCount).toBe(1);
    expect(digioCallInfo?.submitCalled).toBe(true);
    expect(digioCallInfo?.submitCallCount).toBe(1);
    expect(digioCallInfo?.lastSubmitArgs).toEqual([
      mockData.id,
      mockData.customerIdentifier,
      mockData.accessToken,
    ]);
  });
});

test.describe("KYC Page - Error Handling", () => {
  test.beforeEach(async ({ page }) => {
    mockUserProfile(page);
    mockOnboardingState(page);
  });

  test("should show error page when credential fetching fails with 500 server error", async ({
    page,
  }) => {
    const mockData = mockKycServerError(page);

    await page.goto("/onboarding/kyc");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    await expect(page.getByText(mockData.errorMessage)).toBeVisible();

    await expect(
      page.getByText("Redirecting you to Digilocker")
    ).not.toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should show error page when credential fetching fails with 400 client error", async ({
    page,
  }) => {
    const mockData = mockKycClientError(page);

    await page.goto("/onboarding/kyc");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    await expect(page.getByText(mockData.errorMessage)).toBeVisible();

    await expect(
      page.getByText("Redirecting you to Digilocker")
    ).not.toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should show error page when credential fetching fails with network error", async ({
    page,
  }) => {
    const mockData = mockKycNetworkError(page);

    await page.goto("/onboarding/kyc");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    await expect(page.getByText(mockData.errorMessage)).toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });
});

test.describe("KYC Page - SDK Behavior", () => {
  test.beforeEach(async ({ page }) => {
    mockUserProfile(page);
    mockOnboardingState(page);
  });

  test("should trigger navigation when KYC is approved", async ({ page }) => {
    await page.clock.install();

    const mockData = mockKycFullFlowWithPolling(page);

    await page.goto("/onboarding/kyc");

    // Advance time to trigger polling - 3 calls at 1000ms intervals (using default successOnCall)
    for (let i = 0; i < 3; i++) {
      await page.clock.runFor(1000);
      await page.waitForTimeout(100);
    }

    await expect(page).toHaveURL("/onboarding/pan", { timeout: 5000 });
    expect(mockData.getStatusCallCount()).toBe(3);
  });

  test("should show loading state during polling after SDK completion", async ({
    page,
  }) => {
    await page.clock.install();

    const mockData = mockKycFullFlowWithPolling(page);

    await page.goto("/onboarding/kyc");

    await expect(
      page.getByText("Completing your KYC verification")
    ).toBeVisible();
    await expect(
      page.getByText(
        "This usually takes a few moments. Please don't close this page."
      )
    ).toBeVisible();

    await expect(page.locator("img.animate-spin")).toBeVisible();

    // Advance time to trigger polling - 3 calls at 1000ms intervals
    for (let i = 0; i < 3; i++) {
      await page.clock.runFor(1000);
      await page.waitForTimeout(100);
    }

    await expect(page).toHaveURL("/onboarding/pan", { timeout: 5000 });
    expect(mockData.getStatusCallCount()).toBe(3);
  });

  test.skip("should handle polling limit reached and display error screen", async ({
    page,
  }) => {
    await page.clock.install();

    mockKycPollingLimit(page);

    await page.goto("/onboarding/kyc");

    await expect(
      page.getByText("Completing your KYC verification")
    ).toBeVisible();

    // Advance time to reach polling limit - 10 calls at 1000ms intervals
    for (let i = 0; i < 10; i++) {
      await page.clock.runFor(1000);
      await page.waitForTimeout(100);
    }

    await expect(page.getByText("KYC verification failed")).toBeVisible({
      timeout: 5000,
    });
    await expect(
      page.getByText(
        "Please try again. If the issue persists, contact our support team."
      )
    ).toBeVisible();
    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible();
    await expect(
      page.getByRole("link", { name: "Need help? Call us" })
    ).toBeVisible();
  });

  test("should handle SDK error callback and stay on initial screen", async ({
    browser,
  }) => {
    const context = await browser.newContext({
      storageState: "tests/e2e/storage-states/digio-error.json",
    });
    const page = await context.newPage();

    mockKycSdkFailure(page);

    await page.goto("/onboarding/kyc");

    // Verify we stay on the initial screen (SDK errors don't show error page anymore)
    await expect(page.getByText("Redirecting you to Digilocker")).toBeVisible();

    // Verify error page components are NOT shown for SDK failures
    await expect(page.getByRole("button", { name: "Retry" })).not.toBeVisible();
    await expect(page.getByText("KYC verification failed")).not.toBeVisible();

    await context.close();
  });
});
