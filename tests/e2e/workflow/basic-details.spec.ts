import { test, expect } from "@playwright/test";
import { mockQuestionnaireList } from "./basic-details.mocks.ts";

test.describe("Workflow - Basic Details", () => {
  test.describe("Happy Path", () => {
    test("should load basic details page and show form fields", async ({
      page,
    }) => {
      // Mock the questionnaire API
      mockQuestionnaireList(page);

      await page.goto("/workflow/demat-account-opening/basic-details");

      // Verify all form sections are visible by their headings
      await expect(page.getByText("EMPLOYMENT TYPE")).toBeVisible();
      await expect(page.getByText("INCOME RANGE")).toBeVisible();
      await expect(page.getByText("TRADING EXPERIENCE")).toBeVisible();
      await expect(page.getByText("MARITAL STATUS")).toBeVisible();

      // Verify submit button is present
      await expect(page.getByRole("button", { name: "Proceed" })).toBeVisible();
    });

    test("should successfully submit form and navigate to parent details", async ({
      page,
    }) => {
      // Mock the questionnaire API
      mockQuestionnaireList(page);

      await page.goto("/workflow/demat-account-opening/basic-details");

      // Fill out the form by clicking on chip labels
      await page.getByText("Salaried").click();
      await page.getByText("5-10 Lakhs").click();
      await page.getByText("Beginner").click();
      await page.getByText("Single").click();

      // Submit the form
      await page.getByRole("button", { name: "Proceed" }).click();

      // Should navigate to parent details with query parameters
      await expect(page).toHaveURL(
        /\/workflow\/demat-account-opening\/parent-details\?.*incomeRange=3.*maritalStatus=1.*employmentType=1.*tradingExperience=1/
      );
    });
  });

  test.describe("Form Validation", () => {
    test("should disable submit button when employment type is not selected", async ({
      page,
    }) => {
      mockQuestionnaireList(page);
      await page.goto("/workflow/demat-account-opening/basic-details");
      await page.getByRole("button", { name: "Proceed" }).click();
      expect(page.getByText("Employment type is required")).toBeVisible();
    });

    test("should show error when income range is not selected", async ({
      page,
    }) => {
      mockQuestionnaireList(page);
      await page.goto("/workflow/demat-account-opening/basic-details");
      await page.getByRole("button", { name: "Proceed" }).click();
      expect(page.getByText("Income range is required")).toBeVisible();
    });

    test("should show error when trading experience range is not selected", async ({
      page,
    }) => {
      // Mock the questionnaire API
      mockQuestionnaireList(page);
      await page.goto("/workflow/demat-account-opening/basic-details");
      await page.getByRole("button", { name: "Proceed" }).click();
      expect(page.getByText("Trading experience is required")).toBeVisible();
    });

    test("should show error when marital status is not selected", async ({
      page,
    }) => {
      mockQuestionnaireList(page);
      await page.goto("/workflow/demat-account-opening/basic-details");
      await page.getByRole("button", { name: "Proceed" }).click();
      expect(page.getByText("Marital status is required")).toBeVisible();
    });
  });
});
