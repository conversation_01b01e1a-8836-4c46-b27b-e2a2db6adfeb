import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  SelfieStartRequestSchema,
  SelfieInitiationResponseSchema,
  SelfieStatusCheckRequestSchema,
  SelfieStatusCheckResponseSchema,
  StepName,
  WorkflowStatus,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { ErrorResponseSchema } from "@/clients/gen/broking/Common_pb";
import { UserProfileResponseSchema } from "@/clients/gen/broking/Profile_pb";
import { mergeDeep, type DeepPartial } from "../../utils";
import { expect } from "@playwright/test";

type HypervergeCredentialOptions = {
  delay?: number;
};

type HypervergePollingOptions = {
  nextStep?: keyof typeof StepName;
  workflowStatus?: keyof typeof WorkflowStatus;
};

/**
 * Mock user profile endpoint
 */
export function mockUserProfile(page: Page) {
  const mockData = {
    userId: faker.string.uuid(),
    firstName: faker.person.firstName(),
    lastName: faker.person.lastName(),
    email: faker.internet.email(),
    mobile: faker.string.numeric(10),
    panNumber: faker.finance.accountNumber({ length: 10 }),
    aadharNumber: faker.finance.accountNumber({ length: 12 }),
  };

  const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
    data: {
      id: mockData.userId,
      firstName: mockData.firstName,
      lastName: mockData.lastName,
      email: mockData.email,
      mobile: mockData.mobile,
      name: `${mockData.firstName} ${mockData.lastName}`,
    },
    profileData: {
      id: mockData.userId,
      panNumber: mockData.panNumber,
      aadharNumber: mockData.aadharNumber,
      dob: faker.date.past().toISOString(),
      gender: 1,
      incomeRange: 1,
      employmentType: 1,
      tradingExperience: 1,
      maritalStatus: 1,
      fatherName: faker.person.firstName(),
      motherName: faker.person.firstName(),
      kraName: faker.person.fullName(),
      incomeTaxDepartmentName: `${mockData.firstName} ${mockData.lastName}`,
    },
    lifeTimeStatus: "ONBOARDED",
    currentStatus: "ACTIVE",
  };

  page.route("*/**/v1/user/profile", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return mockData;
}

/**
 * Mock successful HyperVerge credential generation
 */
export function mockHypervergeCredential(
  page: Page,
  options: HypervergeCredentialOptions = {}
) {
  const { delay = 0 } = options;

  const responseData: MessageInitShape<typeof SelfieInitiationResponseSchema> =
    {
      transactionId: faker.string.uuid(),
      workflowId: faker.string.uuid(),
      accessToken: faker.string.alphanumeric(32),
      selfie: faker.internet.url(),
    };

  page.route("*/**/v1/workflow-step/start-selfie", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(SelfieStartRequestSchema, requestBuffer);

        // Validate request structure
        expect(requestData.workflow).toBeDefined();

        if (delay > 0) {
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        route.fulfill({
          status: 200,
          body: Buffer.from(
            toBinary(
              SelfieInitiationResponseSchema,
              create(SelfieInitiationResponseSchema, responseData)
            )
          ),
        });
        return;
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock HyperVerge credential generation with error response
 */
export function mockHypervergeCredentialError(
  page: Page,
  status: number = 500,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  const defaultResponseData = {
    errorCode: faker.string.alphanumeric(8).toUpperCase(),
    errorMessage: faker.lorem.sentence(),
  };

  const responseData = mergeDeep(defaultResponseData, overrides);

  page.route("*/**/v1/workflow-step/start-selfie", (route) => {
    route.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });

  return responseData;
}

/**
 * Mock HyperVerge credential generation with network error
 */
export function mockHypervergeCredentialNetworkError(page: Page) {
  page.route("*/**/v1/workflow-step/start-selfie", (route) => {
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);
    if (errorType === "abort") {
      route.abort("internetdisconnected");
    } else {
      route.abort("timedout");
    }
  });
}

/**
 * Mock complete HyperVerge selfie flow with polling
 */
export function mockHypervergePollingFlow(
  page: Page,
  successAfter = 3,
  options: HypervergePollingOptions = {}
) {
  const { nextStep = "USER_PROFILE", workflowStatus = "INITIATED" } = options;

  const credentialResponse: MessageInitShape<
    typeof SelfieInitiationResponseSchema
  > = {
    transactionId: faker.string.uuid(),
    workflowId: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    selfie: faker.internet.url(),
  };

  const statusResponse: MessageInitShape<
    typeof SelfieStatusCheckResponseSchema
  > = {
    nextStep: {
      nextStep: StepName[nextStep],
      workflowStatus: WorkflowStatus[workflowStatus],
    },
  };

  let statusCallCount = 0;

  // Mock credential generation
  page.route("*/**/v1/workflow-step/start-selfie", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          SelfieInitiationResponseSchema,
          create(SelfieInitiationResponseSchema, credentialResponse)
        )
      ),
    });
  });

  // Mock status polling
  page.route("*/**/v1/workflow-step/check-selfie-status", (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(
          SelfieStatusCheckRequestSchema,
          requestBuffer
        );

        // Validate request structure
        expect(requestData.workflow).toBeDefined();

        statusCallCount++;

        // Return pending response until successAfter calls, then success
        const shouldSucceed = statusCallCount >= successAfter;
        const response = shouldSucceed
          ? statusResponse
          : {
              nextStep: {
                nextStep: StepName.SELFIE,
                workflowStatus: WorkflowStatus.INITIATED,
              },
            };

        route.fulfill({
          status: 200,
          body: Buffer.from(
            toBinary(
              SelfieStatusCheckResponseSchema,
              create(SelfieStatusCheckResponseSchema, response)
            )
          ),
        });
        return;
      }
    }
    route.continue();
  });

  return {
    credentialResponse,
    statusResponse,
    getStatusCallCount: () => statusCallCount,
  };
}
