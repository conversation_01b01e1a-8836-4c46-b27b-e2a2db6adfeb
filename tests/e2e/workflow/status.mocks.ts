import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import {
  ContinueWorkflowResponseSchema,
  StepName,
  WorkflowStatus,
  WorkflowName,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { ErrorResponseSchema } from "@/clients/gen/broking/Common_pb";
import { mergeDeep, type DeepPartial } from "../../utils";

type WorkflowStatusOptions = {
  workflowStatus?: keyof typeof WorkflowStatus;
  nextStep?: keyof typeof StepName;
  nextWorkflow?: keyof typeof WorkflowName;
  nextWorkflowStep?: keyof typeof StepName;
  delay?: number;
};

/**
 * Mock workflow status for pending state
 */
export function mockWorkflowStatusPending(
  page: Page,
  options: WorkflowStatusOptions = {}
) {
  const {
    workflowStatus = "INITIATED",
    nextStep = "CVL_PULL",
    delay = 0,
  } = options;

  const responseData: MessageInitShape<typeof ContinueWorkflowResponseSchema> =
    {
      workflowStatus: WorkflowStatus[workflowStatus],
      nextStep: StepName[nextStep],
    };

  page.route("*/**/v1/workflow/*/continue", async (route) => {
    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          ContinueWorkflowResponseSchema,
          create(ContinueWorkflowResponseSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return responseData;
}

/**
 * Mock workflow status for completed state
 */
export function mockWorkflowStatusCompleted(
  page: Page,
  options: WorkflowStatusOptions = {}
) {
  const {
    workflowStatus = "COMPLETED",
    nextStep = "UNKNOWN_STEP",
    delay = 0,
  } = options;

  const responseData: MessageInitShape<typeof ContinueWorkflowResponseSchema> =
    {
      workflowStatus: WorkflowStatus[workflowStatus],
      nextStep: StepName[nextStep],
    };

  page.route("*/**/v1/workflow/*/continue", async (route) => {
    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          ContinueWorkflowResponseSchema,
          create(ContinueWorkflowResponseSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return responseData;
}

/**
 * Mock workflow status with slow response for loading state testing
 */
export function mockWorkflowStatusSlow(
  page: Page,
  delay: number,
  options: WorkflowStatusOptions = {}
) {
  return mockWorkflowStatusPending(page, { ...options, delay });
}

/**
 * Mock error response for any endpoint
 */
export function mockErrorResponse(
  page: Page,
  route: string,
  status: number = 500,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  const defaultResponseData = {
    errorCode: faker.string.alphanumeric(8).toUpperCase(),
    errorMessage: faker.lorem.sentence(),
  };

  const responseData = mergeDeep(defaultResponseData, overrides);

  page.route(route, (routeHandler) => {
    routeHandler.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });

  return responseData;
}

/**
 * Mock network error for any endpoint
 */
export function mockNetworkError(page: Page, route: string) {
  page.route(route, (routeHandler) => {
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);
    if (errorType === "abort") {
      routeHandler.abort("internetdisconnected");
    } else {
      routeHandler.abort("timedout");
    }
  });
}
