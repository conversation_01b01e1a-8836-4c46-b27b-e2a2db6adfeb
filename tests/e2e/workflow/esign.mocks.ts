import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  EsignStep,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { UserProfileResponseSchema } from "@/clients/gen/broking/Profile_pb";
import { CartDetailsSchema } from "@/clients/gen/broking/Cart_pb";

/**
 * Mock user profile endpoint
 */
export function mockUserProfile(page: Page) {
  const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
    data: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      mobile: faker.string.numeric(10),
      name: faker.person.fullName(),
      socialName: faker.person.fullName(),
      profileImageUrl: faker.image.avatar(),
    },
    profileData: {
      kraName: faker.person.fullName(),
      incomeTaxDepartmentName: faker.person.fullName(),
      referralLink: faker.internet.url(),
      fdBookingCount: 0,
      firstFdRewardClaimed: false,
    },
  };

  page.route("*/**/v1/user/profile", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return {
    firstName: responseData.data?.firstName,
    lastName: responseData.data?.lastName,
    email: responseData.data?.email,
    mobile: responseData.data?.mobile,
    name: responseData.data?.name,
    kraName: responseData.profileData?.kraName,
  };
}

/**
 * Mock esign initial state - generates token for SDK initialization
 */
export function mockEsignInitialState(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure
        if (
          requestData.stepName === StepName.ESIGN &&
          requestData.result.case === "esignRequest" &&
          requestData.result.value.step === EsignStep.GENERATE_TOKEN
        ) {
          const responseData: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.ESIGN,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "esignResponse" as const,
              value: {
                result: {
                  case: "generateTokenResponse" as const,
                  value: credentialResponse,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, responseData)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return credentialResponse;
}

/**
 * Mock esign mandatory documents modal
 */
export function mockEsignMandatoryDocs(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure
        if (
          requestData.stepName === StepName.ESIGN &&
          requestData.result.case === "esignRequest" &&
          requestData.result.value.step === EsignStep.GENERATE_TOKEN
        ) {
          const responseData: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.ESIGN,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "esignResponse" as const,
              value: {
                result: {
                  case: "generateTokenResponse" as const,
                  value: credentialResponse,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, responseData)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return credentialResponse;
}

/**
 * Mock cart details endpoint
 */
export function mockCartDetails(page: Page) {
  const cartData = {
    cartItems: [],
  };

  page.route("*/**/v1/cart/details", (route) => {
    const responseData: MessageInitShape<typeof CartDetailsSchema> = cartData;

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(CartDetailsSchema, create(CartDetailsSchema, responseData))
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return cartData;
}

/**
 * Mock esign full flow with polling - handles both token generation and status polling
 */
export function mockEsignFullFlowWithPolling(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  const statusResponse = { kycStatus: "approved" };
  const nextStep = StepName.KYC_TYPE_UNKNOWN;
  const kycStatus = UserLifetimeStatusResponse_KycStatus.ONBOARDING_ON_EXCHANGE;
  const successOnCall = 3;

  let statusCallCount = 0;

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.ESIGN &&
          requestData.result.case === "esignRequest"
        ) {
          const esignRequest = requestData.result.value;

          // Validate request structure
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          if (esignRequest.step === EsignStep.GENERATE_TOKEN) {
            // Initial credential generation
            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.ESIGN,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "esignResponse" as const,
                value: {
                  result: {
                    case: "generateTokenResponse" as const,
                    value: credentialResponse,
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          } else if (esignRequest.step === EsignStep.STATUS) {
            // Status polling - validate request has status step
            if (
              requestJson.esignRequest &&
              (requestJson.esignRequest as Record<string, unknown>).step ===
                "ESIGN_STEP_STATUS"
            ) {
              statusCallCount++;
            }

            const isSuccess = statusCallCount >= successOnCall;

            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: isSuccess
                ? kycStatus
                : UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: isSuccess ? nextStep : StepName.ESIGN,
              lifetimeStatus: isSuccess
                ? UserLifetimeStatus.KYC_INITIATED
                : UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "esignResponse" as const,
                value: {
                  result: {
                    case: "statusResponse" as const,
                    value: {
                      kycStatus: isSuccess
                        ? statusResponse.kycStatus
                        : "pending",
                    },
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    credentialResponse,
    statusResponse,
    nextStep,
    kycStatus,
    successOnCall,
    getStatusCallCount: () => statusCallCount,
  };
}

/**
 * Mock esign polling limit - never succeeds to trigger polling limit
 */
export function mockEsignPollingLimit(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  const statusResponse = { kycStatus: "pending" };
  let statusCallCount = 0;

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.ESIGN &&
          requestData.result.case === "esignRequest"
        ) {
          const esignRequest = requestData.result.value;

          if (esignRequest.step === EsignStep.GENERATE_TOKEN) {
            // Initial credential generation
            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.ESIGN,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "esignResponse" as const,
                value: {
                  result: {
                    case: "generateTokenResponse" as const,
                    value: credentialResponse,
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          } else if (esignRequest.step === EsignStep.STATUS) {
            // Status polling - always returns pending to trigger polling limit
            statusCallCount++;

            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.ESIGN,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "esignResponse" as const,
                value: {
                  result: {
                    case: "statusResponse" as const,
                    value: {
                      kycStatus: "pending", // Always pending to trigger polling limit
                    },
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    credentialResponse,
    statusResponse,
    getStatusCallCount: () => statusCallCount,
  };
}

/**
 * Mock esign SDK failure - returns token but SDK will fail
 */
export function mockEsignSdkFailure(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure
        if (
          requestData.stepName === StepName.ESIGN &&
          requestData.result.case === "esignRequest" &&
          requestData.result.value.step === EsignStep.GENERATE_TOKEN
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate the request structure matches expected format
          if (
            requestJson.stepName === "ESIGN" &&
            requestJson.esignRequest &&
            (requestJson.esignRequest as Record<string, unknown>).step ===
              "ESIGN_STEP_GENERATE_TOKEN"
          ) {
            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.ESIGN,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "esignResponse" as const,
                value: {
                  result: {
                    case: "generateTokenResponse" as const,
                    value: credentialResponse,
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return credentialResponse;
}
