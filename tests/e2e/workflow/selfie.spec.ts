import { test, expect } from "@playwright/test";
import {
  mockHypervergeCredential,
  mockHypervergeCredentialError,
  mockHypervergeCredentialNetworkError,
  mockHypervergePollingFlow,
  mockUserProfile,
} from "./selfie.mocks";
import { mockQuestionnaireList } from "./basic-details.mocks";

test.describe("Workflow - Selfie (HyperVerge)", () => {
  test.describe("Happy Path", () => {
    test("should load selfie page and show connecting animation", async ({
      page,
    }) => {
      // Mock required APIs
      mockUserProfile(page);
      mockHypervergeCredential(page);

      await page.goto("/workflow/demat-account-opening/selfie");

      // Should show connecting animation
      await expect(page.getByText("Connecting with Hyperverge")).toBeVisible();
      await expect(
        page.getByText("for instant selfie verification")
      ).toBeVisible();
    });

    test("should complete selfie flow successfully after polling", async ({
      page,
    }) => {
      // Mock required APIs
      mockUserProfile(page);
      // <PERSON>ck complete selfie flow with polling that succeeds after 3 calls
      const mockData = mockHypervergePollingFlow(page, 3);

      await page.goto("/workflow/demat-account-opening/selfie");

      // Should show connecting animation initially
      await expect(page.getByText("Connecting with Hyperverge")).toBeVisible();
      mockQuestionnaireList(page);
      // Should eventually navigate to next step after polling completes
      await expect(page).toHaveURL(
        "/workflow/demat-account-opening/basic-details",
        {
          timeout: 15000,
        }
      );

      // Verify polling happened the expected number of times
      expect(mockData.getStatusCallCount()).toBe(3);
    });

    test("should handle different next steps based on selfie response", async ({
      page,
    }) => {
      // Mock required APIs
      mockUserProfile(page);
      // Mock selfie flow that leads to wet signature step
      mockHypervergePollingFlow(page, 2, {
        nextStep: "WET_SIGNATURE",
        workflowStatus: "INITIATED",
      });

      await page.goto("/workflow/demat-account-opening/selfie");

      // Should navigate to wet signature step
      await expect(page).toHaveURL("/workflow/demat-account-opening/wetsign", {
        timeout: 15000,
      });
    });
  });

  test.describe("Error Handling", () => {
    test.beforeEach(async ({ page }) => {
      // Mock user profile for all error handling tests
      mockUserProfile(page);
    });

    test("should show error page when credential generation fails with server error", async ({
      page,
    }) => {
      mockHypervergeCredentialError(page, 500, {
        errorMessage: "Internal server error",
      });

      await page.goto("/workflow/demat-account-opening/selfie");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });
      await expect(page.getByText("Internal server error")).toBeVisible();
    });

    test("should show error page when credential generation fails with client error", async ({
      page,
    }) => {
      mockHypervergeCredentialError(page, 400, {
        errorMessage: "Invalid request parameters",
      });

      await page.goto("/workflow/demat-account-opening/selfie");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });
      await expect(page.getByText("Invalid request parameters")).toBeVisible();
    });

    test("should show error page when network connection fails", async ({
      page,
    }) => {
      mockHypervergeCredentialNetworkError(page);

      await page.goto("/workflow/demat-account-opening/selfie");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });

    test("should show error page when polling limit is reached", async ({
      page,
    }) => {
      // Mock polling that never succeeds (success after 15 calls, higher than limit)
      mockHypervergePollingFlow(page, 15);

      await page.goto("/workflow/demat-account-opening/selfie");

      // Should show error page when polling limit is reached
      await expect(page.getByText("Selfie verification failed")).toBeVisible({
        timeout: 20000,
      });
      await expect(
        page.getByText(
          "You can retry and verify selfie to get investment ready"
        )
      ).toBeVisible();
    });

    test("should reload page when retry button is clicked", async ({
      page,
    }) => {
      mockHypervergeCredentialError(page, 500, {
        errorMessage: "Internal server error",
      });

      await page.goto("/workflow/demat-account-opening/selfie");

      // Should show error page first
      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Click retry button should reload the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during credential generation", async ({
      page,
    }) => {
      // Mock required APIs
      mockUserProfile(page);
      // Mock slow credential generation
      mockHypervergeCredential(page, { delay: 2000 });

      await page.goto("/workflow/demat-account-opening/selfie");

      // Should show connecting animation during loading
      await expect(page.getByText("Connecting with Hyperverge")).toBeVisible();
    });

    test("should show loading page during polling", async ({ page }) => {
      // Mock required APIs
      mockUserProfile(page);
      // Mock polling flow
      mockHypervergePollingFlow(page, 3);

      await page.goto("/workflow/demat-account-opening/selfie");

      // Should eventually show loading page during polling
      await expect(
        page.getByText("Completing your selfie verification")
      ).toBeVisible({ timeout: 10000 });
      await expect(
        page.getByText(
          "This usually takes a few moments. Please don't close this page."
        )
      ).toBeVisible();
    });
  });
});
