import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  UserProfileUpdateRequestSchema,
  UserProfileUpdateResponseSchema,
  StepName,
  WorkflowStatus,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { ErrorResponseSchema } from "@/clients/gen/broking/Common_pb";
import { mergeDeep, type DeepPartial } from "../../utils";
import { expect } from "@playwright/test";

type ParentDetailsSubmissionOptions = {
  nextStep?: keyof typeof StepName;
  workflowStatus?: keyof typeof WorkflowStatus;
  delay?: number;
};

/**
 * Mock successful parent details submission
 */
export function mockParentDetailsSubmission(
  page: Page,
  options: ParentDetailsSubmissionOptions = {}
) {
  const {
    nextStep = "DIGIO_KYC",
    workflowStatus = "INITIATED",
    delay = 0,
  } = options;

  const responseData: MessageInitShape<typeof UserProfileUpdateResponseSchema> =
    {
      nextStep: {
        nextStep: StepName[nextStep],
        workflowStatus: WorkflowStatus[workflowStatus],
      },
    };

  page.route("*/**/v1/workflow-step/update-user-profile", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(
          UserProfileUpdateRequestSchema,
          requestBuffer
        );

        // Validate request structure
        expect(requestData.workflow).toBeDefined();
        expect(requestData.fatherName).toBeDefined();
        expect(requestData.motherName).toBeDefined();
        expect(requestData.employmentType).toBeDefined();
        expect(requestData.incomeRange).toBeDefined();
        expect(requestData.tradingExperience).toBeDefined();
        expect(requestData.maritalStatus).toBeDefined();

        if (delay > 0) {
          await new Promise((resolve) => setTimeout(resolve, delay));
        }

        route.fulfill({
          status: 200,
          body: Buffer.from(
            toBinary(
              UserProfileUpdateResponseSchema,
              create(UserProfileUpdateResponseSchema, responseData)
            )
          ),
        });
        return;
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock parent details submission with error response
 */
export function mockParentDetailsSubmissionError(
  page: Page,
  status: number = 500,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  const defaultResponseData = {
    errorCode: faker.string.alphanumeric(8).toUpperCase(),
    errorMessage: faker.lorem.sentence(),
  };

  const responseData = mergeDeep(defaultResponseData, overrides);

  page.route("*/**/v1/workflow-step/update-user-profile", (route) => {
    route.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });

  return responseData;
}

/**
 * Mock parent details submission with network error
 */
export function mockParentDetailsSubmissionNetworkError(page: Page) {
  page.route("*/**/v1/workflow-step/update-user-profile", (route) => {
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);
    if (errorType === "abort") {
      route.abort("internetdisconnected");
    } else {
      route.abort("timedout");
    }
  });
}

/**
 * Mock parent details submission with slow response for loading state testing
 */
export function mockParentDetailsSubmissionSlow(page: Page) {
  const responseData: MessageInitShape<typeof UserProfileUpdateResponseSchema> =
    {
      nextStep: {
        nextStep: StepName.DIGIO_KYC,
        workflowStatus: WorkflowStatus.INITIATED,
      },
    };

  page.route("*/**/v1/workflow-step/update-user-profile", async (route) => {
    // Add delay to simulate slow response
    await new Promise((resolve) => setTimeout(resolve, 2000));

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileUpdateResponseSchema,
          create(UserProfileUpdateResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}
