import { test, expect } from "@playwright/test";
import {
  mockWorkflowStatusPending,
  mockWorkflowStatusCompleted,
  mockWorkflowStatusSlow,
  mockErrorResponse,
  mockNetworkError,
} from "./status.mocks";

test.describe("Workflow Status Page", () => {
  test.describe("Happy Path - Pending State", () => {
    test("should show pending state with correct content and notification button", async ({
      page,
    }) => {
      // Mock workflow status as pending/initiated
      mockWorkflowStatusPending(page, {
        workflowStatus: "INITIATED",
      });

      await page.goto("/workflow/demat-account-opening/status");

      // Verify page title
      await expect(page).toHaveTitle("Processing... | Stable Bonds");

      // Verify pending state content
      await expect(
        page.getByText("All done! We will notify you")
      ).toBeVisible();
      await expect(page.getByText("once your demat is ready")).toBeVisible();
      await expect(
        page.getByText("It can take up to 3 hours, we will notify")
      ).toBeVisible();
      await expect(page.getByText("you over email and whatsapp")).toBeVisible();

      // Verify notification button
      const notifyButton = page.getByRole("button", {
        name: "Notify me after KYC is done",
      });
      await expect(notifyButton).toBeVisible();

      // Verify success state elements are not visible
      await expect(
        page.getByText("Your demat account is now open")
      ).not.toBeVisible();
      await expect(
        page.getByRole("button", { name: "Continue to Home" })
      ).not.toBeVisible();
    });

    test("should show toast notification when notify button is clicked", async ({
      page,
    }) => {
      mockWorkflowStatusPending(page);

      await page.goto("/workflow/demat-account-opening/status");

      // Click the notify button
      await page
        .getByRole("button", { name: "Notify me after KYC is done" })
        .click();

      // Verify toast notification appears
      await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });
      await expect(
        page.getByText("Sure, we'll notify you once your KYC is done")
      ).toBeVisible();
    });
  });

  test.describe("Happy Path - Completed State", () => {
    test("should show success state with correct content and continue button", async ({
      page,
    }) => {
      // Mock workflow status as completed
      mockWorkflowStatusCompleted(page, {
        workflowStatus: "COMPLETED",
      });

      await page.goto("/workflow/demat-account-opening/status");

      // Verify page title
      await expect(page).toHaveTitle("Workflow Complete | Stable Bonds");

      // Verify success state content
      await expect(
        page.getByText("Your demat account is now open")
      ).toBeVisible();

      // Verify continue button
      const continueButton = page.getByRole("button", {
        name: "Continue to Home",
      });
      await expect(continueButton).toBeVisible();

      // Verify pending state elements are not visible
      await expect(
        page.getByText("All done! We will notify you")
      ).not.toBeVisible();
      await expect(
        page.getByRole("button", { name: "Notify me after KYC is done" })
      ).not.toBeVisible();
    });

    test("should navigate to home page when continue button is clicked", async ({
      page,
    }) => {
      mockWorkflowStatusCompleted(page);

      await page.goto("/workflow/demat-account-opening/status");

      // Click continue button and verify navigation
      await page.getByRole("button", { name: "Continue to Home" }).click();

      // Should navigate to home page
      await expect(page).toHaveURL("/");
    });
  });

  test.describe("Different Workflow Types", () => {
    test("should handle trading account opening workflow", async ({ page }) => {
      mockWorkflowStatusPending(page, {
        nextWorkflow: "TRADING_ACCOUNT_OPENING",
      });

      await page.goto("/workflow/trading-account-opening/status");

      // Should show pending state for trading workflow
      await expect(
        page.getByText("All done! We will notify you")
      ).toBeVisible();
    });

    test("should handle trading demat account opening workflow", async ({
      page,
    }) => {
      mockWorkflowStatusPending(page, {
        nextWorkflow: "TRADING_DEMAT_ACCOUNT_OPENING",
      });

      await page.goto("/workflow/trading-demat-account-opening/status");

      // Should show pending state for combined workflow
      await expect(
        page.getByText("All done! We will notify you")
      ).toBeVisible();
    });

    test("should default to demat account opening for unknown workflow", async ({
      page,
    }) => {
      mockWorkflowStatusPending(page);

      await page.goto("/workflow/unknown-workflow/status");

      // Should still show pending state with default workflow
      await expect(
        page.getByText("All done! We will notify you")
      ).toBeVisible();
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during slow API response", async ({
      page,
    }) => {
      // Mock slow API response to capture loading state
      mockWorkflowStatusSlow(page, 2000);

      await page.goto("/workflow/demat-account-opening/status");

      // Page should load but content might be in loading state
      // The query will be loading until the mock response comes back
      await expect(page).toHaveURL("/workflow/demat-account-opening/status");

      // Eventually should show pending state content
      await expect(page.getByText("All done! We will notify you")).toBeVisible({
        timeout: 5000,
      });
    });
  });
});

test.describe("Workflow Status Page - Error Handling", () => {
  test("should show error page when API fails with 500 server error", async ({
    page,
  }) => {
    mockErrorResponse(page, "*/**/v1/workflow/*/continue", 500, {
      errorMessage: "Internal server error",
    });

    await page.goto("/workflow/demat-account-opening/status");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify the correct error message is displayed
    await expect(page.getByText("Internal server error")).toBeVisible();

    // Verify user is not stuck on loading state
    await expect(
      page.getByText("All done! We will notify you")
    ).not.toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should show error page when API fails with 400 client error", async ({
    page,
  }) => {
    mockErrorResponse(page, "*/**/v1/workflow/*/continue", 400, {
      errorMessage: "Invalid workflow request",
    });

    await page.goto("/workflow/demat-account-opening/status");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify the correct error message is displayed
    await expect(page.getByText("Invalid workflow request")).toBeVisible();

    await expect(
      page.getByText("All done! We will notify you")
    ).not.toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should show error page when network connection fails", async ({
    page,
  }) => {
    mockNetworkError(page, "*/**/v1/workflow/*/continue");

    await page.goto("/workflow/demat-account-opening/status");

    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify network error message is displayed
    await expect(
      page.getByText("Please check your internet connection and try again.")
    ).toBeVisible();

    // Test that retry button reloads the page
    const reloadPromise = page.waitForLoadState("load");
    await page.getByRole("button", { name: "Retry" }).click();
    await reloadPromise;
  });

  test("should reload page when retry button is clicked", async ({ page }) => {
    mockErrorResponse(page, "*/**/v1/workflow/*/continue", 500, {
      errorMessage: "Internal server error",
    });

    await page.goto("/workflow/demat-account-opening/status");

    // Should show error page first
    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
      timeout: 10000,
    });

    // Verify we're not on the initial state anymore
    await expect(
      page.getByText("All done! We will notify you")
    ).not.toBeVisible();

    // Click retry button should reload the page
    await page.getByRole("button", { name: "Retry" }).click();

    // After reload, should be back to the same URL
    // (since the error will happen again with the same mock)
    await expect(page).toHaveURL("/workflow/demat-account-opening/status");
  });
});
