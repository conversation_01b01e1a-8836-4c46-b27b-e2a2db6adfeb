import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import {
  InitiateWorkflowResponseSchema,
  ContinueWorkflowResponseSchema,
  StepName,
  WorkflowStatus,
} from "@/clients/gen/broking/WorkflowStep_pb";
import { ErrorResponseSchema } from "@/clients/gen/broking/Common_pb";
import { mergeDeep, type DeepPartial } from "../../utils";

type WorkflowInitiateOptions = {
  nextStep?: keyof typeof StepName;
  workflowStatus?: keyof typeof WorkflowStatus;
  delay?: number;
};

/**
 * Mock successful workflow initiation
 */
export function mockWorkflowInitiate(
  page: Page,
  options: WorkflowInitiateOptions = {}
) {
  const {
    nextStep = "USER_PROFILE",
    workflowStatus = "INITIATED",
    delay = 0,
  } = options;

  // Mock workflow initiation endpoint
  page.route("*/**/v1/workflow/*/initiate", async (route) => {
    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          InitiateWorkflowResponseSchema,
          create(InitiateWorkflowResponseSchema, {})
        )
      ),
    });
  });

  // Mock workflow continue endpoint (called after initiation)
  page.route("*/**/v1/workflow/*/continue", async (route) => {
    if (delay > 0) {
      await new Promise((resolve) => setTimeout(resolve, delay));
    }

    const responseData: MessageInitShape<
      typeof ContinueWorkflowResponseSchema
    > = {
      nextStep: StepName[nextStep as keyof typeof StepName],
      workflowStatus:
        WorkflowStatus[workflowStatus as keyof typeof WorkflowStatus],
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          ContinueWorkflowResponseSchema,
          create(ContinueWorkflowResponseSchema, responseData)
        )
      ),
    });
  });

  return {
    nextStep: StepName[nextStep as keyof typeof StepName],
    workflowStatus:
      WorkflowStatus[workflowStatus as keyof typeof WorkflowStatus],
  };
}

/**
 * Mock workflow initiation with error response
 */
export function mockWorkflowInitiateError(
  page: Page,
  status: number = 500,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  const defaultResponseData = {
    errorCode: faker.string.alphanumeric(8).toUpperCase(),
    errorMessage: faker.lorem.sentence(),
  };

  const responseData = mergeDeep(defaultResponseData, overrides);

  // Mock both initiate and continue endpoints to return error
  page.route("*/**/v1/workflow/*/initiate", (route) => {
    route.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });

  page.route("*/**/v1/workflow/*/continue", (route) => {
    route.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });

  return responseData;
}

/**
 * Mock workflow initiation with network error
 */
export function mockWorkflowInitiateNetworkError(page: Page) {
  // Mock both initiate and continue endpoints to fail with network error
  page.route("*/**/v1/workflow/*/initiate", (route) => {
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);
    if (errorType === "abort") {
      route.abort("internetdisconnected");
    } else {
      route.abort("timedout");
    }
  });

  page.route("*/**/v1/workflow/*/continue", (route) => {
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);
    if (errorType === "abort") {
      route.abort("internetdisconnected");
    } else {
      route.abort("timedout");
    }
  });
}
