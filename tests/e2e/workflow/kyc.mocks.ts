import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  KycStep,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { UserProfileResponseSchema } from "@/clients/gen/platform/public/models/identity/Profile_pb";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";

/**
 * Mock user profile endpoint
 */
export function mockUserProfile(page: Page) {
  const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
    data: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      mobile: faker.string.numeric(10),
    },
  };

  page.route("*/**/v1/user/profile", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return {
    firstName: responseData.data?.firstName,
    lastName: responseData.data?.lastName,
    email: responseData.data?.email,
    mobile: responseData.data?.mobile,
  };
}

/**
 * Mock onboarding state endpoint
 */
export function mockOnboardingState(page: Page) {
  const responseData = {
    next: KycType.PAN_KYC,
  };

  page.route("*/**/v1/user/onboarding-state", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify(responseData),
    });
  });

  return responseData;
}

/**
 * Mock KYC initial state - generates token for SDK initialization
 */
export function mockKycInitialState(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure
        if (
          requestData.stepName === StepName.KYC &&
          requestData.result.case === "kycRequest" &&
          requestData.result.value.step === KycStep.GENERATE_TOKEN
        ) {
          const responseData: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.PAN_KYC,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "kycResponse" as const,
              value: {
                result: {
                  case: "generateTokenResponse" as const,
                  value: credentialResponse,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, responseData)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return credentialResponse;
}

/**
 * Mock KYC full flow with polling - handles both token generation and status polling
 */
export function mockKycFullFlowWithPolling(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  const statusResponse = { kycStatus: "approved" };
  const nextStep = StepName.PAN_KYC;
  const kycStatus = UserLifetimeStatusResponse_KycStatus.INITIATED;
  const successOnCall = 3;

  let statusCallCount = 0;

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.KYC &&
          requestData.result.case === "kycRequest"
        ) {
          const kycRequest = requestData.result.value;

          // Validate request structure
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          if (kycRequest.step === KycStep.GENERATE_TOKEN) {
            // Initial credential generation
            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.PAN_KYC,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "kycResponse" as const,
                value: {
                  result: {
                    case: "generateTokenResponse" as const,
                    value: credentialResponse,
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          } else if (kycRequest.step === KycStep.GET_STATUS) {
            // Status polling - validate request has status step
            if (
              requestJson.kycRequest &&
              (requestJson.kycRequest as Record<string, unknown>).step ===
                "GET_STATUS"
            ) {
              statusCallCount++;
            }

            const isSuccess = statusCallCount >= successOnCall;

            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: isSuccess
                ? kycStatus
                : UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: isSuccess ? nextStep : StepName.KYC,
              lifetimeStatus: isSuccess
                ? UserLifetimeStatus.KYC_INITIATED
                : UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "kycResponse" as const,
                value: {
                  result: {
                    case: "statusResponse" as const,
                    value: {
                      kycStatus: isSuccess
                        ? statusResponse.kycStatus
                        : "pending",
                    },
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    credentialResponse,
    statusResponse,
    nextStep,
    kycStatus,
    successOnCall,
    getStatusCallCount: () => statusCallCount,
  };
}

/**
 * Mock KYC polling limit - never succeeds to trigger polling limit
 */
export function mockKycPollingLimit(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  const statusResponse = { kycStatus: "pending" };
  let statusCallCount = 0;

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.KYC &&
          requestData.result.case === "kycRequest"
        ) {
          const kycRequest = requestData.result.value;

          if (kycRequest.step === KycStep.GENERATE_TOKEN) {
            // Initial credential generation
            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.PAN_KYC,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "kycResponse" as const,
                value: {
                  result: {
                    case: "generateTokenResponse" as const,
                    value: credentialResponse,
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          } else if (kycRequest.step === KycStep.GET_STATUS) {
            // Status polling - always returns pending to trigger polling limit
            statusCallCount++;

            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.KYC,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "kycResponse" as const,
                value: {
                  result: {
                    case: "statusResponse" as const,
                    value: {
                      kycStatus: "pending", // Always pending to trigger polling limit
                    },
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    credentialResponse,
    statusResponse,
    getStatusCallCount: () => statusCallCount,
  };
}

/**
 * Mock KYC SDK failure - returns token but SDK will fail
 */
export function mockKycSdkFailure(page: Page) {
  const credentialResponse = {
    id: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    customerIdentifier: faker.string.uuid(),
    isNew: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure
        if (
          requestData.stepName === StepName.KYC &&
          requestData.result.case === "kycRequest" &&
          requestData.result.value.step === KycStep.GENERATE_TOKEN
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate the request structure matches expected format
          if (
            requestJson.stepName === "KYC" &&
            requestJson.kycRequest &&
            (requestJson.kycRequest as Record<string, unknown>).step ===
              "GENERATE_TOKEN"
          ) {
            const responseData: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.PAN_KYC,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "kycResponse" as const,
                value: {
                  result: {
                    case: "generateTokenResponse" as const,
                    value: credentialResponse,
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, responseData)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return credentialResponse;
}

/**
 * Mock KYC server error (500)
 */
export function mockKycServerError(page: Page) {
  const errorMessage = "Internal server error";

  page.route("*/**/v1/onboarding", (route) => {
    route.fulfill({
      status: 500,
      body: JSON.stringify({
        errorMessage: errorMessage,
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });
  });

  return { errorMessage };
}

/**
 * Mock KYC client error (400)
 */
export function mockKycClientError(page: Page) {
  const errorMessage = "This step is already completed";

  page.route("*/**/v1/onboarding", (route) => {
    route.fulfill({
      status: 400,
      body: JSON.stringify({
        errorMessage: errorMessage,
      }),
      headers: {
        "Content-Type": "application/json",
      },
    });
  });

  return { errorMessage };
}

/**
 * Mock KYC network error
 */
export function mockKycNetworkError(page: Page) {
  page.route("*/**/v1/onboarding", (route) => {
    route.abort("internetdisconnected");
  });

  return {
    errorMessage: "Please check your internet connection and try again.",
  };
}
