import { expect, type Page } from "@playwright/test";

/**
 * Mock the personalization page API call for bonds-home page.
 * This is required by the /app route loader.
 */
export function mockPersonalizationPage(page: Page) {
  page.route("*/**/page*", (route) => {
    const url = new URL(route.request().url());
    const path = url.searchParams.get("path");

    if (path === "bonds-home") {
      route.fulfill({
        status: 200,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          path: "bonds-home",
          name: "Bonds Home",
          root: {
            widget: "Container",
            props: {
              children: [
                {
                  widget: "Text",
                  props: { text: "Welcome to Bonds" },
                },
              ],
            },
          },
        }),
      });
    } else {
      route.continue();
    }
  });
}

/**
 * Mock the nudges API to return a single test nudge.
 * Used for testing nudge display and interaction functionality.
 */
export function mockNudgesWithData(page: Page) {
  page.route("*/**/nudges/all*", (route) => {
    route.fulfill({
      status: 200,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        nudges: [
          {
            nudgeId: "test-nudge-1",
            nudge: JSON.stringify({
              title_keys: [
                {
                  key: "test_title",
                  fallback: "Test Nudge Title",
                },
              ],
              sub_title_keys: [
                {
                  key: "test_subtitle",
                  fallback: "Test nudge description",
                },
              ],
              cta_label: {
                key: "test_cta",
                fallback: "Test Action",
              },
              redirect_deeplink: {
                path: "/test-action",
              },
              dismissible: true,
            }),
            name: "Test Nudge",
            category: "GENERAL",
            type: "CARD",
          },
        ],
      }),
    });
  });
}

/**
 * Mock the nudges API to return an empty array.
 * Used for testing the empty state when no nudges are available.
 */
export function mockNudgesEmpty(page: Page) {
  page.route("*/**/nudges/all*", (route) => {
    route.fulfill({
      status: 200,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ nudges: [] }),
    });
  });
}

/**
 * Mock the nudge dismiss action API.
 * Used for testing nudge dismissal functionality.
 */
export function mockNudgeDismissAction(page: Page) {
  page.route("*/**/nudges/action", (route) => {
    const request = route.request();

    // Validate that it's a POST request
    if (request.method() !== "POST") {
      route.continue();
      return;
    }

    // Validate the request body
    const requestBody = request.postDataJSON();
    expect(requestBody).toEqual({
      nudgeId: "test-nudge-1",
      actionType: "DISMISS",
      category: "GENERAL",
    });

    // For successful dismiss action
    route.fulfill({
      status: 200,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({}),
    });
  });
}

/**
 * Mock the nudges API to return updated data after dismissal.
 * Used for testing that nudges are removed after being dismissed.
 */
export function mockNudgesAfterDismiss(page: Page) {
  page.route("*/**/nudges/all*", (route) => {
    route.fulfill({
      status: 200,
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ nudges: [] }),
    });
  });
}
