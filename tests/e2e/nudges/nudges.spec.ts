import { test, expect } from "@playwright/test";
import {
  mockPersonalizationPage,
  mockNudgesWithData,
  mockNudgesEmpty,
  mockNudgeDismissAction,
  mockNudgesAfterDismiss,
} from "./nudges.mocks";

test.describe("Nudges Component", () => {
  test.skip(({ isMobile }) => !isMobile, "Mobile-only tests");

  test.beforeEach(async ({ page }) => {
    mockPersonalizationPage(page);
  });

  test("should display nudge when visiting /app", async ({ page }) => {
    mockNudgesWithData(page);

    await page.goto("/app");

    await page.waitForTimeout(2000);

    const nudgesContainer = page.locator("#nudges");
    await expect(nudgesContainer).toBeVisible();

    await expect(page.locator(".swiper")).toBeVisible();

    await expect(page.getByText("test_title")).toBeVisible();
    await expect(page.getByText("test_subtitle")).toBeVisible();
    await expect(page.getByText("test_cta")).toBeVisible();

    await expect(page.locator('img[alt="Close"]')).toBeVisible();
  });

  test("should not display nudges when API returns empty array", async ({
    page,
  }) => {
    mockNudgesEmpty(page);

    await page.goto("/app");

    const nudgesContainer = page.locator("#nudges");
    await expect(nudgesContainer).toBeAttached();
    await expect(nudgesContainer).not.toBeVisible();
    await expect(page.locator(".swiper")).not.toBeVisible();
  });

  test("should navigate when nudge is clicked", async ({ page }) => {
    mockNudgesWithData(page);

    await page.goto("/app");
    await page.waitForTimeout(2000);

    await expect(page.locator(".swiper")).toBeVisible();

    await page.locator(".swiper-slide").first().click();

    await expect(page).toHaveURL("/test-action");
  });

  test("should dismiss nudge when close button is clicked", async ({
    page,
  }) => {
    mockNudgesWithData(page);

    await page.goto("/app");

    await expect(page.getByText("test_title")).toBeVisible();

    mockNudgesAfterDismiss(page);
    mockNudgeDismissAction(page);

    const dismissButton = page.locator('img[alt="Close"]');
    await expect(dismissButton).toBeVisible();
    await dismissButton.click();

    await expect(page.getByText("test_title")).not.toBeVisible();
  });
});
