import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  SelfieKycStep,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { UserProfileResponseSchema } from "@/clients/gen/platform/public/models/identity/Profile_pb";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";

// Common mocks for auth and profile
export function mockInitiateAuth(page: Page) {
  page.route("*/**/v1/auth/initiate", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        result: {
          case: "otpChallenge",
          value: {
            challengeId: faker.string.uuid(),
            expiry: BigInt(Date.now() + 300000),
          },
        },
      }),
    });
  });
}

export function mockAuthenticate(page: Page) {
  page.route("*/**/v1/auth/authenticate", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        accessToken: faker.string.alphanumeric(32),
        refreshToken: faker.string.alphanumeric(32),
      }),
    });
  });
}

export function mockUserProfile(page: Page) {
  const responseData = {
    data: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      mobile: faker.string.numeric(10),
    },
  };

  page.route("*/**/v1/user/profile", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

export function mockOnboardingState(page: Page) {
  page.route("*/**/v1/user/onboarding-state", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        next: KycType.SELFIE,
      }),
    });
  });
}

/**
 * Mock selfie credentials generation - basic functionality
 */
export function mockSelfieCredentials(page: Page) {
  const responseData = {
    transactionId: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    workflowId: faker.string.uuid(),
    selfie: faker.image.dataUri(),
    completed: false,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for selfie credentials
        if (
          requestData.stepName === StepName.SELFIE &&
          requestData.result.case === "selfieRequest" &&
          requestData.result.value.step === SelfieKycStep.START_SELFIE_STEP
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.PAN_KYC,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "selfieResponse" as const,
              value: {
                result: {
                  case: "startSelfieStepResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock selfie with polling behavior - complete flow
 */
export function mockSelfieWithPolling(page: Page) {
  const credentialData = {
    transactionId: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    workflowId: faker.string.uuid(),
    selfie: faker.image.dataUri(),
    completed: false,
  };

  const responseConfig = {
    nextStep: StepName.PAN_KYC,
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    successOnCall: 3,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.SELFIE &&
          requestData.result.case === "selfieRequest"
        ) {
          const selfieRequest = requestData.result.value;

          if (selfieRequest.step === SelfieKycStep.START_SELFIE_STEP) {
            // Initial credential generation
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.SELFIE,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "selfieResponse" as const,
                  value: {
                    result: {
                      case: "startSelfieStepResponse" as const,
                      value: credentialData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          } else if (selfieRequest.step === SelfieKycStep.SET_STATUS) {
            // Status polling
            const isSuccess = true; // Always succeed for this mock

            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: isSuccess
                  ? responseConfig.kycStatus
                  : UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: isSuccess ? responseConfig.nextStep : StepName.SELFIE,
                lifetimeStatus: isSuccess
                  ? UserLifetimeStatus.KYC_INITIATED
                  : UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "selfieResponse" as const,
                  value: {
                    result: {
                      case: "setSelfieStatusResponse" as const,
                      value: {
                        completed: isSuccess ? true : false,
                        success: isSuccess ? true : false,
                      },
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    credentialData,
    statusData: { completed: true },
    responseConfig,
  };
}

/**
 * Mock selfie polling that reaches limit - never succeeds
 */
export function mockSelfiePollingLimit(page: Page) {
  const credentialData = {
    transactionId: faker.string.uuid(),
    accessToken: faker.string.alphanumeric(32),
    workflowId: faker.string.uuid(),
    selfie: faker.image.dataUri(),
    completed: false,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.SELFIE &&
          requestData.result.case === "selfieRequest"
        ) {
          const selfieRequest = requestData.result.value;

          if (selfieRequest.step === SelfieKycStep.START_SELFIE_STEP) {
            // Initial credential generation
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.SELFIE,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "selfieResponse" as const,
                  value: {
                    result: {
                      case: "startSelfieStepResponse" as const,
                      value: credentialData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          } else if (selfieRequest.step === SelfieKycStep.SET_STATUS) {
            // Status polling - never succeeds

            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.SELFIE,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "selfieResponse" as const,
                  value: {
                    result: {
                      case: "setSelfieStatusResponse" as const,
                      value: {
                        completed: false, // Always false to trigger polling limit
                        success: false, // Always false to trigger polling limit
                      },
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    credentialData,
    statusData: { completed: false },
  };
}

/**
 * Mock 500 server error for selfie requests
 */
export function mockSelfieServerError(page: Page, errorMessage: string) {
  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Check if this is a selfie-related request
        if (
          requestData.stepName === StepName.SELFIE &&
          requestData.result.case === "selfieRequest"
        ) {
          route.fulfill({
            status: 500,
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              errorMessage: errorMessage,
            }),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

/**
 * Mock 400 client error for selfie requests
 */
export function mockSelfieClientError(page: Page, errorMessage: string) {
  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Check if this is a selfie-related request
        if (
          requestData.stepName === StepName.SELFIE &&
          requestData.result.case === "selfieRequest"
        ) {
          route.fulfill({
            status: 400,
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              errorMessage: errorMessage,
            }),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

/**
 * Mock network error for selfie requests
 */
export function mockSelfieNetworkError(page: Page) {
  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Check if this is a selfie-related request
        if (
          requestData.stepName === StepName.SELFIE &&
          requestData.result.case === "selfieRequest"
        ) {
          route.abort("internetdisconnected");
          return;
        }
      }
    }
    route.continue();
  });
}
