import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  PanKycStep,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
  ErrorResponseSchema,
} from "@/clients/gen/broking/Common_pb";
import { type DeepPartial, mergeDeep } from "../../utils";

/**
 * Mock PAN name fetch - basic functionality
 */
export function mockPanNameFetch(page: Page) {
  const responseData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN name fetch
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.NAME_FETCH
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.PAN_KYC,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.NAME_FETCH,
                result: {
                  case: "kycFetchNameByPanResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock PAN name fetch with slow response for loading state testing
 */
export function mockPanNameFetchSlow(page: Page) {
  const responseData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN name fetch
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.NAME_FETCH
        ) {
          // Add delay to simulate slow response
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.PAN_KYC,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.NAME_FETCH,
                result: {
                  case: "kycFetchNameByPanResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock PAN name fetch with empty name for validation testing
 */
export function mockPanNameFetchEmpty(page: Page) {
  const responseData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: "",
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN name fetch
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.NAME_FETCH
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.PAN_KYC,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.NAME_FETCH,
                result: {
                  case: "kycFetchNameByPanResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock successful PAN submission
 */
export function mockPanSubmissionSuccess(page: Page) {
  const responseData = {
    panKycStatus: true,
    nameMatchStatus: true,
    dobMatchStatus: true,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN submission
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.PAN_STATUS
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.AADHAAR_PAN_MATCH,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.PAN_STATUS,
                result: {
                  case: "kycValidateNameAndGetPanStatusResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock PAN submission with name mismatch error
 */
export function mockPanSubmissionNameMismatch(page: Page) {
  const responseData = {
    panKycStatus: true,
    nameMatchStatus: false,
    dobMatchStatus: true,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN submission
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.PAN_STATUS
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.AADHAAR_PAN_MATCH,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.PAN_STATUS,
                result: {
                  case: "kycValidateNameAndGetPanStatusResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock PAN submission with DOB mismatch error
 */
export function mockPanSubmissionDobMismatch(page: Page) {
  const responseData = {
    panKycStatus: true,
    nameMatchStatus: true,
    dobMatchStatus: false,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN submission
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.PAN_STATUS
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.AADHAAR_PAN_MATCH,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.PAN_STATUS,
                result: {
                  case: "kycValidateNameAndGetPanStatusResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock PAN submission with invalid PAN error
 */
export function mockPanSubmissionInvalidPan(page: Page) {
  const responseData = {
    panKycStatus: true,
    nameMatchStatus: true,
    dobMatchStatus: true,
    isPanValid: false,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN submission
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.PAN_STATUS
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.AADHAAR_PAN_MATCH,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.PAN_STATUS,
                result: {
                  case: "kycValidateNameAndGetPanStatusResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock PAN submission with failed KYC status
 */
export function mockPanSubmissionFailedKyc(page: Page) {
  const responseData = {
    panKycStatus: false,
    nameMatchStatus: true,
    dobMatchStatus: true,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN submission
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.PAN_STATUS
        ) {
          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.AADHAAR_PAN_MATCH,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.PAN_STATUS,
                result: {
                  case: "kycValidateNameAndGetPanStatusResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock PAN submission with slow response for loading state testing
 */
export function mockPanSubmissionSlow(page: Page) {
  const responseData = {
    panKycStatus: true,
    nameMatchStatus: true,
    dobMatchStatus: true,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure for PAN submission
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest" &&
          requestData.result.value.step === PanKycStep.PAN_STATUS
        ) {
          // Add delay to simulate slow response
          await new Promise((resolve) => setTimeout(resolve, 1000));

          const response: MessageInitShape<typeof OnboardingResponseSchema> = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.AADHAAR_PAN_MATCH,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "panKycResponse" as const,
              value: {
                step: PanKycStep.PAN_STATUS,
                result: {
                  case: "kycValidateNameAndGetPanStatusResponse" as const,
                  value: responseData,
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, response)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock network error for any PAN-related request
 */
export function mockPanNetworkError(page: Page) {
  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Check if this is a PAN-related request (both name fetch and submission)
        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest"
        ) {
          route.abort("internetdisconnected");
          return;
        }
      }
    }
    route.continue();
  });
}

/**
 * Mock complete PAN flow - name fetch followed by successful submission
 */
export function mockPanCompleteFlow(page: Page) {
  const nameData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  const submissionData = {
    panKycStatus: true,
    nameMatchStatus: true,
    dobMatchStatus: true,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest"
        ) {
          const panRequest = requestData.result.value;

          if (panRequest.step === PanKycStep.NAME_FETCH) {
            // Handle name fetch
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.PAN_KYC,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.NAME_FETCH,
                    result: {
                      case: "kycFetchNameByPanResponse" as const,
                      value: nameData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          } else if (panRequest.step === PanKycStep.PAN_STATUS) {
            // Handle submission
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.AADHAAR_PAN_MATCH,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.PAN_STATUS,
                    result: {
                      case: "kycValidateNameAndGetPanStatusResponse" as const,
                      value: submissionData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    nameData,
    submissionData,
  };
}

/**
 * Mock PAN flow with name fetch and name mismatch error
 */
export function mockPanFlowNameMismatch(page: Page) {
  const nameData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  const submissionData = {
    panKycStatus: true,
    nameMatchStatus: false,
    dobMatchStatus: true,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest"
        ) {
          const panRequest = requestData.result.value;

          if (panRequest.step === PanKycStep.NAME_FETCH) {
            // Handle name fetch
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.PAN_KYC,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.NAME_FETCH,
                    result: {
                      case: "kycFetchNameByPanResponse" as const,
                      value: nameData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          } else if (panRequest.step === PanKycStep.PAN_STATUS) {
            // Handle submission with name mismatch error
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.AADHAAR_PAN_MATCH,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.PAN_STATUS,
                    result: {
                      case: "kycValidateNameAndGetPanStatusResponse" as const,
                      value: submissionData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    nameData,
    submissionData,
  };
}

/**
 * Mock PAN flow with name fetch and DOB mismatch error
 */
export function mockPanFlowDobMismatch(page: Page) {
  const nameData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  const submissionData = {
    panKycStatus: true,
    nameMatchStatus: true,
    dobMatchStatus: false,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest"
        ) {
          const panRequest = requestData.result.value;

          if (panRequest.step === PanKycStep.NAME_FETCH) {
            // Handle name fetch
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.PAN_KYC,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.NAME_FETCH,
                    result: {
                      case: "kycFetchNameByPanResponse" as const,
                      value: nameData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          } else if (panRequest.step === PanKycStep.PAN_STATUS) {
            // Handle submission with DOB mismatch error
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.AADHAAR_PAN_MATCH,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.PAN_STATUS,
                    result: {
                      case: "kycValidateNameAndGetPanStatusResponse" as const,
                      value: submissionData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    nameData,
    submissionData,
  };
}

/**
 * Mock PAN flow with name fetch and invalid PAN error
 */
export function mockPanFlowInvalidPan(page: Page) {
  const nameData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  const submissionData = {
    panKycStatus: true,
    nameMatchStatus: true,
    dobMatchStatus: true,
    isPanValid: false,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest"
        ) {
          const panRequest = requestData.result.value;

          if (panRequest.step === PanKycStep.NAME_FETCH) {
            // Handle name fetch
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.PAN_KYC,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.NAME_FETCH,
                    result: {
                      case: "kycFetchNameByPanResponse" as const,
                      value: nameData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          } else if (panRequest.step === PanKycStep.PAN_STATUS) {
            // Handle submission with invalid PAN error
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.AADHAAR_PAN_MATCH,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.PAN_STATUS,
                    result: {
                      case: "kycValidateNameAndGetPanStatusResponse" as const,
                      value: submissionData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    nameData,
    submissionData,
  };
}

/**
 * Mock PAN flow with name fetch and failed KYC status
 */
export function mockPanFlowFailedKyc(page: Page) {
  const nameData = {
    pan: faker.string.alphanumeric(10).toUpperCase(),
    fullName: faker.person.fullName(),
  };

  const submissionData = {
    panKycStatus: false,
    nameMatchStatus: true,
    dobMatchStatus: true,
    isPanValid: true,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.PAN_KYC &&
          requestData.result.case === "panKycRequest"
        ) {
          const panRequest = requestData.result.value;

          if (panRequest.step === PanKycStep.NAME_FETCH) {
            // Handle name fetch
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.PAN_KYC,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.NAME_FETCH,
                    result: {
                      case: "kycFetchNameByPanResponse" as const,
                      value: nameData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          } else if (panRequest.step === PanKycStep.PAN_STATUS) {
            // Handle submission with failed KYC status
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
                nextStep: StepName.AADHAAR_PAN_MATCH,
                lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
                result: {
                  case: "panKycResponse" as const,
                  value: {
                    step: PanKycStep.PAN_STATUS,
                    result: {
                      case: "kycValidateNameAndGetPanStatusResponse" as const,
                      value: submissionData,
                    },
                  },
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return {
    nameData,
    submissionData,
  };
}

/**
 * Mock error response for PAN-related endpoints
 */
export function mockErrorResponse(
  page: Page,
  route: string,
  status: number = 400,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  page.route(route, (routeHandler) => {
    const defaultResponseData = {
      errorCode: faker.string.alphanumeric(8).toUpperCase(),
      errorMessage: faker.lorem.sentence(),
    };

    const responseData = mergeDeep(defaultResponseData, overrides);

    routeHandler.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });
}

/**
 * Mock network error for PAN-related endpoints
 */
export function mockNetworkError(page: Page, route: string) {
  page.route(route, (routeHandler) => {
    // Randomly choose between abort and timeout to simulate different network issues
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);

    if (errorType === "abort") {
      // Abort the request to simulate network disconnection
      routeHandler.abort("internetdisconnected");
    } else {
      // Simulate timeout by aborting with timeout error
      routeHandler.abort("timedout");
    }
  });
}
