import type { Page } from "@playwright/test";
import { expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
  AadhaarPanMatchResponseSchema,
} from "@/clients/gen/broking/Kyc_pb";
import { ErrorResponseSchema } from "@/clients/gen/platform/public/models/identity/Common_pb";
import { mergeDeep, type DeepPartial } from "../../utils";

// Test: should display Aadhaar PAN mismatch details and retry button
export function mockOnboardingAadhaarPanMatchStatus(
  page: Page,
  overrides: DeepPartial<
    MessageInitShape<typeof AadhaarPanMatchResponseSchema>
  > = {}
) {
  const defaultResponseData = {
    aadhaarPanMatchStatus: false,
    aadhaarPanDobMatch: false,
    aadhaarPanNameMatch: false,
    aadhaarDetails: {
      aadhaarName: faker.person.fullName(),
      aadhaarNumber: `****-****-${faker.string.numeric(4)}`,
    },
    panDetails: {
      panName: faker.person.fullName(),
      panNumber: faker.string.alphanumeric(10).toUpperCase(),
    },
  };

  const responseData = mergeDeep(defaultResponseData, overrides);

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.AADHAAR_PAN_MATCH &&
          requestData.result.case === "emptyKycRequest"
        ) {
          // Validate the request
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          expect(requestJson).toMatchObject({
            stepName: "AADHAAR_PAN_MATCH",
            emptyKycRequest: {},
          });

          const onboardingResponse = {
            kycStatus: 0,
            nextStep: StepName.AADHAAR_PAN_MATCH,
            lifetimeStatus: 0,
            result: {
              case: "aadhaarPanMatchResponse" as const,
              value: responseData,
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, onboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

// Test: should successfully retry PAN and navigate to next step
export function mockRetryStepPan(
  page: Page,
  overrides: DeepPartial<MessageInitShape<typeof OnboardingResponseSchema>> = {}
) {
  const defaultResponseData: MessageInitShape<typeof OnboardingResponseSchema> =
    {
      nextStep: StepName.PAN_KYC,
      kycStatus: 0,
      lifetimeStatus: 0,
    };

  const responseData = mergeDeep(defaultResponseData, overrides);

  page.route("*/**/v1/onboarding/retry-step*", (route) => {
    const url = new URL(route.request().url());
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Validate the request
    expect(queryParams).toMatchObject({
      step: "PAN_KYC",
    });

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingResponseSchema,
          create(OnboardingResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

// Test: should show loading state during retry (slow response)
export function mockRetryStepPanSlow(page: Page) {
  const responseData: MessageInitShape<typeof OnboardingResponseSchema> = {
    nextStep: StepName.PAN_KYC,
    kycStatus: 0,
    lifetimeStatus: 0,
  };

  page.route("*/**/v1/onboarding/retry-step*", async (route) => {
    // Add delay to simulate slow response
    await new Promise((resolve) => setTimeout(resolve, 1000));

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingResponseSchema,
          create(OnboardingResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

// Test: should display error message when retry fails due to server error
export function mockErrorResponse(
  page: Page,
  route: string,
  status: number = 400,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  page.route(route, (routeHandler) => {
    const defaultResponseData = {
      errorCode: faker.string.alphanumeric(8).toUpperCase(),
      errorMessage: faker.lorem.sentence(),
    };

    const responseData = mergeDeep(defaultResponseData, overrides);

    routeHandler.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });
}

// Test: should display error message when retry fails due to network error
export function mockNetworkError(page: Page, route: string) {
  page.route(route, (routeHandler) => {
    // Randomly choose between abort and timeout to simulate different network issues
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);

    if (errorType === "abort") {
      // Abort the request to simulate network disconnection
      routeHandler.abort("internetdisconnected");
    } else {
      // Simulate timeout by aborting with timeout error
      routeHandler.abort("timedout");
    }
  });
}
