import { test, expect } from "@playwright/test";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";
import {
  mockInitiateEmailVerification,
  mockVerifyEmail,
  mockOnboardingState,
  mockInitiateEmailVerificationSlow,
  mockInitiateEmailVerificationNetworkError,
  mockInitiateEmailVerificationServerError400,
  mockInitiateEmailVerificationServerError500,
  mockInitiateEmailVerificationTimeout,
} from "./email.mocks";

test.describe("Onboarding - Email Verification", () => {
  test("should complete email verification and OTP verification flow", async ({
    page,
  }) => {
    const emailData = mockInitiateEmailVerification(page);
    mockVerifyEmail(page);
    mockOnboardingState(page); // No next step, should go to PAN

    await page.goto("/onboarding/email");
    await page.getByRole("textbox", { name: "<PERSON><PERSON>" }).fill(emailData.email);
    await page.getByRole("button", { name: "Verify email" }).click();

    await page.waitForURL(
      `/onboarding/email-otp?challenge=${emailData.challengeId}&email=${emailData.email}`
    );

    await expect(
      page.getByText(`We have sent it to ${emailData.email}`)
    ).toBeVisible();

    await page.getByLabel("OTP").fill(emailData.otpValue);
    await page.getByRole("button", { name: "Submit OTP" }).click();

    await page.waitForURL("/onboarding/pan");
  });

  test.describe("Email OTP Navigation Based on API Response", () => {
    test("should navigate to name when next step is NAME", async ({ page }) => {
      const emailData = mockInitiateEmailVerification(page);
      mockVerifyEmail(page);
      mockOnboardingState(page, KycType.NAME);

      await page.goto("/onboarding/email");
      await page.getByRole("textbox", { name: "Email" }).fill(emailData.email);
      await page.getByRole("button", { name: "Verify email" }).click();

      await page.waitForURL(
        `/onboarding/email-otp?challenge=${emailData.challengeId}&email=${emailData.email}`
      );

      await page.getByLabel("OTP").fill(emailData.otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await page.waitForURL("/onboarding/name");
    });

    test("should navigate to PAN when next step is unknown", async ({
      page,
    }) => {
      const emailData = mockInitiateEmailVerification(page);
      mockVerifyEmail(page);
      mockOnboardingState(page, KycType.KYC_TYPE_UNKNOWN);

      await page.goto("/onboarding/email");
      await page.getByRole("textbox", { name: "Email" }).fill(emailData.email);
      await page.getByRole("button", { name: "Verify email" }).click();

      await page.waitForURL(
        `/onboarding/email-otp?challenge=${emailData.challengeId}&email=${emailData.email}`
      );

      await page.getByLabel("OTP").fill(emailData.otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await page.waitForURL("/onboarding/pan");
    });

    test("should navigate to PAN when no next step is provided", async ({
      page,
    }) => {
      const emailData = mockInitiateEmailVerification(page);
      mockVerifyEmail(page);
      mockOnboardingState(page); // No next step provided

      await page.goto("/onboarding/email");
      await page.getByRole("textbox", { name: "Email" }).fill(emailData.email);
      await page.getByRole("button", { name: "Verify email" }).click();

      await page.waitForURL(
        `/onboarding/email-otp?challenge=${emailData.challengeId}&email=${emailData.email}`
      );

      await page.getByLabel("OTP").fill(emailData.otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      await page.waitForURL("/onboarding/pan");
    });

    test("should navigate to PAN for unexpected next step", async ({
      page,
    }) => {
      const emailData = mockInitiateEmailVerification(page);
      mockVerifyEmail(page);
      mockOnboardingState(page, KycType.PAN_KYC); // Unsupported alpha step

      await page.goto("/onboarding/email");
      await page.getByRole("textbox", { name: "Email" }).fill(emailData.email);
      await page.getByRole("button", { name: "Verify email" }).click();

      await page.waitForURL(
        `/onboarding/email-otp?challenge=${emailData.challengeId}&email=${emailData.email}`
      );

      await page.getByLabel("OTP").fill(emailData.otpValue);
      await page.getByRole("button", { name: "Submit OTP" }).click();

      // Should navigate to PAN since PAN_KYC is not a supported alpha step
      await page.waitForURL("/onboarding/pan");
    });
  });

  test.describe("Email Validation", () => {
    test("should display validation message for empty email", async ({
      page,
    }) => {
      await page.goto("/onboarding/email");
      await page.getByRole("button", { name: "Verify email" }).click();
      await expect(page.getByText("Email is required")).toBeVisible();
    });

    test("should display validation message for invalid email format", async ({
      page,
    }) => {
      await page.goto("/onboarding/email");
      await page.getByRole("textbox", { name: "Email" }).fill("invalid-email");
      await page.getByRole("button", { name: "Verify email" }).click();
      await expect(
        page.getByText("Please enter a valid email address")
      ).toBeVisible();
    });
  });

  test.describe("Error Handling", () => {
    test("should display network error message when API call fails due to network error", async ({
      page,
    }) => {
      mockInitiateEmailVerificationNetworkError(page);

      await page.goto("/onboarding/email");
      await page
        .getByRole("textbox", { name: "Email" })
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Verify email" }).click();

      await expect(page.locator(".text-red").first()).toBeVisible();
      await expect(page.locator(".text-red").first()).toContainText(
        "Please check your internet connection and try again."
      );
    });

    test("should display server error message when API returns 400 status code", async ({
      page,
    }) => {
      const errorMessage = "Email already exists";
      mockInitiateEmailVerificationServerError400(
        page,
        errorMessage,
        "EMAIL_EXISTS"
      );

      await page.goto("/onboarding/email");
      await page
        .getByRole("textbox", { name: "Email" })
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Verify email" }).click();

      await expect(page.locator(".text-red").first()).toBeVisible();
      await expect(page.locator(".text-red").first()).toContainText(
        errorMessage
      );
    });

    test("should display server error message when API returns 500 status code", async ({
      page,
    }) => {
      mockInitiateEmailVerificationServerError500(page);

      await page.goto("/onboarding/email");
      await page
        .getByRole("textbox", { name: "Email" })
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Verify email" }).click();

      await expect(page.locator(".text-red").first()).toBeVisible();
      await expect(page.locator(".text-red").first()).toContainText(
        "Oops! Something went wrong"
      );
    });

    test("should handle timeout errors gracefully", async ({ page }) => {
      mockInitiateEmailVerificationTimeout(page);

      await page.goto("/onboarding/email");
      await page
        .getByRole("textbox", { name: "Email" })
        .fill("<EMAIL>");
      await page.getByRole("button", { name: "Verify email" }).click();

      await expect(page.locator(".text-red").first()).toBeVisible();
      await expect(page.locator(".text-red").first()).toContainText(
        "Please check your internet connection and try again."
      );
    });
  });

  test.describe("Form Interaction", () => {
    test("should show loading state during form submission", async ({
      page,
    }) => {
      mockInitiateEmailVerificationSlow(page, 500);

      await page.goto("/onboarding/email");
      await page
        .getByRole("textbox", { name: "Email" })
        .fill("<EMAIL>");

      const submitButton = page.getByRole("button", { name: "Verify email" });
      const clickPromise = submitButton.click();

      await expect(submitButton).toHaveAttribute("data-loading", "true", {
        timeout: 1000,
      });
      await clickPromise;
    });

    test("should clear validation errors when user starts typing", async ({
      page,
    }) => {
      await page.goto("/onboarding/email");

      await page.getByRole("button", { name: "Verify email" }).click();
      await expect(page.getByText("Email is required")).toBeVisible();

      const emailInput = page.getByRole("textbox", { name: "Email" });
      await emailInput.fill("<EMAIL>");
      await expect(page.getByText("Email is required")).not.toBeVisible();
    });
  });
});
