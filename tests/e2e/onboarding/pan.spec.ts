import { test, expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  mockPanNameFetch,
  mockPanNameFetchSlow,
  mockPanSubmissionSlow,
  mockPanNetworkError,
  mockPanCompleteFlow,
  mockPanFlowNameMismatch,
  mockPanFlowDobMismatch,
  mockPanFlowInvalidPan,
  mockPanFlowFailedKyc,
} from "./pan.mocks";

// Helper function to generate valid PAN numbers
function generateValidPan(): string {
  const letters1 = faker.string.alpha({ length: 5, casing: "upper" });
  const digits = faker.string.numeric(4);
  const letter2 = faker.string.alpha({ length: 1, casing: "upper" });
  return `${letters1}${digits}${letter2}`;
}

test.describe("Onboarding - PAN Details", () => {
  test.describe("Happy Path", () => {
    test("should load PAN page and show initial form", async ({ page }) => {
      await page.goto("/onboarding/pan");

      await expect(
        page.getByRole("textbox", { name: "PAN", exact: true })
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeVisible();

      // All fields should be visible from the start
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toBeVisible();
      await expect(
        page.getByRole("textbox", { name: "Date of birth" })
      ).toBeVisible();
    });

    test("should auto-fill name when valid PAN is entered", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Mock PAN name fetch before entering PAN
      const responseObject = mockPanNameFetch(page);

      // Name field should be visible from the start
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toBeVisible();

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // Name should be auto-filled after PAN entry
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(responseObject.fullName);
    });

    test("should show loading indicator while fetching PAN name", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Mock slow PAN name fetch before entering PAN
      mockPanNameFetchSlow(page);

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);
      await expect(page.locator("svg").first()).toBeVisible();
    });

    test("should successfully submit PAN details and navigate to next step", async ({
      page,
    }) => {
      const panNumber = generateValidPan();
      const dateOfBirth = "15/08/1990";

      await page.goto("/onboarding/pan");

      // Mock complete PAN flow - handles both name fetch and submission
      const { nameData } = mockPanCompleteFlow(page);

      // All fields should be visible from the start
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toBeVisible();
      await expect(
        page.getByRole("textbox", { name: "Date of birth" })
      ).toBeVisible();

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // Name should be auto-filled after PAN entry
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);

      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill(dateOfBirth);
      await page.getByText("I hereby give my consent").click();

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      await page.waitForURL("/onboarding/aadhar-pan-match");
    });
  });

  test.describe("Form Validation", () => {
    test("should show validation error when PAN is empty and form is submitted", async ({
      page,
    }) => {
      await page.goto("/onboarding/pan");

      // Submit button should be enabled but form should show validation errors
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeEnabled();

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      // Validation message should appear after submission attempt
      await expect(page.getByText("Pan number is required")).toBeVisible();
    });

    test("should show validation error when PAN format is invalid and form is submitted", async ({
      page,
    }) => {
      await page.goto("/onboarding/pan");
      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill("INVALID123");

      // Submit button should be enabled
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeEnabled();

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      // Validation message should appear after submission attempt
      await expect(
        page.getByText("PAN is invalid, check again?")
      ).toBeVisible();
    });

    test("should show validation error when name is empty and form is submitted", async ({
      page,
    }) => {
      const panNumber = generateValidPan();
      await page.goto("/onboarding/pan");

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);
      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Submit button should be enabled
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeEnabled();

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      // Validation message should appear after submission attempt
      await expect(page.getByText("Name is required")).toBeVisible();
    });

    test("should show validation error when date of birth is empty and form is submitted", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Mock PAN name fetch first
      const nameResponse = mockPanNameFetch(page);

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);
      await page
        .getByRole("textbox", { name: "Name as per PAN" })
        .fill(nameResponse.fullName);
      await page.getByText("I hereby give my consent").click();

      // Submit button should be enabled
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeEnabled();

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      // Validation message should appear after submission attempt
      await expect(page.getByText("Date of birth is required")).toBeVisible();
    });

    test("should show validation error when consent is unchecked and form is submitted", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Mock PAN name fetch first
      mockPanNameFetch(page);

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);
      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");

      // Submit button should be enabled
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeEnabled();

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      // Validation message should appear after submission attempt
      await expect(
        page.getByText("You must agree to the terms and conditions")
      ).toBeVisible();
    });

    test("should submit successfully when form is valid", async ({ page }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Submit button should always be enabled
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeEnabled();

      // Mock complete PAN flow
      mockPanCompleteFlow(page);

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);
      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      // Should navigate to next page on successful submission
      await page.waitForURL("/onboarding/aadhar-pan-match");
    });
  });

  test.describe("API Error Handling", () => {
    test("should display error for name mismatch", async ({ page }) => {
      const panNumber = generateValidPan();

      // Set up combined mock for name fetch and name mismatch error
      const { nameData } = mockPanFlowNameMismatch(page);

      await page.goto("/onboarding/pan");

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // Wait for name to be auto-filled
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);

      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Ensure all fields are properly filled before submission
      await expect(
        page.getByRole("textbox", { name: "PAN", exact: true })
      ).toHaveValue(panNumber);
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);
      await expect(
        page.getByRole("textbox", { name: "Date of birth" })
      ).toHaveValue("15/08/1990");
      await expect(page.getByText("I hereby give my consent")).toBeChecked();

      const submitButton = page.getByRole("button", { name: "Confirm PAN" });

      // Ensure button is enabled before clicking
      await expect(submitButton).toBeEnabled();

      await submitButton.click();

      // The error should appear as a field validation error under the name input
      await expect(page.getByText("Name is not as per PAN")).toBeVisible({
        timeout: 10000,
      });
    });

    test("should display error for date of birth mismatch", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      // Set up combined mock for name fetch and DOB mismatch error
      const { nameData } = mockPanFlowDobMismatch(page);

      await page.goto("/onboarding/pan");

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // Wait for name to be auto-filled
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);

      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Ensure all fields are properly filled before submission
      await expect(
        page.getByRole("textbox", { name: "PAN", exact: true })
      ).toHaveValue(panNumber);
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);
      await expect(
        page.getByRole("textbox", { name: "Date of birth" })
      ).toHaveValue("15/08/1990");
      await expect(page.getByText("I hereby give my consent")).toBeChecked();

      const submitButton = page.getByRole("button", { name: "Confirm PAN" });

      // Ensure button is enabled before clicking
      await expect(submitButton).toBeEnabled();

      await submitButton.click();

      await expect(
        page.getByText("Date of birth is not as per PAN")
      ).toBeVisible({ timeout: 10000 });
    });

    test("should display error for invalid PAN", async ({ page }) => {
      const panNumber = generateValidPan();

      // Set up combined mock for name fetch and invalid PAN error
      const { nameData } = mockPanFlowInvalidPan(page);

      await page.goto("/onboarding/pan");

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // Wait for name to be auto-filled
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);

      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Ensure all fields are properly filled before submission
      await expect(
        page.getByRole("textbox", { name: "PAN", exact: true })
      ).toHaveValue(panNumber);
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);
      await expect(
        page.getByRole("textbox", { name: "Date of birth" })
      ).toHaveValue("15/08/1990");
      await expect(page.getByText("I hereby give my consent")).toBeChecked();

      const submitButton = page.getByRole("button", { name: "Confirm PAN" });

      // Ensure button is enabled before clicking
      await expect(submitButton).toBeEnabled();

      await submitButton.click();

      // Wait for form submission to complete and error to appear
      // Check if error appears as field error or toast
      await expect(
        page.getByText("PAN is invalid, please check again")
      ).toBeVisible({ timeout: 10000 });
    });

    test("should display error for failed PAN KYC status", async ({ page }) => {
      const panNumber = generateValidPan();

      // Set up combined mock for name fetch and failed KYC status
      const { nameData } = mockPanFlowFailedKyc(page);

      await page.goto("/onboarding/pan");

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // Wait for name to be auto-filled
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);

      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Ensure all fields are properly filled before submission
      await expect(
        page.getByRole("textbox", { name: "PAN", exact: true })
      ).toHaveValue(panNumber);
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue(nameData.fullName);
      await expect(
        page.getByRole("textbox", { name: "Date of birth" })
      ).toHaveValue("15/08/1990");
      await expect(page.getByText("I hereby give my consent")).toBeChecked();

      const submitButton = page.getByRole("button", { name: "Confirm PAN" });

      // Ensure button is enabled before clicking
      await expect(submitButton).toBeEnabled();

      await submitButton.click();

      await expect(
        page.getByText("PAN is invalid, please check again")
      ).toBeVisible({ timeout: 10000 });
    });

    test("should handle network errors during PAN name fetch gracefully", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      mockPanNetworkError(page);

      await page.goto("/onboarding/pan");
      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // With the new implementation, PAN name fetch errors are handled silently
      // The name field should remain empty and the user can manually enter the name
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toHaveValue("");

      // User should still be able to manually fill the form and submit
      await page
        .getByRole("textbox", { name: "Name as per PAN" })
        .fill("Manual Name");
      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Submit button should be enabled
      await expect(
        page.getByRole("button", { name: "Confirm PAN" })
      ).toBeEnabled();
    });

    test("should handle network errors during form submission", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Mock PAN name fetch first
      mockPanNameFetch(page);

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);
      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Mock network error for PAN submission before clicking submit
      mockPanNetworkError(page);

      await page.getByRole("button", { name: "Confirm PAN" }).click();

      // Should show error toast (withErrorToast shows errors as toasts)
      await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during form submission", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Mock PAN name fetch first
      mockPanNameFetch(page);

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);
      await page
        .getByRole("textbox", { name: "Date of birth" })
        .fill("15/08/1990");
      await page.getByText("I hereby give my consent").click();

      // Mock slow PAN submission before clicking submit
      mockPanSubmissionSlow(page);

      const submitButton = page.getByRole("button", { name: "Confirm PAN" });
      await submitButton.click();

      await expect(submitButton.locator("svg")).toBeVisible();
    });
  });

  test.describe("Form Interaction", () => {
    test("should convert PAN input to uppercase", async ({ page }) => {
      await page.goto("/onboarding/pan");

      const panInput = page.getByRole("textbox", { name: "PAN", exact: true });
      await panInput.fill("abcde12345");
      // The input has textTransform="uppercase" CSS class, but the value might not change immediately
      // Let's check if the input appears uppercase visually
      await expect(panInput).toHaveClass(/uppercase/);
    });

    test("should limit PAN input to 10 characters", async ({ page }) => {
      await page.goto("/onboarding/pan");

      const panInput = page.getByRole("textbox", { name: "PAN", exact: true });
      await panInput.fill("ABCDE123456789");
      await expect(panInput).toHaveValue("ABCDE12345");
    });

    test("should format date input with mask", async ({ page }) => {
      await page.goto("/onboarding/pan");

      // Date field should be visible from the start
      await expect(
        page.getByRole("textbox", { name: "Date of birth" })
      ).toBeVisible();
      const dateInput = page.getByRole("textbox", { name: "Date of birth" });

      // Type the date character by character to trigger the mask
      await dateInput.pressSequentially("15081990");
      await expect(dateInput).toHaveValue("15/08/1990");
    });

    test("should allow manual name editing when auto-filled", async ({
      page,
    }) => {
      const panNumber = generateValidPan();

      await page.goto("/onboarding/pan");

      // Mock PAN name fetch before entering PAN
      const responseObject = mockPanNameFetch(page);

      // Name field should be visible from the start
      await expect(
        page.getByRole("textbox", { name: "Name as per PAN" })
      ).toBeVisible();
      const nameInput = page.getByRole("textbox", { name: "Name as per PAN" });

      await page
        .getByRole("textbox", { name: "PAN", exact: true })
        .fill(panNumber);

      // Name should be auto-filled after PAN entry
      await expect(nameInput).toHaveValue(responseObject.fullName);

      await nameInput.clear();
      await nameInput.fill("Different Name");
      await expect(nameInput).toHaveValue("Different Name");
    });
  });
});
