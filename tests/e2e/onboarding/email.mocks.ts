import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";
import { OnboardingStateSchema } from "@/clients/gen/platform/public/models/identity/Onboarding_pb";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  AuthType,
  InitiateVerifyRequestSchema,
  InitiateVerifyResponseSchema,
  RespondToVerifyChallengeRequestSchema,
  RespondToVerifyChallengeResponseSchema,
} from "@/clients/gen/platform/public/models/identity/Auth_pb";

type EmailVerificationData = {
  email: string;
  challengeId: string;
  otpValue: string;
};

// Mock for POST /v1/auth/initiate-verification endpoint
export function mockInitiateEmailVerification(page: Page) {
  const emailData: EmailVerificationData = {
    email: faker.internet.email(),
    challengeId: faker.string.uuid(),
    otpValue: faker.string.numeric(6),
  };

  page.route("*/**/v1/auth/initiate-verification*", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(
          InitiateVerifyRequestSchema,
          requestBuffer
        );

        // Validate request structure
        if (requestData.authType !== AuthType.EMAIL_OTP) {
          throw new Error(
            `Expected authType EMAIL_OTP, got ${requestData.authType}`
          );
        }

        if (requestData.result.case !== "email") {
          throw new Error(
            `Expected result case 'email', got ${requestData.result.case}`
          );
        }

        const requestJson = toJson(
          InitiateVerifyRequestSchema,
          requestData
        ) as Record<string, unknown>;
        if (
          !requestJson.email ||
          (typeof requestJson.email === "string" &&
            requestJson.email.trim() === "")
        ) {
          throw new Error("Expected non-empty email in request");
        }
      }

      const responseData: MessageInitShape<
        typeof InitiateVerifyResponseSchema
      > = {
        result: {
          case: "otpChallenge",
          value: {
            challengeId: emailData.challengeId,
            expiry: BigInt(Date.now() + 300000), // 5 minutes
          },
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            InitiateVerifyResponseSchema,
            create(InitiateVerifyResponseSchema, responseData)
          )
        ),
      });
      return;
    }
    route.continue();
  });

  return emailData;
}

// Mock for POST /v1/auth/verify endpoint
export function mockVerifyEmail(page: Page) {
  page.route("*/**/v1/auth/verify*", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(
          RespondToVerifyChallengeRequestSchema,
          requestBuffer
        );

        // Validate request structure
        const requestJson = toJson(
          RespondToVerifyChallengeRequestSchema,
          requestData
        ) as Record<string, unknown>;
        if (
          !requestJson.challengeId ||
          (typeof requestJson.challengeId === "string" &&
            requestJson.challengeId.trim() === "")
        ) {
          throw new Error("Expected non-empty challengeId in request");
        }

        if (
          !requestJson.answer ||
          (typeof requestJson.answer === "string" &&
            requestJson.answer.trim() === "")
        ) {
          throw new Error("Expected non-empty answer in request");
        }
      }

      const responseData: MessageInitShape<
        typeof RespondToVerifyChallengeResponseSchema
      > = {};

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            RespondToVerifyChallengeResponseSchema,
            create(RespondToVerifyChallengeResponseSchema, responseData)
          )
        ),
      });
      return;
    }
    route.continue();
  });
}

// Mock for GET /v1/onboarding/state endpoint (identity API)
export function mockOnboardingState(page: Page, nextStep?: KycType) {
  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData: MessageInitShape<typeof OnboardingStateSchema> =
      nextStep ? { next: nextStep } : {};

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
    });
  });
}

// Mock for POST /v1/auth/initiate-verification endpoint with slow response
export function mockInitiateEmailVerificationSlow(
  page: Page,
  delay: number = 500
) {
  const emailData: EmailVerificationData = {
    email: faker.internet.email(),
    challengeId: faker.string.uuid(),
    otpValue: faker.string.numeric(6),
  };

  page.route("*/**/v1/auth/initiate-verification*", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      await new Promise((resolve) => setTimeout(resolve, delay));

      const responseData: MessageInitShape<
        typeof InitiateVerifyResponseSchema
      > = {
        result: {
          case: "otpChallenge",
          value: {
            challengeId: emailData.challengeId,
            expiry: BigInt(Date.now() + 300000),
          },
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            InitiateVerifyResponseSchema,
            create(InitiateVerifyResponseSchema, responseData)
          )
        ),
      });
      return;
    }
    route.continue();
  });

  return emailData;
}

// Mock for POST /v1/auth/initiate-verification endpoint with network error
export function mockInitiateEmailVerificationNetworkError(page: Page) {
  page.route("*/**/v1/auth/initiate-verification*", (route) => {
    route.abort("internetdisconnected");
  });
}

// Mock for POST /v1/auth/initiate-verification endpoint with server error (400)
export function mockInitiateEmailVerificationServerError400(
  page: Page,
  errorMessage: string,
  errorCode: string
) {
  page.route("*/**/v1/auth/initiate-verification*", (route) => {
    route.fulfill({
      status: 400,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        errorMessage: errorMessage,
        errorCode: errorCode,
      }),
    });
  });
}

// Mock for POST /v1/auth/initiate-verification endpoint with server error (500)
export function mockInitiateEmailVerificationServerError500(page: Page) {
  page.route("*/**/v1/auth/initiate-verification*", (route) => {
    route.fulfill({
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        errorMessage: "Oops! Something went wrong",
      }),
    });
  });
}

// Mock for POST /v1/auth/initiate-verification endpoint with timeout error
export function mockInitiateEmailVerificationTimeout(page: Page) {
  page.route("*/**/v1/auth/initiate-verification*", (route) => {
    route.abort("timedout");
  });
}
