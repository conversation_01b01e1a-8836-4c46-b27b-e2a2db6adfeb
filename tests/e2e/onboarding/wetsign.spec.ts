import { test, expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  mockDocumentUpload,
  mockWetSignatureSubmission,
  mockDocumentUploadServerError,
  mockWetSignatureServerError,
  mockDocumentUploadNetworkError,
  mockWetSignatureNetworkError,
  mockDocumentUploadNoId,
  mockSlowDocumentUpload,
  mockSlowWetSignatureSubmission,
} from "./wetsign.mocks";

test.describe("Onboarding - Wet Signature", () => {
  test.describe("Happy Path", () => {
    test("should load wet signature page and show form elements", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      await expect(page.getByText("Verify your signature")).toBeVisible();
      await expect(
        page.getByText("Please sign below to complete your account opening")
      ).toBeVisible();

      await expect(page.locator("canvas")).toBeVisible();
      await expect(page.getByRole("button", { name: "Proceed" })).toBeVisible();

      await expect(
        page.getByText("I am not a PEP (Politically exposed person)")
      ).toBeVisible();
      await expect(
        page.getByText("I am an Indian citizen, born and residing in India")
      ).toBeVisible();
      await expect(
        page.getByText("There has been no action initiated")
      ).toBeVisible();

      await expect(page.getByText("Use till")).toBeVisible();
      await expect(page.locator("select[name='raaDuration']")).toBeVisible();
    });

    test("should successfully submit wet signature and navigate to next step", async ({
      page,
    }) => {
      const documentId = faker.string.uuid();

      await page.goto("/onboarding/wetsign");

      mockDocumentUpload(page, { document_id: documentId });
      mockWetSignatureSubmission(page);

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.move(200, 150);
      await page.mouse.up();

      // Checkboxes should already be checked by default, so no need to click them
      // Verify they are checked
      await expect(page.locator("input[name='pep']")).toBeChecked();
      await expect(page.locator("input[name='indianCitizen']")).toBeChecked();
      await expect(page.locator("input[name='sebi']")).toBeChecked();

      await page.getByRole("button", { name: "Proceed" }).click();

      await page.waitForURL("/onboarding/esign");
    });

    test("should allow selecting different RAA duration options", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      const raaDurationSelect = page.locator("select[name='raaDuration']");
      await expect(raaDurationSelect).toBeVisible();

      await expect(raaDurationSelect.locator("option")).toHaveCount(2);
      await expect(
        raaDurationSelect.locator("option[value='1']")
      ).toContainText("60 days");
      await expect(
        raaDurationSelect.locator("option[value='2']")
      ).toContainText("90 days");

      await raaDurationSelect.selectOption("2");
      await expect(raaDurationSelect).toHaveValue("2");
    });

    test("should clear signature when refresh button is clicked", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      const canvas = page.locator("canvas");

      // Draw a signature on the canvas
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.move(200, 150);
      await page.mouse.up();

      // Get canvas data before clearing to verify it has content
      const canvasDataBefore = await canvas.evaluate(
        (canvas: HTMLCanvasElement) => {
          const ctx = canvas.getContext("2d");
          return ctx
            ?.getImageData(0, 0, canvas.width, canvas.height)
            .data.some((pixel) => pixel !== 0);
        }
      );

      // Verify canvas has signature data
      expect(canvasDataBefore).toBe(true);

      const refreshButton = page.locator("button[type='button']").first();
      await refreshButton.click();

      // Verify canvas is cleared (all pixels should be transparent/empty)
      const canvasDataAfter = await canvas.evaluate(
        (canvas: HTMLCanvasElement) => {
          const ctx = canvas.getContext("2d");
          return ctx
            ?.getImageData(0, 0, canvas.width, canvas.height)
            .data.some((pixel) => pixel !== 0);
        }
      );

      expect(canvasDataAfter).toBe(false);
    });
  });

  test.describe("Form Validation", () => {
    test("should show validation error when signature is missing on submit", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      // No mocks needed for client-side validation
      // Checkboxes should already be checked by default, so no need to click them

      await page.getByRole("button", { name: "Proceed" }).click();

      await expect(
        page
          .locator("#wetsign-form")
          .getByText("Signature is required to proceed")
      ).toBeVisible();
    });

    test("should show validation errors when checkboxes are unchecked on submit", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      // No mocks needed for client-side validation
      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Uncheck one of the checkboxes to trigger validation error
      await page
        .getByText("I am not a PEP (Politically exposed person)")
        .click();

      await page.getByRole("button", { name: "Proceed" }).click();

      await expect(
        page
          .locator("#wetsign-form")
          .getByText("Please confirm to proceed")
          .first()
      ).toBeVisible();
    });
  });

  test.describe("API Error Handling", () => {
    test("should handle document upload server errors", async ({ page }) => {
      await page.goto("/onboarding/wetsign");

      mockDocumentUploadServerError(page);

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Checkboxes should already be checked by default

      await page.getByRole("button", { name: "Proceed" }).click();

      // The current implementation throws this error when document upload fails
      await expect(
        page.getByText("Wet Signature Upload API call failed").first()
      ).toBeVisible();
    });

    test("should handle wet signature submission server errors", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      const documentId = faker.string.uuid();
      mockDocumentUpload(page, { document_id: documentId });
      mockWetSignatureServerError(page, "Wet signature save failed");

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Checkboxes should already be checked by default

      await page.getByRole("button", { name: "Proceed" }).click();

      await expect(
        page.getByText("Wet signature save failed").first()
      ).toBeVisible();
    });

    test("should handle document upload network errors", async ({ page }) => {
      await page.goto("/onboarding/wetsign");

      mockDocumentUploadNetworkError(page);

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Checkboxes should already be checked by default

      await page.getByRole("button", { name: "Proceed" }).click();

      await expect(
        page
          .getByText("Please check your internet connection and try again.")
          .first()
      ).toBeVisible();
    });

    test("should handle wet signature submission network errors", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      const documentId = faker.string.uuid();
      mockDocumentUpload(page, { document_id: documentId });
      mockWetSignatureNetworkError(page);

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Checkboxes should already be checked by default

      await page.getByRole("button", { name: "Proceed" }).click();

      await expect(
        page
          .getByText("Please check your internet connection and try again.")
          .first()
      ).toBeVisible();
    });

    test("should handle document upload failure without document_id", async ({
      page,
    }) => {
      await page.goto("/onboarding/wetsign");

      mockDocumentUploadNoId(page);

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Checkboxes should already be checked by default

      await page.getByRole("button", { name: "Proceed" }).click();

      await expect(
        page.getByText("Wet Signature Upload API call failed").first()
      ).toBeVisible();
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during document upload", async ({
      page,
    }) => {
      const documentId = faker.string.uuid();

      await page.goto("/onboarding/wetsign");

      mockSlowDocumentUpload(page, 1000, { document_id: documentId });
      mockWetSignatureSubmission(page);

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Checkboxes should already be checked by default

      const submitButton = page.getByRole("button", { name: "Proceed" });
      await submitButton.click();

      await expect(submitButton.locator("svg")).toBeVisible();
    });

    test("should show loading state during wet signature submission", async ({
      page,
    }) => {
      const documentId = faker.string.uuid();

      await page.goto("/onboarding/wetsign");

      mockDocumentUpload(page, { document_id: documentId });
      mockSlowWetSignatureSubmission(page, 1000);

      const canvas = page.locator("canvas");
      await canvas.hover();
      await page.mouse.down();
      await page.mouse.move(100, 100);
      await page.mouse.up();

      // Checkboxes should already be checked by default

      const submitButton = page.getByRole("button", { name: "Proceed" });
      await submitButton.click();

      await expect(submitButton.locator("svg")).toBeVisible();
    });
  });
});
