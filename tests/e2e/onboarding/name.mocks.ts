import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  UpdateNameRequestSchema,
  UpdateNameResponseSchema,
} from "@/clients/gen/platform/public/models/identity/Profile_pb";
import { OnboardingStateSchema } from "@/clients/gen/platform/public/models/identity/Onboarding_pb";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";

/**
 * Mock name submission for navigation to email test
 */
export function mockPostNameNavigateToEmail(page: Page) {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();

  page.route("*/**/v1/user/name", async (route) => {
    const request = route.request();
    if (request.method() === "PUT") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(UpdateNameRequestSchema, requestBuffer);

        // Validate request structure
        const requestJson = toJson(
          UpdateNameRequestSchema,
          requestData
        ) as Record<string, unknown>;

        // Validate that the request contains firstName and lastName
        if (
          typeof requestJson.firstName === "string" &&
          typeof requestJson.lastName === "string"
        ) {
          const responseData: MessageInitShape<
            typeof UpdateNameResponseSchema
          > = {};

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                UpdateNameResponseSchema,
                create(UpdateNameResponseSchema, responseData)
              )
            ),
            headers: {
              "Content-Type": "application/x-protobuf",
            },
          });
          return;
        }
      }
    }
    route.continue();
  });

  return { firstName, lastName };
}

/**
 * Mock onboarding state for navigation to email test
 */
export function mockOnboardingStateNavigateToEmail(page: Page) {
  const nextStep = KycType.EMAIL;

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData: MessageInitShape<typeof OnboardingStateSchema> = {
      next: nextStep,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return { nextStep };
}

/**
 * Mock name submission for navigation to PAN (unknown step) test
 */
export function mockPostNameNavigateToPanUnknown(page: Page) {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();

  page.route("*/**/v1/user/name", async (route) => {
    const request = route.request();
    if (request.method() === "PUT") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(UpdateNameRequestSchema, requestBuffer);

        // Validate request structure
        const requestJson = toJson(
          UpdateNameRequestSchema,
          requestData
        ) as Record<string, unknown>;

        // Validate that the request contains firstName and lastName
        if (
          typeof requestJson.firstName === "string" &&
          typeof requestJson.lastName === "string"
        ) {
          const responseData: MessageInitShape<
            typeof UpdateNameResponseSchema
          > = {};

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                UpdateNameResponseSchema,
                create(UpdateNameResponseSchema, responseData)
              )
            ),
            headers: {
              "Content-Type": "application/x-protobuf",
            },
          });
          return;
        }
      }
    }
    route.continue();
  });

  return { firstName, lastName };
}

/**
 * Mock onboarding state for navigation to PAN (unknown step) test
 */
export function mockOnboardingStateNavigateToPanUnknown(page: Page) {
  const nextStep = KycType.KYC_TYPE_UNKNOWN;

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData: MessageInitShape<typeof OnboardingStateSchema> = {
      next: nextStep,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return { nextStep };
}

/**
 * Mock name submission for navigation to PAN (no step) test
 */
export function mockPostNameNavigateToPanNoStep(page: Page) {
  const firstName = faker.person.firstName().replace(/[^a-zA-Z\s]/g, "");
  const lastName = faker.person.lastName().replace(/[^a-zA-Z\s]/g, "");

  page.route("*/**/v1/user/name", async (route) => {
    const request = route.request();
    if (request.method() === "PUT") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(UpdateNameRequestSchema, requestBuffer);

        // Validate request structure
        const requestJson = toJson(
          UpdateNameRequestSchema,
          requestData
        ) as Record<string, unknown>;

        // Validate that the request contains firstName and lastName
        if (
          typeof requestJson.firstName === "string" &&
          typeof requestJson.lastName === "string"
        ) {
          const responseData: MessageInitShape<
            typeof UpdateNameResponseSchema
          > = {};

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                UpdateNameResponseSchema,
                create(UpdateNameResponseSchema, responseData)
              )
            ),
            headers: {
              "Content-Type": "application/x-protobuf",
            },
          });
          return;
        }
      }
    }
    route.continue();
  });

  return { firstName, lastName };
}

/**
 * Mock onboarding state for navigation to PAN (no step) test
 */
export function mockOnboardingStateNavigateToPanNoStep(page: Page) {
  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData: MessageInitShape<typeof OnboardingStateSchema> = {};

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return {};
}

/**
 * Mock name submission for navigation to PAN (unexpected step) test
 */
export function mockPostNameNavigateToPanUnexpected(page: Page) {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();

  page.route("*/**/v1/user/name", async (route) => {
    const request = route.request();
    if (request.method() === "PUT") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(UpdateNameRequestSchema, requestBuffer);

        // Validate request structure
        const requestJson = toJson(
          UpdateNameRequestSchema,
          requestData
        ) as Record<string, unknown>;

        // Validate that the request contains firstName and lastName
        if (
          typeof requestJson.firstName === "string" &&
          typeof requestJson.lastName === "string"
        ) {
          const responseData: MessageInitShape<
            typeof UpdateNameResponseSchema
          > = {};

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                UpdateNameResponseSchema,
                create(UpdateNameResponseSchema, responseData)
              )
            ),
            headers: {
              "Content-Type": "application/x-protobuf",
            },
          });
          return;
        }
      }
    }
    route.continue();
  });

  return { firstName, lastName };
}

/**
 * Mock onboarding state for navigation to PAN (unexpected step) test
 */
export function mockOnboardingStateNavigateToPanUnexpected(page: Page) {
  const nextStep = KycType.KYC_TYPE_UNKNOWN;

  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData: MessageInitShape<typeof OnboardingStateSchema> = {
      next: nextStep,
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return { nextStep };
}

/**
 * Mock name submission with delay for loading state test
 */
export function mockPostNameLoadingState(page: Page, delay: number = 2000) {
  const firstName = faker.person.firstName();
  const lastName = faker.person.lastName();

  page.route("*/**/v1/user/name", async (route) => {
    const request = route.request();
    if (request.method() === "PUT") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(UpdateNameRequestSchema, requestBuffer);

        // Validate request structure
        const requestJson = toJson(
          UpdateNameRequestSchema,
          requestData
        ) as Record<string, unknown>;

        // Validate that the request contains firstName and lastName
        if (
          typeof requestJson.firstName === "string" &&
          typeof requestJson.lastName === "string"
        ) {
          // Add delay to simulate slow response
          await new Promise((resolve) => setTimeout(resolve, delay));

          const responseData: MessageInitShape<
            typeof UpdateNameResponseSchema
          > = {};

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                UpdateNameResponseSchema,
                create(UpdateNameResponseSchema, responseData)
              )
            ),
            headers: {
              "Content-Type": "application/x-protobuf",
            },
          });
          return;
        }
      }
    }
    route.continue();
  });

  return { firstName, lastName };
}

/**
 * Mock onboarding state for loading state test
 */
export function mockOnboardingStateLoadingState(page: Page) {
  page.route("*/**/v1/onboarding/state?module=APP_ONBOARDING", (route) => {
    const responseData: MessageInitShape<typeof OnboardingStateSchema> = {};

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          OnboardingStateSchema,
          create(OnboardingStateSchema, responseData)
        )
      ),
      headers: {
        "Content-Type": "application/x-protobuf",
      },
    });
  });

  return {};
}
