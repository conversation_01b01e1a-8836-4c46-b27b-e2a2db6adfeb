import { test, expect } from "@playwright/test";
import {
  mobileLoadPageMockUserProfile,
  mobileLoadPageMockOnboardingState,
  mobileLoadPageMockRpdInitiation,
  mobileStartPollingMockRpdInitiationWithOnboardingStatus,
  mobileStartPollingMockRpdStatusPolling,
  mobileSuccessScreenMockRpdStatusPolling,
  errorHandlingMockUserProfile,
  errorHandlingMockOnboardingState,
  serverError500MockErrorResponse,
  clientError400MockErrorResponse,
  networkErrorMockNetworkError,
  desktopErrorHandlingMockUserProfile,
  desktopErrorHandlingMockOnboardingState,
  desktopServerError500MockErrorResponse,
  desktopClientError400MockErrorResponse,
  desktopNetworkErrorMockNetworkError,
  mockRpdInitiation,
  mockRpdInitiationWithOnboardingStatus,
  mockRpdStatusPolling,
  mockOnboardingStatusForSuccess,
} from "./bank-rpd.mocks";

test.describe("Onboarding - Bank RPD", () => {
  test.beforeEach(async ({ page }) => {
    mobileLoadPageMockUserProfile(page);
    mobileLoadPageMockOnboardingState(page);
    page.route("upi:**", (route) => {
      route.abort();
    });
  });

  test.describe("Mobile", () => {
    test.skip(({ isMobile }) => !isMobile, "Mobile-only tests");

    test("should load RPD page and show UPI options", async ({ page }) => {
      mobileLoadPageMockRpdInitiation(page);

      await page.goto("/onboarding/bank-rpd");

      await expect(
        page.getByText("Verify bank account with UPI")
      ).toBeVisible();
      await expect(
        page.getByText(
          "Pay ₹1 via UPI from that account. The money will be refunded in 1 day."
        )
      ).toBeVisible();

      // On mobile, UPI app options should be visible with links
      await expect(page.getByText("PhonePe")).toBeVisible();
      await expect(page.getByText("Google Pay")).toBeVisible();

      // Check that the parent anchor elements have the correct href attributes
      const phonePeLink = page
        .locator("a")
        .filter({ has: page.getByText("PhonePe") });
      await expect(phonePeLink).toHaveAttribute(
        "href",
        /^upi:\/\/pay\?pa=test@phonepe&pn=Test&am=1&cu=INR&tn=RPD-/
      );

      const googlePayLink = page
        .locator("a")
        .filter({ has: page.getByText("Google Pay") });
      await expect(googlePayLink).toHaveAttribute(
        "href",
        /^upi:\/\/pay\?pa=test@gpay&pn=Test&am=1&cu=INR&tn=RPD-/
      );
    });

    test("should start polling when UPI app is clicked", async ({ page }) => {
      await page.clock.install();

      mobileStartPollingMockRpdInitiationWithOnboardingStatus(page);
      mobileStartPollingMockRpdStatusPolling(page, { successOnCall: 15 });

      await page.goto("/onboarding/bank-rpd");

      // Wait for page to load and verify UPI options are visible
      await expect(page.getByText("PhonePe")).toBeVisible();

      // Click on PhonePe option - need to click the parent anchor element
      const phonePeLink = page
        .locator("a")
        .filter({ has: page.getByText("PhonePe") });
      await phonePeLink.click();

      // Should navigate to status page with refId as query parameter
      await expect(page).toHaveURL(/\/onboarding\/bank-rpd\?refId=[^&]+$/);

      // Should show loading state initially
      await expect(page.getByText("Verifying your transaction")).toBeVisible();
    });

    test.skip("should show success screen and auto-redirect after successful polling", async ({
      page,
    }) => {
      // Use the same RPD initiation mock as the working test
      mobileLoadPageMockRpdInitiation(page);

      // Add status polling mock for success flow
      mobileSuccessScreenMockRpdStatusPolling(page, { successOnCall: 3 });

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByText("PhonePe")).toBeVisible();

      // Click on PhonePe option to start the flow
      const phonePeLink = page
        .locator("a")
        .filter({ has: page.getByText("PhonePe") });
      await phonePeLink.click();

      // Should navigate to status page with refId
      await expect(page).toHaveURL(/\/onboarding\/bank-rpd\/[^/]+$/);
      await expect(page.getByText("Checking transaction detail")).toBeVisible();

      // Add onboarding status mock after polling starts
      mockOnboardingStatusForSuccess(page);

      // Should show success screen after polling completes
      await expect(page.getByText("Bank account verified!")).toBeVisible({
        timeout: 18000,
      });

      // Note: Auto-redirect to next step is not implemented yet
      // await page.waitForURL("/onboarding/pan");
    });
  });

  test.describe("Mobile - Error Handling", () => {
    test.skip(({ isMobile }) => !isMobile, "Mobile-only tests");

    test.beforeEach(async ({ page }) => {
      errorHandlingMockUserProfile(page);
      errorHandlingMockOnboardingState(page);
    });

    test("should show error page when RPD initiation fails with 500 server error", async ({
      page,
    }) => {
      serverError500MockErrorResponse(page, "Internal server error");

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Verify the correct error message is displayed
      await expect(page.getByText("Internal server error")).toBeVisible();

      // Verify user is not stuck on loading state
      await expect(page.getByText("Loading...")).not.toBeVisible();

      // Test that retry button reloads the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test("should show error page when RPD initiation fails with 400 client error", async ({
      page,
    }) => {
      clientError400MockErrorResponse(page, "This step is already completed");

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Verify the correct error message is displayed
      await expect(
        page.getByText("This step is already completed")
      ).toBeVisible();

      await expect(page.getByText("Loading...")).not.toBeVisible();

      // Test that retry button reloads the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test("should show error page when RPD initiation fails with network error", async ({
      page,
    }) => {
      networkErrorMockNetworkError(page);

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Verify network error message is displayed
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();

      // Test that retry button reloads the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test.skip("should show error page when polling limit is reached", async ({
      page,
    }) => {
      await page.clock.install();

      mockRpdInitiationWithOnboardingStatus(page);
      mockRpdStatusPolling(page, { successOnCall: 50 }); // Higher than mobile rpdStatusFetchLimit = 40

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByText("PhonePe")).toBeVisible();

      // Click on PhonePe option to start the flow
      const phonePeLink = page
        .locator("a")
        .filter({ has: page.getByText("PhonePe") });
      await phonePeLink.click();

      // Should navigate to status page with refId
      await expect(page).toHaveURL(/\/onboarding\/bank-rpd\/[^/]+$/);

      // Should show loading state initially
      await expect(page.getByText("Checking transaction detail")).toBeVisible();

      // Advance time to reach polling limit - 40 calls at 5000ms intervals
      // Use smaller batches to avoid timeout
      for (let i = 0; i < 40; i++) {
        await page.clock.runFor(5000);
        if (i % 10 === 0) {
          await page.waitForTimeout(50); // Smaller waits, less frequent
        }
      }

      // Should show error page when polling limit is reached
      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 5000,
      });

      // Verify timeout error message is displayed (specific message for polling limit)
      await expect(
        page.getByText(
          "The transaction could not be verified. Please try again."
        )
      ).toBeVisible();

      // Verify loading state is no longer visible in error state
      await expect(
        page.getByText("Checking transaction detail")
      ).not.toBeVisible();

      // Verify URL remains the same (no navigation on error)
      await expect(page).toHaveURL(/\/onboarding\/bank-rpd\/[^/]+$/);

      // Test that retry button reloads the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });
  });

  test.describe("Desktop", () => {
    test.skip(({ isMobile }) => isMobile, "Desktop-only tests");

    test.beforeEach(async ({ page }) => {
      mobileLoadPageMockUserProfile(page);
      mobileLoadPageMockOnboardingState(page);
    });

    test("should load RPD page and show QR code", async ({ page }) => {
      mockRpdInitiation(page);

      await page.goto("/onboarding/bank-rpd");

      await expect(
        page.getByText("Verify your bank account with UPI")
      ).toBeVisible();
      await expect(
        page.getByText(
          "Pay ₹1 via UPI from that account. The money will be refunded in 1 day."
        )
      ).toBeVisible();

      // On desktop, QR code section should be visible
      await expect(page.getByText("QR code will expire in")).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Scan QR code" })
      ).toBeVisible();
    });

    test("should reveal QR code and start polling when scan button is clicked", async ({
      page,
    }) => {
      await page.clock.install();

      let callCount = 0;
      mockRpdInitiationWithOnboardingStatus(page);
      mockRpdStatusPolling(page, { successOnCall: 10 }, (requestJson) => {
        expect(requestJson.refId).toBeTruthy();
        callCount++;
      }); // Do not succeed during this test

      await page.goto("/onboarding/bank-rpd");
      await page.getByRole("button", { name: "Scan QR code" }).click();

      // Advance time to trigger 3 polling calls - 3 calls at 5000ms intervals
      for (let i = 0; i < 3; i++) {
        await page.clock.runFor(5000);
        await page.waitForTimeout(100);
      }

      expect(callCount).toBe(4); // Polling starts immediately, then 3 more calls
    });

    test("should show success screen and auto-redirect after successful polling", async ({
      page,
    }) => {
      // Use the same mock setup as the working desktop polling test
      mockRpdInitiationWithOnboardingStatus(page);
      mockRpdStatusPolling(page, { successOnCall: 3 });

      await page.goto("/onboarding/bank-rpd");

      // Verify QR code is initially blurred
      await expect(page.getByText("QR code will expire in")).toBeVisible();
      await page.getByRole("button", { name: "Scan QR code" }).click();

      // Add onboarding status mock after polling starts
      mockOnboardingStatusForSuccess(page);

      // Should show success screen after polling completes
      await expect(page.getByText("Bank account verified!")).toBeVisible({
        timeout: 18000,
      });

      // Note: Auto-redirect to next step is not implemented yet
      // await page.waitForURL("/onboarding/pan");
    });
  });

  test.describe("Desktop - Error Handling", () => {
    test.skip(({ isMobile }) => isMobile, "Desktop-only tests");

    test.beforeEach(async ({ page }) => {
      desktopErrorHandlingMockUserProfile(page);
      desktopErrorHandlingMockOnboardingState(page);
    });

    test("should show error page when RPD initiation fails with 500 server error", async ({
      page,
    }) => {
      desktopServerError500MockErrorResponse(page, "Internal server error");

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Verify the correct error message is displayed
      await expect(page.getByText("Internal server error")).toBeVisible();

      // Verify user is not stuck on loading state
      await expect(page.getByText("Loading...")).not.toBeVisible();

      // Test that retry button reloads the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test("should show error page when RPD initiation fails with 400 client error", async ({
      page,
    }) => {
      desktopClientError400MockErrorResponse(
        page,
        "This step is already completed"
      );

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Verify the correct error message is displayed
      await expect(
        page.getByText("This step is already completed")
      ).toBeVisible();

      await expect(page.getByText("Loading...")).not.toBeVisible();

      // Test that retry button reloads the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test("should show error page when RPD initiation fails with network error", async ({
      page,
    }) => {
      desktopNetworkErrorMockNetworkError(page);

      await page.goto("/onboarding/bank-rpd");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      // Verify network error message is displayed
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();

      // Test that retry button reloads the page
      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    // Note: Desktop RPD polling error handling is not implemented in the current codebase
    // The desktop page doesn't check for polling status errors like the mobile page does
    // So we only test initial page load errors for desktop
  });
});
