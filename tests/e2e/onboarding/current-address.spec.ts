import { test, expect } from "@playwright/test";
import { StepName } from "@/clients/gen/broking/Kyc_pb";
import {
  mockGetCurrentAddressWithData,
  mockGetCurrentAddressEmpty,
  mockGetCurrentAddressNetworkError,
  mockSaveCurrentAddress,
  mockSaveCurrentAddressWithValidation,
  mockSaveCurrentAddressSlow,
  mockSaveCurrentAddressServerError,
  mockSaveCurrentAddressNetworkError,
} from "./current-address.mocks";

test.describe("Onboarding - Current Address", () => {
  test.describe("Navigation Based on API Response", () => {
    test("should navigate to bank account when next step is BANK_ACCOUNT", async ({
      page,
    }) => {
      mockGetCurrentAddressWithData(page);
      mockSaveCurrentAddressWithValidation(page, StepName.BANK_ACCOUNT, true);

      await page.goto("/onboarding/current-address");
      await page.getByRole("button", { name: "Looks good" }).click();

      await page.waitForURL("/onboarding/bank-rpd");
    });

    test("should navigate to demat when next step is DEMAT_ACCOUNT", async ({
      page,
    }) => {
      mockGetCurrentAddressWithData(page);
      mockSaveCurrentAddressWithValidation(page, StepName.DEMAT_ACCOUNT, true);

      await page.goto("/onboarding/current-address");
      await page.getByRole("button", { name: "Looks good" }).click();

      await page.waitForURL("/onboarding/demat");
    });

    test("should navigate to nominee when next step is NOMINEE", async ({
      page,
    }) => {
      mockGetCurrentAddressWithData(page);
      mockSaveCurrentAddressWithValidation(page, StepName.NOMINEE, true);

      await page.goto("/onboarding/current-address");
      await page.getByRole("button", { name: "Looks good" }).click();

      await page.waitForURL("/onboarding/nominee");
    });
  });

  test.describe("Form Pre-filling", () => {
    test("should pre-fill form with existing address data", async ({
      page,
    }) => {
      const addressData = mockGetCurrentAddressWithData(page);

      await page.goto("/onboarding/current-address");

      await expect(
        page.getByRole("textbox", { name: "Address line 1" })
      ).toHaveValue(addressData.addressLine1);
      await expect(
        page.getByRole("textbox", { name: "Address line 2" })
      ).toHaveValue(addressData.addressLine2);
      await expect(
        page.getByRole("textbox", { name: "Address line 3" })
      ).toHaveValue(addressData.addressLine3);
      await expect(page.getByRole("textbox", { name: "City" })).toHaveValue(
        addressData.city
      );
      await expect(page.getByRole("textbox", { name: "State" })).toHaveValue(
        addressData.state
      );
      await expect(page.getByRole("textbox", { name: "PIN Code" })).toHaveValue(
        addressData.postCode
      );
    });

    test("should show empty form when no address data exists", async ({
      page,
    }) => {
      mockGetCurrentAddressEmpty(page);

      await page.goto("/onboarding/current-address");

      await expect(
        page.getByRole("textbox", { name: "Address line 1" })
      ).toHaveValue("");
      await expect(
        page.getByRole("textbox", { name: "Address line 2" })
      ).toHaveValue("");
      await expect(
        page.getByRole("textbox", { name: "Address line 3" })
      ).toHaveValue("");
      await expect(page.getByRole("textbox", { name: "City" })).toHaveValue("");
      await expect(page.getByRole("textbox", { name: "State" })).toHaveValue(
        ""
      );
      await expect(page.getByRole("textbox", { name: "PIN Code" })).toHaveValue(
        ""
      );
    });

    test("should display error message when the prefill API call fails", async ({
      page,
    }) => {
      mockGetCurrentAddressNetworkError(page);

      await page.goto("/onboarding/current-address");

      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });
  });

  test.describe("Form Validation", () => {
    test("should display validation messages for all empty required fields", async ({
      page,
    }) => {
      mockGetCurrentAddressEmpty(page);

      await page.goto("/onboarding/current-address");
      await page.getByRole("button", { name: "Looks good" }).click();

      await expect(page.getByText("Address Line 1 is required")).toBeVisible();
      await expect(page.getByText("Address Line 2 is required")).toBeVisible();
      await expect(page.getByText("City is required")).toBeVisible();
      await expect(page.getByText("State is required")).toBeVisible();
      await expect(page.getByText("postCode is required")).toBeVisible();
    });

    test("should display validation message for invalid PIN code length", async ({
      page,
    }) => {
      mockGetCurrentAddressEmpty(page);

      await page.goto("/onboarding/current-address");
      await page
        .getByRole("textbox", { name: "Address line 1" })
        .fill("123 Main St");
      await page
        .getByRole("textbox", { name: "Address line 2" })
        .fill("Apt 4B");
      await page.getByRole("textbox", { name: "City" }).fill("Mumbai");
      await page.getByRole("textbox", { name: "State" }).fill("Maharashtra");
      await page.getByRole("textbox", { name: "PIN Code" }).fill("12345");
      await page.getByRole("button", { name: "Looks good" }).click();
      await expect(page.getByText("postCode must be 6 digits")).toBeVisible();
    });

    test("should display validation message for non-numeric PIN code", async ({
      page,
    }) => {
      mockGetCurrentAddressEmpty(page);

      await page.goto("/onboarding/current-address");
      await page
        .getByRole("textbox", { name: "Address line 1" })
        .fill("123 Main St");
      await page
        .getByRole("textbox", { name: "Address line 2" })
        .fill("Apt 4B");
      await page.getByRole("textbox", { name: "City" }).fill("Mumbai");
      await page.getByRole("textbox", { name: "State" }).fill("Maharashtra");
      await page.getByRole("textbox", { name: "PIN Code" }).fill("12345A");
      await page.getByRole("button", { name: "Looks good" }).click();
      await expect(
        page.getByText("postCode must contain only digits")
      ).toBeVisible();
    });

    test("should accept valid address data", async ({ page }) => {
      mockGetCurrentAddressEmpty(page);
      mockSaveCurrentAddress(page, StepName.BANK_ACCOUNT);

      await page.goto("/onboarding/current-address");
      await page
        .getByRole("textbox", { name: "Address line 1" })
        .fill("123 Main Street");
      await page
        .getByRole("textbox", { name: "Address line 2" })
        .fill("Apartment 4B");
      await page
        .getByRole("textbox", { name: "Address line 3" })
        .fill("Building A");
      await page.getByRole("textbox", { name: "City" }).fill("Mumbai");
      await page.getByRole("textbox", { name: "State" }).fill("Maharashtra");
      await page.getByRole("textbox", { name: "PIN Code" }).fill("400001");

      await page.getByRole("button", { name: "Looks good" }).click();
      await page.waitForURL("/onboarding/bank-rpd");
    });
  });

  test.describe("Form Interaction", () => {
    test("should clear validation errors when user starts typing", async ({
      page,
    }) => {
      mockGetCurrentAddressEmpty(page);

      await page.goto("/onboarding/current-address");
      await page.getByRole("button", { name: "Looks good" }).click();

      await expect(page.getByText("Address Line 1 is required")).toBeVisible();
      await expect(page.getByText("Address Line 2 is required")).toBeVisible();
      await expect(page.getByText("City is required")).toBeVisible();
      await expect(page.getByText("State is required")).toBeVisible();
      await expect(page.getByText("postCode is required")).toBeVisible();

      const addressLine1Input = page.getByRole("textbox", {
        name: "Address line 1",
      });
      const addressLine2Input = page.getByRole("textbox", {
        name: "Address line 2",
      });
      const cityInput = page.getByRole("textbox", { name: "City" });
      const stateInput = page.getByRole("textbox", { name: "State" });
      const pinCodeInput = page.getByRole("textbox", { name: "PIN Code" });

      await addressLine1Input.fill("1");
      await expect(
        page.getByText("Address Line 1 is required")
      ).not.toBeVisible();

      await addressLine2Input.fill("A");
      await expect(
        page.getByText("Address Line 2 is required")
      ).not.toBeVisible();

      await cityInput.fill("M");
      await expect(page.getByText("City is required")).not.toBeVisible();
      await stateInput.fill("S");
      await expect(page.getByText("State is required")).not.toBeVisible();
      await pinCodeInput.fill("123456");
      await expect(page.getByText("postCode is required")).not.toBeVisible();
    });

    test("should show loading state during form submission", async ({
      page,
    }) => {
      mockGetCurrentAddressEmpty(page);
      mockSaveCurrentAddressSlow(page, 2000);

      await page.goto("/onboarding/current-address");

      await page
        .getByRole("textbox", { name: "Address line 1" })
        .fill("123 Main Street");
      await page
        .getByRole("textbox", { name: "Address line 2" })
        .fill("Apartment 4B");
      await page.getByRole("textbox", { name: "City" }).fill("Mumbai");
      await page.getByRole("textbox", { name: "State" }).fill("Maharashtra");
      await page.getByRole("textbox", { name: "PIN Code" }).fill("400001");

      const submitButton = page.getByRole("button", { name: "Looks good" });

      const clickPromise = submitButton.click();

      // Check for the loading state by looking for the data-loading attribute or the spinner SVG
      await expect(submitButton).toHaveAttribute("data-loading", "true", {
        timeout: 1000,
      });
      await clickPromise;
    });
  });

  test.describe("Error Handling", () => {
    test("should display error message for server error", async ({ page }) => {
      const errorMessage = "Internal server error";
      mockGetCurrentAddressEmpty(page);
      mockSaveCurrentAddressServerError(page, errorMessage);

      await page.goto("/onboarding/current-address");

      await page
        .getByRole("textbox", { name: "Address line 1" })
        .fill("123 Main Street");
      await page
        .getByRole("textbox", { name: "Address line 2" })
        .fill("Apartment 4B");
      await page.getByRole("textbox", { name: "City" }).fill("Mumbai");
      await page.getByRole("textbox", { name: "State" }).fill("Maharashtra");
      await page.getByRole("textbox", { name: "PIN Code" }).fill("400001");

      await page.getByRole("button", { name: "Looks good" }).click();

      // Check for toast notification instead of inline error message
      // Toast notifications appear as divs with specific styling
      await expect(page.locator(".text-red").first()).toBeVisible();
      await expect(page.locator(".text-red").first()).toContainText(
        errorMessage
      );
    });

    test("should display network error message for connection issues", async ({
      page,
    }) => {
      mockGetCurrentAddressEmpty(page);
      mockSaveCurrentAddressNetworkError(page);

      await page.goto("/onboarding/current-address");

      await page
        .getByRole("textbox", { name: "Address line 1" })
        .fill("123 Main Street");
      await page
        .getByRole("textbox", { name: "Address line 2" })
        .fill("Apartment 4B");
      await page.getByRole("textbox", { name: "City" }).fill("Mumbai");
      await page.getByRole("textbox", { name: "State" }).fill("Maharashtra");
      await page.getByRole("textbox", { name: "PIN Code" }).fill("400001");

      await page.getByRole("button", { name: "Looks good" }).click();

      // Check for toast notification instead of inline error message
      // Toast notifications appear as divs with specific styling
      await expect(page.locator(".text-red").first()).toBeVisible();
      await expect(page.locator(".text-red").first()).toContainText(
        "Please check your internet connection and try again."
      );
    });
  });
});
