import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { ErrorResponseSchema } from "@/clients/gen/platform/public/models/identity/Common_pb";
import { mergeDeep, type DeepPartial } from "../../utils";

/**
 * Mock for adult nominee submission - generates fake response data
 */
export function mockAdultNomineeSubmission(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for adult nominee
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (
            addNomineeRequest &&
            Array.isArray(addNomineeRequest.nomineeDetails) &&
            addNomineeRequest.nomineeDetails.length === 1 &&
            !addNomineeRequest.optOut
          ) {
            const nominee = addNomineeRequest.nomineeDetails[0] as Record<
              string,
              unknown
            >;
            if (nominee && !nominee.guardianDetails) {
              const response: MessageInitShape<
                typeof OnboardingResponseSchema
              > = {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

              route.fulfill({
                status: 200,
                body: Buffer.from(
                  toBinary(
                    OnboardingResponseSchema,
                    create(OnboardingResponseSchema, response)
                  )
                ),
              });
              return;
            }
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for minor nominee submission with guardian - generates fake response data
 */
export function mockMinorNomineeSubmission(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for minor nominee with guardian
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (
            addNomineeRequest &&
            Array.isArray(addNomineeRequest.nomineeDetails) &&
            addNomineeRequest.nomineeDetails.length === 1 &&
            !addNomineeRequest.optOut
          ) {
            const nominee = addNomineeRequest.nomineeDetails[0] as Record<
              string,
              unknown
            >;
            if (nominee && nominee.guardianDetails) {
              const response: MessageInitShape<
                typeof OnboardingResponseSchema
              > = {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

              route.fulfill({
                status: 200,
                body: Buffer.from(
                  toBinary(
                    OnboardingResponseSchema,
                    create(OnboardingResponseSchema, response)
                  )
                ),
              });
              return;
            }
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for skipping nominee - generates fake response data
 */
export function mockSkipNominee(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for skipping nominee
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (addNomineeRequest && addNomineeRequest.optOut === true) {
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for nominee submission with bank account next step - generates fake response data
 */
export function mockNomineeBankAccountNextStep(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.BANK_ACCOUNT,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for nominee submission
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (
            addNomineeRequest &&
            Array.isArray(addNomineeRequest.nomineeDetails) &&
            addNomineeRequest.nomineeDetails.length === 1 &&
            !addNomineeRequest.optOut
          ) {
            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for custom nominee relationship submission - generates fake response data
 */
export function mockCustomNomineeRelationship(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for custom nominee relationship
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (
            addNomineeRequest &&
            Array.isArray(addNomineeRequest.nomineeDetails) &&
            addNomineeRequest.nomineeDetails.length === 1 &&
            !addNomineeRequest.optOut
          ) {
            const nominee = addNomineeRequest.nomineeDetails[0] as Record<
              string,
              unknown
            >;
            if (
              nominee &&
              nominee.relationshipType === "OTHER" &&
              nominee.relationship &&
              !nominee.guardianDetails
            ) {
              const response: MessageInitShape<
                typeof OnboardingResponseSchema
              > = {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

              route.fulfill({
                status: 200,
                body: Buffer.from(
                  toBinary(
                    OnboardingResponseSchema,
                    create(OnboardingResponseSchema, response)
                  )
                ),
              });
              return;
            }
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for custom guardian relationship submission - generates fake response data
 */
export function mockCustomGuardianRelationship(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for custom guardian relationship
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (
            addNomineeRequest &&
            Array.isArray(addNomineeRequest.nomineeDetails) &&
            addNomineeRequest.nomineeDetails.length === 1 &&
            !addNomineeRequest.optOut
          ) {
            const nominee = addNomineeRequest.nomineeDetails[0] as Record<
              string,
              unknown
            >;
            const guardian = nominee?.guardianDetails as Record<
              string,
              unknown
            >;
            if (
              nominee &&
              guardian &&
              guardian.relationshipType === "OTHER" &&
              guardian.relationship
            ) {
              const response: MessageInitShape<
                typeof OnboardingResponseSchema
              > = {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

              route.fulfill({
                status: 200,
                body: Buffer.from(
                  toBinary(
                    OnboardingResponseSchema,
                    create(OnboardingResponseSchema, response)
                  )
                ),
              });
              return;
            }
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for both custom nominee and guardian relationships - generates fake response data
 */
export function mockCustomBothRelationships(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for both custom relationships
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (
            addNomineeRequest &&
            Array.isArray(addNomineeRequest.nomineeDetails) &&
            addNomineeRequest.nomineeDetails.length === 1 &&
            !addNomineeRequest.optOut
          ) {
            const nominee = addNomineeRequest.nomineeDetails[0] as Record<
              string,
              unknown
            >;
            const guardian = nominee?.guardianDetails as Record<
              string,
              unknown
            >;
            if (
              nominee &&
              nominee.relationshipType === "OTHER" &&
              nominee.relationship &&
              guardian &&
              guardian.relationshipType === "OTHER" &&
              guardian.relationship
            ) {
              const response: MessageInitShape<
                typeof OnboardingResponseSchema
              > = {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

              route.fulfill({
                status: 200,
                body: Buffer.from(
                  toBinary(
                    OnboardingResponseSchema,
                    create(OnboardingResponseSchema, response)
                  )
                ),
              });
              return;
            }
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for slow nominee submission (loading state) - generates fake response data with delay
 */
export function mockSlowNomineeSubmission(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for nominee submission
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (
            addNomineeRequest &&
            Array.isArray(addNomineeRequest.nomineeDetails) &&
            addNomineeRequest.nomineeDetails.length === 1 &&
            !addNomineeRequest.optOut
          ) {
            // Add delay to simulate loading state
            await new Promise((resolve) => setTimeout(resolve, 2000));

            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock for slow skip nominee (loading state) - generates fake response data with delay
 */
export function mockSlowSkipNominee(page: Page) {
  const responseData = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
    nextStep: StepName.WET_SIGNATURE,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.NOMINEE &&
          requestData.result.case === "addNomineeRequest"
        ) {
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          // Validate request structure for skipping nominee
          const addNomineeRequest = requestJson.addNomineeRequest as Record<
            string,
            unknown
          >;
          if (addNomineeRequest && addNomineeRequest.optOut === true) {
            // Add delay to simulate loading state
            await new Promise((resolve) => setTimeout(resolve, 2000));

            const response: MessageInitShape<typeof OnboardingResponseSchema> =
              {
                kycStatus: responseData.kycStatus,
                nextStep: responseData.nextStep,
                lifetimeStatus: responseData.lifetimeStatus,
                result: {
                  case: "addNomineeResponse" as const,
                  value: {},
                },
              };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, response)
                )
              ),
            });
            return;
          }
        }
      }
    }
    route.continue();
  });

  return responseData;
}

// Test: should display error message when nominee submission fails due to server error
export function mockErrorResponse(
  page: Page,
  route: string,
  status: number = 400,
  overrides: DeepPartial<MessageInitShape<typeof ErrorResponseSchema>> = {}
) {
  page.route(route, (routeHandler) => {
    const defaultResponseData = {
      errorCode: faker.string.alphanumeric(8).toUpperCase(),
      errorMessage: faker.lorem.sentence(),
    };

    const responseData = mergeDeep(defaultResponseData, overrides);

    routeHandler.fulfill({
      status,
      body: Buffer.from(
        toBinary(ErrorResponseSchema, create(ErrorResponseSchema, responseData))
      ),
    });
  });
}

// Test: should display error message when nominee submission fails due to network error
export function mockNetworkError(page: Page, route: string) {
  page.route(route, (routeHandler) => {
    // Randomly choose between abort and timeout to simulate different network issues
    const errorType = faker.helpers.arrayElement(["abort", "timeout"]);

    if (errorType === "abort") {
      // Abort the request to simulate network disconnection
      routeHandler.abort("internetdisconnected");
    } else {
      // Simulate timeout by aborting with timeout error
      routeHandler.abort("timedout");
    }
  });
}
