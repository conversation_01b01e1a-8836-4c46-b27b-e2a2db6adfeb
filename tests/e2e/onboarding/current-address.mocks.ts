import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import { GetCurrentAddressResponseSchema } from "@/clients/gen/broking/Profile_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";

type AddressData = {
  addressLine1: string;
  addressLine2: string;
  addressLine3: string;
  city: string;
  state: string;
  postCode: string;
};

// Mock for GET /v1/user/current-address endpoint with generated address data
export function mockGetCurrentAddressWithData(page: Page) {
  const addressData: AddressData = {
    addressLine1: faker.location.streetAddress(),
    addressLine2: faker.location.secondaryAddress(),
    addressLine3: faker.location.buildingNumber(),
    city: faker.location.city(),
    state: faker.location.state(),
    postCode: faker.string.numeric(6),
  };

  page.route("*/**/v1/user/current-address", (route) => {
    const responseData: MessageInitShape<
      typeof GetCurrentAddressResponseSchema
    > = {
      currentAddress: {
        ...addressData,
        country: "IN",
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          GetCurrentAddressResponseSchema,
          create(GetCurrentAddressResponseSchema, responseData)
        )
      ),
    });
  });

  return addressData;
}

// Mock for GET /v1/user/current-address endpoint with empty address
export function mockGetCurrentAddressEmpty(page: Page) {
  page.route("*/**/v1/user/current-address", (route) => {
    const responseData: MessageInitShape<
      typeof GetCurrentAddressResponseSchema
    > = {
      currentAddress: {
        addressLine1: "",
        addressLine2: "",
        addressLine3: "",
        city: "",
        state: "",
        postCode: "",
        country: "IN",
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          GetCurrentAddressResponseSchema,
          create(GetCurrentAddressResponseSchema, responseData)
        )
      ),
    });
  });
}

// Mock for GET /v1/user/current-address endpoint with network error
export function mockGetCurrentAddressNetworkError(page: Page) {
  page.route("*/**/v1/user/current-address", (route) => {
    route.abort("internetdisconnected");
  });
}

// Mock for POST /v1/onboarding endpoint for saving current address with validation
export function mockSaveCurrentAddressWithValidation(
  page: Page,
  nextStep: StepName = StepName.BANK_ACCOUNT,
  isSameAsPermanentAddress: boolean = false
) {
  // Generate expected address data using faker
  const expectedAddressData = {
    addressLine1: faker.location.streetAddress(),
    addressLine2: faker.location.secondaryAddress(),
    addressLine3: faker.location.buildingNumber(),
    city: faker.location.city(),
    state: faker.location.state(),
    postCode: faker.string.numeric(6),
    country: "IN",
  };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.CURRENT_ADDRESS &&
          requestData.result.case === "addCurrentAddressRequest"
        ) {
          // Validate request structure
          const requestJson = toJson(
            OnboardingRequestSchema,
            requestData
          ) as Record<string, unknown>;

          if (requestJson.stepName !== "CURRENT_ADDRESS") {
            throw new Error(
              `Expected stepName CURRENT_ADDRESS, got ${requestJson.stepName}`
            );
          }

          const addCurrentAddressRequest = requestData.result.value;
          if (
            addCurrentAddressRequest.isSameAsPermanentAddress !==
            isSameAsPermanentAddress
          ) {
            throw new Error(
              `Expected isSameAsPermanentAddress ${isSameAsPermanentAddress}, got ${addCurrentAddressRequest.isSameAsPermanentAddress}`
            );
          }

          // Validate that address fields are present and non-empty
          const actualAddress = addCurrentAddressRequest.addressProto;
          if (!actualAddress) {
            throw new Error("Expected addressProto in request");
          }

          const requiredFields = [
            "addressLine1",
            "addressLine2",
            "city",
            "state",
            "postCode",
            "country",
          ];
          requiredFields.forEach((field) => {
            const fieldValue =
              actualAddress[field as keyof typeof actualAddress];
            if (
              !fieldValue ||
              (typeof fieldValue === "string" && fieldValue.trim() === "")
            ) {
              throw new Error(`Expected non-empty ${field} in addressProto`);
            }
          });

          const responseData: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: nextStep,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "addCurrentAddressResponse" as const,
              value: {},
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, responseData)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return expectedAddressData;
}

// Mock for POST /v1/onboarding endpoint for saving current address (simple version)
export function mockSaveCurrentAddress(
  page: Page,
  nextStep: StepName = StepName.BANK_ACCOUNT
) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.CURRENT_ADDRESS &&
          requestData.result.case === "addCurrentAddressRequest"
        ) {
          const responseData: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: nextStep,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "addCurrentAddressResponse" as const,
              value: {},
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, responseData)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

// Mock for POST /v1/onboarding endpoint with slow response
export function mockSaveCurrentAddressSlow(
  page: Page,
  delay: number = 2000,
  nextStep: StepName = StepName.BANK_ACCOUNT
) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.CURRENT_ADDRESS &&
          requestData.result.case === "addCurrentAddressRequest"
        ) {
          await new Promise((resolve) => setTimeout(resolve, delay));

          const responseData: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: nextStep,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "addCurrentAddressResponse" as const,
              value: {},
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, responseData)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

// Mock for POST /v1/onboarding endpoint with server error
export function mockSaveCurrentAddressServerError(
  page: Page,
  errorMessage: string
) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.CURRENT_ADDRESS &&
          requestData.result.case === "addCurrentAddressRequest"
        ) {
          route.fulfill({
            status: 500,
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              errorMessage: errorMessage,
            }),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

// Mock for POST /v1/onboarding endpoint with network error
export function mockSaveCurrentAddressNetworkError(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.CURRENT_ADDRESS &&
          requestData.result.case === "addCurrentAddressRequest"
        ) {
          route.abort("internetdisconnected");
          return;
        }
      }
    }
    route.continue();
  });
}
