import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import { create, toBinary, type MessageInitShape } from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import {
  CartDetailsSchema,
  CartItemSchema,
  type CartItem,
} from "@/clients/gen/broking/Cart_pb";

/**
 * Mock onboarding status with polling support
 */
export function mockOnboardingStatusWithPolling(
  page: Page,
  options: {
    kycStatus: UserLifetimeStatusResponse_KycStatus;
    successOnCall: number;
    lifetimeStatus?: UserLifetimeStatus;
    nextStep?: StepName;
  }
) {
  let callCount = 0;

  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "GET") {
      callCount++;

      const isSuccess = callCount >= options.successOnCall;
      const kycStatus = isSuccess
        ? options.kycStatus
        : UserLifetimeStatusResponse_KycStatus.ONBOARDING_ON_EXCHANGE;

      const responseData: MessageInitShape<typeof OnboardingResponseSchema> = {
        kycStatus,
        nextStep: options.nextStep ?? StepName.KYC_TYPE_UNKNOWN,
        lifetimeStatus:
          options.lifetimeStatus ?? UserLifetimeStatus.KYC_COMPLETED,
        result: {
          case: "emptyKycResponse" as const,
          value: {},
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            OnboardingResponseSchema,
            create(OnboardingResponseSchema, responseData)
          )
        ),
        headers: {
          "Content-Type": "application/x-protobuf",
        },
      });
      return;
    }
    route.continue();
  });

  return {
    getCallCount: () => callCount,
  };
}

/**
 * Mock onboarding status for exchange failed state
 */
export function mockOnboardingStatusExchangeFailed(
  page: Page,
  options: {
    kycStatus: UserLifetimeStatusResponse_KycStatus;
    lifetimeStatus?: UserLifetimeStatus;
    nextStep?: StepName;
  }
) {
  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "GET") {
      const responseData: MessageInitShape<typeof OnboardingResponseSchema> = {
        kycStatus: options.kycStatus,
        nextStep: options.nextStep ?? StepName.KYC_TYPE_UNKNOWN,
        lifetimeStatus:
          options.lifetimeStatus ?? UserLifetimeStatus.KYC_COMPLETED,
        result: {
          case: "emptyKycResponse" as const,
          value: {},
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            OnboardingResponseSchema,
            create(OnboardingResponseSchema, responseData)
          )
        ),
        headers: {
          "Content-Type": "application/x-protobuf",
        },
      });
      return;
    }
    route.continue();
  });
}

/**
 * Mock onboarding status that never succeeds (for polling limit testing)
 */
export function mockOnboardingStatusProcessingLimit(
  page: Page,
  options: {
    kycStatus: UserLifetimeStatusResponse_KycStatus;
    lifetimeStatus?: UserLifetimeStatus;
    nextStep?: StepName;
  }
) {
  let callCount = 0;

  page.route("*/**/v1/onboarding", (route) => {
    const request = route.request();
    if (request.method() === "GET") {
      callCount++;

      // Always return the same status to trigger polling limit
      const responseData: MessageInitShape<typeof OnboardingResponseSchema> = {
        kycStatus: options.kycStatus,
        nextStep: options.nextStep ?? StepName.KYC_TYPE_UNKNOWN,
        lifetimeStatus:
          options.lifetimeStatus ?? UserLifetimeStatus.KYC_COMPLETED,
        result: {
          case: "emptyKycResponse" as const,
          value: {},
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            OnboardingResponseSchema,
            create(OnboardingResponseSchema, responseData)
          )
        ),
        headers: {
          "Content-Type": "application/x-protobuf",
        },
      });
      return;
    }
    route.continue();
  });

  return {
    getCallCount: () => callCount,
  };
}

/**
 * Mock cart details endpoint
 */
export function mockCartDetails(
  page: Page,
  options: {
    cartItems?: Array<{
      bondDetailId?: string;
      quantity?: number;
      inStock?: boolean;
      outOfStockMessage?: string;
    }>;
  } = {}
) {
  const cartItems: CartItem[] = (options.cartItems || []).map((item) =>
    create(CartItemSchema, {
      bondDetailId: item.bondDetailId || faker.string.uuid(),
      quantity: item.quantity || 1,
      inStock: item.inStock ?? true,
      outOfStockMessage: item.outOfStockMessage || "",
    })
  );

  page.route("*/**/v1/cart/details", (route) => {
    const request = route.request();
    if (request.method() === "GET") {
      const responseData: MessageInitShape<typeof CartDetailsSchema> = {
        cartItems,
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(CartDetailsSchema, create(CartDetailsSchema, responseData))
        ),
        headers: {
          "Content-Type": "application/x-protobuf",
        },
      });
      return;
    }
    route.continue();
  });

  return {
    cartItems,
  };
}
