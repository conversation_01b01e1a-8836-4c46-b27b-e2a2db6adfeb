import { test, expect } from "@playwright/test";
import {
  mockLoadPage,
  mockShowBankDetails,
  mockSuccessfulSubmission,
  mockValidationErrors,
  mockLoadingIndicator,
  mockIfscLookupTrigger,
  mockLoadingState,
  mockClearBankDetails,
  mockPennyDropServerError,
  generateValidAccountNumber,
  generateValidIfscCode,
} from "./bank-pd.mocks";

test.describe("Onboarding - Bank PD", () => {
  test.describe("Happy Path", () => {
    test("should load bank PD page and show form fields", async ({ page }) => {
      mockLoadPage(page);
      await page.goto("/onboarding/bank-pd");

      await expect(
        page.getByRole("heading", { name: "Bank details" })
      ).toBeVisible();
      await expect(
        page.getByText("Bond payments must come from this account")
      ).toBeVisible();
      await expect(
        page.getByRole("textbox", { name: "Bank account number" })
      ).toBeVisible();
      await expect(
        page.getByRole("textbox", { name: "IFSC code" })
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Verify bank account" })
      ).toBeVisible();
    });

    test("should show bank details when valid IFSC is entered", async ({
      page,
    }) => {
      const mockData = mockShowBankDetails(page);

      await page.goto("/onboarding/bank-pd");

      await page
        .getByRole("textbox", { name: "IFSC code" })
        .fill(mockData.ifscCode);

      await expect(
        page.getByText(`${mockData.bankName} - ${mockData.branchName}`)
      ).toBeVisible({ timeout: 2000 });
      await expect(page.getByTestId("bank-logo")).toBeVisible();
    });

    test("should successfully submit bank details and navigate to next step", async ({
      page,
    }) => {
      const { accountNumber, ifscCode } = mockSuccessfulSubmission(page);

      await page.goto("/onboarding/bank-pd");

      await page
        .getByRole("textbox", { name: "Bank account number" })
        .fill(accountNumber);
      await page.getByRole("textbox", { name: "IFSC code" }).fill(ifscCode);
      await page.getByRole("button", { name: "Verify bank account" }).click();

      await page.waitForURL("/onboarding/wetsign");
    });
  });

  test.describe("Form Validation", () => {
    test("should show validation errors when submitting invalid data", async ({
      page,
    }) => {
      mockValidationErrors(page);
      await page.goto("/onboarding/bank-pd");

      // Try to submit with empty fields
      await page.getByRole("button", { name: "Verify bank account" }).click();

      // Should show validation errors
      await expect(page.getByText("Account number is required")).toBeVisible();
      await expect(page.getByText("IFSC code is required")).toBeVisible();
    });

    test("should show validation error for short account number", async ({
      page,
    }) => {
      mockValidationErrors(page);
      await page.goto("/onboarding/bank-pd");

      await page
        .getByRole("textbox", { name: "Bank account number" })
        .fill("********");
      await page.getByRole("button", { name: "Verify bank account" }).click();

      await expect(
        page.getByText("Account number must be at least 9 digits")
      ).toBeVisible();
    });

    test("should show validation error for long account number", async ({
      page,
    }) => {
      mockValidationErrors(page);
      await page.goto("/onboarding/bank-pd");

      await page
        .getByRole("textbox", { name: "Bank account number" })
        .fill("********90********");
      await page.getByRole("button", { name: "Verify bank account" }).click();

      await expect(
        page.getByText("Account number must be at most 17 digits")
      ).toBeVisible();
    });

    test("should show validation error for non-numeric account number", async ({
      page,
    }) => {
      mockValidationErrors(page);
      await page.goto("/onboarding/bank-pd");

      await page
        .getByRole("textbox", { name: "Bank account number" })
        .fill("12345abc67");
      await page.getByRole("button", { name: "Verify bank account" }).click();

      await expect(
        page.getByText("Account number must contain only digits")
      ).toBeVisible();
    });

    test("should show validation error for short IFSC code", async ({
      page,
    }) => {
      mockValidationErrors(page);
      await page.goto("/onboarding/bank-pd");

      await page.getByRole("textbox", { name: "IFSC code" }).fill("HDFC0001");
      await page.getByRole("button", { name: "Verify bank account" }).click();

      await expect(
        page.getByText("IFSC code must be 11 characters")
      ).toBeVisible();
    });
  });

  test.describe("IFSC Lookup", () => {
    test("should show loading indicator while fetching IFSC details", async ({
      page,
    }) => {
      const ifscCode = generateValidIfscCode();

      mockLoadingIndicator(page, {
        ifscCode,
        delay: 1000,
      });

      await page.goto("/onboarding/bank-pd");

      await page.getByRole("textbox", { name: "IFSC code" }).fill(ifscCode);

      await expect(page.getByTestId("ifsc-loading-indicator")).toBeVisible();
    });

    test("should only trigger IFSC lookup when code is less than 10 characters", async ({
      page,
    }) => {
      mockIfscLookupTrigger(page);

      await page.goto("/onboarding/bank-pd");

      await page.getByRole("textbox", { name: "IFSC code" }).fill("HDFC000");
      await expect(
        page.getByTestId("ifsc-loading-indicator")
      ).not.toBeVisible();
    });
  });

  test.describe("API Error Handling", () => {
    test("should handle penny drop submission server errors", async ({
      page,
    }) => {
      const errorMessage = "Bank verification failed";
      const { ifscCode, bankName, branchName } = mockPennyDropServerError(
        page,
        errorMessage
      );

      await page.goto("/onboarding/bank-pd");

      await page
        .getByRole("textbox", { name: "Bank account number" })
        .fill(generateValidAccountNumber());
      await page.getByRole("textbox", { name: "IFSC code" }).fill(ifscCode);

      // Wait for IFSC details to load
      await expect(page.getByText(`${bankName} - ${branchName}`)).toBeVisible();

      await page.getByRole("button", { name: "Verify bank account" }).click();

      // The error should be handled by withErrorToast and displayed as a toast
      await expect(page.getByText(errorMessage)).toBeVisible();
    });
  });

  test.describe("Loading States", () => {
    test("should show loading state during form submission", async ({
      page,
    }) => {
      const accountNumber = generateValidAccountNumber();
      const ifscCode = generateValidIfscCode();

      mockLoadingState(page, {
        accountNumber,
        ifscCode,
        delay: 1000,
      });

      await page.goto("/onboarding/bank-pd");

      await page
        .getByRole("textbox", { name: "Bank account number" })
        .fill(accountNumber);
      await page.getByRole("textbox", { name: "IFSC code" }).fill(ifscCode);

      const submitButton = page.getByRole("button", {
        name: "Verify bank account",
      });
      await submitButton.click();

      await expect(submitButton.locator("svg")).toBeVisible();
    });
  });

  test.describe("Form Interaction", () => {
    test("should convert IFSC input to uppercase", async ({ page }) => {
      mockLoadPage(page);
      await page.goto("/onboarding/bank-pd");

      const ifscInput = page.getByRole("textbox", { name: "IFSC code" });
      await ifscInput.fill("hdfc0000123");

      await expect(ifscInput).toHaveClass(/uppercase/);
    });

    test("should clear bank details when IFSC is modified", async ({
      page,
    }) => {
      const ifscCode = generateValidIfscCode();
      const bankName = "HDFC Bank";

      const mockData = mockClearBankDetails(page, {
        ifscCode,
        bankName,
      });

      await page.goto("/onboarding/bank-pd");

      await page.getByRole("textbox", { name: "IFSC code" }).fill(ifscCode);
      await expect(page.getByText(mockData.bankName)).toBeVisible();

      await page
        .getByRole("textbox", { name: "IFSC code" })
        .fill(ifscCode.slice(0, -1));
      await expect(page.getByText(mockData.bankName)).not.toBeVisible();
    });
  });
});
