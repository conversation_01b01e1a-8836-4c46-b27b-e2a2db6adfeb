import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";

/**
 * Mock document upload endpoint - returns document_id for successful upload
 */
export function mockDocumentUpload(
  page: Page,
  responseData?: { document_id: string }
) {
  const documentId = responseData?.document_id || faker.string.uuid();

  page.route("*/**/v1/document/upload", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      // Validate request has form data with file and file-type
      const contentType = request.headers()["content-type"];
      if (contentType && contentType.includes("multipart/form-data")) {
        // Built-in request validation
        const postData = request.postData();
        if (!postData) {
          throw new Error("Form data is required for document upload");
        }

        // Validate that form data contains file and file-type
        if (!postData.includes('name="file"')) {
          throw new Error("File field is required in form data");
        }
        if (!postData.includes('name="file-type"')) {
          throw new Error("File-type field is required in form data");
        }

        route.fulfill({
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            document_id: documentId,
          }),
        });
        return;
      }
    }
    route.continue();
  });

  return { documentId };
}

/**
 * Mock wet signature submission endpoint - handles protobuf request/response
 */
export function mockWetSignatureSubmission(
  page: Page,
  responseOverrides: Partial<
    MessageInitShape<typeof OnboardingResponseSchema>
  > = {}
) {
  const defaultResponse: MessageInitShape<typeof OnboardingResponseSchema> = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.ONBOARDING_ON_EXCHANGE,
    nextStep: StepName.ESIGN,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
    result: {
      case: "wetSignatureResponse" as const,
      value: {},
    },
  };

  const responseData = { ...defaultResponse, ...responseOverrides };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        // Validate request structure
        if (
          requestData.stepName === StepName.WET_SIGNATURE &&
          requestData.result.case === "wetSignatureRequest"
        ) {
          // Built-in request validation
          if (requestData.result.case === "wetSignatureRequest") {
            const wetSignatureRequest = requestData.result.value;

            // Validate required fields
            if (!wetSignatureRequest.documentId) {
              throw new Error(
                "documentId is required in wet signature request"
              );
            }
            if (typeof wetSignatureRequest.isIndianCitizen !== "boolean") {
              throw new Error("isIndianCitizen must be a boolean");
            }
            if (typeof wetSignatureRequest.creditReportConsent !== "boolean") {
              throw new Error("creditReportConsent must be a boolean");
            }
            if (!wetSignatureRequest.raaDuration) {
              throw new Error("raaDuration is required");
            }
          }

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(
                  OnboardingResponseSchema,
                  responseData as MessageInitShape<
                    typeof OnboardingResponseSchema
                  >
                )
              )
            ),
            headers: {
              "Content-Type": "application/x-protobuf",
            },
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

/**
 * Mock document upload server error
 */
export function mockDocumentUploadServerError(
  page: Page,
  errorMessage = "Document upload failed"
) {
  page.route("*/**/v1/document/upload", (route) => {
    route.fulfill({
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        error_message: errorMessage,
      }),
    });
  });

  return { errorMessage };
}

/**
 * Mock wet signature submission server error
 */
export function mockWetSignatureServerError(
  page: Page,
  errorMessage = "Wet signature save failed"
) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.WET_SIGNATURE &&
          requestData.result.case === "wetSignatureRequest"
        ) {
          route.fulfill({
            status: 500,
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              errorMessage: errorMessage,
            }),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return { errorMessage };
}

/**
 * Mock document upload network error
 */
export function mockDocumentUploadNetworkError(page: Page) {
  page.route("*/**/v1/document/upload", (route) => {
    route.abort("internetdisconnected");
  });
}

/**
 * Mock wet signature submission network error
 */
export function mockWetSignatureNetworkError(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.WET_SIGNATURE &&
          requestData.result.case === "wetSignatureRequest"
        ) {
          route.abort("internetdisconnected");
          return;
        }
      }
    }
    route.continue();
  });
}

/**
 * Mock document upload without document_id (failure case)
 */
export function mockDocumentUploadNoId(page: Page) {
  page.route("*/**/v1/document/upload", (route) => {
    route.fulfill({
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({}),
    });
  });
}

/**
 * Mock slow document upload for loading state testing
 */
export function mockSlowDocumentUpload(
  page: Page,
  delay: number,
  responseData?: { document_id: string }
) {
  const documentId = responseData?.document_id || faker.string.uuid();

  page.route("*/**/v1/document/upload", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      // Built-in request validation
      const contentType = request.headers()["content-type"];
      if (contentType && contentType.includes("multipart/form-data")) {
        const postData = request.postData();
        if (!postData) {
          throw new Error("Form data is required for document upload");
        }

        // Validate that form data contains file and file-type
        if (!postData.includes('name="file"')) {
          throw new Error("File field is required in form data");
        }
        if (!postData.includes('name="file-type"')) {
          throw new Error("File-type field is required in form data");
        }

        await new Promise((resolve) => setTimeout(resolve, delay));

        route.fulfill({
          status: 200,
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            document_id: documentId,
          }),
        });
        return;
      }
    }
    route.continue();
  });

  return { documentId };
}

/**
 * Mock slow wet signature submission for loading state testing
 */
export function mockSlowWetSignatureSubmission(
  page: Page,
  delay: number,
  responseOverrides: Partial<
    MessageInitShape<typeof OnboardingResponseSchema>
  > = {}
) {
  const defaultResponse: MessageInitShape<typeof OnboardingResponseSchema> = {
    kycStatus: UserLifetimeStatusResponse_KycStatus.ONBOARDING_ON_EXCHANGE,
    nextStep: StepName.ESIGN,
    lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
    result: {
      case: "wetSignatureResponse" as const,
      value: {},
    },
  };

  const responseData = { ...defaultResponse, ...responseOverrides };

  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.WET_SIGNATURE &&
          requestData.result.case === "wetSignatureRequest"
        ) {
          // Built-in request validation
          if (requestData.result.case === "wetSignatureRequest") {
            const wetSignatureRequest = requestData.result.value;

            // Validate required fields
            if (!wetSignatureRequest.documentId) {
              throw new Error(
                "documentId is required in wet signature request"
              );
            }
            if (typeof wetSignatureRequest.isIndianCitizen !== "boolean") {
              throw new Error("isIndianCitizen must be a boolean");
            }
            if (typeof wetSignatureRequest.creditReportConsent !== "boolean") {
              throw new Error("creditReportConsent must be a boolean");
            }
            if (!wetSignatureRequest.raaDuration) {
              throw new Error("raaDuration is required");
            }
          }

          await new Promise((resolve) => setTimeout(resolve, delay));

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(
                  OnboardingResponseSchema,
                  responseData as MessageInitShape<
                    typeof OnboardingResponseSchema
                  >
                )
              )
            ),
            headers: {
              "Content-Type": "application/x-protobuf",
            },
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}
