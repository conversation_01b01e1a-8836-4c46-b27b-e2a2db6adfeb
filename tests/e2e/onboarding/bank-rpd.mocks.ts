import type { Page } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  toJson,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { UserProfileResponseSchema } from "@/clients/gen/platform/public/models/identity/Profile_pb";
import { KycType } from "@/clients/gen/platform/public/models/identity/Kyc_pb";
import {
  BankVerificationType,
  RpdStatusRequestSchema,
  RpdStatusResponseSchema,
  InitiateRpdResponse_PaymentApp,
} from "@/clients/gen/broking/BankAccountVerification_pb";

// Test: should load RPD page and show UPI options (Mobile)
export function mobileLoadPageMockUserProfile(page: Page) {
  page.route("*/**/v1/user/profile", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof UserProfileResponseSchema
    > = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, defaultResponseData)
        )
      ),
    });
  });
}

// Test: should load RPD page and show UPI options (Mobile)
export function mobileLoadPageMockOnboardingState(page: Page) {
  page.route("*/**/v1/user/onboarding-state", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        next: KycType.BANK_ACCOUNT,
      }),
    });
  });
}

// Test: should load RPD page and show UPI options (Mobile)
export function mobileLoadPageMockRpdInitiation(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.BANK_ACCOUNT &&
          requestData.result.case === "verifyBankRequest"
        ) {
          const defaultOnboardingResponse: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.BANK_ACCOUNT,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "verifyBankResponse" as const,
              value: {
                result: {
                  case: "initiateRpdResponse" as const,
                  value: {
                    refId: faker.string.uuid(),
                    qrCode: faker.string.alphanumeric(100),
                    validUpto: new Date(Date.now() + 60000).toISOString(),
                    paymentOptions: [
                      {
                        app: InitiateRpdResponse_PaymentApp.PHONEPE,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/phonepe.webp",
                        upiIntent: `upi://pay?pa=test@phonepe&pn=Test&am=1&cu=INR&tn=RPD-${faker.string.uuid()}`,
                      },
                      {
                        app: InitiateRpdResponse_PaymentApp.GPAY,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/gpay.webp",
                        upiIntent: `upi://pay?pa=test@gpay&pn=Test&am=1&cu=INR&tn=RPD-${faker.string.uuid()}`,
                      },
                    ],
                  },
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, defaultOnboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

// Test: should start polling when UPI app is clicked (Mobile)
export function mobileStartPollingMockUserProfile(page: Page) {
  page.route("*/**/v1/user/profile", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof UserProfileResponseSchema
    > = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, defaultResponseData)
        )
      ),
    });
  });
}

// Test: should start polling when UPI app is clicked (Mobile)
export function mobileStartPollingMockRpdInitiationWithOnboardingStatus(
  page: Page
) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.BANK_ACCOUNT &&
          requestData.result.case === "verifyBankRequest"
        ) {
          const refId = faker.string.uuid();
          const defaultOnboardingResponse: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.BANK_ACCOUNT,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "verifyBankResponse" as const,
              value: {
                result: {
                  case: "initiateRpdResponse" as const,
                  value: {
                    refId: refId,
                    qrCode: faker.string.alphanumeric(100),
                    validUpto: new Date(Date.now() + 60000).toISOString(),
                    paymentOptions: [
                      {
                        app: InitiateRpdResponse_PaymentApp.PHONEPE,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/phonepe.webp",
                        upiIntent: `upi://pay?pa=test@phonepe&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                      {
                        app: InitiateRpdResponse_PaymentApp.GPAY,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/gpay.webp",
                        upiIntent: `upi://pay?pa=test@gpay&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                    ],
                  },
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, defaultOnboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

// Test: should start polling when UPI app is clicked (Mobile)
export function mobileStartPollingMockRpdStatusPolling(
  page: Page,
  options: { successOnCall: number } = { successOnCall: 15 }
) {
  let callCount = 0;

  page.route("*/**/v1/bank/rpd-status", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(RpdStatusRequestSchema, requestBuffer);

        callCount++;

        const isSuccess = callCount >= options.successOnCall;

        const defaultStatusResponse: MessageInitShape<
          typeof RpdStatusResponseSchema
        > = {
          refId: requestData.refId,
          status: isSuccess ? "SUCCESS" : "PENDING",
          verified: isSuccess,
        };

        route.fulfill({
          status: 200,
          body: Buffer.from(
            toBinary(
              RpdStatusResponseSchema,
              create(RpdStatusResponseSchema, defaultStatusResponse)
            )
          ),
        });
        return;
      }
    }
    route.continue();
  });
}

// Test: should show success screen and auto-redirect after successful polling (Mobile)
export function mobileSuccessScreenMockUserProfile(page: Page) {
  page.route("*/**/v1/user/profile", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof UserProfileResponseSchema
    > = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, defaultResponseData)
        )
      ),
    });
  });
}

// Test: should show success screen and auto-redirect after successful polling (Mobile)
export function mobileSuccessScreenMockRpdInitiationWithOnboardingStatus(
  page: Page
) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.BANK_ACCOUNT &&
          requestData.result.case === "verifyBankRequest"
        ) {
          const refId = faker.string.uuid();
          const defaultOnboardingResponse: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.BANK_ACCOUNT,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "verifyBankResponse" as const,
              value: {
                result: {
                  case: "initiateRpdResponse" as const,
                  value: {
                    refId: refId,
                    qrCode: faker.string.alphanumeric(100),
                    validUpto: new Date(Date.now() + 60000).toISOString(),
                    paymentOptions: [
                      {
                        app: InitiateRpdResponse_PaymentApp.PHONEPE,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/phonepe.webp",
                        upiIntent: `upi://pay?pa=test@phonepe&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                      {
                        app: InitiateRpdResponse_PaymentApp.GPAY,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/gpay.webp",
                        upiIntent: `upi://pay?pa=test@gpay&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                    ],
                  },
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, defaultOnboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

// Test: should show success screen and auto-redirect after successful polling (Mobile)
export function mobileSuccessScreenMockRpdStatusPolling(
  page: Page,
  options: { successOnCall: number } = { successOnCall: 3 }
) {
  let callCount = 0;

  page.route("*/**/v1/bank/rpd-status", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(RpdStatusRequestSchema, requestBuffer);

        callCount++;

        const isSuccess = callCount >= options.successOnCall;

        const defaultStatusResponse: MessageInitShape<
          typeof RpdStatusResponseSchema
        > = {
          refId: requestData.refId,
          status: isSuccess ? "SUCCESS" : "PENDING",
          verified: isSuccess,
        };

        route.fulfill({
          status: 200,
          body: Buffer.from(
            toBinary(
              RpdStatusResponseSchema,
              create(RpdStatusResponseSchema, defaultStatusResponse)
            )
          ),
        });
        return;
      }
    }
    route.continue();
  });
}

// Mock for getOnboardingStatus() call made after successful RPD verification
export function mobileSuccessScreenMockOnboardingStatus(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "GET") {
      const defaultOnboardingResponse: MessageInitShape<
        typeof OnboardingResponseSchema
      > = {
        kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
        nextStep: StepName.PAN_KYC,
        lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
        result: {
          case: "emptyKycResponse" as const,
          value: {},
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            OnboardingResponseSchema,
            create(OnboardingResponseSchema, defaultOnboardingResponse)
          )
        ),
      });
      return;
    }
    route.continue();
  });
}

// Error handling mocks
export function errorHandlingMockUserProfile(page: Page) {
  page.route("*/**/v1/user/profile", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof UserProfileResponseSchema
    > = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, defaultResponseData)
        )
      ),
    });
  });
}

export function errorHandlingMockOnboardingState(page: Page) {
  page.route("*/**/v1/user/onboarding-state", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        next: KycType.BANK_ACCOUNT,
      }),
    });
  });
}

// Error handling - 500 server error
export function serverError500MockErrorResponse(
  page: Page,
  errorMessage: string
) {
  page.route("*/**/v1/onboarding", (route) => {
    route.fulfill({
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        errorMessage: errorMessage,
      }),
    });
  });
}

// Error handling - 400 client error
export function clientError400MockErrorResponse(
  page: Page,
  errorMessage: string
) {
  page.route("*/**/v1/onboarding", (route) => {
    route.fulfill({
      status: 400,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        errorMessage: errorMessage,
      }),
    });
  });
}

// Error handling - network error
export function networkErrorMockNetworkError(page: Page) {
  page.route("*/**/v1/onboarding", (route) => {
    route.abort("internetdisconnected");
  });
}

// Desktop error handling mocks
export function desktopErrorHandlingMockUserProfile(page: Page) {
  page.route("*/**/v1/user/profile", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof UserProfileResponseSchema
    > = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, defaultResponseData)
        )
      ),
    });
  });
}

export function desktopErrorHandlingMockOnboardingState(page: Page) {
  page.route("*/**/v1/user/onboarding-state", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        next: KycType.BANK_ACCOUNT,
      }),
    });
  });
}

export function desktopServerError500MockErrorResponse(
  page: Page,
  errorMessage: string
) {
  page.route("*/**/v1/onboarding", (route) => {
    route.fulfill({
      status: 500,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        errorMessage: errorMessage,
      }),
    });
  });
}

export function desktopClientError400MockErrorResponse(
  page: Page,
  errorMessage: string
) {
  page.route("*/**/v1/onboarding", (route) => {
    route.fulfill({
      status: 400,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        errorMessage: errorMessage,
      }),
    });
  });
}

export function desktopNetworkErrorMockNetworkError(page: Page) {
  page.route("*/**/v1/onboarding", (route) => {
    route.abort("internetdisconnected");
  });
}

// Desktop load page mocks
export function desktopLoadPageMockUserProfile(page: Page) {
  page.route("*/**/v1/user/profile", (route) => {
    const defaultResponseData: MessageInitShape<
      typeof UserProfileResponseSchema
    > = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, defaultResponseData)
        )
      ),
    });
  });
}

export function desktopLoadPageMockOnboardingState(page: Page) {
  page.route("*/**/v1/user/onboarding-state", (route) => {
    route.fulfill({
      status: 200,
      body: JSON.stringify({
        next: KycType.BANK_ACCOUNT,
      }),
    });
  });
}

export function desktopLoadPageMockRpdInitiation(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.BANK_ACCOUNT &&
          requestData.result.case === "verifyBankRequest" &&
          requestData.result.value.type ===
            BankVerificationType.REVERSE_PENNY_DROP
        ) {
          const refId = faker.string.uuid();
          const qrCode = faker.string.alphanumeric(100); // Base64-like string

          const defaultOnboardingResponse: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.BANK_ACCOUNT,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "verifyBankResponse" as const,
              value: {
                result: {
                  case: "initiateRpdResponse" as const,
                  value: {
                    refId: refId,
                    qrCode: qrCode,
                    validUpto: new Date(Date.now() + 60000).toISOString(), // 60 seconds from now
                    paymentOptions: [
                      {
                        app: InitiateRpdResponse_PaymentApp.PHONEPE,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/phonepe.webp",
                        upiIntent: `upi://pay?pa=test@phonepe&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                      {
                        app: InitiateRpdResponse_PaymentApp.GPAY,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/gpay.webp",
                        upiIntent: `upi://pay?pa=test@gpay&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                    ],
                  },
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, defaultOnboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

// General mock functions used by both mobile and desktop tests
export function mockRpdInitiation(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.BANK_ACCOUNT &&
          requestData.result.case === "verifyBankRequest" &&
          requestData.result.value.type ===
            BankVerificationType.REVERSE_PENNY_DROP
        ) {
          const refId = faker.string.uuid();
          const qrCode = faker.string.alphanumeric(100);

          const defaultOnboardingResponse: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.BANK_ACCOUNT,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "verifyBankResponse" as const,
              value: {
                result: {
                  case: "initiateRpdResponse" as const,
                  value: {
                    refId: refId,
                    qrCode: qrCode,
                    validUpto: new Date(Date.now() + 60000).toISOString(),
                    paymentOptions: [
                      {
                        app: InitiateRpdResponse_PaymentApp.PHONEPE,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/phonepe.webp",
                        upiIntent: `upi://pay?pa=test@phonepe&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                      {
                        app: InitiateRpdResponse_PaymentApp.GPAY,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/gpay.webp",
                        upiIntent: `upi://pay?pa=test@gpay&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                    ],
                  },
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, defaultOnboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

export function mockRpdInitiationWithOnboardingStatus(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.BANK_ACCOUNT &&
          requestData.result.case === "verifyBankRequest" &&
          requestData.result.value.type ===
            BankVerificationType.REVERSE_PENNY_DROP
        ) {
          const refId = faker.string.uuid();
          const qrCode = faker.string.alphanumeric(100);

          const defaultOnboardingResponse: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.BANK_ACCOUNT,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "verifyBankResponse" as const,
              value: {
                result: {
                  case: "initiateRpdResponse" as const,
                  value: {
                    refId: refId,
                    qrCode: qrCode,
                    validUpto: new Date(Date.now() + 60000).toISOString(),
                    paymentOptions: [
                      {
                        app: InitiateRpdResponse_PaymentApp.PHONEPE,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/phonepe.webp",
                        upiIntent: `upi://pay?pa=test@phonepe&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                      {
                        app: InitiateRpdResponse_PaymentApp.GPAY,
                        logoUrl:
                          "https://assets.stablemoney.in/web-frontend/payment-apps/gpay.webp",
                        upiIntent: `upi://pay?pa=test@gpay&pn=Test&am=1&cu=INR&tn=RPD-${refId}`,
                      },
                    ],
                  },
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, defaultOnboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });
}

export function mockRpdStatusPolling(
  page: Page,
  options: { successOnCall?: number } = {},
  validateRequest?: (requestJson: Record<string, unknown>) => void
) {
  let statusCallCount = 0;
  const successOnCall = options.successOnCall ?? 3;

  page.route("*/**/v1/bank/rpd-status", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(RpdStatusRequestSchema, requestBuffer);

        statusCallCount++;

        if (validateRequest) {
          const requestJson = toJson(
            RpdStatusRequestSchema,
            requestData
          ) as Record<string, unknown>;
          validateRequest(requestJson);
        }

        // Determine if this call should succeed based on successOnCall
        const shouldSucceed = statusCallCount >= successOnCall;

        const defaultStatusResponse: MessageInitShape<
          typeof RpdStatusResponseSchema
        > = {
          refId: requestData.refId,
          status: shouldSucceed ? "SUCCESS" : "PENDING",
          verified: shouldSucceed,
        };

        route.fulfill({
          status: 200,
          body: Buffer.from(
            toBinary(
              RpdStatusResponseSchema,
              create(RpdStatusResponseSchema, defaultStatusResponse)
            )
          ),
        });
        return;
      }
    }
    route.continue();
  });
}

// Mock for getOnboardingStatus() call made after successful RPD verification (general)
export function mockOnboardingStatusForSuccess(page: Page) {
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "GET") {
      const defaultOnboardingResponse: MessageInitShape<
        typeof OnboardingResponseSchema
      > = {
        kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
        nextStep: StepName.PAN_KYC,
        lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
        result: {
          case: "emptyKycResponse" as const,
          value: {},
        },
      };

      route.fulfill({
        status: 200,
        body: Buffer.from(
          toBinary(
            OnboardingResponseSchema,
            create(OnboardingResponseSchema, defaultOnboardingResponse)
          )
        ),
      });
      return;
    }
    route.continue();
  });
}
