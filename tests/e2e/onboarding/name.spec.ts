import { test, expect } from "@playwright/test";
import {
  mockPostNameNavigateToEmail,
  mockOnboardingStateNavigateToEmail,
  mockPostNameNavigateToPanUnknown,
  mockOnboardingStateNavigateToPanUnknown,
  mockPostNameNavigateToPanNoStep,
  mockOnboardingStateNavigateToPanNoStep,
  mockPostNameNavigateToPanUnexpected,
  mockOnboardingStateNavigateToPanUnexpected,
  mockPostNameLoadingState,
  mockOnboardingStateLoadingState,
} from "./name.mocks";

test.describe("Onboarding - Name Collection", () => {
  test.describe("Navigation Based on API Response", () => {
    test("should navigate to email when next step is EMAIL", async ({
      page,
    }) => {
      const mockData = mockPostNameNavigateToEmail(page);
      mockOnboardingStateNavigateToEmail(page);

      await page.goto("/onboarding/name");
      await page
        .getByRole("textbox", { name: "First name" })
        .fill(mockData.firstName);
      await page
        .getByRole("textbox", { name: "Last name" })
        .fill(mockData.lastName);
      await page.getByRole("button", { name: "Continue" }).click();

      await page.waitForURL("/onboarding/email");
    });

    test("should navigate to PAN when next step is unknown", async ({
      page,
    }) => {
      const mockData = mockPostNameNavigateToPanUnknown(page);
      mockOnboardingStateNavigateToPanUnknown(page);

      await page.goto("/onboarding/name");
      await page
        .getByRole("textbox", { name: "First name" })
        .fill(mockData.firstName);
      await page
        .getByRole("textbox", { name: "Last name" })
        .fill(mockData.lastName);
      await page.getByRole("button", { name: "Continue" }).click();

      await page.waitForURL("/onboarding/pan");
    });

    test("should navigate to PAN when no next step is provided", async ({
      page,
    }) => {
      const mockData = mockPostNameNavigateToPanNoStep(page);
      mockOnboardingStateNavigateToPanNoStep(page);

      await page.goto("/onboarding/name");
      await page
        .getByRole("textbox", { name: "First name" })
        .fill(mockData.firstName);
      await page
        .getByRole("textbox", { name: "Last name" })
        .fill(mockData.lastName);
      await page.getByRole("button", { name: "Continue" }).click();

      await page.waitForURL("/onboarding/pan");
    });

    test("should navigate to PAN for unexpected next step", async ({
      page,
    }) => {
      const mockData = mockPostNameNavigateToPanUnexpected(page);
      mockOnboardingStateNavigateToPanUnexpected(page);

      await page.goto("/onboarding/name");
      await page
        .getByRole("textbox", { name: "First name" })
        .fill(mockData.firstName);
      await page
        .getByRole("textbox", { name: "Last name" })
        .fill(mockData.lastName);

      const submitButton = page.getByRole("button", { name: "Continue" });
      await submitButton.click();

      // Should navigate to PAN since PAN_KYC is not a supported alpha step
      await page.waitForURL("/onboarding/pan");
    });
  });

  test.describe("Name Validation", () => {
    test("should display validation message for empty first name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(page.getByText("First name is required")).toBeVisible();
    });

    test("should display validation message for empty last name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("textbox", { name: "First name" }).fill("John");
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(page.getByText("Last name is required")).toBeVisible();
    });

    test("should display validation message for short first name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("textbox", { name: "First name" }).fill("J");
      await page.getByRole("textbox", { name: "Last name" }).fill("Doe");
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(
        page.getByText("First name must be at least 2 characters")
      ).toBeVisible();
    });

    test("should display validation message for short last name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("textbox", { name: "First name" }).fill("John");
      await page.getByRole("textbox", { name: "Last name" }).fill("D");
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(
        page.getByText("Last name must be at least 2 characters")
      ).toBeVisible();
    });

    test("should display validation message for invalid characters in first name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("textbox", { name: "First name" }).fill("John123");
      await page.getByRole("textbox", { name: "Last name" }).fill("Doe");
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(
        page.getByText("First name can only contain letters and spaces")
      ).toBeVisible();
    });

    test("should display validation message for invalid characters in last name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("textbox", { name: "First name" }).fill("John");
      await page.getByRole("textbox", { name: "Last name" }).fill("Doe@123");
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(
        page.getByText("Last name can only contain letters and spaces")
      ).toBeVisible();
    });

    test("should accept valid names with spaces", async ({ page }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("textbox", { name: "First name" }).fill("Mary Jane");
      await page
        .getByRole("textbox", { name: "Last name" })
        .fill("Van Der Berg");

      await page.getByRole("button", { name: "Continue" }).click();

      await expect(
        page.getByText("First name can only contain letters and spaces")
      ).not.toBeVisible();
      await expect(
        page.getByText("Last name can only contain letters and spaces")
      ).not.toBeVisible();
    });

    test("should display validation message for too long first name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page
        .getByRole("textbox", { name: "First name" })
        .fill("A".repeat(51));
      await page.getByRole("textbox", { name: "Last name" }).fill("Doe");
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(
        page.getByText("First name must be less than 50 characters")
      ).toBeVisible();
    });

    test("should display validation message for too long last name", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");
      await page.getByRole("textbox", { name: "First name" }).fill("John");
      await page
        .getByRole("textbox", { name: "Last name" })
        .fill("D".repeat(51));
      await page.getByRole("button", { name: "Continue" }).click();
      await expect(
        page.getByText("Last name must be less than 50 characters")
      ).toBeVisible();
    });
  });

  test.describe("Form Interaction", () => {
    test("should clear validation errors when user starts typing", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");

      await page.getByRole("button", { name: "Continue" }).click();
      await expect(page.getByText("First name is required")).toBeVisible();
      await expect(page.getByText("Last name is required")).toBeVisible();

      const firstNameInput = page.getByRole("textbox", { name: "First name" });
      const lastNameInput = page.getByRole("textbox", { name: "Last name" });

      await firstNameInput.fill("J");
      await expect(page.getByText("First name is required")).not.toBeVisible();

      await lastNameInput.fill("D");
      await expect(page.getByText("Last name is required")).not.toBeVisible();
    });

    test("should show loading state during form submission", async ({
      page,
    }) => {
      await page.goto("/onboarding/name");

      const mockData = mockPostNameLoadingState(page, 2000);
      mockOnboardingStateLoadingState(page);

      await page
        .getByRole("textbox", { name: "First name" })
        .fill(mockData.firstName);
      await page
        .getByRole("textbox", { name: "Last name" })
        .fill(mockData.lastName);

      const submitButton = page.getByRole("button", { name: "Continue" });

      // Start the click and immediately check for loading state
      const clickPromise = submitButton.click();

      // Check for loading state (spinner) within a short timeout
      await expect(submitButton.locator("svg")).toBeVisible();

      // Wait for the click to complete
      await clickPromise;
    });
  });
});
