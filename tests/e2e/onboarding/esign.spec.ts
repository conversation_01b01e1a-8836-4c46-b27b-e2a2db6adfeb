import { test, expect } from "@playwright/test";
import {
  mockUserProfile,
  mockEsignInitialState,
  mockEsignMandatoryDocs,
  mockCartDetails,
  mockEsignFullFlowWithPolling,
  mockEsignPollingLimit,
  mockEsignSdkFailure,
} from "./esign.mocks";

test.describe("Esign Page", () => {
  test.beforeEach(async ({ page }) => {
    mockUserProfile(page);
  });

  test("should display initial state and initialize Digio SDK when button is clicked", async ({
    page,
  }) => {
    mockEsignInitialState(page);

    await page.goto("/onboarding/esign");

    // Verify initial state UI elements
    await expect(
      page.getByText("Finish KYC using Aadhaar eSign")
    ).toBeVisible();
    await expect(
      page.getByText(
        "Enter your Aadhaar and verify using OTP sent to your Aadhaar linked mobile number"
      )
    ).toBeVisible();
    await expect(page.getByText("AADHAAR NUMBER")).toBeVisible();
    await expect(page.getByText("XXXX XXXX XXXX")).toBeVisible();
    await expect(page.getByText("FULL NAME")).toBeVisible();
    await expect(
      page.getByRole("button", { name: "Mandatory and important documents" })
    ).toBeVisible();

    const finishKycButton = page.getByRole("button", { name: "Finish KYC" });
    await expect(finishKycButton).toBeVisible();

    // Verify loading/error states are not visible initially
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).not.toBeVisible();
    await expect(page.getByText("eSign verification failed")).not.toBeVisible();
    await expect(finishKycButton.locator("svg")).not.toBeVisible();

    // Test SDK initialization
    await finishKycButton.click();

    // Verify Digio SDK was called correctly
    const digioCallInfo = await page.evaluate(() =>
      window.testUtils?.getDigioCallInfo()
    );
    expect(digioCallInfo?.constructorCalled).toBe(true);
    expect(digioCallInfo?.constructorCallCount).toBe(1);
    expect(digioCallInfo?.submitCalled).toBe(true);
    expect(digioCallInfo?.submitCallCount).toBe(1);

    // Verify SDK was called with the expected parameters structure
    expect(digioCallInfo?.lastSubmitArgs).toHaveLength(3);
    expect(typeof digioCallInfo?.lastSubmitArgs?.[0]).toBe("string"); // document ID
    expect(typeof digioCallInfo?.lastSubmitArgs?.[1]).toBe("string"); // customer identifier
    expect(typeof digioCallInfo?.lastSubmitArgs?.[2]).toBe("string"); // access token
  });

  test("should display mandatory documents modal when link is clicked", async ({
    page,
  }) => {
    mockEsignMandatoryDocs(page);

    await page.goto("/onboarding/esign");

    await page
      .getByRole("button", { name: "Mandatory and important documents" })
      .click();

    await expect(
      page.getByRole("heading", { name: "Mandatory and important documents" })
    ).toBeVisible();
  });
});

test.describe("Esign Page - Flow Tests", () => {
  test.beforeEach(async ({ page }) => {
    mockUserProfile(page);
  });

  test.skip("should complete full esign flow with UI state transitions and navigate on success", async ({
    page,
  }) => {
    await page.clock.install();

    // Mock cart API to prevent 401 errors
    mockCartDetails(page);
    const mockData = mockEsignFullFlowWithPolling(page);

    await page.goto("/onboarding/esign");

    // Verify initial state UI elements
    await expect(
      page.getByText("Finish KYC using Aadhaar eSign")
    ).toBeVisible();
    await expect(
      page.getByText(
        "Enter your Aadhaar and verify using OTP sent to your Aadhaar linked mobile number"
      )
    ).toBeVisible();
    await expect(page.getByText("AADHAAR NUMBER")).toBeVisible();
    await expect(page.getByText("XXXX XXXX XXXX")).toBeVisible();
    await expect(page.getByText("FULL NAME")).toBeVisible();

    const finishKycButton = page.getByRole("button", { name: "Finish KYC" });
    await expect(finishKycButton).toBeVisible();

    // Verify loading/error states are not visible initially
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).not.toBeVisible();
    await expect(page.getByText("eSign verification failed")).not.toBeVisible();

    // Click button and verify UI transitions
    await finishKycButton.click();

    // Verify loading state UI
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).toBeVisible();
    await expect(
      page.getByText(
        "This usually takes a few moments. Please don't close this page."
      )
    ).toBeVisible();

    // Verify spinner is present during loading
    await expect(page.locator("img.animate-spin").first()).toBeVisible();

    // Verify initial state is no longer visible during loading
    await expect(
      page.getByText("Finish KYC using Aadhaar eSign")
    ).not.toBeVisible();

    // Advance time to trigger polling - 3 calls at 1000ms intervals
    for (let i = 0; i < 3; i++) {
      await page.clock.runFor(1000);
      await page.waitForTimeout(100);
    }

    // Verify successful navigation
    await expect(page).toHaveURL("/onboarding/status", { timeout: 5000 });
    expect(mockData.getStatusCallCount()).toBe(3);
  });

  test.skip("should show complete UI state transitions from initial to loading to error when polling limit reached", async ({
    page,
  }) => {
    await page.clock.install();

    mockEsignPollingLimit(page);

    await page.goto("/onboarding/esign");

    // Verify initial state UI elements
    await expect(
      page.getByText("Finish KYC using Aadhaar eSign")
    ).toBeVisible();
    await expect(
      page.getByText(
        "Enter your Aadhaar and verify using OTP sent to your Aadhaar linked mobile number"
      )
    ).toBeVisible();
    await expect(page.getByText("AADHAAR NUMBER")).toBeVisible();
    await expect(page.getByText("XXXX XXXX XXXX")).toBeVisible();
    await expect(page.getByText("FULL NAME")).toBeVisible();

    const finishKycButton = page.getByRole("button", { name: "Finish KYC" });
    await expect(finishKycButton).toBeVisible();

    // Verify loading/error states are not visible initially
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).not.toBeVisible();
    await expect(page.getByText("eSign verification failed")).not.toBeVisible();

    // Click button and verify UI transitions
    await finishKycButton.click();

    // Verify loading state UI
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).toBeVisible();
    await expect(
      page.getByText(
        "This usually takes a few moments. Please don't close this page."
      )
    ).toBeVisible();

    // Verify spinner is present during loading
    await expect(page.locator("img.animate-spin")).toBeVisible();

    // Verify initial state is no longer visible during loading
    await expect(
      page.getByText("Finish KYC using Aadhaar eSign")
    ).not.toBeVisible();

    // Advance time to reach polling limit - 10 calls at 1000ms intervals
    for (let i = 0; i < 10; i++) {
      await page.clock.runFor(1000);
      await page.waitForTimeout(100);
    }

    // Wait for polling to complete and error state to show
    await expect(page.getByText("eSign verification failed")).toBeVisible({
      timeout: 5000,
    });
    await expect(
      page.getByText(
        "Please try again. If the issue persists, contact our support team."
      )
    ).toBeVisible();
    await expect(page.getByRole("button", { name: "Retry" })).toBeVisible();

    // Verify support call link is present and has correct href
    const supportLink = page.getByRole("link", { name: "Need help? Call us" });
    await expect(supportLink).toBeVisible();
    await expect(supportLink).toHaveAttribute("href", "tel:+************");

    // Verify loading state is no longer visible in error state
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).not.toBeVisible();

    await expect(page).toHaveURL("/onboarding/esign");
  });

  test("should show error toast and remain on initial screen when SDK fails", async ({
    browser,
  }) => {
    const context = await browser.newContext({
      storageState: "tests/e2e/storage-states/digio-esign-error.json",
    });
    const page = await context.newPage();

    // Mock user profile for this specific page instance
    mockUserProfile(page);
    mockEsignSdkFailure(page);

    await page.goto("/onboarding/esign");

    // Verify initial state UI elements
    await expect(
      page.getByText("Finish KYC using Aadhaar eSign")
    ).toBeVisible();
    await expect(
      page.getByText(
        "Enter your Aadhaar and verify using OTP sent to your Aadhaar linked mobile number"
      )
    ).toBeVisible();
    await expect(page.getByText("AADHAAR NUMBER")).toBeVisible();
    await expect(page.getByText("XXXX XXXX XXXX")).toBeVisible();
    await expect(page.getByText("FULL NAME")).toBeVisible();

    const finishKycButton = page.getByRole("button", { name: "Finish KYC" });
    await expect(finishKycButton).toBeVisible();

    // Verify loading/error states are not visible initially
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).not.toBeVisible();
    await expect(page.getByText("eSign verification failed")).not.toBeVisible();

    // Click button and verify SDK failure handling
    await finishKycButton.click();

    // Verify error toast is shown after SDK failure (global error handling)
    await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });

    // Verify we stay on the initial screen (no error screen for SDK failures)
    await expect(
      page.getByText("Finish KYC using Aadhaar eSign")
    ).toBeVisible();
    await expect(
      page.getByText(
        "Enter your Aadhaar and verify using OTP sent to your Aadhaar linked mobile number"
      )
    ).toBeVisible();
    await expect(page.getByText("AADHAAR NUMBER")).toBeVisible();
    await expect(finishKycButton).toBeVisible();

    // Verify error screen components are NOT shown for SDK failures
    await expect(page.getByText("eSign verification failed")).not.toBeVisible();
    await expect(page.getByRole("button", { name: "Retry" })).not.toBeVisible();
    await expect(
      page.getByRole("link", { name: "Need help? Call us" })
    ).not.toBeVisible();

    // Verify loading screen is NOT shown for SDK failures
    await expect(
      page.getByText("Completing your 1-time bonds investment KYC")
    ).not.toBeVisible();

    await expect(page).toHaveURL("/onboarding/esign");

    await context.close();
  });
});
