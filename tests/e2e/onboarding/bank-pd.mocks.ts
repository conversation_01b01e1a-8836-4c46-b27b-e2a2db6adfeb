import type { Page } from "@playwright/test";
import { expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  create,
  toBinary,
  fromBinary,
  type MessageInitShape,
} from "@bufbuild/protobuf";
import {
  OnboardingResponseSchema,
  OnboardingRequestSchema,
  StepName,
} from "@/clients/gen/broking/Kyc_pb";
import {
  UserLifetimeStatusResponse_KycStatus,
  UserLifetimeStatus,
} from "@/clients/gen/broking/Common_pb";
import { IfscListSchema } from "@/clients/gen/broking/BankAccountVerification_pb";
import { UserProfileResponseSchema } from "@/clients/gen/platform/public/models/identity/Profile_pb";
import { mergeDeep } from "../../utils";

export function generateValidAccountNumber(): string {
  return faker.string.numeric({
    length: faker.number.int({ min: 9, max: 17 }),
  });
}

export function generateValidIfscCode(): string {
  const bankCode = faker.string.alpha({ length: 4, casing: "upper" });
  const branchCode = faker.string.alphanumeric({ length: 6, casing: "upper" });
  return `${bankCode}0${branchCode}`;
}

// Test: should load bank PD page and show form fields
export function mockLoadPage(page: Page) {
  const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
    data: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      mobile: faker.string.numeric(10),
    },
    profileData: {
      // Add minimal profile data to satisfy the getProfile validation
      dob: faker.date.past().toISOString().split("T")[0],
    },
  };

  page.route("*/**/v1/user/profile", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

// Test: should show bank details when valid IFSC is entered
export function mockShowBankDetails(page: Page) {
  const responseData = {
    ifscCode: faker.string.alphanumeric(11).toUpperCase(),
    bankName: faker.company.name() + " Bank",
    branchName: faker.location.city() + " Branch",
  };

  // Mock user profile
  page.route("*/**/v1/user/profile", (route) => {
    const profileData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
      profileData: {
        // Add minimal profile data to satisfy the getProfile validation
        dob: faker.date.past().toISOString().split("T")[0],
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, profileData)
        )
      ),
    });
  });

  // Mock IFSC details
  page.route("*/**/v1/bank/ifsc*", (route) => {
    const url = new URL(route.request().url());
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Validate the request
    expect(queryParams).toMatchObject({
      ifsc: responseData.ifscCode,
    });

    const ifscResponseData: MessageInitShape<typeof IfscListSchema> = {
      id: faker.string.uuid(),
      ifsc: responseData.ifscCode,
      bank: responseData.bankName,
      branch: responseData.branchName,
      bankLogoUrl: faker.image.url(),
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(IfscListSchema, create(IfscListSchema, ifscResponseData))
      ),
    });
  });

  return responseData;
}

// Test: should successfully submit bank details and navigate to next step
export function mockSuccessfulSubmission(
  page: Page,
  overrides: {
    accountNumber?: string;
    ifscCode?: string;
  } = {}
) {
  const defaultData = {
    accountNumber: faker.string.numeric({
      length: faker.number.int({ min: 9, max: 17 }),
    }),
    ifscCode: generateValidIfscCode(),
  };

  const responseData = mergeDeep(defaultData, overrides);

  // Mock user profile
  page.route("*/**/v1/user/profile", (route) => {
    const profileData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
      profileData: {
        // Add minimal profile data to satisfy the getProfile validation
        dob: faker.date.past().toISOString().split("T")[0],
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, profileData)
        )
      ),
    });
  });

  // Mock IFSC details
  page.route("*/**/v1/bank/ifsc*", (route) => {
    const url = new URL(route.request().url());
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Validate the request
    expect(queryParams).toMatchObject({
      ifsc: responseData.ifscCode,
    });

    const ifscResponseData: MessageInitShape<typeof IfscListSchema> = {
      id: faker.string.uuid(),
      ifsc: responseData.ifscCode,
      bank: faker.company.name() + " Bank",
      branch: faker.location.city() + " Branch",
      bankLogoUrl: faker.image.url(),
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(IfscListSchema, create(IfscListSchema, ifscResponseData))
      ),
    });
  });

  // Mock penny drop submission
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();

    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        try {
          const requestData = fromBinary(
            OnboardingRequestSchema,
            requestBuffer
          );

          if (
            requestData.stepName === StepName.BANK_ACCOUNT &&
            requestData.result.case === "verifyBankRequest"
          ) {
            // Validate the request structure matches what the implementation sends
            expect(requestData.stepName).toBe(StepName.BANK_ACCOUNT);
            expect(requestData.result.case).toBe("verifyBankRequest");

            if (requestData.result.case === "verifyBankRequest") {
              const verifyBankRequest = requestData.result.value;
              expect(verifyBankRequest.result?.case).toBe("bankAccount");

              if (verifyBankRequest.result?.case === "bankAccount") {
                const bankAccount = verifyBankRequest.result.value;
                expect(bankAccount.beneficiaryAccountNo).toBe(
                  responseData.accountNumber
                );
                expect(bankAccount.ifscId).toBeDefined();
                expect(bankAccount.beneficiaryName).toBeDefined();
              }
            }

            const onboardingResponse: MessageInitShape<
              typeof OnboardingResponseSchema
            > = {
              kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
              nextStep: StepName.WET_SIGNATURE,
              lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
              result: {
                case: "verifyBankResponse" as const,
                value: {
                  result: {
                    case: "initiatePdResponse" as const,
                    value: {
                      verified: true,
                    },
                  },
                },
              },
            };

            route.fulfill({
              status: 200,
              body: Buffer.from(
                toBinary(
                  OnboardingResponseSchema,
                  create(OnboardingResponseSchema, onboardingResponse)
                )
              ),
            });
            return;
          }
        } catch {
          // If parsing fails, continue with the request
        }
      }
    }
    route.continue();
  });

  return responseData;
}

// Test: should show validation errors when submitting invalid data
export function mockValidationErrors(page: Page) {
  const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
    data: {
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      email: faker.internet.email(),
      mobile: faker.string.numeric(10),
    },
    profileData: {
      // Add minimal profile data to satisfy the getProfile validation
      dob: faker.date.past().toISOString().split("T")[0],
    },
  };

  page.route("*/**/v1/user/profile", (route) => {
    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  return responseData;
}

// Test: should show loading indicator while fetching IFSC details
export function mockLoadingIndicator(
  page: Page,
  overrides: {
    ifscCode?: string;
    delay?: number;
  } = {}
) {
  const defaultData = {
    ifscCode: faker.string.alphanumeric(11).toUpperCase(),
    delay: 1000,
  };

  const responseData = mergeDeep(defaultData, overrides);

  // Mock user profile
  page.route("*/**/v1/user/profile", (route) => {
    const profileData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
      profileData: {
        // Add minimal profile data to satisfy the getProfile validation
        dob: faker.date.past().toISOString().split("T")[0],
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, profileData)
        )
      ),
    });
  });

  // Mock slow IFSC details
  page.route("*/**/v1/bank/ifsc*", async (route) => {
    const url = new URL(route.request().url());
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Validate the request
    expect(queryParams).toMatchObject({
      ifsc: responseData.ifscCode,
    });

    await new Promise((resolve) => setTimeout(resolve, responseData.delay));

    const ifscResponseData: MessageInitShape<typeof IfscListSchema> = {
      id: faker.string.uuid(),
      bank: faker.company.name() + " Bank",
      branch: faker.location.city() + " Branch",
      ifsc: responseData.ifscCode,
      bankLogoUrl: faker.image.url(),
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(IfscListSchema, create(IfscListSchema, ifscResponseData))
      ),
    });
  });

  return responseData;
}

// Test: should only trigger IFSC lookup when code is less than 10 characters
export function mockIfscLookupTrigger(page: Page) {
  // Mock user profile
  page.route("*/**/v1/user/profile", (route) => {
    const responseData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
      profileData: {
        // Add minimal profile data to satisfy the getProfile validation
        dob: faker.date.past().toISOString().split("T")[0],
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, responseData)
        )
      ),
    });
  });

  // Mock IFSC details - should not be called for short codes
  page.route("*/**/v1/bank/ifsc*", (route) => {
    const ifscResponseData: MessageInitShape<typeof IfscListSchema> = {
      id: faker.string.uuid(),
      bank: faker.company.name() + " Bank",
      branch: faker.location.city() + " Branch",
      ifsc: faker.string.alphanumeric(11).toUpperCase(),
      bankLogoUrl: faker.image.url(),
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(IfscListSchema, create(IfscListSchema, ifscResponseData))
      ),
    });
  });
}

// Test: should show loading state during form submission
export function mockLoadingState(
  page: Page,
  overrides: {
    accountNumber?: string;
    ifscCode?: string;
    delay?: number;
  } = {}
) {
  const defaultData = {
    accountNumber: faker.string.numeric({
      length: faker.number.int({ min: 9, max: 17 }),
    }),
    ifscCode: faker.string.alphanumeric(11).toUpperCase(),
    delay: 1000,
  };

  const responseData = mergeDeep(defaultData, overrides);

  // Mock user profile
  page.route("*/**/v1/user/profile", (route) => {
    const profileData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
      profileData: {
        // Add minimal profile data to satisfy the getProfile validation
        dob: faker.date.past().toISOString().split("T")[0],
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, profileData)
        )
      ),
    });
  });

  // Mock IFSC details
  page.route("*/**/v1/bank/ifsc*", (route) => {
    const url = new URL(route.request().url());
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Validate the request
    expect(queryParams).toMatchObject({
      ifsc: responseData.ifscCode,
    });

    const ifscResponseData: MessageInitShape<typeof IfscListSchema> = {
      id: faker.string.uuid(),
      bank: faker.company.name() + " Bank",
      branch: faker.location.city() + " Branch",
      ifsc: responseData.ifscCode,
      bankLogoUrl: faker.image.url(),
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(IfscListSchema, create(IfscListSchema, ifscResponseData))
      ),
    });
  });

  // Mock slow penny drop submission
  page.route("*/**/v1/onboarding", async (route) => {
    const request = route.request();
    if (request.method() === "POST") {
      const requestBuffer = request.postDataBuffer();
      if (requestBuffer) {
        const requestData = fromBinary(OnboardingRequestSchema, requestBuffer);

        if (
          requestData.stepName === StepName.BANK_ACCOUNT &&
          requestData.result.case === "verifyBankRequest"
        ) {
          // Validate the request structure matches what the implementation sends
          expect(requestData.stepName).toBe(StepName.BANK_ACCOUNT);
          expect(requestData.result.case).toBe("verifyBankRequest");

          if (requestData.result.case === "verifyBankRequest") {
            const verifyBankRequest = requestData.result.value;
            expect(verifyBankRequest.result?.case).toBe("bankAccount");

            if (verifyBankRequest.result?.case === "bankAccount") {
              const bankAccount = verifyBankRequest.result.value;
              expect(bankAccount.beneficiaryAccountNo).toBe(
                responseData.accountNumber
              );
              expect(bankAccount.ifscId).toBeDefined();
              expect(bankAccount.beneficiaryName).toBeDefined();
            }
          }

          await new Promise((resolve) =>
            setTimeout(resolve, responseData.delay)
          );

          const onboardingResponse: MessageInitShape<
            typeof OnboardingResponseSchema
          > = {
            kycStatus: UserLifetimeStatusResponse_KycStatus.INITIATED,
            nextStep: StepName.WET_SIGNATURE,
            lifetimeStatus: UserLifetimeStatus.KYC_INITIATED,
            result: {
              case: "verifyBankResponse" as const,
              value: {
                result: {
                  case: "initiatePdResponse" as const,
                  value: {
                    verified: true,
                  },
                },
              },
            },
          };

          route.fulfill({
            status: 200,
            body: Buffer.from(
              toBinary(
                OnboardingResponseSchema,
                create(OnboardingResponseSchema, onboardingResponse)
              )
            ),
          });
          return;
        }
      }
    }
    route.continue();
  });

  return responseData;
}

// Test: should clear bank details when IFSC is modified
export function mockClearBankDetails(
  page: Page,
  overrides: {
    ifscCode?: string;
    bankName?: string;
  } = {}
) {
  const defaultData = {
    ifscCode: faker.string.alphanumeric(11).toUpperCase(),
    bankName: faker.company.name() + " Bank",
  };

  const responseData = mergeDeep(defaultData, overrides);

  // Mock user profile
  page.route("*/**/v1/user/profile", (route) => {
    const profileData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
      profileData: {
        // Add minimal profile data to satisfy the getProfile validation
        dob: faker.date.past().toISOString().split("T")[0],
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, profileData)
        )
      ),
    });
  });

  // Mock IFSC details
  page.route("*/**/v1/bank/ifsc*", (route) => {
    const url = new URL(route.request().url());
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Validate the request
    expect(queryParams).toMatchObject({
      ifsc: responseData.ifscCode,
    });

    const ifscResponseData: MessageInitShape<typeof IfscListSchema> = {
      id: faker.string.uuid(),
      ifsc: responseData.ifscCode,
      bank: responseData.bankName,
      branch: faker.location.city() + " Branch",
      bankLogoUrl: faker.image.url(),
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(IfscListSchema, create(IfscListSchema, ifscResponseData))
      ),
    });
  });

  return responseData;
}

// Test: should handle penny drop submission server errors
export function mockPennyDropServerError(
  page: Page,
  errorMessage: string = "Bank verification failed"
) {
  const responseData = {
    ifscCode: generateValidIfscCode(),
    bankName: faker.company.name() + " Bank",
    branchName: faker.location.city() + " Branch",
  };

  // Mock user profile
  page.route("*/**/v1/user/profile", (route) => {
    const profileData: MessageInitShape<typeof UserProfileResponseSchema> = {
      data: {
        firstName: faker.person.firstName(),
        lastName: faker.person.lastName(),
        email: faker.internet.email(),
        mobile: faker.string.numeric(10),
      },
      profileData: {
        dob: faker.date.past().toISOString().split("T")[0],
      },
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(
          UserProfileResponseSchema,
          create(UserProfileResponseSchema, profileData)
        )
      ),
    });
  });

  // Mock IFSC details
  page.route("*/**/v1/bank/ifsc*", (route) => {
    const url = new URL(route.request().url());
    const queryParams = Object.fromEntries(url.searchParams.entries());

    // Validate the request
    expect(queryParams).toMatchObject({
      ifsc: responseData.ifscCode,
    });

    const ifscResponseData: MessageInitShape<typeof IfscListSchema> = {
      id: faker.string.uuid(),
      ifsc: responseData.ifscCode,
      bank: responseData.bankName,
      branch: responseData.branchName,
      bankLogoUrl: faker.image.url(),
    };

    route.fulfill({
      status: 200,
      body: Buffer.from(
        toBinary(IfscListSchema, create(IfscListSchema, ifscResponseData))
      ),
    });
  });

  // Mock error response for onboarding endpoint
  page.route("*/**/v1/onboarding", (route) => {
    route.fulfill({
      status: 400,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        errorMessage: errorMessage,
      }),
    });
  });

  return responseData;
}

// Generic error response mock
export function mockErrorResponse(
  page: Page,
  route: string,
  status: number = 400,
  overrides: Record<string, unknown> = {}
) {
  page.route(route, (routeHandler) => {
    const defaultResponseData = {
      errorCode: faker.string.alphanumeric(8).toUpperCase(),
      errorMessage: faker.lorem.sentence(),
    };

    const responseData = mergeDeep(defaultResponseData, overrides);

    routeHandler.fulfill({
      status,
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(responseData),
    });
  });
}
