import { test, expect } from "@playwright/test";
import { faker } from "@faker-js/faker";
import {
  mockOnboardingStatusWithPolling,
  mockCartDetails,
  mockOnboardingStatusExchangeFailed,
  mockOnboardingStatusProcessingLimit,
} from "./status.mocks";
import { UserLifetimeStatusResponse_KycStatus } from "@/clients/gen/broking/Common_pb";

test.describe("Onboarding Status Page", () => {
  test("should show KYC completed state and handle polling with loading states", async ({
    page,
  }) => {
    await page.clock.install();

    const { getCallCount } = mockOnboardingStatusWithPolling(page, {
      kycStatus: UserLifetimeStatusResponse_KycStatus.COMPLETED,
      successOnCall: 4,
    });

    mockCartDetails(page, { cartItems: [] });

    await page.goto("/onboarding/status");

    await expect(page.getByText("Registering you with NSE")).toBeVisible({
      timeout: 5000,
    });
    await expect(
      page.getByText(
        "Just a moment! This is usually completed in under a minute"
      )
    ).toBeVisible();
    await expect(page.locator("img.animate-spin")).toBeVisible();

    await page.clock.runFor(50000);

    await expect(page.getByText("Your KYC is completed")).toBeVisible();
    await expect(
      page.getByRole("link", { name: "Proceed to buy" })
    ).toBeVisible();
    await expect(page.getByText("Registering you with NSE")).not.toBeVisible();
    await expect(page.locator("img.animate-spin")).not.toBeVisible();

    const proceedButton = page.getByRole("link", { name: "Proceed to buy" });
    await expect(proceedButton).toHaveAttribute("href", "/");

    expect(getCallCount()).toBe(4);
  });

  test("should show exchange onboarding failed state with support options", async ({
    page,
  }) => {
    mockOnboardingStatusExchangeFailed(page, {
      kycStatus:
        UserLifetimeStatusResponse_KycStatus.EXCHANGE_ONBOARDING_FAILED,
    });

    mockCartDetails(page, { cartItems: [] });

    await page.goto("/onboarding/status");

    await expect(page.getByText("Your NSE Exchange")).toBeVisible();
    await expect(page.getByText("onboarding failed")).toBeVisible();

    await expect(
      page.getByRole("link", { name: "Need help? Call us" })
    ).toBeVisible();
    await expect(page.getByRole("link", { name: "Go to home" })).toBeVisible();

    await expect(page.getByText("Registering you with NSE")).not.toBeVisible();
    await expect(page.getByText("Your KYC is completed")).not.toBeVisible();
    await expect(page.locator("img.animate-spin")).not.toBeVisible();
  });

  test("should show processing state when polling limit is reached", async ({
    page,
  }) => {
    await page.clock.install();

    const { getCallCount } = mockOnboardingStatusProcessingLimit(page, {
      kycStatus: UserLifetimeStatusResponse_KycStatus.ONBOARDING_ON_EXCHANGE,
    });

    mockCartDetails(page, { cartItems: [] });

    await page.goto("/onboarding/status");

    await expect(page.getByText("Registering you with NSE")).toBeVisible();
    await expect(page.locator("img.animate-spin")).toBeVisible();

    for (let i = 0; i < 5; i++) {
      await page.clock.runFor(10000);
      await page.waitForTimeout(100);
    }

    await expect(
      page.getByText("You'll be investment-ready within 10 minutes")
    ).toBeVisible({ timeout: 5000 });
    await expect(
      page.getByText("Your KYC is under processing with NSE")
    ).toBeVisible();

    await expect(page.locator("img.animate-spin")).not.toBeVisible();

    await expect(page.getByRole("link", { name: "Go to home" })).toBeVisible();
    await expect(
      page.getByRole("link", { name: "Need help? Call us" })
    ).toBeVisible();

    expect(getCallCount()).toBeGreaterThanOrEqual(5);
  });
});

test.describe("Onboarding Status Page - Cart Integration", () => {
  test("should link to bond calculator when cart has items", async ({
    page,
  }) => {
    const bondDetailId = faker.string.uuid();

    mockOnboardingStatusExchangeFailed(page, {
      kycStatus: UserLifetimeStatusResponse_KycStatus.COMPLETED,
    });

    const { cartItems } = mockCartDetails(page, {
      cartItems: [
        {
          bondDetailId,
          quantity: 1,
          inStock: true,
          outOfStockMessage: "",
        },
      ],
    });

    await page.goto("/onboarding/status");

    await expect(page.getByText("Your KYC is completed")).toBeVisible();

    const proceedButton = page.getByRole("link", { name: "Proceed to buy" });
    await expect(proceedButton).toHaveAttribute(
      "href",
      `/bonds/unknown/${cartItems[0].bondDetailId}/calculator`
    );
  });

  test("should link to home when cart is empty", async ({ page }) => {
    mockOnboardingStatusExchangeFailed(page, {
      kycStatus: UserLifetimeStatusResponse_KycStatus.COMPLETED,
    });

    mockCartDetails(page, { cartItems: [] });

    await page.goto("/onboarding/status");

    await expect(page.getByText("Your KYC is completed")).toBeVisible();

    const proceedButton = page.getByRole("link", { name: "Proceed to buy" });
    await expect(proceedButton).toHaveAttribute("href", "/");
  });
});
