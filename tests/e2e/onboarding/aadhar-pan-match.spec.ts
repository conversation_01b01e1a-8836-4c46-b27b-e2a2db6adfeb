import { test, expect } from "@playwright/test";
import {
  mockOnboardingAadhaarPanMatchStatus,
  mockRetryStepPan,
  mockRetryStepPanSlow,
  mockErrorResponse,
  mockNetworkError,
} from "./aadhar-pan-match.mocks";

test.describe("Onboarding - Aadhaar PAN Match", () => {
  test.describe("Happy Path", () => {
    test("should display Aadhaar PAN mismatch details and retry button", async ({
      page,
    }) => {
      const mockData = mockOnboardingAadhaarPanMatchStatus(page);

      await page.goto("/onboarding/aadhar-pan-match");

      await expect(
        page.getByRole("heading", {
          name: "The name on your PAN differs from name on Aadha<PERSON>",
        })
      ).toBeVisible();
      await expect(
        page.getByText(
          "As per SEBI's guidelines, your personal details should be same across all documents"
        )
      ).toBeVisible();

      await expect(page.getByText("PAN NUMBER")).toBeVisible();
      await expect(page.getByText(mockData.panDetails.panNumber)).toBeVisible();
      await expect(page.getByText("FULL NAME").first()).toBeVisible();
      await expect(
        page.getByText(mockData.panDetails.panName.toUpperCase())
      ).toBeVisible();

      await expect(page.getByText("AADHAAR NUMBER")).toBeVisible();
      await expect(
        page.getByText(mockData.aadhaarDetails.aadhaarNumber)
      ).toBeVisible();
      await expect(page.getByText("FULL NAME").nth(1)).toBeVisible();
      await expect(
        page.getByText(mockData.aadhaarDetails.aadhaarName.toUpperCase())
      ).toBeVisible();

      await expect(
        page.getByRole("button", { name: "Retry PAN" })
      ).toBeVisible();
      await expect(
        page.getByRole("button", { name: "Retry PAN" })
      ).toBeEnabled();

      await expect(page.getByAltText("Error")).toBeVisible();
    });

    test("should successfully retry PAN and navigate to next step", async ({
      page,
    }) => {
      mockOnboardingAadhaarPanMatchStatus(page);
      mockRetryStepPan(page);

      await page.goto("/onboarding/aadhar-pan-match");

      await expect(
        page.getByRole("button", { name: "Retry PAN" })
      ).toBeVisible();

      await page.getByRole("button", { name: "Retry PAN" }).click();

      await expect(page).toHaveURL("/onboarding/pan");
    });

    test("should show loading state during retry", async ({ page }) => {
      mockOnboardingAadhaarPanMatchStatus(page);
      mockRetryStepPanSlow(page);

      await page.goto("/onboarding/aadhar-pan-match");

      const retryButton = page.getByRole("button", { name: "Retry PAN" });
      await expect(retryButton).toBeVisible();

      await retryButton.click();

      await expect(retryButton).toBeDisabled();
      await expect(retryButton.locator("svg")).toBeVisible();
    });
  });

  test.describe("Error Handling", () => {
    test("should show error page when API fails with 500 server error", async ({
      page,
    }) => {
      mockErrorResponse(page, "*/**/v1/onboarding", 500, {
        errorMessage: "Internal server error",
      });

      await page.goto("/onboarding/aadhar-pan-match");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      await expect(page.getByText("Internal server error")).toBeVisible();
      await expect(page.getByText("Loading...")).not.toBeVisible();

      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test("should show error page when API fails with 400 client error", async ({
      page,
    }) => {
      mockErrorResponse(page, "*/**/v1/onboarding", 400, {
        errorMessage: "This step is already completed",
      });

      await page.goto("/onboarding/aadhar-pan-match");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      await expect(
        page.getByText("This step is already completed")
      ).toBeVisible();
      await expect(page.getByText("Loading...")).not.toBeVisible();

      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test("should show error page when network connection fails", async ({
      page,
    }) => {
      mockNetworkError(page, "*/**/v1/onboarding");

      await page.goto("/onboarding/aadhar-pan-match");

      await expect(page.getByRole("button", { name: "Retry" })).toBeVisible({
        timeout: 10000,
      });

      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();

      const reloadPromise = page.waitForLoadState("load");
      await page.getByRole("button", { name: "Retry" }).click();
      await reloadPromise;
    });

    test("should handle retry PAN mutation errors", async ({ page }) => {
      mockOnboardingAadhaarPanMatchStatus(page);

      mockErrorResponse(page, "*/**/v1/onboarding/retry-step*", 500, {
        errorMessage: "Failed to retry PAN",
      });

      await page.goto("/onboarding/aadhar-pan-match");

      await expect(
        page.getByRole("button", { name: "Retry PAN" })
      ).toBeVisible();

      await page.getByRole("button", { name: "Retry PAN" }).click();

      await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });
      await expect(page.getByText("Failed to retry PAN")).toBeVisible();
    });

    test("should handle retry PAN network errors", async ({ page }) => {
      mockOnboardingAadhaarPanMatchStatus(page);

      mockNetworkError(page, "*/**/v1/onboarding/retry-step*");

      await page.goto("/onboarding/aadhar-pan-match");

      await expect(
        page.getByRole("button", { name: "Retry PAN" })
      ).toBeVisible();

      await page.getByRole("button", { name: "Retry PAN" }).click();

      await expect(page.getByRole("status")).toBeVisible({ timeout: 5000 });
      await expect(
        page.getByText("Please check your internet connection and try again.")
      ).toBeVisible();
    });
  });
});
