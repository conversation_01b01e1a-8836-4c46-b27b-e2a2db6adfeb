/**
 * SDK loader stub for testing using Sinon.js
 *
 * This file replaces src/utils/sdk-loader.ts when VITE_USE_STUB_SDK=true.
 * It uses Sinon.js to create robust mocks for Digio and HyperVerge SDKs.
 */

import sinon from "sinon";

interface TestUtils {
  reset: () => void;
  getDigioStubs: () => typeof digioStubs;
  getHyperVergeStubs: () => typeof hyperVergeStubs;
  getDigioCallInfo: () => {
    constructorCalled: boolean;
    constructorCallCount: number;
    initCalled: boolean;
    initCallCount: number;
    submitCalled: boolean;
    submitCallCount: number;
    lastSubmitArgs?: [string, string, string];
  };
  getHyperVergeCallInfo: () => {
    constructorCalled: boolean;
    constructorCallCount: number;
    launchCalled: boolean;
    launchCallCount: number;
  };
}

declare global {
  interface Window {
    testUtils?: TestUtils;
  }
}

// Helper function to get mock response from localStorage
function getDigioMockResponse(): DigioResponse {
  let mockResponse: DigioResponse = { digio_doc_id: "mock_doc_id_123" };
  try {
    const localStorageResponse = localStorage.getItem(
      "test_digio_mock_response"
    );
    if (localStorageResponse) {
      mockResponse = JSON.parse(localStorageResponse);
    }
  } catch {
    // Use default response
  }
  return mockResponse;
}

function getHyperVergeMockResponse(): HyperKycResult {
  let mockResponse: HyperKycResult = {
    status: "auto_approved" as const,
    transactionId: "mock_transaction_123",
  };
  try {
    const localStorageResponse = localStorage.getItem(
      "test_hyperverge_mock_response"
    );
    if (localStorageResponse) {
      mockResponse = JSON.parse(localStorageResponse);
    }
  } catch {
    // Use default response
  }
  return mockResponse;
}

// Create Sinon stubs for Digio
const digioStubs = {
  constructor: sinon.spy(),
  init: sinon.stub(),
  submit: sinon.stub(),
};

// Create Sinon stubs for HyperVerge
const hyperVergeStubs = {
  constructor: sinon.spy(),
  launch: sinon.stub(),
};

class MockDigioInstance implements DigioInstance {
  private config: DigioConfig;

  constructor(config: DigioConfig) {
    this.config = config;
    digioStubs.constructor(config);
  }

  init(): void {
    digioStubs.init();
  }

  submit(
    documentId: string,
    customerIdentifier: string,
    accessToken: string
  ): void {
    // Track the call with Sinon
    digioStubs.submit(documentId, customerIdentifier, accessToken);

    // Handle the callback with mock response
    const mockResponse = getDigioMockResponse();
    setTimeout(() => this.config.callback(mockResponse), 100);
  }
}

class MockHyperKycConfig implements HyperKycConfigInstance {
  public inputs: { input_img: string } = { input_img: "" };

  constructor(
    public accessToken: string,
    public workflowId: string,
    public transactionId: string
  ) {
    hyperVergeStubs.constructor(accessToken, workflowId, transactionId);
  }
}

const MockHyperKYCModule = {
  launch: (
    config: HyperKycConfigInstance,
    handler: (result: HyperKycResult) => void
  ) => {
    // Configure stub to call handler asynchronously with mock response
    hyperVergeStubs.launch.callsFake(() => {
      const mockResponse = getHyperVergeMockResponse();
      setTimeout(() => handler(mockResponse), 100);
    });

    // Execute the stub
    hyperVergeStubs.launch(config, handler);
  },
};

const testUtils: TestUtils = {
  reset: () => {
    // Clear localStorage
    localStorage.removeItem("test_digio_mock_response");
    localStorage.removeItem("test_hyperverge_mock_response");
    // Reset Sinon stubs and restore their behavior
    digioStubs.constructor.resetHistory();
    digioStubs.init.resetHistory();
    digioStubs.submit.resetHistory();
    digioStubs.submit.resetBehavior();
    hyperVergeStubs.constructor.resetHistory();
    hyperVergeStubs.launch.resetHistory();
    hyperVergeStubs.launch.resetBehavior();
  },

  getDigioStubs: () => digioStubs,

  getHyperVergeStubs: () => hyperVergeStubs,

  getDigioCallInfo: () => ({
    constructorCalled: digioStubs.constructor.called,
    constructorCallCount: digioStubs.constructor.callCount,
    initCalled: digioStubs.init.called,
    initCallCount: digioStubs.init.callCount,
    submitCalled: digioStubs.submit.called,
    submitCallCount: digioStubs.submit.callCount,
    lastSubmitArgs: digioStubs.submit.lastCall?.args as
      | [string, string, string]
      | undefined,
  }),

  getHyperVergeCallInfo: () => ({
    constructorCalled: hyperVergeStubs.constructor.called,
    constructorCallCount: hyperVergeStubs.constructor.callCount,
    launchCalled: hyperVergeStubs.launch.called,
    launchCallCount: hyperVergeStubs.launch.callCount,
  }),
};

export function loadDigioSDK(): Promise<void> {
  window.Digio = MockDigioInstance;
  window.testUtils = testUtils;
  return Promise.resolve();
}

export function loadHyperVergeSDK(): Promise<void> {
  window.HyperKycConfig = MockHyperKycConfig;
  window.HyperKYCModule = MockHyperKYCModule;
  window.testUtils = testUtils;
  return Promise.resolve();
}
