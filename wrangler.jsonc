{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "bonds-web",
  "main": "./worker/index.js",
  "assets": {
    "binding": "ASSETS",
    "directory": "./dist",
    "run_worker_first": ["/", "/sitemap/*", "/sitemap.xml"]
  },
  "compatibility_flags": ["nodejs_compat"],
  "compatibility_date": "2025-06-04",
  "observability": {
    "enabled": true,
    "head_sampling_rate": 1,
    "logs": {
      "enabled": true,
      "head_sampling_rate": 1
    }
  },
  "placement": {
    "mode": "smart",
  },
  "routes": [
    {
      "pattern": "staging.stablebonds.in",
      "custom_domain": true,
    },
  ],
  "env": {
    "production": {
      "routes": [
        {
          "pattern": "stablebonds.in",
          "custom_domain": true,
        },
        {
          "pattern": "preprod.stablebonds.in",
          "custom_domain": true,
        },
        {
          "pattern": "next.stablebonds.in",
          "custom_domain": true,
        },
      ],
    },
  },
}
