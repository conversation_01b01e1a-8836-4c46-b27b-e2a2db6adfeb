name: Deployment

on:
  push:
    branches:
      - main
  workflow_dispatch:
    inputs:
      PROFILE:
        description: "The environment to deploy to"
        required: true
        default: staging
        type: choice
        options:
          - staging
          - production

jobs:
  deploy:
    name: Deploy to Cloudflare
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23

      - name: Install dependencies
        run: npm ci

      - name: Set environment variables based on profile
        run: |
          echo "VITE_GOOGLE_LOGIN_CLIENT_ID=${{ vars.VITE_GOOGLE_LOGIN_CLIENT_ID }}" >> $GITHUB_ENV
          echo "VITE_GOOGLE_LOGIN_CLIENT_SECRET=${{ vars.VITE_GOOGLE_LOGIN_CLIENT_SECRET }}" >> $GITHUB_ENV
          echo "VITE_SENTRY_DSN=${{ vars.VITE_SENTRY_DSN }}" >> $GITHUB_ENV
          echo "SENTRY_ORG=${{ secrets.SENTRY_ORG }}" >> $GITHUB_ENV
          echo "SENTRY_PROJECT=${{ secrets.SENTRY_PROJECT }}" >> $GITHUB_ENV
          echo "SENTRY_AUTH_TOKEN=${{ secrets.SENTRY_AUTH_TOKEN }}" >> $GITHUB_ENV

          if [ "${{ github.event_name }}" = "workflow_dispatch" ]; then
            PROFILE="${{ github.event.inputs.PROFILE }}"
          else
            PROFILE="staging"
          fi

          if [ "$PROFILE" = "staging" ]; then
            echo "VITE_PERSONALIZATION_BASE_URL=${{ vars.VITE_PERSONALIZATION_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_IDENTITY_BASE_URL=${{ vars.VITE_IDENTITY_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_BROKING_BASE_URL=${{ vars.VITE_BROKING_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_BUSINESS_BASE_URL=${{ vars.VITE_BUSINESS_BASE_URL_STAGING }}" >> $GITHUB_ENV
            echo "VITE_MIXPANEL_TOKEN=${{ vars.VITE_MIXPANEL_TOKEN_STAGING }}" >> $GITHUB_ENV
            echo "VITE_SENTRY_ENVIRONMENT=staging" >> $GITHUB_ENV
            echo "VITE_CASHFREE_ENV=sandbox" >> $GITHUB_ENV
            echo "DEPLOY_PROFILE=" >> $GITHUB_ENV
          elif [ "$PROFILE" = "production" ]; then
            echo "VITE_PERSONALIZATION_BASE_URL=${{ vars.VITE_PERSONALIZATION_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_IDENTITY_BASE_URL=${{ vars.VITE_IDENTITY_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_BROKING_BASE_URL=${{ vars.VITE_BROKING_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_BUSINESS_BASE_URL=${{ vars.VITE_BUSINESS_BASE_URL_PROD }}" >> $GITHUB_ENV
            echo "VITE_MIXPANEL_TOKEN=${{ vars.VITE_MIXPANEL_TOKEN_PROD }}" >> $GITHUB_ENV
            echo "VITE_SENTRY_ENVIRONMENT=production" >> $GITHUB_ENV
            echo "VITE_CASHFREE_ENV=production" >> $GITHUB_ENV
            echo "DEPLOY_PROFILE=production" >> $GITHUB_ENV
          fi

      - name: Copy robots.txt based on environment
        run: |
          if [ "${{ env.DEPLOY_PROFILE }}" = "production" ]; then
            cp resources/robots.prod.txt public/robots.txt
            echo "Copied production robots.txt"
          else
            cp resources/robots.txt public/robots.txt
            echo "Copied staging robots.txt"
          fi

      - name: Build project
        run: npm run build

      - name: Deploy with Wrangler
        uses: cloudflare/wrangler-action@v3
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          environment: ${{ env.DEPLOY_PROFILE }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
