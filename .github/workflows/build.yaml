name: Build and Test

on:
  pull_request:
    branches:
      - main
  workflow_dispatch:

jobs:
  build-and-test:
    name: Build and E2E Tests
    runs-on: ubuntu-larger
    timeout-minutes: 30

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 23
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Set up environment variables
        run: |
          # Copy example env file for testing
          cp .env.example .env
          
          # Set test-specific environment variables
          echo "VITE_PERSONALIZATION_BASE_URL=https://personalization-staging-api.stablemoney.in" >> .env
          echo "VITE_IDENTITY_BASE_URL=https://staging-api.stablemoney.in" >> .env
          echo "VITE_BROKING_BASE_URL=https://broking-staging-api.stablebonds.in" >> .env
          echo "VITE_BUSINESS_BASE_URL=https://business-staging-api.stablemoney.in" >> .env
          echo "VITE_GOOGLE_LOGIN_CLIENT_ID=test-client-id" >> .env
          echo "VITE_GOOGLE_LOGIN_CLIENT_SECRET=test-client-secret" >> .env
          echo "VITE_MIXPANEL_TOKEN=test-mixpanel-token" >> .env
          echo "VITE_CASHFREE_ENV=sandbox" >> .env
          echo "VITE_ALLOW_INDEXING=false" >> .env
          echo "VITE_USE_STUB_SDK=true" >> .env
          echo "VITE_SENTRY_DSN=" >> .env
          echo "VITE_SENTRY_ENVIRONMENT=test" >> .env
          echo "VITE_SENTRY_RELEASE=test-build" >> .env

      - name: Lint code
        run: npm run lint

      - name: Build project
        run: npm run build

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Run E2E tests
        run: npm run test:e2e
        env:
          CI: true

      - name: Upload Playwright report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 14

      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results
          path: test-results/
          retention-days: 14
