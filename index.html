<!doctype html>
<html lang="en" class="scrollbar-none overscroll-none antialiased">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <meta name="theme-color" content="#FFFFFF" />
    <meta
      name="viewport"
      content="width=device-width, height=device-height, target-densitydpi=device-dpi, initial-scale=1, minimum-scale=1, maximum-scale=1, viewport-fit=cover, user-scalable=no"
    />
    <meta
      name="description"
      content="Invest in government and corporate bonds with Stable Bonds. Secure, transparent, and regulated bond investments with competitive returns."
    />
    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta
      property="og:title"
      content="Stable Bonds - Invest in Government & Corporate Bonds"
    />
    <meta
      property="og:description"
      content="Invest in government and corporate bonds with Stable Bonds. Secure, transparent, and regulated bond investments with competitive returns."
    />
    <meta property="og:url" content="https://stablebonds.in" />
    <meta
      property="og:image"
      content="https://assets.stablemoney.in/web-frontend/bonds-preview.jpg"
    />
    <meta property="og:image:alt" content="Stable Money" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Stable Bonds - Invest in Government & Corporate Bonds"
    />
    <meta
      name="twitter:description"
      content="Invest in government and corporate bonds with Stable Bonds. Secure, transparent, and regulated bond investments with competitive returns."
    />
    <meta
      name="twitter:image"
      content="https://assets.stablemoney.in/web-frontend/bonds-preview.jpg"
    />

    <!-- PWA -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black" />
    <meta name="application-name" content="Stable Bonds" />
    <meta name="msapplication-TileColor" content="#000000" />
    <meta name="theme-color" content="#ffffff" />
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />

    <script>
      window.prerenderReady = false;
    </script>
    <!-- Analytics -->
    <script
      async
      src="https://www.googletagmanager.com/gtag/js?id=G-E6J3M5CGHX"
    ></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag("js", new Date());
      gtag("config", "G-E6J3M5CGHX");
    </script>
    <script>
      (function (w, d, s, l, i) {
        w[l] = w[l] || [];
        w[l].push({ "gtm.start": new Date().getTime(), event: "gtm.js" });
        var f = d.getElementsByTagName(s)[0],
          j = d.createElement(s),
          dl = l != "dataLayer" ? "&l=" + l : "";
        j.async = true;
        j.src = "https://www.googletagmanager.com/gtm.js?id=" + i + dl;
        f.parentNode.insertBefore(j, f);
      })(window, document, "script", "dataLayer", "GTM-5HMNFT2J");
    </script>
    <script type="text/javascript">
      _linkedin_partner_id = "7211940";
      window._linkedin_data_partner_ids =
        window._linkedin_data_partner_ids || [];
      window._linkedin_data_partner_ids.push(_linkedin_partner_id);

      (function (l) {
        if (!l) {
          window.lintrk = function (a, b) {
            window.lintrk.q.push([a, b]);
          };
          window.lintrk.q = [];
        }
        var s = document.getElementsByTagName("script")[0];
        var b = document.createElement("script");
        b.type = "text/javascript";
        b.async = true;
        b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
        s.parentNode.insertBefore(b, s);
      })(window.lintrk);
    </script>
  </head>

  <body class="min-h-screen overscroll-none">
    <div id="root" class="contents"></div>
    <div
      id="floating-footer"
      class="fixed right-0 bottom-0 left-0 z-10 md:hidden"
    >
      <div id="toaster-root" class="empty:hidden"></div>
      <div id="nudges" class="empty:hidden"></div>
      <div id="floating-footer-content" class="empty:hidden"></div>
      <div id="landing-page-floating-footer" class="empty:hidden"></div>
      <div id="url-bar" class="empty:hidden"></div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
