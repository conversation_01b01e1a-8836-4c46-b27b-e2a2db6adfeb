# Sentry Integration

This document explains how Sentry is integrated into the bonds-web project for error monitoring and performance tracking.

## Overview

Sentry is configured to:

- Capture and report JavaScript errors
- Monitor React component errors via Error Boundaries
- Track performance metrics
- Capture user sessions with replay functionality
- Integrate with existing analytics and error handling

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```bash
# Required for <PERSON><PERSON> to work
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_SENTRY_ENVIRONMENT=staging # or production, development
VITE_SENTRY_RELEASE=bonds-web@1.0.0

# Optional: For source maps upload in production builds
SENTRY_ORG=your-sentry-org
SENTRY_PROJECT=your-sentry-project
SENTRY_AUTH_TOKEN=your-sentry-auth-token
```

### Sentry Project Setup

1. Create a new project in Sentry
2. Choose "React" as the platform
3. Copy the DSN from the project settings
4. Configure the environment variables

## Features

### Error Monitoring

- **React Router Error Boundary**: Leverages existing error boundary for route-level errors
- **Global Error Handler**: Captures unhandled JavaScript errors
- **React Query Integration**: Reports API and mutation errors
- **Custom Error Reporting**: Manual error reporting with context

### Performance Monitoring

- **Transaction Tracking**: Monitors page loads and navigation
- **Custom Transactions**: Track specific operations
- **Sample Rate**: Configurable based on environment

### Session Replay

- **Error Sessions**: 100% of sessions with errors are recorded
- **Sample Sessions**: 10% of all sessions are recorded
- **Privacy**: Sensitive data is automatically masked

### User Context

- **User Identification**: Integrates with existing user store
- **Analytics Properties**: Includes user status and properties
- **Dynamic Updates**: User context updates when authentication state changes

## Usage

### Automatic Error Reporting

Most errors are automatically captured:

```typescript
// React component errors - automatically captured
function MyComponent() {
  throw new Error("Component error"); // Automatically sent to Sentry
}

// API errors via React Query - automatically captured
const mutation = useMutation({
  mutationFn: async () => {
    throw new Error("API error"); // Automatically sent to Sentry
  },
});

// Errors in withErrorToast wrapper - automatically captured
const handleSubmit = withErrorToast(async () => {
  throw new Error("Form error"); // Automatically sent to Sentry
});
```

### Manual Error Reporting

For custom error reporting:

```typescript
import { captureException, captureMessage, setContext } from "@/utils/sentry";

// Report an exception with context
try {
  riskyOperation();
} catch (error) {
  captureException(error, {
    operation: "riskyOperation",
    userId: user.id,
  });
}

// Report a message
captureMessage("Something important happened", "info", {
  feature: "bond-purchase",
  bondId: "123",
});

// Set context for subsequent errors
setContext("purchase", {
  bondId: "123",
  amount: 1000,
  timestamp: new Date().toISOString(),
});
```

### Adding Breadcrumbs

Breadcrumbs help understand the sequence of events leading to an error:

```typescript
import { addBreadcrumb } from "@/utils/sentry";

// Add a breadcrumb for user actions
addBreadcrumb("User clicked purchase button", "user", {
  bondId: "123",
  amount: 1000,
});

// Analytics events automatically add breadcrumbs
trackEvent("bond_purchased", { bondId: "123" }); // Automatically adds breadcrumb
```

### Performance Monitoring

```typescript
import { startTransaction } from "@/utils/sentry";

// Monitor a specific operation
const transaction = startTransaction("bond-purchase-flow", "navigation");
try {
  await purchaseBond();
  transaction.setStatus("ok");
} catch (error) {
  transaction.setStatus("internal_error");
  throw error;
} finally {
  transaction.finish();
}
```

## Integration Points

### React Query

- Mutation errors are automatically reported
- Network errors are captured with request context
- Authentication errors are filtered out

### Analytics

- All `trackEvent` calls add breadcrumbs to Sentry
- User properties from analytics are included in Sentry context

### Error Boundaries

- Existing React Router error boundary enhanced with Sentry reporting
- Route-level errors are captured automatically
- No additional error boundary wrapper needed

### User Store

- User context is automatically updated when authentication state changes
- User properties are included in all error reports

## Development vs Production

### Development

- Sentry is initialized but with minimal sampling
- Performance transactions are not sent
- Console warnings for missing configuration

### Production

- Full error and performance monitoring
- Source maps are uploaded for better stack traces
- Session replay is enabled
- Release tracking is enabled

## Best Practices

1. **Don't Report Expected Errors**: Authentication errors and validation errors are filtered out
2. **Add Context**: Use `setContext` before operations that might fail
3. **Use Breadcrumbs**: Add breadcrumbs for important user actions
4. **Test Error Reporting**: Verify errors are being captured in staging
5. **Monitor Performance**: Use custom transactions for critical user flows
6. **Avoid Redundant Data**: Sentry automatically captures URL, browser info, and user agent - no need to manually add these

## Troubleshooting

### Sentry Not Capturing Errors

1. Check that `VITE_SENTRY_DSN` is set correctly
2. Verify the DSN is valid and the project exists
3. Check browser console for Sentry initialization errors
4. Ensure errors are not being caught and handled elsewhere

### Source Maps Not Working

1. Verify `SENTRY_AUTH_TOKEN` has the correct permissions
2. Check that the build is generating source maps
3. Ensure the Sentry Vite plugin is configured correctly
4. Verify the release name matches between build and runtime

### Performance Data Missing

1. Check that `tracesSampleRate` is set appropriately
2. Verify performance monitoring is enabled in your Sentry project
3. Ensure transactions are being started and finished correctly

## Monitoring and Alerts

Set up alerts in Sentry for:

- New error types
- Error rate increases
- Performance degradation
- High error volume

Configure notifications to go to the appropriate team channels.
