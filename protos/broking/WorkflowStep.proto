syntax = "proto3";

package com.stablemoney.api.broking.identity;

import "Common.proto";
import "Nominee.proto";
import "BankAccountVerification.proto";

option java_package = "com.stablemoney.api.broking.identity";
option java_multiple_files = true;

enum WorkflowName {
    UNKNOWN_WORKFLOW = 0;
    DEMAT_ACCOUNT_OPENING = 1;
    TRADING_ACCOUNT_OPENING = 2;
    TRADING_DEMAT_ACCOUNT_OPENING = 3;
    ONBOARDING_INITIATION = 4;
}

enum StepName {
    UNKNOWN_STEP = 0;
    DIGIO_KYC = 1;
    USER_PROFILE = 2;
    SELFIE = 3;
    WET_SIGNATURE = 4;
    CVL_PULL = 5;
    ESIGN = 6;
    CVL_PUSH = 7;
    CDSL_PUSH_INITIATE = 8;
    CDSL_PUSH = 9;
    CDSL_STATUS_CHECK = 10;
    NSE_ONBOARDING = 11;
    CDSL_PUSH_WAITING = 12;
    CVL_PUSH_WAITING = 13;
    NSE_PUSH_WAITING = 14;
    NOMINEE = 15;
    NOMINEE_SYNC = 16;
    DEMAT_ACCOUNT = 17;
    PAN_KYC = 18;
    AADHAAR_PAN_MATCH = 19;
    BANK_ACCOUNT = 20;
    ADDRESS_SYNC_CVL_PUSH = 22;
    ESIGN_WITH_DEMAT = 23;
    ADD_DEMAT_TO_NSE = 24;
    CVL_PULL_WAITING = 25;
}

message InitiateDigioKycRequest {
    WorkflowName workflow = 1;
}

message InitiateDigioKycResponse {
    string id = 1;
    string access_token = 2;
    string customer_identifier = 3;
    ContinueWorkflowResponse next_step = 4;
    bool is_complete = 5;
}

message CheckDigioKycStatusRequest {
    WorkflowName workflow = 1;
}

message CheckDigioKycStatusResponse {
    string kyc_status = 1;
    string description = 2;
    ContinueWorkflowResponse next_step = 3;
}

message UserProfileUpdateRequest {
    WorkflowName workflow = 1;
    optional string dob = 2;
    optional IncomeRange income_range = 3 ;
    optional EmploymentType employment_type = 4 ;
    optional TradingExperience trading_experience = 5 ;
    optional MaritalStatus marital_status = 6 ;
    optional string father_name = 7 ;
    optional string mother_name = 8 ;
    optional Gender gender = 9 ;
}

message UserProfileUpdateResponse {
    ContinueWorkflowResponse next_step = 1;
}

message SelfieInitiationRequest {
    WorkflowName workflow = 1;
}

message SelfieInitiationResponse {
    string selfie = 1;
    string workflow_id = 2;
    string access_token = 3;
    string transaction_id = 4;
    ContinueWorkflowResponse next_step = 5;
    bool is_complete = 6;
}

message SelfieStatusCheckRequest {
    WorkflowName workflow = 1;
    string transaction_id = 2;
}

message SelfieStatusCheckResponse {
    string selfie_status = 1;
    string description = 2;
    ContinueWorkflowResponse next_step = 3;
    bool is_complete = 4;
}

message WetSignatureRequest {
    optional bool is_pep = 1 ;
    optional bool is_indian_citizen = 2 ;
    string document_id = 4 ;
    optional bool credit_report_consent = 5 ;
    optional bool is_edis = 6 ;
    optional bool is_sms_consent = 7 ;
    optional bool is_unlawful = 8 ;
    WorkflowName workflow = 9;
}

message WetSignatureResponse {
    ContinueWorkflowResponse next_step = 1;
}

message GenerateEsignTokenRequest {
    WorkflowName workflow = 1;
    StepName step_name = 2;
}

message GenerateEsignTokenResponse {
    string id = 1;
    string access_token = 2;
    string customer_identifier = 3;
    ContinueWorkflowResponse next_step = 4;
    bool is_complete = 5;
}

message EsignStatusRequest {
    WorkflowName workflow = 1;
    string id = 2;
    StepName step_name = 3;
}

message EsignStatusResponse {
    string esign_status = 1;
    string description = 2;
    ContinueWorkflowResponse next_step = 3;
}

message SelfieStartRequest {
    WorkflowName workflow = 1;
    string transaction_id = 2;
}

message SelfieStartResponse {
    string selfie = 1;
    string workflow_id = 2;
    string access_token = 3;
    string transaction_id = 4;
    bool completed = 5;
}

message ContinueWorkflowResponse {
    WorkflowStatus workflow_status = 1;
    StepName next_step = 2;
    WorkflowName workflow_name = 3;
    bool is_background_step = 4;
}


message InitiateWorkflowResponse {
}

enum NomineeIdentifierType {
    UNKNOWN_IDENTITY = 0;
    AADHAAR = 1;
    PAN = 2;
    DRIVING_LICENSE = 3;
}

message GuardianDetails {
    string name = 1 ;
    RelationshipType relationship_type = 2;
    string relationship = 3;
    AddressProto address = 4;
}

message NomineeDetails {
    string name = 1 ;
    RelationshipType relationship_type = 2 ;
    string dob = 3;
    string relationship = 4;
    GuardianDetails guardian_details = 5;
    AddressProto address = 6;
    optional double allocation_percentage = 7 ;
    optional NomineeIdentifierType identifier_type = 8 ;
    optional string identifier = 9 ;
    optional string email = 10 ;
    optional string phone_number = 11 ;
}

message NomineeRequest {
    bool opt_out = 1;
    repeated NomineeDetails nominee_details = 2 ;
    WorkflowName workflow = 3;
}

enum WorkflowStatus {
    UNKNOWN_STATUS = 0;
    INITIATED = 1;
    COMPLETED = 2;
    EXPIRED = 3;
    NOT_INITIATED = 4;
}

message DematRequest {
    bool create_account = 1;
    optional string demat_account_number = 2 ;
    WorkflowName workflow = 3;
}

message DematResponse {
    ContinueWorkflowResponse next_step = 1;
    optional bool is_verified = 2;
    optional string verification_response = 3;
}

message NomineeResponse {
    ContinueWorkflowResponse next_step = 1;
}

message PanVerificationRequest {
    string full_name = 1;
    string dob = 2;
    string pan_number = 3 ;
    WorkflowName workflow_name = 4;
}

message PanVerificationResponse {
    bool pan_kyc_status = 1;
    bool name_match_status = 2;
    bool dob_match_status = 3;
    bool is_pan_valid = 4;
    ContinueWorkflowResponse next_step = 5;
    optional string error_message = 6;
}

message AadhaarPanMatchRequest {
    WorkflowName workflow = 1;
}

message AadhaarPanMatchResponse {
    message AadhaarDetails {
        string aadhaar_name = 1;
        string aadhaar_number = 2;
    }
    message PanDetails {
        string pan_name = 1;
        string pan_number = 2;
    }
    bool aadhaar_pan_match_status = 1;
    bool aadhaar_pan_dob_match = 2;
    bool aadhaar_pan_name_match = 3;
    AadhaarDetails aadhaar_details = 4;
    PanDetails pan_details = 5;
    ContinueWorkflowResponse next_step = 6;
}

message RetryStepRequest {
    WorkflowName workflow = 1;
    StepName step_name = 2;
}

message RetryStepResponse {
    ContinueWorkflowResponse next_step = 1;
}

message InitiateBankVerificationRequest {
    BankVerificationType type = 1;
    oneof result {
        BankAccount bank_account = 2;
        Cheque cheque = 3;
    }
    string transaction_id = 4;
    WorkflowName workflow = 5;
}

message InitiateBankVerificationResponse {
    oneof result {
        InitiatePdResponse initiate_pd_response = 2;
        InitiateRpdResponse initiate_rpd_response = 3;
        InitiateChequeResponse initiate_cheque_response = 4;
    }
    ContinueWorkflowResponse next_step = 5;
}

message BankVerificationStatusRequest {
    string ref_id = 1;
    WorkflowName workflow = 2;
}

message BankVerificationStatusResponse {
    string ref_id = 1;
    string status = 3;
    bool verified = 4;
    string message = 5;
    ContinueWorkflowResponse next_step = 6;
}