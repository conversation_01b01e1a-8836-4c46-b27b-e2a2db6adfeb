syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;


message GetProviderDetailsRequest {
  string partial_demat_account_number = 1 ;
}

message GetDematProviderDetailsResponse{
  string provider_id = 1;
  string name = 2;
  string icon_url = 3;
}

message AddDematAccountRequest {
  string demat_account_number = 3 ;
  string demat_cml_document_id = 4;
  optional string demat_cml_document_password = 5;
}

message AddDematAccountResponse {
  bool is_verified = 1;
  string verification_response = 2;
}

message DematAccountDB {
  optional string id = 1;
  optional string user_id = 2;
  optional string account_number = 3;
  optional string provider_id = 4;
  optional string demat_cml_document_id = 5;
  optional string demat_cml_document_password = 6;
  optional bool is_verified = 7;
}

message DematAccountDBUpdateRequest {
  DematAccountDB demat_account = 1;
}

message DematAccountDBUpdateResponse {
  DematAccountDB demat_account = 1;
}

message DematVerificationResponse {
  bool is_verified = 1;
  string verification_response = 2;
}
