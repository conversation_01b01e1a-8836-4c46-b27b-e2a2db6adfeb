syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Order.proto";
import "BondDetails.proto";

enum CartAction {
  CART_ACTION_UNKNOWN = 0;
  ADD = 1;
  REMOVE = 2;
  RESET = 3;
}

message UpdateCartRequest {
  optional string bond_detail_id = 1;
  CartAction action = 2;
  optional int32 units = 3;
}

message UpdateCartResponse {
  string cart_item_id = 1;
}

message CartItem {
  string bond_detail_id = 1;
  int32 quantity = 2;
  bool in_stock = 3;
  string out_of_stock_message = 4;
}

message CartDetails {
  repeated CartItem cart_items = 1;
}

message AddCheckoutCartRequest {
  string bond_detail_id = 1;
  int32 quantity = 2;
}