syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Kyc.proto";

message UserOnboardingStepsResponse {
  repeated UserStateData data = 2;
}

message UserStateData {
  StepName onboarding_step = 1;
  OnBoardingStatus status = 2;
}

message OnboardingModuleStepsResponse {
  repeated OnboardingModuleSteps data = 1;
}

message OnboardingModuleSteps {
  StepName onboarding_step = 1;
  int32 kyc_sequence = 2;
  string module = 3;
  bool is_skippable = 4;
}

message SkipOnboardingResponse {
}

enum OnboardingModule {
  ONBOARDING_MODULE_UNKNOWN = 0;
  APP_ONBOARDING = 1;
  FIXED_DEPOSIT = 2;
  MUTUAL_FUND = 3;
  BOND_ONBOARDING = 4;
  POST_BOND_ONBOARDING = 5;
}

message ChangeOnboardingStepRequest {
  string user_id = 1;
  StepName onboarding_step = 2;
}

message ChangeOnboardingStepResponse {
  bool changed = 1;
}