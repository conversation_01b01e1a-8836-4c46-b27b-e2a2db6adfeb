syntax = "proto3";

package com.stablemoney.api.broking;

option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;


message RedirectDeeplink {
    string path = 1;
    string path_type = 2;
}

enum CollectionType {
    MANUAL = 0;
    EXPRESSION = 1;
}
enum MessageType {
    UNKNOWN_MESSAGE_TYPE = 0;
    PROMOTIONAL_MESSAGE = 1;
    TRANSACTIONAL_MESSAGE = 2;
    OTP_MESSAGE = 3;
}

enum ContentType {
    UNKNOWN_CONTENT_TYPE = 0;
    TEXT = 1;
    UNICODE = 2;
}

message ErrorResponse {
    string error_code = 1;
    string error_message = 2;
}

enum OptinStatus {
    UNKNOWN_OPTIN_STATUS = 0;
    OPTED_IN = 1;
    OPTED_OUT = 2;
}

enum AppConfigType {
    APP_CONFIG_TYPE_UNKNOWN = 0;
    BANK_VERIFICATION_RPD = 1;
    BANK_VERIFICATION_PD = 2;
    BASIC_DETAILS_QUESTIONS = 3;
    PAN_CVL_CHECK = 4;
    RPD_SUPPORTED_UPI_APPS = 5;
    CREDIT_REPORT = 6;
    MOBILE_RESEND = 7;
    MOBILE_ATTEMPT = 8;
    MOBILE_OTP_LENGTH = 9;
    EMAIL_OTP_LENGTH = 10;
    EMAIL_RESEND = 11;
    EMAIL_ATTEMPT = 12;
    ANDROID_APP_VERSION = 13;
    IOS_APP_VERSION = 14;
    WEB_APP_VERSION = 15;
    MOBILE_LOGIN_BLOCKED_TIME_IN_HOURS = 16;
    EMAIL_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 17;
    MOBILE_OTP_EXPIRY_TIME_IN_MINUTES = 18;
    EMAIL_OTP_EXPIRY_TIME_IN_MINUTES = 19;
    EMAIL_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 20;
    EMAIL_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 21;
    MOBILE_LOGIN_ATTEMPT_BLOCKED_TIME_IN_HOURS = 22;
    MOBILE_LOGIN_RESEND_BLOCKED_TIME_IN_HOURS = 23;
    MOBILE_OTP_REGENERATE_BEFORE_EXPIRY_TIME_IN_MINUTES = 24;
    EMAIL_OTP_RESEND_TIMER_IN_SECONDS = 25;
    MOBILE_OTP_RESEND_TIMER_IN_SECONDS = 26;
    TOKEN_VALIDITY_IN_HOURS = 27;
    REFRESH_TOKEN_VALIDITY_IN_HOURS = 28;
}

enum AppConfigValueType {
    APP_CONFIG_VALUE_TYPE_UNKNOWN = 0;
    TEXT_TYPE = 1;
    BOOLEAN_TYPE = 2;
    STRING_TYPE = 3;
    JSON_TYPE = 4;
    INTEGER_TYPE = 5;
}

enum UserConfigType {
    UNKNOWN_USER_CONFIG_TYPE = 0;
    BANNER_CONFIG = 1;
}

enum UserLifetimeStatus {
    LIFETIME_STATUS_UNKNOWN = 0;
    NOT_REGISTERED = 2;
    NEW_USER = 4;
    KYC_INITIATED = 6;
    MIN_KYC_DONE = 7;
    KYC_COMPLETED = 8;
    ORDER_INITIATED = 10;
    PAYMENT_DONE = 12;
    BOND_PURCHASED = 16;
}

message UserContextRequest {
    string user_id = 1;
}

message UserContextResponse {
    string user_id = 1;
    UserLifetimeStatus lifetime_status = 3;
    UserLifetimeStatusResponse.KycStatus kyc_status = 5;
    double total_investment = 7;
    string first_payment_date = 11;
    string last_payment_date = 13;
    int64 bonds_consent_timestamp = 15;
    bool nse_onboarding_in_progress = 16;
}

message UserLifetimeStatusRequest {
    string user_id = 1;
}

message UserLifetimeStatusResponse {
    enum KycStatus {
        KYC_STATUS_UNKNOWN = 0;
        NOT_INITIATED = 1;
        INITIATED = 2;
        CVL_PUSH_REQUIRED = 5;
        CVL_PUSH_COMPLETED = 10;
        CVL_VALIDATED = 15;
        ONBOARDING_ON_EXCHANGE = 20;
        EXCHANGE_ONBOARDING_FAILED = 25;
        COMPLETED = 30;
    }
    UserLifetimeStatus lifetime_status = 1;
    KycStatus kyc_status = 2;
}

enum IncomeRange {
    UNKNOWN_INCOME_RANGE = 0;
    LESS_THAN_1L = 1;
    BETWEEN_1L_AND_5L = 2;
    BETWEEN_5L_AND_10L = 3;
    BETWEEN_10L_AND_25L = 4;
    BETWEEN_25L_AND_1CR = 5;
    ABOVE_1CR = 6;
}

enum EmploymentType {
    UNKNOWN_EMPLOYMENT_TYPE = 0;
    PRIVATE_SECTOR_SERVICE = 1;
    PUBLIC_SECTOR = 2;
    BUSINESS = 3;
    PROFESSIONAL = 4;
    AGRICULTURIST = 5;
    RETIRED = 6;
    HOUSEWIFE = 7;
    STUDENT = 8;
    FOREX_DEALER = 9;
    GOVERNMENT_SERVICE = 10;
    OTHERS_EMPLOYMENT_TYPE = 11;
    SELF_EMPLOYED = 12;
}

enum Gender {
    UNKNOWN_GENDER = 0;
    MALE = 1;
    FEMALE = 2;
    OTHER_GENDER = 3;
}

enum MaritalStatus {
    UNKNOWN_MARITAL_STATUS = 0;
    SINGLE = 1;
    MARRIED = 2;
}

enum TradingExperience {
    UNKNOWN_TRADING_EXPERIENCE = 0;
    LESS_THAN_1_MONTH = 1;
    BETWEEN_1_MONTH_AND_6_MONTH = 2;
    BETWEEN_6_MONTH_AND_1_YEAR = 3;
    BETWEEN_1_YEAR_AND_5_YEARS = 4;
    ABOVE_5_YEARS = 5;
}

message ConfigDataResponse {
    repeated ConfigData data = 1;
}

message ConfigData {
    AppConfigType config_name = 1;
    string config_value = 2;
    AppConfigValueType config_type = 3;
    int32 min_version = 4;
    int32 max_version = 5;
    string description = 6;
}

message UserConfigResponse {
    repeated UserConfigData data = 1;
}

message UserConfigData {
    UserConfigType config_name = 1;
    AppConfigValueType config_type = 2;
    string config_value = 3;
    string description = 4;
    bool show_cta = 5;
}

message DataKey {
    enum VariableType {
        UNKNOWN = 0;
        STRING = 1;
        CURRENCY = 2;
        DATE = 3;
        DATE_TIME = 4;
        USER_FIRST_NAME = 5;
        SHORT_CURRENCY = 6;
        PERCENT = 7;
        PERCENT_2F = 8;
    }
    message Variable {
        string name = 1;
        VariableType type = 2;
        string value = 3;
    }
    string key = 1;
    repeated Variable context_variables = 3;
}

message TagConfig {
    string name = 1;
    string icon_url = 2;
    string color = 3;
    string bg_color = 4;
    string type = 5;
}

message AddressProto {
    string address_line1 = 1 ;
    string address_line2 = 2;
    string address_line3 = 3;
    string city = 4 ;
    string post_code = 5 ;
    string state = 6 ;
    string country = 7 ;
}

message UpdateProfileRequest {
    optional string dob = 1;
    optional IncomeRange income_range = 2 ;
    optional EmploymentType employment_type = 3 ;
    optional TradingExperience trading_experience = 4 ;
    optional MaritalStatus marital_status = 5 ;
    optional string father_name = 6 ;
    optional string mother_name = 7 ;
    optional Gender gender = 8 ;
}

message UpdateProfileResponse {
    bool status = 1;
    string description = 2;
}

message AddCurrentAddressRequest {
    bool is_same_as_permanent_address = 1;
    optional AddressProto address_proto = 2;
}

message AddressDB {
    optional string id = 1;
    optional string address_line1 = 2;
    optional string address_line2 = 3;
    optional string address_line3 = 4;
    optional string city = 5;
    optional string post_code = 6;
    optional string state = 7;
    optional string country = 8;
    optional double latitude = 9;
    optional double longitude = 10;
}

message AddressDBUpdateRequest {
    AddressDB address = 1;
}

message AddressDBUpdateResponse {
    AddressDB address = 1;
}

message AddCurrentAddressResponse {
}

message NseStatusResponse {
    enum NseStatus {
        UNKNOWN = 0;
        PENDING = 1;
        SUCCESS = 2;
        FAILED = 3;
    }
    NseStatus nse_status = 1;
}

message PaginationRequest {
  int32 page = 1;
  int32  size = 2;
}