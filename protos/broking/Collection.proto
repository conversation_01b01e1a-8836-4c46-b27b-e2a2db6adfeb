syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "BondDetails.proto";
import "Common.proto";


message CollectionResponse {
  enum DisplayType {
    DISPLAY_TYPE_UNKNOWN = 0;
    DEFAULT = 1;
    MINIMUM_INVESTMENT = 2;
    SHORT_TERM = 4;
    SHORT_TERM_XIRR = 6;
    SELLING_OUT_SOON = 8;
  }

  string id = 1;
  string title = 2;
  string description = 3;
  string icon_url = 4;
  repeated CollectionItem collection_item = 5;
  string name = 6;
  bool include_sold_out = 7;
  DisplayType display_type = 8;
}

message CollectionItemOfferingDetails {
  string id = 1;
  int32 min_lot_size = 2;
}

message CollectionItem {
  string display_title = 1;
  string rating_agency = 2;
  string rating = 3;
  double xirr = 4;
  string id = 5;
  string bg_color = 6;
  InstitutionDetails about_the_institution = 7;
  string interest_payment = 8;
  string face_value = 9;
  string cover_image_url = 10;
  InvestabilityStatus investability_status = 11;
  string maturity_date = 12;
  string tenure = 13;
  int32 tenure_in_days = 14;
  TagConfig tag_config = 15;
  string isin_code = 18;
  bool is_tzero_supported = 20;
  repeated CollectionItemOfferingDetails bond_offering_details = 21;
  double minimum_investment = 23;
  double sold_out_percentage = 24;
  string button_cta = 25;
  double strucken_yield = 26;
  string selling_point = 27;
  repeated MediaItem media_items = 28;
}

message AllCollectionsRequest {
  string secret_key = 1;
}

message AllCollectionsResponse {
  message Collection {
    string name = 1;
    string title = 2;
  }
  repeated Collection collections = 1;
}