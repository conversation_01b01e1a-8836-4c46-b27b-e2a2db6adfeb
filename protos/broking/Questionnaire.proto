syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

enum QuestionType {
  QUESTION_TYPE_UNKNOWN = 0;
  SINGLE_CHOICE = 1;
  MULTIPLE_CHOICE = 2;
}

message QuestionListResponse {
  repeated QuestionResponse question_data = 1;
}
message QuestionResponse {
  string id = 1;
  string question = 2;
  string description = 3;
  string button_text = 4;
  bool is_skippable = 5;
  QuestionType question_type = 6;
  repeated QuestionAnswerResponse answer_data = 7;
}

message QuestionAnswerResponse {
  string id = 1;
  string answer = 2;
}

message QuestionsAnswerSubmitRequest {
  repeated AnswerData data = 1;
  bool is_skipped = 2;
}

message AnswerData {
  string question_id = 1;
  repeated string answer_data = 2;
}

message QuestionsAnswerSubmitResponse {
}