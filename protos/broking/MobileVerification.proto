syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Auth.proto";

message InitiateMobileVerificationRequest {
  string mobile = 1 ;
  string country_code = 2 ;
}

message InitiateMobileVerificationResponse {
  OTPChallenge otp_challenge = 1;
}

message RespondToMobileVerificationChallenge {
  string challenge_id = 1 ;
  string answer = 2 ;
}

message RespondToMobileVerificationChallengeResponse {
  bool expired = 1;
  string message = 2;
}
