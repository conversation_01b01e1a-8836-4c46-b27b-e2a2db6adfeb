syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

enum BankVerificationType {
  UNKNOWN_TYPE = 0;
  PENNY_DROP = 1;
  REVERSE_PENNY_DROP = 2;
  CANCEL_CHEQUE = 3;
}

enum BankVerificationProvider {
  UNKNOWN_PROVIDER = 0;
  DIGIO_PD = 1;
  SETU_RPD = 2;
  CHEQUE = 3;
  SIGNZY_OCR_READ = 4;
  CASHFREE_RPD = 5;
}

message InitiateBankVerificationRequest {
  BankVerificationType type = 1;
  oneof result {
    BankAccount bank_account = 2;
    Cheque cheque = 3;
  }
  string transaction_id = 4;
}

message BankAccount {
  string beneficiary_account_no = 1 ;
  string ifsc_id = 2 ;
  string beneficiary_name = 3 ;
}



message InitiateBankVerificationResponse {
  oneof result{
    InitiatePdResponse initiate_pd_response = 2;
    InitiateRpdResponse initiate_rpd_response = 3;
    InitiateChequeResponse initiate_cheque_response = 4;
  }
}

message InitiatePdResponse {
  bool verified = 1;
}

message InitiateRpdResponse {
  enum PaymentApp {
      UNKNOWN_APP = 0;
      PAYTM = 1;
      BHIM = 2;
      GPAY = 3;
      PHONEPE = 4;
      MISC = 5;
  };
  message PaymentOption {
      PaymentApp app = 1;
      string logo_url = 2;
      string upi_intent = 3;
  };
  repeated PaymentOption payment_options = 1;
  string valid_upto = 2;
  string ref_id = 3;
  string qr_code = 4;
}

message RpdStatusRequest {
  string ref_id = 1;
}

message RpdStatusResponse {
  string ref_id = 1;
  string status = 3;
  bool verified = 4;
}

message AdditionalData {
  string ref_id = 1;
}

message BankListResponse {
  repeated BankList data = 2;
}

message BankList {
  string bank_name = 1;
  string ifsc = 2;
  string logo = 3;
  bool is_popular = 4;
  string id = 5;
}

message AddBankAccountRequest {
  string phone_no = 1;
  string beneficiary_account_no = 2 ;
  string ifsc_code = 3 ;
}

message AddBankAccountResponse {
  bool is_verified = 1;
  string is_added_to_nse = 2;
}

message BankAccountDB {
  optional string id = 1;
  optional string account_number = 2;
  optional bool is_verified = 3;
  optional BankVerificationProvider verification_provider = 4;
  optional BankVerificationType verification_type = 5;
  optional string beneficiary_name_with_bank = 6;
  optional string ifsc_code = 7;
  optional string metadata = 8;
}

message BankAccountDBUpdateRequest {
  BankAccountDB bank_account = 1;
}

message BankAccountDBUpdateResponse {
  BankAccountDB bank_account = 1;
}

message UserBankAccountsResponse {
  repeated UserBankAccountInfo user_bank_accounts = 4;
}

message UserBankAccountInfo {
  string user_id = 1;
  string name = 2;
  string phone_no = 3;
  bool is_verified = 5;
  bool is_primary = 6;
  string pd_status = 7;
  string rpd_status = 8;
  bool fuzzy_match_result = 9;
  double fuzzy_match_score = 10;
  BankAccountDB bank_account = 11;
}

message IfscListResponse {
  repeated IfscList data = 2;
}

message IfscList {
  string bank = 1;
  string branch = 2;
  string ifsc = 3;
  string id = 4;
  string bank_logo_url = 5;
}

message BankStatusResponse {
  bool is_verified = 1;
  string rpd_status = 2;
  string pd_status = 3;
  string cheque_status = 4;
  string metadata = 5;
  bool fuzzy_match_result = 6;
  string account_number = 7;
  string account_holder_name = 8;
  string bank_name = 9;
  string branch_name = 10;
  string ifsc_code = 11;
  string logo = 12;
}

message Cheque {
  string document_id = 1;
}

message InitiateChequeResponse {
}