syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

enum DocumentType {
  UNKNOWN_DOCUMENT_TYPE = 0;
  WET_SIGNATURE_DOCUMENT_TYPE = 1;
  ESIGNATURE_DOCUMENT_TYPE = 2;
  CANCEL_CHEQUE_DOCUMENT_TYPE = 3;
  SELFIE_IMAGE_DOCUMENT_TYPE = 4;
  DEMAT_CML_REPORT_TYPE = 5;
}

enum BondDocumentType {
  UNKNOWN_BOND_DOCUMENT_TYPE = 0;
  ORDER_RECEIPT_BOND_DOCUMENT_TYPE = 1;
  DEAL_SHEET_BOND_DOCUMENT_TYPE = 2;
  QUOTE_RECEIPT_BOND_DOCUMENT_TYPE = 3;
}

message DocumentUploadResponse {
  string document_id = 1;
}

message BondDocumentUploadResponse {
  string s3_key = 1;
}

message BondDocumentUploadRequest {
  BondDocumentType bond_document_type = 1;
  optional CreateOrderTicketRequest create_order_ticket_request = 2;
  optional DealSheetRequest deal_sheet_request = 3;
  optional QuoteReceiptRequest quote_receipt_request = 5;
  string user_id = 4;
}

message CreateOrderTicketRequest {
  string trade_type = 1;
  string security_name = 2;
  string isin = 3;
  string security_issuer_name = 4;
  string seller_name = 5;
  string buyer_name = 6;
  string buyer_pan = 7;
  string buyer_bank_account_number = 8;
  string buyer_dp_id = 9;
  string buyer_demat_account_number = 10;
  string debenture_trustee = 11;
  string rating_agency = 12;
  string rating = 13;
  string issuance = 14;
  string depository = 15;
  string settlement_platform = 16;
  string trade_date_time = 17;
  string settlement_date = 18;
  string interest_type = 19;
  string offered_yield = 20;
  string interest_payment_frequency = 21;
  int32 purchase_quantity = 22;
  double face_value = 23;
  double bond_price = 24;
  double clean_price = 25;
  double dirty_price = 26;
  double accrued_interest = 27;
  double total_consideration = 28;
  string iccl_benficiary_name = 29;
  string iccl_bank_name = 30;
  string iccl_bank_account_number = 31;
  string iccl_account_type = 32;
  string iccl_ifsc_code = 33;
  string iccl_branch = 34;
  string iccl_mode = 35;
  string order_id = 36;
}

message DealSheetRequest {
  string market_type = 1;
  string quote_type = 2;
  string deal_type = 3;
  string deal_id = 4;
  string order_number = 5;
  string settlement_no = 6;
  string buyer = 7;
  string seller = 8;
  string isin = 9;
  string issuer_name = 10;
  string maturity_date = 11;
  int32 quantity = 12;
  string trade_date = 13;
  string settlement_type = 14;
  string settlement_date = 15;
  double bond_price = 16;
  double trade_value = 17;
  double accrued_interest = 18;
  double buyer_consideration = 19;
  double stamp_duty = 20;
  double seller_consideration = 21;
  string deal_time = 22;
  string reported_time = 23;
  string approved_time = 24;
  string yield_type = 25;
  double yield = 26;
  string status = 27;
  string order_id = 28;
  string trade_date_time = 29;
  string settlement_time = 30;
}

message QuoteReceiptRequest {
  string order_number = 1;
  string name_of_seller = 2;
  string seller_cin = 3;
  string security_name = 4;
  string isin = 5;
  string issuer_name = 6;
  string name_of_buyer = 7;
  string buyer_pan = 8;
  string buyer_demat_account_number = 9;
  string rating = 10;
  string issuance = 11;
  string depository = 12;
  string settlement_platform = 13;
  string date_and_time_of_quote = 14;
  string date_of_quote = 15;
  int32 quantity_quoted = 16;
  int32 amount_quoted = 17;
}