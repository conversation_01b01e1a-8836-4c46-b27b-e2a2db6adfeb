syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Common.proto";
import "BondDetails.proto";


enum FilterType {
  UNKNOWN_FILTER_TYPE = 0;
  RANGE_MULTI_SELECT = 1;
  MULTI_SELECT_WITH_ICON = 2;
  MULTI_SELECT_PILLS = 3;
  BOOLEAN_SELECT = 4;
  MULTI_SELECT = 5;
}

enum OptionLogic {
  AND = 0;
  OR = 1;
}

enum SortType {
  UNKNOWN_SORT_TYPE = 0;
  YTM_HIGH_TO_LOW = 1;
  TENURE_HIGH_TO_LOW = 2;
  TENURE_LOW_TO_HIGH = 3;
  RATING_HIGH_TO_LOW = 4;
  POPULARITY_HIGH_TO_LOW = 5;
}

message RangeValue {
  double lower_bound = 1;
  double upper_bound = 2;
}

message FilterOptionValue {
  oneof option_value {
    string string_value = 1;
    RangeValue range_value = 2;
    bool bool_value = 3;
  }
}

message FilterOption {
  string label = 1;
  string icon = 2;
  FilterOptionValue option_value = 3;
  bool is_selected = 4;
  string label_description = 5;
}

message FilterItem {
  FilterType type = 1;
  string label = 2;
  string short_label = 3;
  string key = 4;
  bool is_collapsible = 5;
  bool is_collapsed = 6;
  bool is_quick_filter = 7;
  repeated FilterOption options = 8;
  optional string info = 9;
}

message FilterConfig {
  string title = 1;
  repeated FilterItem items = 2;
}

message SortItem {
  SortType type = 1;
  string label = 2;
}

message SortConfig {
  string title = 1;
  repeated SortItem items = 2;
}

message FilterSortConfigResponse {
  FilterConfig filter = 1;
  SortConfig sort = 2;
}

message FilterSortConfigRequest {
}

message BondItem {
  string tenure = 1;
  TagConfig tag = 2;
  string logo_url = 3;
  string display_title = 4;
  double ytm = 5;
  string rating = 6;
  string isin = 7;
  string slug = 8;
  InvestabilityStatus investability_status = 9;
}

message EmptyState {
  string title = 1;
  string sub_title = 2;
  repeated FilterItem suggested_filters = 3;
  string alternate_title = 4;
  repeated BondItem alternate_items = 5;
}

message FilterSortQueryResponse {
  int32 count = 1;
  repeated BondItem items = 2;
  map<int32, FilterItem> suggested_filters = 3;
  EmptyState empty_state = 4;
}

message FilterSortQueryRequest {
  repeated FilterQuery filters = 1;
  SortType sort = 2;
  PaginationRequest pagination_request = 3;
}

message FilterQuery {
  string key = 1;
  repeated FilterOptionValue options = 2;
}