syntax = "proto3";

package com.stablemoney.api.broking;

option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

import "Common.proto";

enum RepaymentFrequency {
  REPAYMENT_FREQUENCY_UNKNOWN = 0;
  MONTHLY = 1;
  QUARTERLY = 2;
  HALF_YEARLY = 3;
  YEARLY = 4;
  CUMULATIVE = 5;
}

enum InvestabilityStatus {
  INVESTABILITY_STATUS_UNKNOWN = 0;
  LIVE = 1;
  SOLD_OUT = 2;
  COMING_SOON = 3;
  INACTIVE = 4;
}

message InstitutionStat {
  string key = 1;
  string value = 2;
}

message InstitutionDetails {
  string title = 1;
  string description = 2;
  string sector = 3;
  string bond_institution_webpage = 4;
  string bond_institution_name = 5;
  string logo = 6;
  repeated InstitutionStat stats = 7;
  string id = 8;
  string slug = 10;
}

enum InventoryAlarmStatus {
  ALARM_STATUS_UNKNOWN = 0;
  NORMAL = 1;
  LEVEL_1 = 2;
}

message InterestPaymentSchedule {
  string type = 1;
  double interest_amount = 2;
  string date = 3;
  double principal_amount = 4;
  string date_time = 5;
}

message BondTagItem {
  string name = 1;
  string tag_type = 2;
}

message BondHighlight {
  DataKey data_key = 1;
}

message BondFactSheet {
  DataKey label = 1;
  DataKey value = 2;
}

message BondFaqItem {
  string question = 1;
  string answer = 2;
}

message BondQuantity {
  int32 total_count = 1;
  int32 available_count = 2;
  int32 min_count = 3;
  int32 max_count = 4;
}

message MarketStatus {
  bool is_active = 1;
  string message = 2;
}

message SupportedDocument {
  string name = 1;
  string url = 2;
}

message MediaItem {
  enum MediaType {
    UNKNOWN = 0;
    IMAGE = 1;
    VIDEO = 2;
  }
  enum ScreenType {
    SCREEN_TYPE_UNKNOWN = 0;
    MOBILE = 1;
    DESKTOP = 2;
    TABLET = 3;
  }
  string section = 1;
  MediaType media_type = 2;
  string url = 3;
  ScreenType screen_type = 4;
  RedirectDeeplink redirect_deeplink = 5;
}

message BondDetailItem {
  string bond_name = 1;
  string bond_type = 2;
  double xirr = 3;
  string time_left_to_maturity = 4;
  string maturity_date = 5;
  string isin_code = 6;
  string interest_payment = 7;
  string principal_repayment = 8;
  double minimum_investment = 9;
  string display_title = 10;
  string id = 11;
  string rating = 12;
  double price_per_bond = 13;
  double clean_price = 14;
  double dirty_price = 15;
  double accrued_interest = 16;
  int32 issue_size = 17;
  string rating_agency = 18;
  string rating_supporting_url = 19;
  double coupon_rate = 20;
  int32 count = 21;
  string settlement_date = 22;
  InstitutionDetails about_the_institution = 23;
  repeated InterestPaymentSchedule interest_payment_and_principal_repayment_schedule = 24;
  repeated BondTagItem bond_tags = 25;
  repeated BondFaqItem bond_faqs = 26;
  BondQuantity bond_quantity = 27;
  MarketStatus market_status = 28;
  string bg_color = 29;
  string face_value = 30;
  string issue_date = 31;
  string issue_mode = 32;
  string information_memorandum = 33;
  string debenture_trustee = 34;
  string rating_date = 35;
  string financial_snapshot_url = 36;
  repeated SupportedDocument supported_documents = 37;
  bool is_active = 38;
  string cover_image_url = 39;
  string nature = 40;
  string seniority = 41;
  string coupon_type = 42;
  string seller_entity_disclosure = 43;
  repeated BondHighlight highlights = 45;
  InvestabilityStatus investability_status = 46;
  int32 default_quantity = 47;
  repeated BondFactSheet bond_fact_sheet = 48;
  repeated MediaItem media_items = 50;
  int32 min_quantity = 51;
  optional BondAmountCalculatorResponse default_calculation_response = 52;
}

message HomePageBondItem {
  string display_title = 1;
  string rating_agency = 2;
  string rating = 3;
  double xirr = 4;
  string id = 5;
  string bg_color = 6;
  InstitutionDetails about_the_institution = 7;
  string interest_payment = 8;
  string face_value = 9;
  string cover_image_url = 10;
}

message HomepageConfig {
  string key = 1;
  string value = 2;
}

message HomaPageResponse {
  repeated HomePageBondItem featured_bonds = 2;
}

message BondAmountCalculatorResponse {
  double total_consideration = 1;
  double accrued_interest = 2;
  double stamp_duty = 3;
  double purchase_amount = 4;
  double maturity_amount = 5;
  double average_payout_amount = 6;
}

message BondDetailsRequest {
  string bond_detail_id = 1;
  optional string user_id = 2;
}

message BondDetailsResponse {
  BondDetailItem bond_details = 1;
}

message BondOfferingUpdatesRequest {
  repeated BondOfferingUpdate bond_offering_updates = 1;
}

message BondOfferingUpdate {
  string bond_offering_detail_id = 1;
  int32 inventory_delta = 2;
  bool set_is_active_status = 3;
}

message GenerateBondCreationSheetRequest {
  string bond_detail_id = 1;
}

message ChangeBondStatusRequest {
  string bond_detail_id = 1;
  InvestabilityStatus status = 2;
}

message BondDetailsByIssuerResponse {
  string display_title = 1;
  string id = 2;
  string information_memorandum = 3;
  bool is_active = 4;
  string isin_code = 5;
  string issue_date = 6;
  double issue_face_value = 7;
  string issue_mode = 8;
  double issue_price = 9;
  int32 issue_size = 10;
  string issuer = 11;
  string maturity_date = 12;
  string name = 13;
  string nature_of_bond = 14;
  RepaymentFrequency principal_repayment_frequency = 15;
  string principal_repayment_frequency_desc = 16;
  string put_call = 17;
  string rating = 18;
  string rating_agency = 19;
  string rating_date = 20;
  string rating_supporting_url = 21;
  string type = 22;
  string type_of_yield = 23;
  string updated_at = 24;
  string bond_issuing_institution_id = 25;
  string coupon_type = 26;
  string nature = 27;
  string seniority = 28;
  double yield = 29;
  int32 per_user_purchase_limit = 30;
  int32 min_lot_size = 31;
  InvestabilityStatus investability_status = 32;
}

message AllBondsRequest {
  int32 page_number = 1;
  int32 page_size = 2;
  string secret_key = 3;
}

message AllBondsResponse {
  message Bond {
    string issuer_slug = 1;
    string isin_code = 2;
    string name = 3;
  }
  repeated Bond bonds = 1;
  int32 total_count = 2;
}