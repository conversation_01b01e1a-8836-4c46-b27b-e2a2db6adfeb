syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

enum DeviceType {
  CLIENT_TYPE_UNKNOWN = 0;
  ANDROID = 1;
  IOS = 2;
  WEB = 3;
  MOBILE_WEB = 4;
  APP_WEB = 5 [deprecated = true];
}

enum DevicePlatform {
  PLATFORM_UNKNOWN = 0;
  PLATFORM_FLUTTER = 1;
  PLATFORM_WEB = 2;
}

message UserDevice {
  string id = 1 ;
  DeviceType device_type = 2;
  string os_version = 3;
  string model = 4;
  string app_version = 5;
  string last_active_time = 6;
  string notification_token = 7;
}

message UpdateDeviceRequest {
  UserDevice user_device = 1;
}

message UpdateDeviceResponse {
}