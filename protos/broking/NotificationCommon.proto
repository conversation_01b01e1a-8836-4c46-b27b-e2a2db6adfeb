syntax = "proto3";

package com.stablemoney.api.notifications;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;


enum NotificationChannelType {
  UNKNOWN_CHANNEL = 0;
  EMAIL = 1;
  SMS = 2;
  PUSH = 3;
  WHATSAPP = 4;
}

message SendNotificationRequest{
  string user_id = 1;
  string notification_name = 2;
  optional OtpRequest otp_request = 3;
  optional int32 retry_count = 4;
  optional string failed_provider = 5;
  optional string s3_key = 6;
}

message VkycInviteRequest{
  string id = 1;
  string dtstamp = 2;
  string dtstart = 3;
  string dtend = 4;
  string bank_name = 5;
  string sequence = 6;

}
message ProfileCompletionEmailRequest{
  bool is_large_city = 1;
  int32 user_city_count = 2;
  string ordinal_suffix = 3;
  string city_name = 4;
  string first_name = 5;
  string last_name = 6;
  int32 national_average_cost_of_living = 7;
  int32 user_city_cost_of_living = 8;
  bool has_fd = 9;
  string formatted_city_count = 10;
}

message OtpRequest{
  string otp = 1;
}

message SendNotificationResponse{
}

message GetInvestedUsersResponse{
  repeated string user_ids = 1;
}

message GetInvestedUsersRequest{
  int32 page = 1;
  int32 size = 2;
}

service NotificationService {
  rpc sendNotification(SendNotificationRequest) returns (SendNotificationResponse);
  rpc whatsappOptin(WhatsappOptinRequest) returns (WhatsappOptinResponse);
  rpc getInvestedUsers(GetInvestedUsersRequest) returns (GetInvestedUsersResponse);
}

message WhatsappOptinRequest{
  string mobile_number = 1;

}
message WhatsappOptinResponse{
}

enum TemplateType {
  UNKNOWN_TEMPLATE = 0;
  OTP = 1;
  TRANSACTIONAL = 2;
  PROMOTIONAL = 3;
}

enum WhatsappMessageType {
  UNKNOWN_TYPE = 0;
  TEXT = 1;
  IMAGE = 2;
  TEXT_WITHOUT_CTA = 3;
  IMAGE_WITHOUT_CTA = 4;
}

enum WhatsappMethodType {
  UNKNOWN_METHOD_TYPE = 0;
  SendMessage = 1;
  SENDMEDIAMESSAGE = 2;
}

message NotificationStatus{
  string notification_id = 1;
  string user_id = 2;
  string notification_name = 3;
  NotificationChannelType channel_type = 4;
  NotificationStatusEnum notificationStatus = 5;
}

enum NotificationStatusEnum {
  UNKNOWN_STATUS = 0;
  ADDED_TO_QUEUE = 1;
  SENT = 2;
  FAILED = 3;
}

enum NotificationHandlerType{
  UNKNOWN_HANDLER = 0;
  STATIC_CONTENT_HANDLER = 1;
  USER_DATA_HANDLER = 2;
  OTP_HANDLER = 3;
  OTP_AND_USER_DATA_HANDLER = 4;
  DOCUMENTS_HANDLER = 5;
}


enum AttachmentType{
  UNKNOWN_ATTACHMENT_TYPE = 0;
  PDF_ATTACHMENT_TYPE = 1;
}
