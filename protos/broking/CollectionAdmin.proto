syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Common.proto";

message CreateCollectionRequest {
  string name = 1;
  string title = 2;
  string description = 3;
  CollectionType collection_type = 4;
  string expression_where_clause = 5;
  string expression_order_by_clause = 6;
  bool is_active = 7;
  bool include_sold_out = 8;
  string icon_url = 9;
}
message CreateCollectionResponse {
}
message CreateBulkCollectionsRequest {
  repeated CreateCollectionRequest collections = 1;
}
message CreateBulkCollectionsResponse{

}


message CreateCollectionItemRequest {
  string isin_code = 1;
  string issuing_institution_id = 2;
  string display_title = 3;
  bool is_active = 4;
  int32 priority = 5;
  string collection_id = 6;
  TagConfig tagConfig = 7;
  optional string id = 8;
}
message CreateCollectionItemResponse {

}
message CreateBulkCollectionItemsRequest {
  repeated CreateCollectionItemRequest collection_items = 1;
}
message CreateBulkCollectionItemsResponse {

}