syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Common.proto";

enum RelationshipType {
    UNKNOWN_RELATIONSHIP = 0;
    FATHER = 1;
    MOTHER = 2;
    DAUGHTER = 3;
    SON = 4;
    SPOUSE = 5;
    OTHER = 7;
    BROTHER = 8;
    SISTER = 9;
}

message NomineeDetails {
  string name = 1 ;
  RelationshipType relationship_type = 2  ;
  string dob = 3;
  string relationship = 4;
  GuardianDetails guardian_details = 5;
  AddressProto address = 6;
  double allocation_percentage = 7 ;
  string id = 8;
}

message GuardianDetails {
  string name = 1 ;
  RelationshipType relationship_type = 2;
  string relationship = 3;
  AddressProto address = 4;
}

message AddNomineeRequest {
  bool opt_out = 1;
  repeated NomineeDetails nominee_details = 2 ;
}

message AddNomineeResponse {
  bool status = 1;
  string description = 2;
}

message GetNomineeRequest {
}

message GetNomineeResponse {
  repeated NomineeDetails nominee_details = 1;
}


message UpdateNomineeRequest {
  NomineeDetails nominee_details = 2;
}