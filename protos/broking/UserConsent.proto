syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

enum ConsentType {
  BONDS_CONSENT = 0;
}
message Consent {
  ConsentType consent_type = 1;
  bool consent_value = 2;
}
message UserConsentRequest {
  repeated Consent consents = 1;
}

message UserConsentResponse {

}

message GetUserConsentResponse {
  repeated Consent consents = 1;
}