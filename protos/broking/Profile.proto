syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Onboarding.proto";
import "Common.proto";

message UserData {
  string id = 1;
  string email = 2;
  bool email_verified = 3;
  string mobile = 4;
  bool mobile_verified = 5;
  string name = 6;
  UserStatus status = 7;
  string masked_email = 8;
  string first_name = 9;
  string last_name = 10;
  string social_name = 11;
  string profile_image_url = 12;
}

enum UserStatus {
  USER_STATUS_UNKNOWN = 0;
  REGISTRATION_PENDING = 1;
  ACTIVE = 2;
  DISABLED = 3;
}

message NomineeDetail {
  string name = 1;
  string dob = 2;
  string relationship = 3;
}

message UserProfileResponse {
  UserData data = 2;
  UserProfileData profile_data = 3;
  repeated OnboardingModuleStatusData module_status = 4;
  string life_time_status = 5; //
  string current_status = 6; // KYC_NOT_INITIATED , KYC_INITIATED , KYC_COMPLETED, BOND_PURCHASED
  optional string account_opening_form_url = 7;
  optional BankAccountDetail bank_account_detail = 8;
  optional DematAccountDetail demat_account_detail = 9;
  bool cvl_validated = 10;
  optional NomineeDetail nominee_detail = 11;
}

message UserProfileData {
  string id = 1;
  string pan_number = 2;
  string aadhar_number = 3;
  string dob = 4;
  Gender gender = 5;
  IncomeRange income_range = 6;
  EmploymentType employment_type = 7;
  TradingExperience trading_experience = 8;
  MaritalStatus marital_status = 9;
  string father_name = 10;
  string mother_name = 11;
  string e_sign_url = 12;
  string income_tax_department_name = 13;
  string kra_name = 14;
  string referral_link = 15;
  int32 fd_booking_count = 16;
  bool first_fd_reward_claimed = 17;
}

message BankAccountDetail {
  string bank_name = 1;
  string account_number_masked = 2;
  string branch = 3;
  string ifsc_code = 4;
}

message DematAccountDetail {
  string demat_id = 1;
  string broker_name = 2;
}

message OnboardingModuleStatusData{
  OnboardingModule name = 1;
  bool status = 2;
}

message UpdateNameResponse {
}

message UpdateNameRequest {
  string first_name = 1;
  string last_name = 2;
  optional bool update_name = 3;
}

message DeleteUserRequest {
  string user_id = 1;
  string reason = 2 ;
}

message DeleteUserResponse {
}

enum ClientCategory {
  USER_CATEGORY_UNKNOWN = 0;
  INDIVIDUAL = 1;
  CORPORATE = 2;
  BROKER = 3;
  INSURANCE_COMPANY = 4;
  HUF = 5;
}

message UserOnboardingResponse {
  bool is_registered = 1;
  string status = 2;
}

message UserResponse {
  string id = 1;
  string name = 2;
  string phone_number = 3;
  bool phone_verified = 4;
  string email = 5;
  bool email_verified = 6;
  string profile_image_url = 8;
  string masked_email = 12;
  string social_name = 13;
  string first_name = 14;
  string last_name = 15;
  string unverified_email = 16;
  string created_at = 17;
}

message GetCurrentAddressRequest {
}

message GetCurrentAddressResponse {
  AddressProto current_address = 1;
}