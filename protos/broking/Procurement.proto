syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;


message ProcurementDetails{
  string bond_detail_id = 1;
  string seller_id = 2;
  int32 count = 3;
  double clean_price = 4;
  double dirty_price = 5;
  double face_value = 6;
  double xirr = 7;
  string procurement_date = 8;
  string id = 9;
  string procurement_purpose = 10;
}

message ProcurementDetailsList{
  repeated ProcurementDetails procurement_details = 1;
}
message ExternalPnlLedger{
  string bond_procurement_id = 1;
  int32 quantity = 2;
  double sale_clean_price = 3;
  double sale_dirty_price = 4;
  string settlement_date = 5;
  double sale_face_value = 6;
  string bond_offering_id = 7;
  string entry_type = 8;
  double pnl_per_bond = 9;
}