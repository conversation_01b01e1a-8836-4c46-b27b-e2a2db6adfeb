syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;
import "Common.proto";
import "BankAccountVerification.proto";
import "Demat.proto";
import "Nominee.proto";

enum StepName {
  KYC_TYPE_UNKNOWN = 0;
  POI = 1;
  KYC = 2;
  POA = 3;
  SELFIE = 4;
  ESIGN = 5;
  BANK_ACCOUNT = 6;
  DEMAT_ACCOUNT = 7;
  NOMINEE = 8;
  WET_SIGNATURE = 9;
  USER_PROFILE = 10;
  PAN_KYC = 11;
  NAME = 12;
  QUESTIONNAIRE = 13;
  EMAIL = 14;
  WHITELIST_CHECK = 15;
  KRA_KYC = 16;
  CVL_PUSH = 17;
  CVL_PULL = 18;
  ADDRESS_SYNC_CVL_PUSH = 19;
  AADHAAR_PAN_MATCH = 20;
  CURRENT_ADDRESS = 21;
  CVL_PARTIAL_FETCH = 22;
}

enum ProofType{
  PROOF_TYPE_UNKNOWN = 0;
  PAN = 1;
  AADHAR = 2;
  PASSPORT = 3;
  DRIVING_LICENSE = 4;
  VOTER_ID = 5;
  GOVT_ID = 6;
  REGULATORY_ID = 7;
  PSU_ID = 8;
  BANK_ID = 9;
  PUBLIC_FINANCIAL_INSTITUTION_ID = 10;
  COLLEGE_ID = 11;
  PROFESSIONAL_BODY_ID = 12;
  CREDIT_CARD = 13;
  OTHER_ID = 16;
  BANK_PASSBOOK = 17;
  BANK_ACCOUNT_STATEMENT = 18;
  RATION_CARD = 19;
  LEASE_AGREEMENT_OR_SALE_AGREEMENT_OF_RESIDENCE = 20;
  LAND_LINE_TELEPHONE_BILL = 21;
  ELECTRICITY_BILL = 22;
  GAS_BILL = 23;
  FLAT_MAINTENANCE_BILL = 24;
  INSURANCE_COPY = 25;
  SELF_DECLARATION_BY_HIGH_COURT_OR_SUPREME_COURT_JUDGE = 26;
  POWER_OF_ATTORNEY_GIVEN_BY_FII_OR_SUB_ACCOUNT_TO_THE_CUSTODIANS = 27;
  POA_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS_OR_SCHEDULED_CO_OPERATIVE_BANKS_OR_MULTINATIONAL_FOREIGN_BANKS = 28;
  POA_ISSUED_BY_ELECTED_REPRESENTATIVES_TO_THE_LEGISLATIVE_ASSEMBLY_OR_PARLIAMENT = 29;
  POA_ISSUED_BY_PARLIAMENT = 30;
  POA_ISSUED_BY_ANY_GOVERNMENT_OR_STATUTORY_AUTHORITY = 31;
  POA_ISSUED_BY_NOTARY_PUBLIC = 32;
  POA_ISSUED_BY_GAZETTED_OFFICER = 33;
  ID_CARD_ISSUED_BY_CENTRAL_OR_STATE_GOVERNMENT = 34;
  ID_CARD_ISSUED_BY_STATUTORY_OR_REGULATORY_AUTHORITIES = 35;
  ID_CARD_ISSUED_BY_PUBLIC_SECTOR_UNDERTAKINGS = 36;
  ID_CARD_ISSUED_BY_SCHEDULED_COMMERCIAL_BANKS = 37;
  ID_CARD_ISSUED_BY_PUBLIC_FINANCIAL_INSTITUTIONS = 38;
  ID_CARD_ISSUED_BY_COLLEGES_AFFILIATED_TO_UNIVERSITIES = 39;
  ID_CARD_ISSUED_BY_PROFESSIONAL_BODIES_SUCH_AS_ICAI_ICWAI_ICSI_BAR_COUNCIL = 40;
  UIN_CARD = 41;
  OTHER_ID_MF = 42;
}

enum KycProvider {
  KYC_PROVIDER_UNKNOWN = 0;
  NONE = 1;
  CDSL = 2;
  DIGIO = 3;
  HYPERVERGE = 4;
  TARRAKKI = 5;
}

enum OnBoardingStatus {
  ONBOARDING_STATUS_UNKNOWN = 0;
  PENDING = 1;
  COMPLETE = 2;
  REJECTED = 3;
  SKIPPED = 4;
}

enum CvlMode {
  CVL_MODE_UNKNOWN = 0;
  VALIDATED_CVL_MODE = 1;
  CREATE_CVL_MODE = 2;
  UPDATE_CVL_MODE = 3;
  WAIT_CVL_MODE = 4;
}

enum CvlPushStatus {
  CVL_PUSH_STATUS_UNKNOWN = 0;
  CVL_PUSH_INITIATED = 1;
  CVL_PUSH_API_FAILED = 2;
  CVL_PUSH_API_SUCCESSFUL = 3;
  CVL_SFTP_UPLOAD_FAILED = 4;
  CVL_SFTP_UPLOAD_SUCCESSFUL = 5;
  CVL_VALIDATED = 6;
  CVL_PUSH_WAITING = 7;
  CVL_SOLICIT_PAN_SUCCESSFUL = 8;
}


message GenerateTokenRequest {
}

message GenerateTokenResponse {
  string id = 1;
  string access_token = 2;
  string customer_identifier = 3;
  bool is_new = 4;
}

message EmptyKycRequest {
}

message InitiateKycRequest {
  StepName kyc_type = 1;
  oneof result {
    PanKycRequest pan_kyc_request = 2;
    WetSignatureRequest wet_signature_request = 3;
    SelfieRequest selfie_request = 4;
    KycRequest kyc_request = 5;
    EsignRequest esign_request = 6;
    EmptyKycRequest empty_kyc_request = 7;
  }
}

message EsignRequest {
  EsignStep step = 1 ;
  oneof result {
    GenerateTokenRequest generate_token_request = 2;
    StatusRequest status_request = 3;
  }
}

enum EsignStep{
  ESIGN_STEP_UNKNOWN = 0;
  ESIGN_STEP_GENERATE_TOKEN = 1;
  ESIGN_STEP_STATUS = 2;
}

message KycRequest {
  KycStep step = 1 ;
  oneof result {
    GenerateTokenRequest generate_token_request = 2;
    StatusRequest status_request = 3;
  }
}

message InitiateKycResponse {
  oneof result{
    PanKycResponse pan_kyc_response = 1;
    WetSignatureResponse wet_signature_response = 2;
    SelfieResponse selfie_response = 3;
    KycResponse kyc_response = 4;
    EsignKycResponse esign_response = 5;
    KraKycResponse kra_kyc_response = 6;
    CvlPullResponse cvl_pull_response = 7;
    CvlPushResponse cvl_push_response = 8;
    AadhaarPanMatchResponse aadhaar_pan_match_response = 9;
    CvlPartialFetchResponse cvl_partial_fetch_response = 10;
  }
}

message OnboardingRequest {
  optional StepName step_name = 1;
  oneof result {
    PanKycRequest pan_kyc_request = 10;
    SelfieRequest selfie_request = 12;
    KycRequest kyc_request = 14;
    InitiateBankVerificationRequest verify_bank_request = 16;
    AddDematAccountRequest add_demat_request = 18;
    WetSignatureRequest wet_signature_request = 20;
    UpdateProfileRequest update_profile_request = 22;
    AddCurrentAddressRequest add_current_address_request = 23;
    AddNomineeRequest add_nominee_request = 24;
    EsignRequest esign_request = 26;
    EmptyKycRequest empty_kyc_request = 28;
  }
}

message OnboardingResponse {
  UserLifetimeStatusResponse.KycStatus kyc_status = 1;
  StepName next_step = 2;
  UserLifetimeStatus lifetime_status = 3;
  oneof result {
    EmptyKycResponse empty_kyc_response = 4;
    PanKycResponse pan_kyc_response = 10;
    CvlPartialFetchResponse cvl_partial_fetch_response = 12;
    SelfieResponse selfie_response = 14;
    KycResponse kyc_response = 16;
    AadhaarPanMatchResponse aadhaar_pan_match_response = 18;
    InitiateBankVerificationResponse verify_bank_response = 20;
    AddDematAccountResponse add_demat_response = 22;
    WetSignatureResponse wet_signature_response = 24;
    UpdateProfileResponse update_profile_response = 26;
    AddCurrentAddressResponse add_current_address_response = 27;
    AddNomineeResponse add_nominee_response = 28;
    EsignKycResponse esign_response = 30;
    CvlPullResponse cvl_pull_response = 32;
    CvlPushResponse cvl_push_response = 34;
  }
}

message EsignKycResponse {
  oneof result {
    GenerateTokenResponse generate_token_response = 1;
    StatusResponse status_response = 2;
  }
}

message KycResponse {
  oneof result {
    GenerateTokenResponse generate_token_response = 1;
    StatusResponse status_response = 2;
  }
}

message AadhaarPanMatchResponse {
  message AadhaarDetails {
    string aadhaar_name = 1;
    string aadhaar_number = 2;
  }
  message PanDetails {
    string pan_name = 1;
    string pan_number = 2;
  }
  bool aadhaar_pan_match_status  = 1;
  bool aadhaar_pan_dob_match = 2;
  bool aadhaar_pan_name_match = 3;
  AadhaarDetails aadhaar_details = 4;
  PanDetails pan_details = 5;
}

message SelfieRequest {
  SelfieKycStep step = 1 ;
  oneof result {
    StartSelfieStepRequest start_selfie_step_request = 2;
    SetSelfieStatusRequest set_selfie_status_request = 3;
  }
}

message StartSelfieStepRequest{
  string transaction_id = 1;
}


message SetSelfieStatusRequest{
  string transaction_id = 1;
  string status = 2 ;
}

message SelfieResponse {
  oneof result {
    StartSelfieStepResponse start_selfie_step_response = 1;
    SetSelfieStatusResponse set_selfie_status_response = 2;
  }
}

message StartSelfieStepResponse {
  string selfie = 1;
  string workflow_id = 2;
  string access_token = 3;
  string transaction_id = 4;
  bool completed = 5;
}

message SetSelfieStatusResponse {
  bool completed = 1;
  bool success = 2;
  string error_message = 3;
}

message PanKycResponse{
  PanKycStep step = 1;
  oneof result {
    KycFetchNameByPanResponse kyc_fetch_name_by_pan_response = 2;
    KycValidateNameAndGetPanStatusResponse kyc_validate_name_and_get_pan_status_response = 3;
  }
}

message KraKycResponse{
  PanKycStep step = 1;
  oneof result {
    string name = 2;
    string pan = 3;
  }
}

message CvlPullResponse {
  bool status = 1;
}

message CvlPushResponse {
  bool status = 1;
}

message CvlPartialFetchResponse {
  bool status = 1;
}

message EmptyKycResponse {}

enum PanKycStep {
  UNKNOWN_PAN_STEP = 0;
  NAME_FETCH = 1;
  PAN_STATUS = 2;
}


enum KycStep {
  UNKNOWN_STEP = 0;
  GENERATE_TOKEN = 1;
  GET_STATUS = 2;
}


enum SelfieKycStep {
  UNKNOWN_SELFIE_STEP = 0;
  START_SELFIE_STEP = 1;
  SET_STATUS = 2;
}


message PanKycRequest {
  PanKycStep step = 1;
  oneof result {
    KycFetchNameByPanRequest kyc_fetch_name_by_pan_request = 2;
    KycValidateNameAndGetKycStatusRequest kyc_validate_name_and_get_kyc_status_request = 3;
  }
}

message KycFetchNameByPanResponse {
  string pan = 1;
  string full_name = 2;
}

message KycFetchNameByPanRequest {
  string pan = 1 ;
}

message KycValidateNameAndGetPanStatusRequest{
  bool is_name_match = 1;
  string dob = 2 ;
}

message KycValidateNameAndGetPanStatusResponse {
  bool pan_kyc_status = 1;
  bool name_match_status = 2;
  bool dob_match_status = 3;
  bool is_pan_valid = 4;
}

message StatusRequest {
  string id = 1;
}

message StatusResponse {
  string kyc_status = 1;
  string description = 2;
}

enum RaaDuration {
  UNKNOWN_RAA_DURATION = 0;
  RAA_60_DAYS = 1;
  RAA_90_DAYS = 2;
}

message WetSignatureRequest{
  RaaDuration raa_duration = 1 ;
  bool is_pep = 2 ;
  bool is_indian_citizen = 3 ;
  string document_id = 4 ;
  bool credit_report_consent = 5 ;
}

message WetSignatureResponse{
}

message KycValidateNameAndGetKycStatusRequest {
  string full_name = 1;
  string dob = 2;
  string pan_number = 3 ;
}

message AddSanctionedPeopleRequest {
  string excel_s3_bucket_name = 1;
  string excel_s3_key = 2;
}