syntax = "proto3";

package com.stablemoney.api.broking;
option java_package = "com.stablemoney.api.broking";
option java_multiple_files = true;

message BankFaqResponse {
  repeated Faq bank_faq = 1;
}

message Faq {
  string question = 1;
  string answer = 2;
  string html_answer = 3;
}

message SupportFaqResponse {
  repeated Faq bank_faq = 1;
}

message SupportFaqV2 {
  Faq faq = 1;
  string support_faq_icon_url = 3;
}

message SupportFaqResponseV2 {
  string category_id = 1 ;
  string category_name = 2;
  string category_icon_url = 3;
  repeated SupportFaqV2 faqs = 4;
}

message SupportFaqCategoryResponseV2 {
  repeated SupportFaqResponseV2 category_support_faq = 1 ;
  string top_category_id = 2 ;
}

enum FaqType {
  UNKNOWN_FAQ_TYPE = 0;
  DEFAULT_FAQ_TYPE = 1;
  REWARD_FAQ_TYPE = 2;
  REFERRAL_FAQ_TYPE = 3;
  CATEGORY_FAQ_TYPE = 4;
}
