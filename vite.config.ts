import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react-swc";
import tailwindcss from "@tailwindcss/vite";
import tsconfigPaths from "vite-tsconfig-paths";
import { sentryVitePlugin } from "@sentry/vite-plugin";
import path from "path";

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), "");
  const useStub = env.VITE_USE_STUB_SDK === "true";
  const isProduction = mode === "production";

  const plugins = [react(), tailwindcss(), tsconfigPaths({ loose: true })];

  // Add Sentry plugin only for production builds with source maps
  if (isProduction && env.VITE_SENTRY_DSN) {
    plugins.push(
      sentryVitePlugin({
        org: env.SENTRY_ORG,
        project: env.SENTRY_PROJECT,
        authToken: env.SENTRY_AUTH_TOKEN,
        sourcemaps: {
          assets: "./dist/**",
        },
        release: {
          name:
            env.VITE_SENTRY_RELEASE || `bonds-web@${new Date().toISOString()}`,
        },
      })
    );
  }

  return {
    base: env.VITE_PUBLIC_BASE_URL || "/",
    plugins,
    build: {
      manifest: true,
      sourcemap: isProduction, // Generate source maps for production
    },
    resolve: {
      alias: {
        ...(useStub
          ? {
              "@/utils/sdk-loader": path.resolve(
                __dirname,
                "tests/stubs/sdk-loader.ts"
              ),
            }
          : {}),
      },
    },
  };
});
